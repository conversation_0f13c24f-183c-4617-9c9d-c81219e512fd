"""
Diagnostic script for city search issues in the Czech Property Registry application.

This script:
1. Tests the Google Maps geocoding for specific cities
2. Tests the city boundary detection
3. Tests the coordinate generation for batch search
4. Tests the coordinate conversion between different systems
5. Outputs detailed diagnostic information
"""

import os
import sys
import logging
import json
import time
from typing import Dict, Any, List, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Cities to test
TEST_CITIES = [
    "Stod",
    "Mariánské Lázně",
    "Praha",
    "Brno",
    "Ostrava",
    "Plzeň",
    "Liberec",
    "Olomouc",
    "České Budějovice",
    "Hradec Králové",
    "Ústí nad Labem",
    "Pardubice",
]

def import_required_modules():
    """Import required modules from the application."""
    try:
        # Add the current directory to the path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # Import required modules
        from api.google_maps.google_maps_integration import GoogleMapsIntegration
        from api.google_maps.city_boundaries import get_city_boundaries
        from api.cuzk.cuzk_integration import CUZKIntegration
        from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk, convert_sjtsk_to_wgs84
        
        return {
            'GoogleMapsIntegration': GoogleMapsIntegration,
            'get_city_boundaries': get_city_boundaries,
            'CUZKIntegration': CUZKIntegration,
            'convert_wgs84_to_sjtsk': convert_wgs84_to_sjtsk,
            'convert_sjtsk_to_wgs84': convert_sjtsk_to_wgs84,
        }
    except ImportError as e:
        logger.error(f"Error importing required modules: {e}")
        return None

def test_google_maps_geocoding(modules, city: str) -> Dict[str, Any]:
    """Test the Google Maps geocoding for a specific city."""
    logger.info(f"Testing Google Maps geocoding for city: {city}")
    
    try:
        # Create a Google Maps Integration instance
        google_maps = modules['GoogleMapsIntegration']()
        
        # Geocode the city
        geocode_result = google_maps.geocode(f"{city}, Czech Republic")
        
        if geocode_result:
            logger.info(f"Geocode result for {city}: {geocode_result}")
            return {
                'success': True,
                'city': city,
                'lat': geocode_result.get('lat'),
                'lng': geocode_result.get('lng'),
                'formatted_address': geocode_result.get('formatted_address'),
                'place_id': geocode_result.get('place_id'),
            }
        else:
            logger.error(f"Failed to geocode city: {city}")
            return {
                'success': False,
                'city': city,
                'error': "Failed to geocode city",
            }
    except Exception as e:
        logger.error(f"Error testing Google Maps geocoding for {city}: {e}")
        return {
            'success': False,
            'city': city,
            'error': str(e),
        }

def test_city_boundary_detection(modules, city: str, geocode_result: Dict[str, Any]) -> Dict[str, Any]:
    """Test the city boundary detection for a specific city."""
    logger.info(f"Testing city boundary detection for city: {city}")
    
    try:
        # Get city boundaries
        boundaries = modules['get_city_boundaries'](
            city_name=city,
            country="Czech Republic",
            google_maps_api=modules['GoogleMapsIntegration']()
        )
        
        if boundaries:
            logger.info(f"City boundaries for {city}: {boundaries}")
            return {
                'success': True,
                'city': city,
                'boundaries': boundaries,
            }
        else:
            logger.error(f"Failed to get city boundaries for: {city}")
            return {
                'success': False,
                'city': city,
                'error': "Failed to get city boundaries",
            }
    except Exception as e:
        logger.error(f"Error testing city boundary detection for {city}: {e}")
        return {
            'success': False,
            'city': city,
            'error': str(e),
        }

def test_coordinate_conversion(modules, city: str, lat: float, lng: float) -> Dict[str, Any]:
    """Test the coordinate conversion between different systems."""
    logger.info(f"Testing coordinate conversion for city: {city} at coordinates: {lat}, {lng}")
    
    try:
        # Convert WGS84 to S-JTSK
        x, y = modules['convert_wgs84_to_sjtsk'](lat, lng)
        
        # Convert back to WGS84
        lat2, lng2 = modules['convert_sjtsk_to_wgs84'](x, y)
        
        # Calculate the difference
        lat_diff = abs(lat - lat2)
        lng_diff = abs(lng - lng2)
        
        logger.info(f"Coordinate conversion for {city}:")
        logger.info(f"  WGS84: {lat}, {lng}")
        logger.info(f"  S-JTSK: {x}, {y}")
        logger.info(f"  WGS84 (converted back): {lat2}, {lng2}")
        logger.info(f"  Difference: {lat_diff}, {lng_diff}")
        
        # Generate CUZK URL
        cuzk_integration = modules['CUZKIntegration']()
        url = cuzk_integration.generate_cuzk_url(lat, lng, use_mapa_identifikace=True)
        
        return {
            'success': True,
            'city': city,
            'wgs84': {'lat': lat, 'lng': lng},
            'sjtsk': {'x': x, 'y': y},
            'wgs84_converted_back': {'lat': lat2, 'lng': lng2},
            'difference': {'lat': lat_diff, 'lng': lng_diff},
            'cuzk_url': url,
        }
    except Exception as e:
        logger.error(f"Error testing coordinate conversion for {city}: {e}")
        return {
            'success': False,
            'city': city,
            'error': str(e),
        }

def generate_diagnostic_report(results: List[Dict[str, Any]]) -> None:
    """Generate a diagnostic report from the test results."""
    logger.info("Generating diagnostic report...")
    
    # Create a report directory if it doesn't exist
    report_dir = "diagnostic_reports"
    os.makedirs(report_dir, exist_ok=True)
    
    # Generate a timestamp for the report filename
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    report_file = os.path.join(report_dir, f"city_search_diagnostic_{timestamp}.json")
    
    # Write the report to a JSON file
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Diagnostic report saved to: {report_file}")
    
    # Also print a summary to the console
    print("\n=== DIAGNOSTIC REPORT SUMMARY ===")
    for result in results:
        city = result.get('city', 'Unknown')
        if result.get('success', False):
            geocode_success = result.get('geocode', {}).get('success', False)
            boundaries_success = result.get('boundaries', {}).get('success', False)
            conversion_success = result.get('conversion', {}).get('success', False)
            
            status = "✓" if geocode_success and boundaries_success and conversion_success else "✗"
            print(f"{status} {city}")
            
            if 'conversion' in result and 'cuzk_url' in result['conversion']:
                print(f"  CUZK URL: {result['conversion']['cuzk_url']}")
        else:
            print(f"✗ {city}: {result.get('error', 'Unknown error')}")
    
    print("===============================\n")

def main():
    """Main function to diagnose city search issues."""
    logger.info("Starting city search diagnostics...")
    
    # Import required modules
    modules = import_required_modules()
    if not modules:
        logger.error("Failed to import required modules. Exiting.")
        return
    
    # Test each city
    results = []
    for city in TEST_CITIES:
        try:
            city_result = {'city': city}
            
            # Test Google Maps geocoding
            geocode_result = test_google_maps_geocoding(modules, city)
            city_result['geocode'] = geocode_result
            
            if geocode_result.get('success', False):
                # Test city boundary detection
                boundaries_result = test_city_boundary_detection(modules, city, geocode_result)
                city_result['boundaries'] = boundaries_result
                
                # Test coordinate conversion
                lat = geocode_result.get('lat')
                lng = geocode_result.get('lng')
                if lat is not None and lng is not None:
                    conversion_result = test_coordinate_conversion(modules, city, lat, lng)
                    city_result['conversion'] = conversion_result
            
            city_result['success'] = (
                geocode_result.get('success', False) and
                city_result.get('boundaries', {}).get('success', False) and
                city_result.get('conversion', {}).get('success', False)
            )
            
            results.append(city_result)
        except Exception as e:
            logger.error(f"Error testing city {city}: {e}")
            results.append({
                'city': city,
                'success': False,
                'error': str(e),
            })
    
    # Generate a diagnostic report
    generate_diagnostic_report(results)
    
    logger.info("City search diagnostics completed")

if __name__ == "__main__":
    main()
