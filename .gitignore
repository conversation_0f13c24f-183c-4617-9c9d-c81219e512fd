# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.venv/
.env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
*.log
logs/

# Cache
data/cache/
api_cache/
test_cache/

# Configuration
config.ini

# Generated files
*.db
*.sqlite
*.sqlite3
property_cache.db

# Coverage
.coverage
coverage.xml
htmlcov/
.pytest_cache/

# OS specific
.DS_Store
Thumbs.db
