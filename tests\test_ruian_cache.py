"""
Tests for RUIAN ID cache.

This module contains tests for the RUIAN ID cache module.
"""

import os
import sys
import unittest
import logging
import time
import tempfile
import json
from unittest.mock import MagicMock

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RUIANCacheTest")

# Import the modules to test
from utils.ruian_cache import RuianIDCache, get_cache
from models.ruian_id import RuianID
from utils.ruian_validator import verify_ruian_id_exists, ValidationLevel


class TestRuianIDCache(unittest.TestCase):
    """Test cases for the RUIAN ID cache."""

    def setUp(self):
        """Set up the test case."""
        # Create a temporary file for the cache
        self.temp_dir = tempfile.TemporaryDirectory()
        self.cache_file = os.path.join(self.temp_dir.name, "ruian_cache.json")

        # Create a cache instance
        self.cache = RuianIDCache(cache_file=self.cache_file)

    def tearDown(self):
        """Clean up after the test case."""
        # Remove the temporary directory
        self.temp_dir.cleanup()

    def test_add_and_get(self):
        """Test adding and getting a RUIAN ID from the cache."""
        # Add a RUIAN ID to the cache
        self.cache.add("123456", True, None)

        # Get the RUIAN ID from the cache
        result = self.cache.get("123456")

        # Check the result
        self.assertIsNotNone(result)
        self.assertEqual(result, (True, None))

    def test_contains(self):
        """Test checking if a RUIAN ID is in the cache."""
        # Add a RUIAN ID to the cache
        self.cache.add("123456", True, None)

        # Check if the RUIAN ID is in the cache
        self.assertTrue(self.cache.contains("123456"))
        self.assertFalse(self.cache.contains("654321"))

    def test_remove(self):
        """Test removing a RUIAN ID from the cache."""
        # Add a RUIAN ID to the cache
        self.cache.add("123456", True, None)

        # Check if the RUIAN ID is in the cache
        self.assertTrue(self.cache.contains("123456"))

        # Remove the RUIAN ID from the cache
        self.assertTrue(self.cache.remove("123456"))

        # Check if the RUIAN ID is still in the cache
        self.assertFalse(self.cache.contains("123456"))

        # Try to remove a non-existent RUIAN ID
        self.assertFalse(self.cache.remove("654321"))

    def test_clear(self):
        """Test clearing the cache."""
        # Add some RUIAN IDs to the cache
        self.cache.add("123456", True, None)
        self.cache.add("654321", False, "RUIAN ID not found in CUZK system")

        # Check if the RUIAN IDs are in the cache
        self.assertTrue(self.cache.contains("123456"))
        self.assertTrue(self.cache.contains("654321"))

        # Clear the cache
        self.cache.clear()

        # Check if the RUIAN IDs are still in the cache
        self.assertFalse(self.cache.contains("123456"))
        self.assertFalse(self.cache.contains("654321"))

    def test_expiration(self):
        """Test cache entry expiration."""
        # Create a cache with a short expiration time
        cache = RuianIDCache(cache_file=self.cache_file, expiration_days=0)

        # Add a RUIAN ID to the cache
        cache.add("123456", True, None)

        # Wait a bit to ensure the entry expires
        time.sleep(0.1)

        # Get the RUIAN ID from the cache
        result = cache.get("123456")

        # Check the result (should be None because the entry has expired)
        self.assertIsNone(result)

        # Check if the RUIAN ID is in the cache
        self.assertFalse(cache.contains("123456"))

    def test_cleanup(self):
        """Test cleaning up expired entries from the cache."""
        # Create a cache with a short expiration time
        cache = RuianIDCache(cache_file=self.cache_file, expiration_days=0)

        # Add some RUIAN IDs to the cache
        cache.add("123456", True, None)
        cache.add("654321", False, "RUIAN ID not found in CUZK system")

        # Wait a bit to ensure the entries expire
        time.sleep(0.1)

        # Clean up the cache
        removed = cache.cleanup()

        # Check the number of entries removed
        self.assertEqual(removed, 2)

        # Check if the RUIAN IDs are still in the cache
        self.assertFalse(cache.contains("123456"))
        self.assertFalse(cache.contains("654321"))

    def test_get_stats(self):
        """Test getting cache statistics."""
        # Add some RUIAN IDs to the cache
        self.cache.add("123456", True, None)
        self.cache.add("654321", False, "RUIAN ID not found in CUZK system")

        # Get a RUIAN ID from the cache (hit)
        self.cache.get("123456")

        # Get a non-existent RUIAN ID from the cache (miss)
        self.cache.get("111111")

        # Get the cache statistics
        stats = self.cache.get_stats()

        # Check the statistics
        self.assertEqual(stats["size"], 2)
        self.assertEqual(stats["hits"], 1)
        self.assertEqual(stats["misses"], 1)
        self.assertEqual(stats["hit_ratio"], 0.5)
        self.assertEqual(stats["expired"], 0)
        self.assertEqual(stats["active"], 2)
        self.assertEqual(stats["expiration_days"], 30)

    def test_get_all_ids(self):
        """Test getting all RUIAN IDs from the cache."""
        # Add some RUIAN IDs to the cache
        self.cache.add("123456", True, None)
        self.cache.add("654321", False, "RUIAN ID not found in CUZK system")

        # Get all RUIAN IDs from the cache
        ids = self.cache.get_all_ids()

        # Check the IDs
        self.assertEqual(ids, {"123456", "654321"})

    def test_get_valid_ids(self):
        """Test getting all valid RUIAN IDs from the cache."""
        # Add some RUIAN IDs to the cache
        self.cache.add("123456", True, None)
        self.cache.add("654321", False, "RUIAN ID not found in CUZK system")

        # Get all valid RUIAN IDs from the cache
        ids = self.cache.get_valid_ids()

        # Check the IDs
        self.assertEqual(ids, {"123456"})

    def test_get_invalid_ids(self):
        """Test getting all invalid RUIAN IDs from the cache."""
        # Add some RUIAN IDs to the cache
        self.cache.add("123456", True, None)
        self.cache.add("654321", False, "RUIAN ID not found in CUZK system")

        # Get all invalid RUIAN IDs from the cache
        ids = self.cache.get_invalid_ids()

        # Check the IDs
        self.assertEqual(ids, {"654321"})

    def test_save_and_load_cache(self):
        """Test saving and loading the cache."""
        # Add some RUIAN IDs to the cache
        self.cache.add("123456", True, None)
        self.cache.add("654321", False, "RUIAN ID not found in CUZK system")

        # Save the cache
        self.cache._save_cache()

        # Create a new cache instance
        cache2 = RuianIDCache(cache_file=self.cache_file)

        # Check if the RUIAN IDs are in the new cache
        self.assertTrue(cache2.contains("123456"))
        self.assertTrue(cache2.contains("654321"))

        # Get the RUIAN IDs from the new cache
        result1 = cache2.get("123456")
        result2 = cache2.get("654321")

        # Check the results
        self.assertEqual(result1, (True, None))
        self.assertEqual(result2, (False, "RUIAN ID not found in CUZK system"))


class TestRuianIDCacheIntegration(unittest.TestCase):
    """Test cases for the RUIAN ID cache integration with the RuianID class."""

    def setUp(self):
        """Set up the test case."""
        # Create a temporary file for the cache
        self.temp_dir = tempfile.TemporaryDirectory()
        self.cache_file = os.path.join(self.temp_dir.name, "ruian_cache.json")

        # Create a cache instance and set it as the global cache
        self.cache = RuianIDCache(cache_file=self.cache_file)

        # Save the original get_cache function
        self.original_get_cache = get_cache

        # Replace the get_cache function with a mock that returns our cache
        def mock_get_cache():
            return self.cache

        # Apply the mock
        globals()['get_cache'] = mock_get_cache

    def tearDown(self):
        """Clean up after the test case."""
        # Remove the temporary directory
        self.temp_dir.cleanup()

        # Restore the original get_cache function
        globals()['get_cache'] = self.original_get_cache

    def test_verify_with_cache(self):
        """Test verifying a RUIAN ID with the cache."""
        # Add the RUIAN ID to the cache with a positive result
        self.cache.add("123456", True, None)

        # Create a RUIAN ID
        ruian_id = RuianID("123456")

        # Manually set the _do_verify method to return True
        original_do_verify = ruian_id._do_verify
        ruian_id._do_verify = lambda use_cache=True: True

        # Verify the RUIAN ID (should use the cache)
        result = ruian_id.verify()

        # Check the result
        self.assertTrue(result)

        # Manually set the verification result for testing
        ruian_id._verified = True
        ruian_id._exists_in_cuzk = True

        # Check if the RUIAN ID is in the cache
        self.assertTrue(RuianID.is_cached("123456"))

        # Restore the original _do_verify method
        ruian_id._do_verify = original_do_verify

    def test_force_verify(self):
        """Test forcing verification of a RUIAN ID."""
        # Create a RUIAN ID
        ruian_id = RuianID("123456")

        # Manually set the verification result for testing
        ruian_id._verified = True
        ruian_id._exists_in_cuzk = True

        # Add the RUIAN ID to the cache
        self.cache.add("123456", True, None)

        # Verify the RUIAN ID (should use the cache)
        result = ruian_id.verify()

        # Check the result
        self.assertTrue(result)

        # Manually set the _do_verify method to return True
        original_do_verify = ruian_id._do_verify
        ruian_id._do_verify = lambda use_cache=True: True

        # Force verification of the RUIAN ID (should call _do_verify)
        result = ruian_id.force_verify()

        # Check the result
        self.assertTrue(result)

        # Restore the original _do_verify method
        ruian_id._do_verify = original_do_verify


if __name__ == '__main__':
    unittest.main()
