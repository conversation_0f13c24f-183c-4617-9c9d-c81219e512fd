"""
OpenStreetMap search UI components for the Czech Property Registry application.

This module provides UI components for searching properties using OpenStreetMap.
"""

import tkinter as tk
from tkinter import ttk
from utils import AutocompleteEntry
from ui.message_boxes import MessageBoxes


class OSMSearchUI:
    """
    UI components for OpenStreetMap search functionality.

    This class creates and manages the UI elements for searching
    properties using OpenStreetMap, including address and coordinate search.
    """

    def __init__(self, parent_frame, app):
        """
        Initialize the OSM search UI.

        Args:
            parent_frame: The parent frame to place the UI elements in
            app: The main application instance for callbacks
        """
        self.parent = parent_frame
        self.app = app

        # Create the UI elements
        self.create_ui()

    def create_ui(self):
        """Create the OSM search UI elements"""
        # Create a frame for the search options
        self.osm_search_frame = ttk.LabelFrame(self.parent, text="Search Buildings with RUIAN References")
        self.osm_search_frame.pack(fill="both", expand=True, padx=1, pady=1)

        # Create a notebook for different OSM search methods
        self.osm_notebook = ttk.Notebook(self.osm_search_frame)
        self.osm_notebook.pack(fill="both", expand=True, padx=1, pady=1)

        # Create tabs for different OSM search methods
        self.osm_address_frame = ttk.Frame(self.osm_notebook)
        self.osm_coords_frame = ttk.Frame(self.osm_notebook)

        self.osm_notebook.add(self.osm_address_frame, text="Search by Address")
        self.osm_notebook.add(self.osm_coords_frame, text="Search by Coordinates")

        # Create the address search tab
        self.create_address_search_tab()

        # Create the coordinates search tab
        self.create_coordinates_search_tab()

        # Add a separator
        ttk.Separator(self.osm_search_frame, orient="horizontal").pack(fill="x", padx=5, pady=5)

        # Add a frame for additional options
        osm_additional_options = ttk.Frame(self.osm_search_frame)
        osm_additional_options.pack(fill="x", padx=2, pady=2)

        # Add a label with information about the OSM search
        ttk.Label(
            osm_additional_options,
            text="OpenStreetMap Search finds buildings with RUIAN references.\n"
                 "These can be used to retrieve property owner information from the CUZK website.",
            wraplength=300,
            justify="center"
        ).pack(pady=5)

    def create_address_search_tab(self):
        """Create the address search tab"""
        # Create a frame for the address search
        osm_address_search_frame = ttk.Frame(self.osm_address_frame)
        osm_address_search_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Full address with autocomplete
        ttk.Label(osm_address_search_frame, text="Full Address:").grid(row=0, column=0, sticky="w", padx=2, pady=2)
        self.osm_full_address = AutocompleteEntry(
            osm_address_search_frame,
            width=30,
            autocomplete_function=self.get_osm_address_suggestions
        )
        self.osm_full_address.grid(row=0, column=1, columnspan=2, sticky="ew", padx=2, pady=2)

        # Add a description label
        ttk.Label(
            osm_address_search_frame,
            text="e.g., 'Václavské náměstí 1, Prague'",
            foreground="gray",
            font=("Arial", 7)
        ).grid(row=1, column=0, columnspan=3, padx=2, pady=1, sticky="w")

        # Add a separator
        ttk.Separator(osm_address_search_frame, orient="horizontal").grid(
            row=2, column=0, columnspan=3, sticky="ew", padx=2, pady=3
        )

        # Individual address components
        ttk.Label(osm_address_search_frame, text="Or enter components:", font=("Arial", 8)).grid(
            row=3, column=0, columnspan=3, padx=2, pady=1, sticky="w"
        )

        # City input with autocomplete
        ttk.Label(osm_address_search_frame, text="City:").grid(row=4, column=0, sticky="w", padx=2, pady=2)
        self.osm_city_entry = AutocompleteEntry(
            osm_address_search_frame,
            width=30,
            autocomplete_function=self.get_osm_city_suggestions
        )
        self.osm_city_entry.grid(row=4, column=1, columnspan=2, sticky="ew", padx=2, pady=2)

        # Street input with autocomplete
        ttk.Label(osm_address_search_frame, text="Street:").grid(row=5, column=0, sticky="w", padx=2, pady=2)
        self.osm_street_entry = AutocompleteEntry(
            osm_address_search_frame,
            width=30,
            autocomplete_function=self.get_osm_street_suggestions
        )
        self.osm_street_entry.grid(row=5, column=1, columnspan=2, sticky="ew", padx=2, pady=2)

        # House number input
        ttk.Label(osm_address_search_frame, text="Number:").grid(row=6, column=0, sticky="w", padx=2, pady=2)
        self.osm_number_entry = ttk.Entry(osm_address_search_frame, width=10)
        self.osm_number_entry.grid(row=6, column=1, sticky="w", padx=2, pady=2)

        # Postal code input
        ttk.Label(osm_address_search_frame, text="PSČ:").grid(row=7, column=0, sticky="w", padx=2, pady=2)
        self.osm_postal_entry = AutocompleteEntry(
            osm_address_search_frame,
            width=10,
            autocomplete_function=self.get_osm_postal_suggestions
        )
        self.osm_postal_entry.grid(row=7, column=1, sticky="w", padx=2, pady=2)

        # Add event handlers for field interaction
        # When full address is selected, update component fields
        self.osm_full_address.entry.bind("<FocusOut>", self.update_osm_component_fields)

        # When component fields are updated, update full address
        self.osm_city_entry.entry.bind("<FocusOut>", self.update_osm_full_address)
        self.osm_street_entry.entry.bind("<FocusOut>", self.update_osm_full_address)
        self.osm_number_entry.bind("<FocusOut>", self.update_osm_full_address)
        self.osm_postal_entry.entry.bind("<FocusOut>", self.update_osm_full_address)

        # Search radius
        ttk.Label(osm_address_search_frame, text="Search Radius (m):").grid(row=8, column=0, sticky="w", padx=2, pady=2)
        self.osm_address_radius_var = tk.StringVar(value="2000")
        radius_entry = ttk.Entry(osm_address_search_frame, textvariable=self.osm_address_radius_var, width=10)
        radius_entry.grid(row=8, column=1, sticky="w", padx=2, pady=2)

        # Search button
        self.osm_address_search_btn = ttk.Button(
            osm_address_search_frame,
            text="Search Buildings in OSM",
            command=self.on_search_buildings_by_address,
            style="Accent.TButton"
        )
        self.osm_address_search_btn.grid(row=9, column=0, columnspan=3, padx=2, pady=4, sticky="ew")

    def create_coordinates_search_tab(self):
        """Create the coordinates search tab"""
        # Create a frame for the coordinates search
        osm_coords_search_frame = ttk.Frame(self.osm_coords_frame)
        osm_coords_search_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Latitude input
        ttk.Label(osm_coords_search_frame, text="Latitude:").grid(row=0, column=0, sticky="w", padx=2, pady=2)
        self.osm_lat_entry = ttk.Entry(osm_coords_search_frame, width=20)
        self.osm_lat_entry.grid(row=0, column=1, sticky="ew", padx=2, pady=2)

        # Longitude input
        ttk.Label(osm_coords_search_frame, text="Longitude:").grid(row=1, column=0, sticky="w", padx=2, pady=2)
        self.osm_lng_entry = ttk.Entry(osm_coords_search_frame, width=20)
        self.osm_lng_entry.grid(row=1, column=1, sticky="ew", padx=2, pady=2)

        # Search radius
        ttk.Label(osm_coords_search_frame, text="Search Radius (m):").grid(row=2, column=0, sticky="w", padx=2, pady=2)
        self.osm_coords_radius_var = tk.StringVar(value="2000")
        radius_entry = ttk.Entry(osm_coords_search_frame, textvariable=self.osm_coords_radius_var, width=10)
        radius_entry.grid(row=2, column=1, sticky="w", padx=2, pady=2)

        # Search button
        self.osm_coords_search_btn = ttk.Button(
            osm_coords_search_frame,
            text="Search Buildings in OSM",
            command=self.on_search_buildings_by_coordinates,
            style="Accent.TButton"
        )
        self.osm_coords_search_btn.grid(row=3, column=0, columnspan=2, padx=2, pady=4, sticky="ew")

    def get_osm_address_suggestions(self, text):
        """Get address suggestions from the app"""
        if hasattr(self.app, 'get_osm_address_suggestions'):
            return self.app.get_osm_address_suggestions(text)
        return []

    def get_osm_city_suggestions(self, text):
        """Get city suggestions from the app"""
        if hasattr(self.app, 'get_osm_city_suggestions'):
            return self.app.get_osm_city_suggestions(text)
        return []

    def get_osm_street_suggestions(self, text):
        """Get street suggestions from the app"""
        if hasattr(self.app, 'get_osm_street_suggestions'):
            return self.app.get_osm_street_suggestions(text)
        return []

    def get_osm_postal_suggestions(self, text):
        """Get postal code suggestions from the app"""
        if hasattr(self.app, 'get_osm_postal_suggestions'):
            return self.app.get_osm_postal_suggestions(text)
        return []

    def update_osm_component_fields(self, event=None):
        """Update the component fields based on the full address"""
        if hasattr(self.app, 'update_osm_component_fields'):
            self.app.update_osm_component_fields(self, event)

    def update_osm_full_address(self, event=None):
        """Update the full address field based on the component fields"""
        if hasattr(self.app, 'update_osm_full_address'):
            self.app.update_osm_full_address(self, event)

    def on_search_buildings_by_address(self):
        """Handle the search button click for address search"""
        if hasattr(self.app, 'osm_manager') and hasattr(self.app.osm_manager, 'search_buildings_in_osm_by_address'):
            # Get the address components
            full_address = self.osm_full_address.get().strip()
            city = self.osm_city_entry.get().strip()
            street = self.osm_street_entry.get().strip()
            number = self.osm_number_entry.get().strip()
            postal = self.osm_postal_entry.get().strip()
            radius = self.osm_address_radius_var.get().strip()

            # Call the OSM manager's search method
            self.app.osm_manager.search_buildings_in_osm_by_address(
                full_address=full_address,
                city=city,
                street=street,
                number=number,
                postal=postal,
                radius=radius
            )
        else:
            print("OSM manager not initialized or search method not available")

    def on_search_buildings_by_coordinates(self):
        """Handle the search button click for coordinates search"""
        if hasattr(self.app, 'osm_manager') and hasattr(self.app.osm_manager, 'search_buildings_in_osm_by_coordinates'):
            # Get the coordinates
            try:
                lat = float(self.osm_lat_entry.get().strip())
                lng = float(self.osm_lng_entry.get().strip())
                radius = int(self.osm_coords_radius_var.get().strip())
            except ValueError:
                MessageBoxes.show_warning("Invalid Input", "Please enter valid numeric coordinates and radius.")
                return

            # Call the OSM manager's search method
            self.app.osm_manager.search_buildings_in_osm_by_coordinates(lat, lng, radius)
        else:
            print("OSM manager not initialized or search method not available")
