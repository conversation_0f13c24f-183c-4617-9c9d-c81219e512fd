"""
GPT Integration for Czech Property Registry Application

This module provides integration with OpenAI's GPT models for:
1. Gender determination from Czech names
2. Personalized letter generation
3. CAPTCHA solving using vision capabilities

It includes fallback mechanisms for when the API is unavailable or fails.
"""

import os
import json
import time
import threading
import base64
from io import BytesIO
from typing import Dict, Optional, List, Tuple, Any, Union

# Try to import OpenAI
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    openai = None

try:
    from openai import OpenAI
    OPENAI_CLIENT_AVAILABLE = True
except ImportError:
    OPENAI_CLIENT_AVAILABLE = False
    OpenAI = None

class GPTIntegration:
    """Integration with OpenAI's GPT models for the Czech Property Registry application"""

    def __init__(self, api_key: Optional[str] = None, cache_file: str = "gpt_cache.json"):
        """
        Initialize the GPT integration

        Args:
            api_key: OpenAI API key (optional, can also be set via environment variable)
            cache_file: Path to the cache file for storing API responses
        """
        self.api_key = api_key
        self.cache_file = cache_file
        self._cache = self._load_cache()
        self._cache_lock = threading.Lock()
        self._initialized = False

        # Try to initialize OpenAI
        self._initialize_openai()

    def _initialize_openai(self) -> bool:
        """
        Initialize the OpenAI API

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        if not OPENAI_AVAILABLE:
            print("OpenAI package not available. Install with: pip install openai")
            return False

        # Try to get API key from instance, then environment, then config file
        api_key = self.api_key or os.environ.get("OPENAI_API_KEY")

        if not api_key:
            print("No OpenAI API key found. Set OPENAI_API_KEY environment variable or pass api_key parameter.")
            return False

        # Set the API key for the legacy client
        openai.api_key = api_key

        # Initialize the new client if available
        if OPENAI_CLIENT_AVAILABLE:
            self.client = OpenAI(api_key=api_key)
        else:
            self.client = None

        self._initialized = True
        return True

    def _load_cache(self) -> Dict:
        """
        Load the cache from file

        Returns:
            Dict: The loaded cache or an empty dict if the file doesn't exist
        """
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {"gender": {}, "letters": {}}
        except Exception as e:
            print(f"Error loading GPT cache: {e}")
            return {"gender": {}, "letters": {}}

    def _save_cache(self) -> None:
        """Save the cache to file"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self._cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving GPT cache: {e}")

    def determine_gender(self, name: str) -> str:
        """
        Determine gender from a Czech name using GPT

        Args:
            name: The name to determine gender for

        Returns:
            str: 'male', 'female', or 'unknown'
        """
        if not name:
            return "unknown"

        # Check cache first
        with self._cache_lock:
            if name in self._cache["gender"]:
                return self._cache["gender"][name]

        # If OpenAI is not initialized, return unknown
        if not self._initialized and not self._initialize_openai():
            return "unknown"

        try:
            # Prepare the prompt
            prompt = f"Determine the gender (male or female) of this Czech name: {name}. Reply with just the word 'male' or 'female'."

            # Try the new client first
            if self.client:
                response = self.client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant that determines gender from Czech names."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=10,
                    temperature=0.1
                )
                gender = response.choices[0].message.content.strip().lower()
            # Fall back to legacy client
            elif hasattr(openai, 'ChatCompletion'):
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant that determines gender from Czech names."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=10,
                    temperature=0.1
                )
                gender = response.choices[0].message.content.strip().lower()
            else:
                return "unknown"

            # Validate and normalize the response
            if gender in ["male", "man", "masculine", "muž", "muz"]:
                result = "male"
            elif gender in ["female", "woman", "feminine", "žena", "zena"]:
                result = "female"
            else:
                result = "unknown"

            # Cache the result
            with self._cache_lock:
                self._cache["gender"][name] = result
                self._save_cache()

            return result
        except Exception as e:
            print(f"Error determining gender with GPT: {e}")
            return "unknown"

    def solve_captcha(self, image_data: Union[str, bytes]) -> Optional[str]:
        """
        Solve a CAPTCHA image using GPT's vision capabilities

        Args:
            image_data: Either a file path to the CAPTCHA image or the raw image data as bytes

        Returns:
            str: The solved CAPTCHA text or None if solving failed
        """
        # If OpenAI is not initialized, return None
        if not self._initialized and not self._initialize_openai():
            print("OpenAI not initialized. Cannot solve CAPTCHA.")
            return None

        # Check if GPT-4 Vision is available (requires newer OpenAI client)
        if not self.client or not hasattr(self.client, 'chat'):
            print("GPT-4 Vision not available. Install the latest OpenAI client.")
            return None

        try:
            # Convert image to base64 if it's a file path
            if isinstance(image_data, str):
                if os.path.exists(image_data):
                    with open(image_data, "rb") as image_file:
                        image_data = image_file.read()
                else:
                    print(f"Image file not found: {image_data}")
                    return None

            # Convert bytes to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')

            # Create the prompt for GPT-4 Vision
            response = self.client.chat.completions.create(
                model="gpt-4-vision-preview",  # Use GPT-4 Vision model
                messages=[
                    {
                        "role": "system",
                        "content": "You are a CAPTCHA solving assistant. Your task is to analyze the image and extract the text or numbers shown in the CAPTCHA. Respond with ONLY the characters you see, nothing else."
                    },
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "What text do you see in this CAPTCHA image? Respond with ONLY the characters, no explanations."},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=20,
                temperature=0.1
            )

            # Extract the CAPTCHA solution
            captcha_solution = response.choices[0].message.content.strip()

            # Clean up the solution (remove any non-alphanumeric characters)
            import re
            captcha_solution = re.sub(r'[^a-zA-Z0-9]', '', captcha_solution)

            print(f"GPT solved CAPTCHA: {captcha_solution}")
            return captcha_solution

        except Exception as e:
            print(f"Error solving CAPTCHA with GPT: {e}")
            return None

    def generate_letter(self, owner_name: str, owner_address: str, owner_gender: str,
                        buyer_name: str, buyer_address: str, buyer_contact: str,
                        style: str = "formal", tone: str = "neutral",
                        personalization: int = 1) -> Optional[str]:
        """
        Generate a personalized letter using GPT

        Args:
            owner_name: Name of the property owner
            owner_address: Address of the property owner
            owner_gender: Gender of the property owner ('male', 'female', or 'unknown')
            buyer_name: Name of the potential buyer
            buyer_address: Address of the potential buyer
            buyer_contact: Contact information of the potential buyer
            style: Style of the letter ('formal', 'semiformal', or 'casual')
            tone: Tone of the letter ('neutral', 'friendly', 'persuasive', or 'direct')
            personalization: Level of personalization (1-5, where 1 is minimal and 5 is maximum)

        Returns:
            str: The generated letter or None if generation failed
        """
        # Create a cache key based on the input parameters
        cache_key = f"{owner_name}_{owner_gender}_{style}_{tone}_{personalization}"

        # Check cache first
        with self._cache_lock:
            if cache_key in self._cache["letters"]:
                letter = self._cache["letters"][cache_key]
                # Replace the placeholder values with actual values
                return letter.replace("{{BUYER_NAME}}", buyer_name) \
                             .replace("{{BUYER_ADDRESS}}", buyer_address) \
                             .replace("{{BUYER_CONTACT}}", buyer_contact) \
                             .replace("{{OWNER_NAME}}", owner_name) \
                             .replace("{{OWNER_ADDRESS}}", owner_address)

        # If OpenAI is not initialized, return None
        if not self._initialized and not self._initialize_openai():
            return None

        try:
            # Prepare the prompt
            system_prompt = (
                "You are a professional Czech real estate agent writing a letter to a property owner. "
                "Your task is to create a letter in Czech language that expresses interest in buying their property. "
                "The letter should be formatted properly with sender and recipient information at the top."
            )

            user_prompt = (
                f"Write a letter in Czech to a property owner expressing interest in buying their property. "
                f"The letter should be in {style} style with a {tone} tone. "
                f"The personalization level is {personalization} (on a scale of 1-5, where 1 is minimal and 5 is maximum). "
                f"\n\nOwner information:\n"
                f"Name: {owner_name}\n"
                f"Address: {owner_address}\n"
                f"Gender: {owner_gender}\n"
                f"\nUse {{{{BUYER_NAME}}}} as a placeholder for the buyer's name, "
                f"{{{{BUYER_ADDRESS}}}} for the buyer's address, and "
                f"{{{{BUYER_CONTACT}}}} for the buyer's contact information. "
                f"Also use {{{{OWNER_NAME}}}} and {{{{OWNER_ADDRESS}}}} as placeholders for the owner's information. "
                f"Include the current date in Czech format."
            )

            # Try the new client first
            if self.client:
                response = self.client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    max_tokens=500,
                    temperature=0.7
                )
                letter = response.choices[0].message.content.strip()
            # Fall back to legacy client
            elif hasattr(openai, 'ChatCompletion'):
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    max_tokens=500,
                    temperature=0.7
                )
                letter = response.choices[0].message.content.strip()
            else:
                return None

            # Cache the result
            with self._cache_lock:
                self._cache["letters"][cache_key] = letter
                self._save_cache()

            # Replace the placeholder values with actual values
            return letter.replace("{{BUYER_NAME}}", buyer_name) \
                         .replace("{{BUYER_ADDRESS}}", buyer_address) \
                         .replace("{{BUYER_CONTACT}}", buyer_contact) \
                         .replace("{{OWNER_NAME}}", owner_name) \
                         .replace("{{OWNER_ADDRESS}}", owner_address)
        except Exception as e:
            print(f"Error generating letter with GPT: {e}")
            return None
