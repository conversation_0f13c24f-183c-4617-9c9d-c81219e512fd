"""
Example of using embedded Google Maps in the Czech Property Registry application.
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Add the parent directory to the path so we can import from the api package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.google_maps.google_maps_integration import GoogleMapsIntegration

class EmbeddedMapExample:
    """Example application demonstrating embedded Google Maps"""
    
    def __init__(self, root):
        """Initialize the example application"""
        self.root = root
        self.root.title("Embedded Google Maps Example")
        self.root.geometry("800x600")
        
        # Create the Google Maps integration
        self.google_maps = GoogleMapsIntegration()
        
        # Create the main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create a frame for the controls
        self.controls_frame = ttk.Frame(self.main_frame)
        self.controls_frame.pack(fill="x", padx=5, pady=5)
        
        # Create input fields for coordinates
        ttk.Label(self.controls_frame, text="Latitude:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.lat_entry = ttk.Entry(self.controls_frame, width=15)
        self.lat_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")
        self.lat_entry.insert(0, "50.0755")  # Default to Prague
        
        ttk.Label(self.controls_frame, text="Longitude:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.lng_entry = ttk.Entry(self.controls_frame, width=15)
        self.lng_entry.grid(row=0, column=3, padx=5, pady=5, sticky="w")
        self.lng_entry.insert(0, "14.4378")  # Default to Prague
        
        # Create input field for address
        ttk.Label(self.controls_frame, text="Address:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.address_entry = ttk.Entry(self.controls_frame, width=40)
        self.address_entry.grid(row=1, column=1, columnspan=3, padx=5, pady=5, sticky="we")
        self.address_entry.insert(0, "Prague, Czech Republic")
        
        # Create buttons for showing the map
        self.show_coords_btn = ttk.Button(self.controls_frame, text="Show by Coordinates", 
                                         command=self.show_map_by_coordinates)
        self.show_coords_btn.grid(row=2, column=0, columnspan=2, padx=5, pady=5, sticky="w")
        
        self.show_address_btn = ttk.Button(self.controls_frame, text="Show by Address", 
                                          command=self.show_map_by_address)
        self.show_address_btn.grid(row=2, column=2, columnspan=2, padx=5, pady=5, sticky="w")
        
        # Create a frame for the map
        self.map_frame = ttk.Frame(self.main_frame, borderwidth=1, relief="solid")
        self.map_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Set the map frame in the Google Maps integration
        self.google_maps.set_map_frame(self.map_frame)
        
        # Show the default map
        self.show_default_map()
    
    def show_default_map(self):
        """Show the default map (Prague)"""
        try:
            lat = float(self.lat_entry.get())
            lng = float(self.lng_entry.get())
            self.google_maps.show_map(lat, lng)
        except ValueError:
            # If the coordinates are invalid, use default Prague coordinates
            self.google_maps.show_map(50.0755, 14.4378)
    
    def show_map_by_coordinates(self):
        """Show the map using the coordinates from the entry fields"""
        try:
            lat = float(self.lat_entry.get())
            lng = float(self.lng_entry.get())
            self.google_maps.show_map(lat, lng)
        except ValueError as e:
            print(f"Invalid coordinates: {e}")
            # Show an error message or use default coordinates
    
    def show_map_by_address(self):
        """Show the map using the address from the entry field"""
        address = self.address_entry.get()
        if address:
            self.google_maps.open_map_in_browser(address=address)
        else:
            print("Please enter an address")

def main():
    """Main function to run the example"""
    root = tk.Tk()
    app = EmbeddedMapExample(root)
    root.mainloop()

if __name__ == "__main__":
    main()
