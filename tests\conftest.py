"""
Configuration file for pytest.
This file contains fixtures and configuration for pytest.
"""

import os
import sys
import pytest

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Set up the test environment."""
    # Create necessary directories
    os.makedirs('czech_data', exist_ok=True)
    os.makedirs('data/cache', exist_ok=True)
    
    # Set environment variables for testing
    os.environ['TESTING'] = 'True'
    
    yield
    
    # Clean up after tests if needed
    # This code runs after all tests have completed
