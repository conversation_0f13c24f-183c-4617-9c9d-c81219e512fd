"""
Message box utilities for the Czech Property Registry application.

This module provides standardized message boxes and notifications
for consistent user interaction throughout the application.
All message boxes should be defined here to ensure consistent
appearance and behavior throughout the application.
"""

import logging
from tkinter import messagebox, simpledialog

# Configure logging
logger = logging.getLogger(__name__)


class MessageBoxes:
    """
    Provides standardized message boxes and notifications for the application.

    This class centralizes all user notifications to ensure consistent
    messaging and appearance throughout the application. It also logs
    all message box interactions for debugging purposes.
    """

    # Track whether to log message box interactions
    log_interactions = True

    @staticmethod
    def show_info(title, message):
        """
        Show an information message box.

        Args:
            title (str): The title of the message box
            message (str): The message to display

        Returns:
            str: The result of the message box
        """
        if MessageBoxes.log_interactions:
            logger.info(f"INFO: {title} - {message}")
        return messagebox.showinfo(title, message)

    @staticmethod
    def show_warning(title, message):
        """
        Show a warning message box.

        Args:
            title (str): The title of the message box
            message (str): The message to display

        Returns:
            str: The result of the message box
        """
        if MessageBoxes.log_interactions:
            logger.warning(f"WARNING: {title} - {message}")
        return messagebox.showwarning(title, message)

    @staticmethod
    def show_error(title, message):
        """
        Show an error message box.

        Args:
            title (str): The title of the message box
            message (str): The message to display

        Returns:
            str: The result of the message box
        """
        if MessageBoxes.log_interactions:
            logger.error(f"ERROR: {title} - {message}")
        return messagebox.showerror(title, message)

    @staticmethod
    def ask_yes_no(title, message):
        """
        Show a yes/no question message box.

        Args:
            title (str): The title of the message box
            message (str): The message to display

        Returns:
            bool: True if the user clicked Yes, False otherwise
        """
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_YES_NO: {title} - {message}")
        result = messagebox.askyesno(title, message)
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_YES_NO RESULT: {result}")
        return result

    @staticmethod
    def ask_ok_cancel(title, message):
        """
        Show an OK/Cancel question message box.

        Args:
            title (str): The title of the message box
            message (str): The message to display

        Returns:
            bool: True if the user clicked OK, False otherwise
        """
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_OK_CANCEL: {title} - {message}")
        result = messagebox.askokcancel(title, message)
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_OK_CANCEL RESULT: {result}")
        return result

    @staticmethod
    def ask_retry_cancel(title, message):
        """
        Show a Retry/Cancel question message box.

        Args:
            title (str): The title of the message box
            message (str): The message to display

        Returns:
            bool: True if the user clicked Retry, False otherwise
        """
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_RETRY_CANCEL: {title} - {message}")
        result = messagebox.askretrycancel(title, message)
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_RETRY_CANCEL RESULT: {result}")
        return result

    @staticmethod
    def ask_string(title, prompt, **kwargs):
        """
        Show a dialog box that asks the user to input a string.

        Args:
            title (str): The title of the dialog box
            prompt (str): The prompt to display
            **kwargs: Additional arguments to pass to simpledialog.askstring

        Returns:
            str: The string entered by the user, or None if cancelled
        """
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_STRING: {title} - {prompt}")
        result = simpledialog.askstring(title, prompt, **kwargs)
        if MessageBoxes.log_interactions:
            # Don't log the actual result for privacy reasons
            logger.info(f"ASK_STRING RESULT: {'<value entered>' if result else 'None'}")
        return result

    @staticmethod
    def ask_integer(title, prompt, **kwargs):
        """
        Show a dialog box that asks the user to input an integer.

        Args:
            title (str): The title of the dialog box
            prompt (str): The prompt to display
            **kwargs: Additional arguments to pass to simpledialog.askinteger

        Returns:
            int: The integer entered by the user, or None if cancelled
        """
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_INTEGER: {title} - {prompt}")
        result = simpledialog.askinteger(title, prompt, **kwargs)
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_INTEGER RESULT: {result}")
        return result

    @staticmethod
    def ask_float(title, prompt, **kwargs):
        """
        Show a dialog box that asks the user to input a float.

        Args:
            title (str): The title of the dialog box
            prompt (str): The prompt to display
            **kwargs: Additional arguments to pass to simpledialog.askfloat

        Returns:
            float: The float entered by the user, or None if cancelled
        """
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_FLOAT: {title} - {prompt}")
        result = simpledialog.askfloat(title, prompt, **kwargs)
        if MessageBoxes.log_interactions:
            logger.info(f"ASK_FLOAT RESULT: {result}")
        return result

    # Utility methods

    @staticmethod
    def set_log_interactions(enabled=True):
        """
        Enable or disable logging of message box interactions.

        Args:
            enabled (bool): Whether to enable logging
        """
        MessageBoxes.log_interactions = enabled
        logger.info(f"Message box interaction logging {'enabled' if enabled else 'disabled'}")

    @staticmethod
    def show_exception(title="Error", exception=None, message=None):
        """
        Show an error message box for an exception.

        Args:
            title (str): The title of the message box
            exception (Exception, optional): The exception to display
            message (str, optional): Additional message to display

        Returns:
            str: The result of the message box
        """
        if exception:
            error_message = f"{message}\n\nError: {str(exception)}" if message else f"Error: {str(exception)}"
        else:
            error_message = message or "An unknown error occurred."

        if MessageBoxes.log_interactions:
            logger.error(f"EXCEPTION: {title} - {error_message}", exc_info=exception is not None)

        return messagebox.showerror(title, error_message)

    @staticmethod
    def show_confirmation(title, message):
        """
        Show a confirmation message box.

        Args:
            title (str): The title of the message box
            message (str): The message to display

        Returns:
            bool: True if the user confirmed, False otherwise
        """
        return MessageBoxes.ask_yes_no(title, message)

    # Application-specific message boxes

    @staticmethod
    def show_login_info():
        """Show information about the ČÚZK login requirements."""
        return MessageBoxes.show_info(
            "Bank Identity Required",
            "The ČÚZK portal now requires bank identity verification for login.\n\n"
            "This application cannot automatically log in. You have two options:\n"
            "1. Use the application without login (CAPTCHA solving required)\n"
            "2. Log in manually through a web browser and copy cookies"
        )

    @staticmethod
    def show_captcha_solved(solution):
        """Show a message that the CAPTCHA was solved automatically."""
        return MessageBoxes.show_info(
            "CAPTCHA Solved Automatically",
            f"GPT has automatically solved the CAPTCHA: {solution}\n\n"
            f"Continuing with the search..."
        )

    @staticmethod
    def show_captcha_required():
        """Show a message that a CAPTCHA is required."""
        return MessageBoxes.show_info(
            "CAPTCHA Required",
            "A CAPTCHA is required to continue. Please open the website in your browser, "
            "solve the CAPTCHA, and then try again."
        )

    @staticmethod
    def ask_open_website():
        """Ask if the user wants to open the ČÚZK website in the browser."""
        return MessageBoxes.ask_yes_no(
            "Open Website",
            "Would you like to open the ČÚZK website in your browser with your current search parameters?"
        )

    @staticmethod
    def show_captcha_page_opened(url):
        """Show a message that the CAPTCHA page has been opened in the browser."""
        return MessageBoxes.show_info(
            "CAPTCHA Page Opened",
            "The CAPTCHA page has been opened in your browser.\n\n"
            f"URL: {url}\n\n"
            "Please solve the CAPTCHA there and then return to the application."
        )

    @staticmethod
    def show_cuzk_search_info(search_params):
        """
        Show information about the ČÚZK search parameters.

        Args:
            search_params (dict): Dictionary of search parameters to display
        """
        # Build the message from the search parameters
        message = "Please enter the following in the search form:\n\n"
        for key, value in search_params.items():
            if value:  # Only include non-empty values
                message += f"{key}: {value}\n"

        message += "\nThen click 'Vyhledat' (Search)"

        return MessageBoxes.show_info("ČÚZK Search", message)

    @staticmethod
    def show_direct_browser_scraping():
        """Show a message about direct browser scraping."""
        return MessageBoxes.show_info(
            "Direct Browser Scraping",
            "The CUZK website has been opened in your browser.\n\n"
            "Please complete any CAPTCHA that appears and navigate to the property details page.\n\n"
            "Once you are on the property details page, click OK to continue."
        )

    @staticmethod
    def show_cuzk_direct_access(parcel_id, cadastral_territory):
        """Show a message about direct ČÚZK access."""
        return MessageBoxes.show_info(
            "ČÚZK Direct Access",
            f"Opening ČÚZK website with direct access to property information.\n\n"
            f"Parcel ID: {parcel_id}\n"
            f"Cadastral Territory: {cadastral_territory}\n\n"
            f"If you are redirected to the search page, please enter the information manually."
        )

    @staticmethod
    def ask_scraping_method():
        """Ask the user which scraping method to use."""
        return MessageBoxes.ask_yes_no(
            "Scraping Method",
            "Would you like to use direct browser scraping?\n\n"
            "This will open the ČÚZK website in your browser, where you can complete the CAPTCHA "
            "and then return to the application to extract the property data.\n\n"
            "This method is more reliable than automatic scraping."
        )

    @staticmethod
    def show_letter_generated():
        """Show a message that a letter was generated successfully."""
        return MessageBoxes.show_info(
            "Success",
            "Letter generated successfully.\n\n"
            "You can now edit the letter and save it to a file."
        )

    @staticmethod
    def show_export_complete(count, file_path):
        """Show a message that export was completed successfully."""
        return MessageBoxes.show_info(
            "Export Complete",
            f"Successfully exported {count} buildings to {file_path}"
        )

    @staticmethod
    def show_property_found(owner_name):
        """Show a message that a property was found."""
        return MessageBoxes.show_info(
            "Success",
            f"Found property owned by {owner_name}"
        )

    @staticmethod
    def show_missing_information(message="Please enter the required information."):
        """Show a message about missing information."""
        return MessageBoxes.show_warning("Missing Information", message)

    @staticmethod
    def show_no_letters():
        """Show a message that no letters are available."""
        return MessageBoxes.show_error("No Letters", "Please generate letters first")


