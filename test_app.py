#!/usr/bin/env python3
"""
Test script to verify the application is working correctly.
"""

import sys
import os
import logging
import tkinter as tk
from tkinter import messagebox

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_app.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required modules can be imported."""
    try:
        logger.info("Testing imports...")
        
        # Test core imports
        from core.app_factory import AppFactory
        logger.info("✓ AppFactory imported successfully")
        
        from core.app import PropertyScraperApp
        logger.info("✓ PropertyScraperApp imported successfully")
        
        # Test UI imports
        from ui.simple_loading_window import SimpleLoadingWindow
        logger.info("✓ SimpleLoadingWindow imported successfully")
        
        logger.info("All imports successful!")
        return True
    except Exception as e:
        logger.error(f"Import error: {e}")
        return False

def test_app_creation():
    """Test if the application can be created."""
    try:
        logger.info("Testing app creation...")
        
        # Create a root window
        root = tk.Tk()
        root.withdraw()  # Hide it for testing
        
        # Create the app
        from core.app_factory import AppFactory
        app = AppFactory.create_app(root=root, app_type="standard")
        logger.info("✓ Standard app created successfully")
        
        # Clean up
        root.destroy()
        
        return True
    except Exception as e:
        logger.error(f"App creation error: {e}")
        return False

def test_gui():
    """Test basic GUI functionality."""
    try:
        logger.info("Testing GUI...")
        
        # Create a simple test window
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("300x200")
        
        # Add a label
        label = tk.Label(root, text="Application Test Successful!", font=("Arial", 12))
        label.pack(pady=50)
        
        # Add a button to close
        def close_window():
            logger.info("Test window closed by user")
            root.destroy()
        
        button = tk.Button(root, text="Close", command=close_window)
        button.pack(pady=10)
        
        logger.info("✓ Test GUI window created")
        
        # Show the window for a few seconds then close automatically
        def auto_close():
            logger.info("Auto-closing test window")
            root.destroy()
        
        root.after(3000, auto_close)  # Close after 3 seconds
        root.mainloop()
        
        return True
    except Exception as e:
        logger.error(f"GUI test error: {e}")
        return False

def main():
    """Main test function."""
    logger.info("Starting application tests...")
    
    # Test 1: Imports
    if not test_imports():
        logger.error("Import test failed!")
        return 1
    
    # Test 2: App creation
    if not test_app_creation():
        logger.error("App creation test failed!")
        return 1
    
    # Test 3: GUI
    if not test_gui():
        logger.error("GUI test failed!")
        return 1
    
    logger.info("All tests passed! The application should be working correctly.")
    
    # Ask user if they want to run the full application
    root = tk.Tk()
    root.withdraw()
    
    result = messagebox.askyesno(
        "Test Complete", 
        "All tests passed!\n\nWould you like to run the full application now?"
    )
    
    root.destroy()
    
    if result:
        logger.info("User chose to run the full application")
        try:
            # Import and run the main application
            import main
            return main.main()
        except Exception as e:
            logger.error(f"Error running main application: {e}")
            return 1
    else:
        logger.info("User chose not to run the full application")
        return 0

if __name__ == "__main__":
    sys.exit(main())
