"""
Run the batch search coordinates test.

This script runs the batch search coordinates test to verify that the application
can search for properties by location with a specified radius and output all coordinates.
"""

import os
import sys
import logging
import unittest

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RunBatchSearchCoordinatesTest")

# Import the test module
from tests.test_batch_search_coordinates import TestBatchSearchCoordinates


def run_test():
    """Run the batch search coordinates test."""
    logger.info("Running batch search coordinates test...")

    # Create a test suite with the batch search coordinates test
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestBatchSearchCoordinates)

    # Run the test suite
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)

    # Log the results
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Failures: {len(result.failures)}")
    logger.info(f"Skipped: {len(result.skipped)}")

    # Return True if all tests passed
    return len(result.errors) == 0 and len(result.failures) == 0


if __name__ == '__main__':
    success = run_test()
    sys.exit(0 if success else 1)
