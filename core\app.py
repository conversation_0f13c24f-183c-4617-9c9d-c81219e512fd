"""
Core application class for the Czech Property Scraper.
This module contains the main application class that initializes the UI and services.
"""

import threading
import os
import configparser
import logging
import time
import tkinter as tk
from tkinter import ttk
from concurrent.futures import ThreadPoolExecutor

# Import base class
from core.app_base import AppBase

# Import lazy module loader first to enable lazy loading of other modules
from utils.lazy_module_loader import lazy_import, lazy_import_all

# Core modules
from core.optimized_network import create_optimized_session
from core.ui_manager import UIManager
from core.service_manager import ServiceManager
from core.data_manager import DataManager
from core.settings_manager import SettingsManager

# API modules
from api import GoogleMapsIntegration, CUZKScraper
from api.osm.osm_integration import OSMIntegration
from api.cuzk.cuzk_integration import CUZKIntegration

# Core managers
from core.batch_search_manager import BatchSearchManager
from core.smart_batch_search_manager import SmartBatchSearchManager

# Utility modules
from utils.enhanced_cache_manager import EnhancedCacheManager, CacheStorageType
from utils.thread_manager import ThreadManager
from utils.data_source_manager import DataSourceManager
from utils.font_scaling import FontScaler
from utils.property_formatter import PropertyDetailsFormatter
from utils.property_processor import PropertyDataProcessor
from utils.db_cache import DBCache
from utils.memory_manager import memory_monitor, weak_cache
from utils.lazy_loader import LazyDataLoader, lazy_property
from utils.profiler import function_profiler, Timer
from utils.data_compression import data_compressor, CompressionMethod
from utils.adaptive_rate_limiter import adaptive_rate_limiter
from utils.background_processor import background_processor
from utils.progressive_loader import progressive_loader

# UI components
from ui.virtualized_list import VirtualizedList
from ui.virtualized_tree import VirtualizedTree

# Configuration
from config.settings import Settings

# Optional GPT integration
try:
    from services.gpt_integration import GPTIntegration
    GPT_AVAILABLE = True
except ImportError:
    GPT_AVAILABLE = False

# Optional Tesseract OCR for CAPTCHA solving
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("PropertyScraperApp")


class PropertyScraperApp(AppBase):
    """Main application class for the Czech Property Scraper."""

    def __init__(self, root):
        """
        Initialize the application.

        Args:
            root (tk.Tk): The root Tkinter window
        """
        # Call the parent class constructor
        super().__init__(root)

        # Override title
        self.root.title("Czech Property Registry - Batch Search")

        # Initialize settings
        self.settings = Settings()

        logger.info("Initializing PropertyScraperApp")

        # Initialize font scaler
        self.font_scaler = FontScaler(root, self.settings)

        # Store original window state for fullscreen toggle
        self._fullscreen = False
        self._normal_state = {
            'geometry': self.root.geometry()
        }

        # Initialize database cache with compression for better performance
        self.db_cache = DBCache(db_path="property_cache.db")

        logger.info("Initializing caches")

        # Create specific caches with appropriate expiry times
        self.db_cache.create_cache(
            'address_suggestions',
            expiry=600  # 10 minutes for address suggestions
        )

        self.db_cache.create_cache(
            'city_suggestions',
            expiry=86400   # 24 hours for city data (changes rarely)
        )

        self.db_cache.create_cache(
            'street_suggestions',
            expiry=3600  # 1 hour for street data
        )

        self.db_cache.create_cache(
            'postal_suggestions',
            expiry=86400 # 24 hours for postal codes (change rarely)
        )

        self.db_cache.create_cache(
            'gender',
            expiry=604800  # 1 week for gender data (doesn't change)
        )

        self.db_cache.create_cache(
            'encrypted_urls',
            expiry=300  # 5 minutes for encrypted URLs
        )

        self.db_cache.create_cache(
            'osm_buildings',
            expiry=3600  # 1 hour for OSM building data
        )

        self.db_cache.create_cache(
            'property_data',
            expiry=1800  # 30 minutes for property data
        )

        # Keep the enhanced cache manager for backward compatibility
        self.cache_manager = EnhancedCacheManager(
            default_expiry=300,  # 5 minutes default expiry
            disk_cache_dir="api_cache",
            max_memory_items=1000,
            cleanup_interval=300,  # 5 minutes cleanup interval
            always_prefer_api=False  # Prefer cached data for faster startup
        )

        # Initialize data compression
        logger.info("Initializing data compression")
        # Configure default compression method and level
        data_compressor.default_method = CompressionMethod.ZLIB
        data_compressor.default_level = 6  # Balanced compression

        # Initialize thread manager for better thread management
        logger.info("Initializing thread manager")
        self.thread_manager = ThreadManager(max_workers=5, thread_name_prefix="PropertyScraper")

        # Initialize lazy data loader
        self.lazy_data_loader = LazyDataLoader()

        # Start memory monitoring
        logger.info("Starting memory monitoring")
        memory_monitor.start()

        # Register memory usage callback
        memory_monitor.register_callback(
            'app_memory_warning',
            self._on_memory_warning
        )

        # Initialize adaptive rate limiter
        logger.info("Initializing adaptive rate limiter")
        # Configure domain-specific rate limits
        adaptive_rate_limiter.set_domain_rate("nahlizenidokn.cuzk.cz", 1.0)
        adaptive_rate_limiter.set_domain_rate("overpass-api.de", 0.5)
        adaptive_rate_limiter.set_domain_rate("maps.googleapis.com", 5.0)

        # Initialize background processor
        logger.info("Initializing background processor")
        # Start the background processor if it's not already running
        if not background_processor._running:
            background_processor.start()

        # Initialize progressive loader
        logger.info("Initializing progressive loader")
        # Start the progressive loader
        progressive_loader.start_loading()

        # Register progressive loader callbacks
        progressive_loader.register_callback('all_chunks_loaded', self._on_all_chunks_loaded)

        # Initialize helper classes with lazy loading
        self.property_formatter = PropertyDetailsFormatter()
        self.property_processor = PropertyDataProcessor()

        # Session for maintaining login state - initialize this before other classes that need it
        logger.info("Creating optimized network session")
        self.session = self.create_session()
        self.is_logged_in = False

        # Keep thread_pool for backward compatibility
        self.thread_pool = self.thread_manager._executor

        # Initialize CAPTCHA solving options from config
        use_gpt_default = self.settings.get('CAPTCHA', 'use_gpt', 'true').lower() == 'true' and GPT_AVAILABLE
        use_tesseract_default = self.settings.get('CAPTCHA', 'use_tesseract', 'true').lower() == 'true' and TESSERACT_AVAILABLE

        self.use_gpt_captcha = tk.BooleanVar(value=use_gpt_default)
        self.use_tesseract_captcha = tk.BooleanVar(value=use_tesseract_default)

        # Make the availability flags accessible to the UI
        self.GPT_AVAILABLE = GPT_AVAILABLE
        self.TESSERACT_AVAILABLE = TESSERACT_AVAILABLE

        # Set Tesseract path if available
        if TESSERACT_AVAILABLE:
            tesseract_path = self.settings.get('CAPTCHA', 'tesseract_path', None)
            if tesseract_path and os.path.exists(tesseract_path):
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
                logger.info(f"Tesseract path set to: {tesseract_path}")

        # Enable function profiling in development mode
        if self.settings.get('development_mode', False):
            self._enable_profiling()

        # Initialize integrations
        self.initialize_integrations()

        # Initialize settings manager
        self.settings_manager = SettingsManager(self)

        # Initialize data storage
        self.initialize_data_storage()

        # Initialize services
        self.service_manager = ServiceManager(self)

        # Initialize UI components
        self.initialize_ui()

        # Initialize notification banner
        from ui.notification_banner import NotificationBanner
        self.notification_banner = NotificationBanner(self.root, self.font_scaler)

        # Set API preference for dynamic data only, not static data
        self.set_api_preference(False)

        # Don't automatically load regions at startup
        # self.root.after(1000, self.load_regions)

        # Reset the startup flag after initialization is complete
        self._is_startup = False
        logger.info("Application initialization complete, startup mode disabled")

    def initialize_integrations(self):
        """Initialize all external integrations and services."""
        # Initialize CUZK integration
        self.cuzk_integration = CUZKIntegration()

        # Initialize Mapy.cz integration first (primary)
        from api.mapycz.mapycz_integration import MapyCZIntegration
        self.mapycz_integration = MapyCZIntegration()
        logger.info("Initialized Mapy.cz integration as primary geocoding service")

        # Initialize Google Maps integration as fallback
        self.google_maps = GoogleMapsIntegration()
        self.google_maps.app = self
        logger.info("Initialized Google Maps integration as fallback geocoding service")

        # Create city boundaries cache with 30-day expiry
        if hasattr(self, 'cache_manager'):
            self.cache_manager.create_cache('city_boundaries', expiry=86400*30)

        # Initialize CUZK scraper
        self.cuzk_scraper = CUZKScraper()

        # Initialize CUZK Data API client
        from api.cuzk.cuzk_data_api import CUZKDataAPI
        self.cuzk_data_api = CUZKDataAPI(self)
        logger.info("Initialized CUZK Data API client")

        # Initialize Data Source Manager with the app reference
        self.data_source_manager = DataSourceManager(google_maps_api=self.google_maps, root=self.root, app=self)

        # Initialize the OSM integration
        self.osm_integration = OSMIntegration(self)

        # Initialize batch search managers
        self.batch_search_manager = BatchSearchManager(self)
        self.smart_batch_search_manager = SmartBatchSearchManager(self)

        # Initialize real batch search manager
        from core.real_batch_search_manager import RealBatchSearchManager
        self.real_batch_search_manager = RealBatchSearchManager(self)

        # Print debug information
        print("Initialized real_batch_search_manager:", self.real_batch_search_manager)

        # Initialize GPT integration if available
        self.gpt = self._initialize_gpt_integration() if GPT_AVAILABLE else None

    def _initialize_gpt_integration(self):
        """Initialize GPT integration with API key from environment or config file."""
        try:
            # Try to get API key from environment
            api_key = os.environ.get("OPENAI_API_KEY")

            # If not in environment, try to get from config file
            if not api_key and os.path.exists('config.ini'):
                config = configparser.ConfigParser()
                config.read('config.ini')
                api_key = config.get('OPENAI', 'api_key', fallback=None)

            # Initialize and return GPT integration if we have an API key
            if api_key:
                gpt = GPTIntegration(api_key=api_key)
                print("GPT integration initialized successfully")
                return gpt
        except Exception as e:
            print(f"Error initializing GPT integration: {e}")

        return None

    def initialize_ui(self):
        """Initialize UI components."""
        # Configure ttk styles
        self._configure_styles()

        # Initialize UI manager
        self.ui_manager = UIManager(self)

    def _configure_styles(self):
        """Configure ttk styles for the application."""
        style = ttk.Style()

        # Configure the Accent.TButton style for primary buttons
        style.configure("Accent.TButton",
                        background="#4285F4",
                        foreground="white",
                        padding=(10, 5),
                        font=("Arial", 10, "bold"))

        # Configure normal buttons
        style.configure("TButton",
                        padding=(8, 4),
                        font=("Arial", 9))

        # Configure entry widgets
        style.configure("TEntry",
                        padding=(5, 2),
                        font=("Arial", 9))

        # Configure labels
        style.configure("TLabel",
                        font=("Arial", 9))

        # Configure frames
        style.configure("TFrame",
                        background="#f5f5f5")

        # Configure labelframes
        style.configure("TLabelframe",
                        background="#f5f5f5",
                        font=("Arial", 10, "bold"))

        # Configure notebook
        style.configure("TNotebook",
                        background="#f5f5f5",
                        tabposition="n")

        # Configure notebook tabs
        style.configure("TNotebook.Tab",
                        background="#e0e0e0",
                        padding=(10, 4),
                        font=("Arial", 9))

    def initialize_data_storage(self):
        """Initialize data storage for the application."""
        # Initialize data manager
        self.data_manager = DataManager(self)

    def show_api_data_notification(self, data_type: str) -> None:
        """
        Show a notification that real API data is being used.

        Args:
            data_type: Type of data being loaded from API
        """
        # Only show notification if we have a notification banner
        if hasattr(self, 'notification_banner'):
            # Create the notification message
            message = f"Using real API data for {data_type}."

            # Show the notification
            self.notification_banner.show(
                message=message,
                notification_type="success",
                auto_hide=True,
                duration=3000,
                key=f"api_{data_type}"
            )

            # Also update the status bar
            self.show_status(f"Using real API data for {data_type}")

    def set_api_preference(self, prefer_api=True):
        """
        Set whether to always prefer API data over cached data for all components.
        For static data (regions, cities, etc.), we always prefer cache first.

        Args:
            prefer_api (bool): Whether to always prefer API data (default: True)
        """
        logger.info(f"Setting API preference to {prefer_api} for dynamic data components")

        # For data manager, we'll handle static data differently
        if hasattr(self, 'data_manager'):
            # For static data, always prefer cache first regardless of the setting
            static_data_preference = False  # Always prefer cache for static data

            # Set the preference for dynamic data
            self.data_manager.set_api_preference(prefer_api, static_data_types=["regions", "cities", "districts", "cadastral"])

        # Update cache manager - but keep static data preference to cache
        if hasattr(self, 'cache_manager'):
            # Only set API preference for dynamic data
            self.cache_manager.set_api_preference(prefer_api,
                                                 static_cache_names=["city_suggestions", "regions_english", "city_boundaries"])

        # Update API cache
        if hasattr(self, 'google_maps') and hasattr(self.google_maps, 'api_cache'):
            # Google Maps API cache should prefer cache for static data
            self.google_maps.api_cache.set_api_preference(prefer_api)

        # Update batch search manager
        if hasattr(self, 'batch_search_manager'):
            if hasattr(self.batch_search_manager, 'set_api_preference'):
                self.batch_search_manager.set_api_preference(prefer_api)

        # Update smart batch search manager
        if hasattr(self, 'smart_batch_search_manager'):
            if hasattr(self.smart_batch_search_manager, 'set_api_preference'):
                self.smart_batch_search_manager.set_api_preference(prefer_api)

        # Update real batch search manager
        if hasattr(self, 'real_batch_search_manager'):
            if hasattr(self.real_batch_search_manager, 'set_api_preference'):
                self.real_batch_search_manager.set_api_preference(prefer_api)

        # Update optimized batch search manager
        if hasattr(self, 'optimized_batch_search_manager'):
            if hasattr(self.optimized_batch_search_manager, 'set_api_preference'):
                self.optimized_batch_search_manager.set_api_preference(prefer_api)

        # Show notification about the preference
        if hasattr(self, 'notification_banner'):
            if prefer_api:
                message = "Now preferring real API data over cached data."
                notification_type = "success"
            else:
                message = "Now allowing cached data to be used when available."
                notification_type = "info"

            self.notification_banner.show(
                message=message,
                notification_type=notification_type,
                auto_hide=True,
                duration=5000,
                key="api_preference"
            )

    def create_session(self):
        """Create a requests session with optimized connection pooling and retry capability."""
        return create_optimized_session()

    def load_regions(self):
        """Load the regions into the combobox using lazy loading for better performance."""
        # Delegate to the location search component
        if hasattr(self, 'location_search'):
            self.location_search.load_regions()

    def batch_search_properties(self, property_type, location, radius, max_results):
        """
        Perform a batch search for properties.

        Args:
            property_type (str): Type of property to search for
            location (str): Location to search in
            radius (float): Search radius in kilometers
            max_results (int): Maximum number of results to return
        """
        # Delegate to the batch search manager
        if hasattr(self, 'batch_search_manager'):
            self.batch_search_manager.batch_search_properties(property_type, location, radius, max_results)
        else:
            logger.error("Batch search manager not initialized")

    def batch_search_by_address(self, address, property_types=None, radius=2.0, max_results=50, callback=None):
        """
        Perform a batch search for properties by address with Google Maps autocomplete.

        Args:
            address (str): Full address to search in
            property_types (list, optional): List of property types to include
            radius (float): Search radius in kilometers
            max_results (int): Maximum number of results to return
            callback (callable, optional): Function to call with the results
        """
        # Delegate to the batch search manager
        if hasattr(self, 'batch_search_manager'):
            self.batch_search_manager.batch_search_by_address(address, property_types, radius, max_results, callback)
        else:
            logger.error("Batch search manager not initialized")

    def show_location_on_map(self, location):
        """
        Show a location on the map.

        Args:
            location (str): Location to show on the map
        """
        # Delegate to the map view
        if hasattr(self, 'map_view'):
            self.map_view.show_location(location)
        else:
            logger.error("Map view not initialized")

    def process_selected_buildings(self, selected_buildings, progress_callback=None, completion_callback=None):
        """
        Process selected buildings to retrieve owner information.

        Args:
            selected_buildings (list): List of selected building dictionaries
            progress_callback (callable, optional): Callback function for progress updates
            completion_callback (callable, optional): Callback function for completion notification
        """
        # Delegate to the batch processing manager
        if hasattr(self, 'batch_processing_manager'):
            self.batch_processing_manager.process_selected_buildings(
                selected_buildings, progress_callback, completion_callback
            )
        else:
            logger.error("Batch processing manager not initialized")

    def export_results(self):
        """Export the search results to a file."""
        # Delegate to the appropriate component
        if hasattr(self, 'property_display'):
            self.property_display.export_results()

    def save_letter(self):
        """Save the current letter to a file."""
        # Delegate to the letter manager
        if hasattr(self, 'letter_manager'):
            self.letter_manager.save_letter()

    def toggle_fullscreen(self):
        """Toggle fullscreen mode."""
        self._fullscreen = not self._fullscreen

        if self._fullscreen:
            # Save current geometry
            self._normal_state['geometry'] = self.root.geometry()
            # Set fullscreen
            self.root.attributes('-fullscreen', True)
        else:
            # Restore normal state
            self.root.attributes('-fullscreen', False)
            self.root.geometry(self._normal_state['geometry'])

    def show_about(self):
        """Show the about dialog."""
        # Implement about dialog
        pass

    def show_documentation(self):
        """Show the documentation."""
        # Implement documentation viewer
        pass

    def show_status(self, message):
        """Show a message in the status bar."""
        if hasattr(self, 'status_bar'):
            self.status_bar.config(text=message)

    # OSM-related methods delegated to OSM manager
    def _delegate_to_osm_manager(self, method_name, *args, **kwargs):
        """Delegate a method call to the OSM manager."""
        if hasattr(self, 'osm_manager'):
            method = getattr(self.osm_manager, method_name, None)
            if method:
                return method(*args, **kwargs)
        return [] if 'suggestions' in method_name else None

    def get_osm_address_suggestions(self, text):
        """Get address suggestions for OSM search."""
        return self._delegate_to_osm_manager('get_osm_address_suggestions', text)

    def get_osm_city_suggestions(self, text):
        """Get city suggestions for OSM search."""
        return self._delegate_to_osm_manager('get_osm_city_suggestions', text)

    def get_osm_street_suggestions(self, text):
        """Get street suggestions for OSM search."""
        return self._delegate_to_osm_manager('get_osm_street_suggestions', text)

    def get_osm_postal_suggestions(self, text):
        """Get postal code suggestions for OSM search."""
        return self._delegate_to_osm_manager('get_osm_postal_suggestions', text)

    def update_osm_component_fields(self, ui_component, event=None):
        """Update the OSM component fields based on the full address."""
        self._delegate_to_osm_manager('update_osm_component_fields', ui_component, event)

    def update_osm_full_address(self, ui_component, event=None):
        """Update the OSM full address field based on the component fields."""
        self._delegate_to_osm_manager('update_osm_full_address', ui_component, event)

    def cleanup(self):
        """Clean up resources before application exit."""
        try:
            logger.info("Cleaning up resources before exit")

            # Shutdown the thread manager
            if hasattr(self, 'thread_manager'):
                self.thread_manager.shutdown(wait=False)
                logger.info("Thread manager shut down")

            # Close the session
            if hasattr(self, 'session') and hasattr(self.session, 'close'):
                self.session.close()
                logger.info("Network session closed")

            # Close the database cache
            if hasattr(self, 'db_cache'):
                self.db_cache.close()
                logger.info("Database cache closed")

            # Create a separate thread to handle potentially blocking cleanup operations
            def background_cleanup():
                try:
                    # Stop memory monitoring
                    memory_monitor.stop()
                    logger.info("Memory monitoring stopped")

                    # Stop background processor
                    background_processor.stop(wait=False)
                    logger.info("Background processor stopped")

                    # Stop progressive loader
                    progressive_loader.stop_loading()
                    logger.info("Progressive loader stopped")

                    # Log cache statistics before exit
                    if hasattr(self, 'cache_manager'):
                        stats = self.cache_manager.get_stats()
                        size_info = self.cache_manager.get_size_info()
                        logger.info(f"Cache statistics: {stats}")
                        logger.info(f"Cache size info: {size_info}")

                    # Log compression statistics
                    compression_stats = data_compressor.get_stats()
                    logger.info(f"Compression statistics: {compression_stats}")

                    # Log background processor statistics
                    bg_stats = background_processor.get_stats()
                    logger.info(f"Background processor statistics: {bg_stats}")

                    # Log progressive loader statistics
                    pl_stats = progressive_loader.get_stats()
                    logger.info(f"Progressive loader statistics: {pl_stats}")

                    # Print function profiling statistics if enabled
                    if self.settings.get('development_mode', False):
                        function_profiler.print_stats(sort_by='total_time', limit=20)
                except Exception as e:
                    logger.error(f"Error during background cleanup: {e}")

            # Start the background cleanup thread as daemon so it doesn't block application exit
            cleanup_thread = threading.Thread(target=background_cleanup, daemon=True)
            cleanup_thread.start()

            # Don't wait for the thread to complete - let the application exit
            logger.info("Background cleanup thread started")

            # Clean up any other resources
            # ...
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def _on_memory_warning(self, memory_info):
        """
        Handle memory warning callback.

        Args:
            memory_info: Dictionary with memory usage information
        """
        # If memory usage is critical, force garbage collection
        if memory_info['percent'] >= 90.0:
            logger.warning(f"Critical memory usage: {memory_info['percent']:.1f}% - forcing cleanup")
            self._force_memory_cleanup()

    def _force_memory_cleanup(self):
        """Force memory cleanup to reduce memory usage."""
        # Clear caches
        if hasattr(self, 'cache_manager'):
            self.cache_manager.cleanup()

        # Clear weak references
        weak_cache.clear()

        # Force garbage collection
        memory_monitor.force_garbage_collection()

    def _enable_profiling(self):
        """Enable function profiling for performance analysis."""
        # Wrap key methods with the profiler
        self.search_by_address = function_profiler.profile(self.search_by_address)
        self.search_by_coordinates = function_profiler.profile(self.search_by_coordinates)
        self.fetch_property_details = function_profiler.profile(self.fetch_property_details)

        logger.info("Function profiling enabled")

    def _on_all_chunks_loaded(self):
        """Called when all chunks are loaded in the progressive loader."""
        logger.info("All data chunks loaded")

    def submit_background_task(self, func, *args, **kwargs):
        """
        Submit a task to run in the background.

        Args:
            func: Function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            str: Task ID
        """
        # Add progress callback if not provided
        if 'on_progress' not in kwargs:
            kwargs['on_progress'] = self._on_task_progress

        # Add completion callback if not provided
        if 'on_complete' not in kwargs:
            kwargs['on_complete'] = self._on_task_complete

        # Add error callback if not provided
        if 'on_error' not in kwargs:
            kwargs['on_error'] = self._on_task_error

        # Submit the task
        task_id = background_processor.submit(func, *args, **kwargs)
        logger.info(f"Submitted background task {task_id}")

        return task_id

    def _on_task_progress(self, progress):
        """
        Handle task progress updates.

        Args:
            progress (float): Progress value (0.0 to 1.0)
        """
        # Update the status bar with progress
        progress_percent = int(progress * 100)
        self.show_status(f"Processing... {progress_percent}%")

    def _on_task_complete(self, result):
        """
        Handle task completion.

        Args:
            result: Task result
        """
        # Update the status bar
        self.show_status("Ready")

    def _on_task_error(self, error):
        """
        Handle task errors.

        Args:
            error: Exception that occurred
        """
        # Update the status bar
        self.show_status(f"Error: {error}")
        logger.error(f"Background task error: {error}")

    def reload_settings(self):
        """Reload settings from the configuration."""
        if hasattr(self, 'settings_manager'):
            self.settings_manager.reload_settings()

    def load_data_progressively(self, data_provider, total_items, chunk_size=100):
        """
        Load data progressively in chunks.

        Args:
            data_provider: Function that takes (start_index, end_index) and returns data
            total_items (int): Total number of items to load
            chunk_size (int): Number of items per chunk

        Returns:
            The progressive loader instance
        """
        # Configure the progressive loader
        progressive_loader.set_data_provider(data_provider)
        progressive_loader.set_total_items(total_items)
        progressive_loader.chunk_size = chunk_size

        # Start loading
        progressive_loader.start_loading()

        return progressive_loader

    def compress_data(self, data, method=None, level=None):
        """
        Compress data for storage or transmission.

        Args:
            data: Data to compress
            method: Compression method (None for default)
            level: Compression level (None for default)

        Returns:
            Tuple of (compressed_data, metadata)
        """
        if isinstance(data, (dict, list, tuple)):
            # Use JSON compression for structured data
            return data_compressor.compress_json(data, method, level)
        elif isinstance(data, bytes):
            # Use binary compression for raw bytes
            return data_compressor.compress(data, method, level)
        else:
            # Use object compression for other Python objects
            return data_compressor.compress_object(data, method, level)

    def decompress_data(self, compressed_data, metadata):
        """
        Decompress data.

        Args:
            compressed_data: Compressed data
            metadata: Compression metadata

        Returns:
            The decompressed data
        """
        # Determine the type of compression used
        if isinstance(compressed_data, str):
            # JSON compression
            return data_compressor.decompress_json(compressed_data, metadata)
        elif isinstance(compressed_data, bytes):
            # Check if this is object compression or binary compression
            if metadata.get('method') == 'pickle':
                return data_compressor.decompress_object(compressed_data, metadata)
            else:
                return data_compressor.decompress(compressed_data, metadata)
