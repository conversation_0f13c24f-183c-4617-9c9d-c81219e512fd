"""
Loading Window for the Czech Property Registry application.

This module provides a splash screen with loading progress information
that appears during application startup.
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import logging

# Configure logging
logger = logging.getLogger(__name__)

class LoadingWindow:
    """
    A splash screen window that shows loading progress during application startup.

    This window displays a progress bar and status messages to inform the user
    about what's happening during the initialization process.
    """

    def __init__(self, parent=None, app_title="Czech Property Registry"):
        """
        Initialize the loading window.

        Args:
            parent: Parent window (if None, creates a new Toplevel window)
            app_title: Title of the application
        """
        self.parent = parent
        self.app_title = app_title

        # Create the window
        if parent is None:
            self.window = tk.Tk()
            self.window.withdraw()  # Hide until ready
        else:
            self.window = tk.Toplevel(parent)

        # Configure the window
        self.window.title(f"Loading {app_title}")
        self.window.geometry("500x300")
        self.window.resizable(False, False)
        self.window.configure(bg="#f0f0f0")

        # Center the window on screen
        self._center_window()

        # Set window attributes
        self.window.attributes("-topmost", True)

        # Remove window decorations if supported on platform
        try:
            self.window.overrideredirect(True)  # Remove title bar
        except:
            pass  # Not supported on all platforms

        # Create UI elements
        self._create_ui()

        # Initialize progress variables
        self.progress_value = 0
        self.progress_max = 100
        self.current_task = "Initializing..."
        self.tasks_completed = 0
        self.total_tasks = 0

        # Show the window
        self.window.deiconify()
        self.window.update()

    def _center_window(self):
        """Center the window on the screen."""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def _create_ui(self):
        """Create the UI elements for the loading window."""
        # Create main frame
        main_frame = ttk.Frame(self.window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Application title
        title_label = ttk.Label(
            main_frame,
            text=self.app_title,
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=(0, 20))

        # Loading image or logo (placeholder)
        logo_frame = ttk.Frame(main_frame, width=200, height=100)
        logo_frame.pack(pady=(0, 20))
        logo_frame.pack_propagate(False)

        logo_label = ttk.Label(
            logo_frame,
            text="LOADING",
            font=("Arial", 24, "bold")
        )
        logo_label.pack(fill=tk.BOTH, expand=True)

        # Current task label
        self.task_label = ttk.Label(
            main_frame,
            text="Initializing application...",
            font=("Arial", 10)
        )
        self.task_label.pack(pady=(0, 10), anchor=tk.W)

        # Progress bar
        self.progress_bar = ttk.Progressbar(
            main_frame,
            orient=tk.HORIZONTAL,
            length=460,
            mode="determinate"
        )
        self.progress_bar.pack(pady=(0, 10), fill=tk.X)

        # Progress percentage
        self.percentage_label = ttk.Label(
            main_frame,
            text="0%",
            font=("Arial", 9)
        )
        self.percentage_label.pack(anchor=tk.E)

        # Status message
        self.status_label = ttk.Label(
            main_frame,
            text="Starting up...",
            font=("Arial", 9, "italic")
        )
        self.status_label.pack(pady=(10, 0), anchor=tk.W)

    def set_task(self, task_name, status_message=""):
        """
        Set the current task name and status message.

        Args:
            task_name: Name of the current task
            status_message: Additional status information
        """
        self.current_task = task_name

        # Update the UI
        self.task_label.config(text=task_name)
        if status_message:
            self.status_label.config(text=status_message)

        # Increment tasks completed if this is a new task
        self.tasks_completed += 1
        if self.total_tasks > 0:
            progress = int((self.tasks_completed / self.total_tasks) * 100)
            self.set_progress(progress)

        # Update the window
        self.window.update()

    def set_progress(self, value):
        """
        Set the progress bar value.

        Args:
            value: Progress value (0-100)
        """
        self.progress_value = max(0, min(value, 100))

        # Update the UI
        self.progress_bar["value"] = self.progress_value
        self.percentage_label.config(text=f"{self.progress_value}%")

        # Update the window
        self.window.update()

    def set_status(self, message):
        """
        Set the status message.

        Args:
            message: Status message to display
        """
        # Update the UI
        self.status_label.config(text=message)

        # Update the window
        self.window.update()

    def set_total_tasks(self, total):
        """
        Set the total number of tasks for progress calculation.

        Args:
            total: Total number of tasks
        """
        self.total_tasks = total
        self.tasks_completed = 0

    def increment_progress(self, increment=1):
        """
        Increment the progress bar by the specified amount.

        Args:
            increment: Amount to increment (default: 1)
        """
        self.set_progress(self.progress_value + increment)

    def close(self):
        """Close the loading window."""
        try:
            self.window.destroy()
        except:
            pass  # Window might already be closed

    def finish(self, delay=0.5):
        """
        Complete the loading process and close the window after a delay.

        Args:
            delay: Delay in seconds before closing
        """
        # Set progress to 100%
        self.set_progress(100)
        self.set_task("Loading complete", "Application is ready")

        # Wait for the specified delay
        if delay > 0:
            self.window.after(int(delay * 1000), self.close)
        else:
            self.close()

    def run_with_loading(self, tasks, on_complete=None):
        """
        Run a list of tasks with loading indicators.

        Args:
            tasks: List of (task_name, function, status_message) tuples
            on_complete: Function to call when all tasks are complete
        """
        self.set_total_tasks(len(tasks))
        self.tasks = tasks
        self.on_complete = on_complete
        self.current_task_index = 0

        # Schedule the first task
        self.window.after(100, self._run_next_task)

    def _run_next_task(self):
        """Run the next task in the queue."""
        if self.current_task_index >= len(self.tasks):
            # All tasks completed
            self.finish()

            # Call the completion callback if provided
            if self.on_complete:
                self.on_complete()
            return

        # Get the current task
        task_name, task_func, status_msg = self.tasks[self.current_task_index]

        # Update the UI
        self.set_task(task_name, status_msg)

        try:
            # Run the task in a separate thread to avoid blocking the UI
            def execute_task():
                try:
                    task_func()

                    # Schedule the next task on the main thread
                    self.window.after(100, self._task_completed)
                except Exception as e:
                    logger.error(f"Error in task '{task_name}': {e}")

                    # Update status on the main thread
                    self.window.after(0, lambda: self.set_status(f"Error: {str(e)}"))

                    # Continue to the next task
                    self.window.after(100, self._task_completed)

            # Start the task in a separate thread
            threading.Thread(target=execute_task, daemon=True).start()
        except Exception as e:
            logger.error(f"Error starting task '{task_name}': {e}")
            self.set_status(f"Error: {str(e)}")

            # Continue to the next task
            self.window.after(100, self._task_completed)

    def _task_completed(self):
        """Handle task completion."""
        # Update progress
        self.current_task_index += 1
        progress = int((self.current_task_index / len(self.tasks)) * 100)
        self.set_progress(progress)

        # Schedule the next task
        self.window.after(100, self._run_next_task)
