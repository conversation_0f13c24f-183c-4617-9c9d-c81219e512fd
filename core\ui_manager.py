"""
UI Manager for the Czech Property Scraper application.
Handles UI initialization and component creation.
"""

import tkinter as tk
from tkinter import ttk
import logging

from ui.property_display import PropertyDisplay
from ui.captcha_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ui.treeview_manager import TreeviewManager
from ui.address_search import Ad<PERSON><PERSON><PERSON>ch<PERSON>
from ui.batch_search import Batch<PERSON><PERSON><PERSON>UI
from ui.smart_batch_search import SmartBatchSearchUI
from ui.real_batch_search_ui import RealBatchSearchUI
from ui.coordinate_search import CoordinateSearchUI
from ui.location_search import LocationSearchUI
from ui.osm_search import OSMSearchUI
from ui.buyer_info import BuyerInfo<PERSON>
from ui.map_view import MapView
from ui.virtualized_list import VirtualizedList
from ui.virtualized_tree import VirtualizedTree

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("UIManager")


class UIManager:
    """Manages the UI components of the application."""

    def __init__(self, app):
        """
        Initialize the UI manager.

        Args:
            app: The main application instance
        """
        self.app = app
        self.root = app.root

        # Initialize UI components
        self.create_main_layout()
        self.create_notebook()
        self.create_status_bar()
        self.create_menu()

        # Initialize UI component classes
        self.initialize_component_classes()

        # Set initial PanedWindow sash positions after a short delay
        self.root.after(500, self._set_initial_sash_positions)

    def create_main_layout(self):
        """Create the main layout of the application."""
        # Create main frame and paned window
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill="both", expand=True)

        self.v_paned = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        self.v_paned.pack(fill="both", expand=True)

        # Create and add left and right frames to the paned window
        frames = {
            'left_frame': {'weight': 3},
            'right_frame': {'weight': 2}
        }

        for frame_name, options in frames.items():
            frame = ttk.Frame(self.v_paned, padding=(5, 5))
            self.v_paned.add(frame, weight=options['weight'])
            # Set the frame as an attribute of both self and the app
            setattr(self, frame_name, frame)
            setattr(self.app, frame_name, frame)

        # Create buyer frame in left pane
        self.buyer_frame = ttk.LabelFrame(self.left_frame, text="Buyer Information", padding=(5, 5))
        self.buyer_frame.pack(fill="x", side=tk.BOTTOM, padx=5, pady=5)
        self.app.buyer_frame = self.buyer_frame

        # Create map frame in right pane
        self.app.map_frame = ttk.LabelFrame(self.right_frame, text="Map", padding=(5, 5))
        self.app.map_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Create map container
        ttk.Frame(self.app.map_frame).pack(fill="both", expand=True)

        # Create horizontal scrollbar
        self.app.hsb = ttk.Scrollbar(self.root, orient=tk.HORIZONTAL)
        self.app.hsb.pack(side=tk.BOTTOM, fill=tk.X)

    def create_notebook(self):
        """Create the notebook with tabs for different search methods."""
        # Create a notebook in the left frame
        self.notebook = ttk.Notebook(self.left_frame)
        self.notebook.pack(fill="both", expand=True, padx=5, pady=5)

        # Define tabs with their titles
        tabs = {
            'real_batch_frame': "Real Batch Search",  # Add real batch search as the first tab
            'batch_frame': "Smart Batch Search",
            'osm_frame': "OpenStreetMap Search",
            'address_frame': "Address Search",
            'coordinates_frame': "Coordinate Search",
            'location_frame': "Location Search"
        }

        # Create frames and add them to the notebook
        for attr_name, tab_title in tabs.items():
            frame = ttk.Frame(self.notebook, padding=(10, 10))
            self.notebook.add(frame, text=tab_title)

            # Store the frame as an attribute of self and the app
            setattr(self, attr_name, frame)
            setattr(self.app, attr_name, frame)

        # Store the notebook in the app
        self.app.notebook = self.notebook

        # Select the first tab by default
        self.notebook.select(0)

    def create_status_bar(self):
        """Create the status bar at the bottom of the window."""
        # Create status bar (place it outside the scrollable area)
        self.app.status_bar = ttk.Label(self.root, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.app.status_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.app.hsb)

    def create_menu(self):
        """Create the main menu for the application."""
        # Create a menu bar
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Create API preference variable
        self.app.api_preference_var = tk.BooleanVar(value=True)

        # Define menu structure
        menus = {
            "File": [
                {"label": "Export Results", "command": self.app.export_results},
                {"label": "Save Letter", "command": self.app.save_letter},
                {"type": "separator"},
                {"label": "Exit", "command": self.root.quit}
            ],
            "View": [
                {"label": "Toggle Fullscreen", "command": self.app.toggle_fullscreen},
                {"type": "separator"},
                {"label": "Always Prefer API Data", "variable": self.app.api_preference_var,
                 "command": lambda: self.app.set_api_preference(self.app.api_preference_var.get())}
            ],
            "Tools": [
                {"label": "Clear Geocoding Cache", "command": self._clear_geocoding_cache},
                {"label": "Clear All Caches", "command": self._clear_all_caches}
            ],
            "Help": [
                {"label": "About", "command": self.app.show_about},
                {"label": "Documentation", "command": self.app.show_documentation}
            ]
        }

        # Create menus based on the structure
        for menu_name, items in menus.items():
            menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label=menu_name, menu=menu)

            for item in items:
                if item.get("type") == "separator":
                    menu.add_separator()
                elif "variable" in item:
                    # Add a checkbutton menu item
                    menu.add_checkbutton(
                        label=item["label"],
                        variable=item["variable"],
                        command=item["command"]
                    )
                else:
                    # Add a regular command menu item
                    menu.add_command(label=item["label"], command=item["command"])

    def initialize_component_classes(self):
        """Initialize the UI component classes."""
        # Initialize components that take only the app parameter
        app_only_components = {
            'property_display': PropertyDisplay,
            'captcha_handler': CaptchaHandler,
            'treeview_manager': TreeviewManager,
            'map_view': MapView
        }

        # Initialize components that take a parent frame and the app
        framed_components = {
            'osm_search': (OSMSearchUI, self.app.osm_frame),
            'address_search': (AddressSearchUI, self.app.address_frame),
            'batch_search': (BatchSearchUI, self.app.batch_frame),
            'smart_batch_search': (SmartBatchSearchUI, self.app.batch_frame),
            'real_batch_search': (RealBatchSearchUI, self.app.real_batch_frame),  # Use the correct parent frame
            'coordinate_search': (CoordinateSearchUI, self.app.coordinates_frame),
            'location_search': (LocationSearchUI, self.app.location_frame),
            'buyer_info': (BuyerInfoUI, self.buyer_frame)
        }

        # Initialize app-only components
        for attr_name, component_class in app_only_components.items():
            setattr(self.app, attr_name, component_class(self.app))

        # Initialize framed components
        for attr_name, (component_class, parent_frame) in framed_components.items():
            setattr(self.app, attr_name, component_class(parent_frame, self.app))

        # Initialize virtualized components
        self._initialize_virtualized_components()

        logger.info("UI components initialized")

    def _initialize_virtualized_components(self):
        """Initialize virtualized list and tree components for better performance."""
        # Create a virtualized property list for the batch search results
        self.app.virtualized_property_list = VirtualizedList(
            self.app.batch_frame,
            item_height=40,
            buffer_items=10,
            width=400,
            height=300
        )

        # Set up the property list renderer
        self.app.virtualized_property_list.set_item_renderer(
            create_widget_func=lambda: ttk.Frame(self.app.virtualized_property_list._content_frame),
            render_func=self._render_property_item
        )

        # Create a virtualized tree for the property hierarchy
        self.app.virtualized_property_tree = VirtualizedTree(
            self.app.right_frame,
            item_height=30,
            buffer_items=10,
            width=400,
            height=300,
            indent_width=20
        )

        # Set up the property tree renderer
        self.app.virtualized_property_tree.set_node_renderer(
            create_widget_func=lambda: ttk.Frame(self.app.virtualized_property_tree._content_frame),
            render_func=self._render_property_node
        )

        logger.info("Virtualized components initialized")

    def _render_property_item(self, property_data, widget):
        """
        Render a property item in the virtualized list.

        Args:
            property_data: Property data to render
            widget: Widget to render into
        """
        # Clear existing widgets
        for child in widget.winfo_children():
            child.destroy()

        # Create labels for property information
        if isinstance(property_data, dict):
            # Address label
            address = property_data.get('address', 'Unknown Address')
            address_label = ttk.Label(widget, text=address, font=('TkDefaultFont', 10, 'bold'))
            address_label.grid(row=0, column=0, sticky='w', padx=5, pady=2)

            # Owner label
            owner = property_data.get('owner', 'Unknown Owner')
            owner_label = ttk.Label(widget, text=f"Owner: {owner}")
            owner_label.grid(row=1, column=0, sticky='w', padx=5, pady=0)

            # Add a separator
            ttk.Separator(widget, orient='horizontal').grid(row=2, column=0, sticky='ew', pady=2)

    def _render_property_node(self, node, widget, indent_level):
        """
        Render a property node in the virtualized tree.

        Args:
            node: Tree node to render
            widget: Widget to render into
            indent_level: Indentation level
        """
        # Clear existing widgets
        for child in widget.winfo_children():
            child.destroy()

        # Calculate indentation
        indent = indent_level * 20

        # Create expand/collapse button if the node has children
        if node.children:
            button_text = "-" if node.is_expanded else "+"
            expand_button = ttk.Button(
                widget,
                text=button_text,
                width=2,
                command=lambda: self._toggle_tree_node(node.id)
            )
            expand_button.grid(row=0, column=0, padx=(indent, 0), pady=2)
            label_column = 1
        else:
            label_column = 0

        # Create label for node data
        if isinstance(node.data, dict):
            label_text = node.data.get('name', 'Unknown')
        else:
            label_text = str(node.data)

        node_label = ttk.Label(widget, text=label_text)
        node_label.grid(row=0, column=label_column, sticky='w', padx=(5 if label_column > 0 else indent + 5, 0), pady=2)

    def _toggle_tree_node(self, node_id):
        """
        Toggle a tree node's expanded state.

        Args:
            node_id: ID of the node to toggle
        """
        self.app.virtualized_property_tree.toggle_node(node_id)

    def _set_initial_sash_positions(self):
        """Set the initial positions of the PanedWindow sashes."""
        # Get the width of the main window
        window_width = self.root.winfo_width()

        # Set the sash position to 60% of the window width
        self.v_paned.sashpos(0, int(window_width * 0.6))

    def _clear_geocoding_cache(self):
        """Clear the geocoding cache."""
        try:
            from ui.message_boxes import MessageBoxes

            # Ask for confirmation
            confirm = MessageBoxes.ask_yes_no(
                "Clear Geocoding Cache",
                "Are you sure you want to clear the geocoding cache? This will force fresh lookups for all addresses."
            )

            if confirm == 'yes':
                # Clear the geocoding cache
                count = 0
                if hasattr(self.app, 'google_maps'):
                    count = self.app.google_maps.clear_geocoding_cache()

                # Show a success message
                MessageBoxes.show_info(
                    "Geocoding Cache Cleared",
                    f"The geocoding cache has been cleared successfully. {count} entries were removed."
                )

                # Show status
                self.app.show_status(f"Cleared {count} entries from geocoding cache")
        except Exception as e:
            # Show an error message
            from ui.message_boxes import MessageBoxes
            MessageBoxes.show_error("Error", f"Error clearing geocoding cache: {str(e)}")

    def _clear_all_caches(self):
        """Clear all application caches."""
        try:
            from ui.message_boxes import MessageBoxes

            # Ask for confirmation
            confirm = MessageBoxes.ask_yes_no(
                "Clear All Caches",
                "Are you sure you want to clear all caches? This will force fresh data to be fetched from all APIs."
            )

            if confirm == 'yes':
                cache_stats = {}

                # Clear the geocoding cache
                if hasattr(self.app, 'google_maps'):
                    cache_stats['geocoding'] = self.app.google_maps.clear_geocoding_cache()

                # Clear the cache manager caches
                if hasattr(self.app, 'cache_manager'):
                    cache_stats['cache_manager'] = self.app.cache_manager.clear_all()

                # Clear the address suggestions cache
                if hasattr(self.app, '_address_suggestions_cache'):
                    cache_size = len(self.app._address_suggestions_cache)
                    self.app._address_suggestions_cache.clear()
                    cache_stats['address_suggestions'] = cache_size

                # Clear the gender cache
                if hasattr(self.app, '_gender_cache'):
                    cache_size = len(self.app._gender_cache)
                    self.app._gender_cache.clear()
                    cache_stats['gender'] = cache_size

                # Clear the encrypted URLs cache
                if hasattr(self.app, 'encrypted_urls_cache'):
                    cache_size = len(self.app.encrypted_urls_cache)
                    self.app.encrypted_urls_cache.clear()
                    cache_stats['encrypted_urls'] = cache_size

                # Show a message with the number of items cleared
                total_cleared = sum(cache_stats.values())
                MessageBoxes.show_info(
                    "All Caches Cleared",
                    f"Cleared {total_cleared} items from all caches.\n\n"
                    f"Details:\n" + "\n".join([f"- {k}: {v} items" for k, v in cache_stats.items()])
                )

                # Show status
                self.app.show_status(f"Cleared {total_cleared} items from all caches")
        except Exception as e:
            # Show an error message
            from ui.message_boxes import MessageBoxes
            MessageBoxes.show_error("Error", f"Error clearing all caches: {str(e)}")
