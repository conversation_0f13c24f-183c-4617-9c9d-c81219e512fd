"""
Google Maps API integration for the Czech Property Registry application.
"""

# Import necessary modules
import requests
import os
import webbrowser
import configparser
import threading
from utils.api_cache import APICache
from utils.rate_limiter import RateLimiter
from utils.template_loader import Template<PERSON>oader
from utils.geocoding_cache import GeocodingCache

# Try to import tkinterweb for embedded browser
try:
    from tkinterweb import HtmlFrame
    TKINTERWEB_AVAILABLE = True
except ImportError:
    TKINTERWEB_AVAILABLE = False
    print("tkinterweb not available. Will use external browser for maps.")

class GoogleMapsIntegration:
    """Class for handling Google Maps integration"""

    def __init__(self):
        """Initialize the Google Maps integration"""
        self.api_key = self.load_api_key()

        # Initialize API cache with 24-hour expiration
        self.cache = APICache(cache_dir='api_cache', expiration_hours=24)

        # Initialize specialized geocoding cache with 24-hour expiration
        self.geocoding_cache = GeocodingCache(cache_dir='geocoding_cache', expiry_seconds=86400)

        # Initialize rate limiter (5 requests per second, 1000 per day)
        self.rate_limiter = RateLimiter(requests_per_second=5, requests_per_day=1000)

        # Track ongoing API requests for UI feedback
        self.active_requests = 0
        self.request_lock = threading.Lock()

        # Initialize template loader for map HTML templates
        self.template_loader = TemplateLoader(template_dir='templates')

        # Reference to the map frame and HTML frame (will be set when needed)
        self.map_frame = None
        self.html_frame = None

        # Reference to the app (will be set by the app)
        self.app = None

        # Clear expired cache entries on startup
        threading.Thread(target=self.cache.clear_expired, daemon=True).start()

        # Validate the API key
        if self.api_key:
            self.validate_api_key()

    def load_api_key(self):
        """Load Google Maps API key from config file"""
        config = configparser.ConfigParser()
        if os.path.exists('config.ini'):
            config.read('config.ini')
            print("Loading Google Maps API key from config.ini")
            if 'GOOGLE_MAPS' in config:
                print("Found GOOGLE_MAPS section")
                if 'api_key' in config['GOOGLE_MAPS']:
                    api_key = config['GOOGLE_MAPS']['api_key']
                    print(f"API key found: {api_key[:5]}..." if api_key else "API key is empty")
                    return api_key
                else:
                    print("api_key not found in GOOGLE_MAPS section")
            else:
                print("GOOGLE_MAPS section not found in config.ini")
        else:
            print("config.ini file not found")
        return None

    def geocode(self, address, country="Czech Republic"):
        """Alias for geocode_address for compatibility"""
        return self.geocode_address(address, country)

    def geocode_address(self, address, country="Czech Republic"):
        """Convert address to coordinates using Google Maps Geocoding API"""
        # Add country to address if not already included
        if country and country.lower() not in address.lower():
            full_address = f"{address}, {country}"
        else:
            full_address = address

        # If we have an API key, use the Geocoding API
        if self.api_key:
            print(f"Geocoding address: {full_address}")

            # Check specialized geocoding cache first
            cached_result = self.geocoding_cache.get(full_address)
            if cached_result:
                print(f"Using cached geocoding result for '{full_address}'")
                return cached_result

            # Try direct geocoding first
            result = self._direct_geocode(full_address)
            if result:
                # Cache the result in our specialized geocoding cache
                self.geocoding_cache.set(full_address, result)
                return result

            # If direct geocoding failed, try using the API request mechanism
            try:
                # Construct the URL
                url = f"https://maps.googleapis.com/maps/api/geocode/json"
                params = {
                    "address": full_address,
                    "key": self.api_key
                }

                # Make the API request
                data = self.make_api_request(url, params)

                if data and data.get('status') == 'OK':
                    # Get the first result
                    result = data.get('results', [])[0]
                    location = result.get('geometry', {}).get('location', {})
                    formatted_address = result.get('formatted_address', '')

                    print(f"Geocoding successful for '{full_address}'")
                    print(f"Formatted address: {formatted_address}")
                    print(f"Coordinates: {location.get('lat')}, {location.get('lng')}")

                    geocode_result = {
                        'lat': location.get('lat'),
                        'lng': location.get('lng'),
                        'formatted_address': formatted_address
                    }

                    # Cache the result in our specialized geocoding cache
                    self.geocoding_cache.set(full_address, geocode_result)

                    return geocode_result
                else:
                    print(f"Geocoding failed with status: {data.get('status')}")
                    error_msg = f"Geocoding failed with status: {data.get('status')}"
                    raise ValueError(error_msg)
            except Exception as e:
                print(f"Error in geocoding: {e}")
                raise ValueError(f"Error in geocoding: {e}")

        # If we don't have an API key, raise an error
        error_msg = "Google Maps API key is required for geocoding"
        print(error_msg)
        raise ValueError(error_msg)

    def reverse_geocode(self, lat, lng):
        """Convert coordinates to address using Google Maps Geocoding API"""
        # If we don't have an API key, return an error
        if not self.api_key:
            print("Google Maps API key is required for reverse geocoding")
            return {
                'error': True,
                'error_message': 'Google Maps API key is required for reverse geocoding',
                'formatted_address': None,
                'address_components': {}
            }

        # Try direct reverse geocoding first
        result = self._direct_reverse_geocode(lat, lng)
        if result:
            return result

        # If direct reverse geocoding failed, try using the API request mechanism
        try:
            # Construct the URL
            url = f"https://maps.googleapis.com/maps/api/geocode/json"
            params = {
                "latlng": f"{lat},{lng}",
                "key": self.api_key
            }

            # Make the API request
            data = self.make_api_request(url, params)

            if data and data.get('status') == 'OK':
                # Get the first result
                result = data.get('results', [])[0]
                formatted_address = result.get('formatted_address', '')

                # Extract address components
                address_components = {}
                for component in result.get('address_components', []):
                    for component_type in component.get('types', []):
                        address_components[component_type] = component.get('long_name')

                print(f"Reverse geocoding successful")
                print(f"Formatted address: {formatted_address}")

                return {
                    'formatted_address': formatted_address,
                    'address_components': address_components
                }
            else:
                print(f"Reverse geocoding failed with status: {data.get('status')}")
                return {
                    'error': True,
                    'error_message': f"Reverse geocoding failed with status: {data.get('status')}",
                    'formatted_address': None,
                    'address_components': {}
                }
        except Exception as e:
            print(f"Error in reverse geocoding: {e}")
            return {
                'error': True,
                'error_message': f"Error in reverse geocoding: {str(e)}",
                'formatted_address': None,
                'address_components': {}
            }

    def get_region_name_mapping(self):
        """
        Get a mapping of Czech region names to English region names

        Returns:
            dict: A dictionary mapping Czech region names to English region names
        """
        # Define a hardcoded mapping of Czech region names to English names
        # This is not fallback data, but a fixed mapping that's part of the application
        mapping = {
            "praha": "Prague",
            "hlavní město praha": "Prague",
            "středočeský kraj": "Central Bohemian Region",
            "jihočeský kraj": "South Bohemian Region",
            "plzeňský kraj": "Plzeň Region",
            "karlovarský kraj": "Karlovy Vary Region",
            "ústecký kraj": "Ústí nad Labem Region",
            "liberecký kraj": "Liberec Region",
            "královéhradecký kraj": "Hradec Králové Region",
            "pardubický kraj": "Pardubice Region",
            "kraj vysočina": "Vysočina Region",
            "jihomoravský kraj": "South Moravian Region",
            "olomoucký kraj": "Olomouc Region",
            "zlínský kraj": "Zlín Region",
            "moravskoslezský kraj": "Moravian-Silesian Region"
        }

        return mapping

    def get_czech_regions(self):
        """Get list of Czech regions using Google Maps Geocoding API"""
        if not self.api_key:
            print("Google Maps API key is required to get Czech regions")
            return []

        # Use the Google Maps API to get regions
        try:
            # Geocode Czech Republic to get its boundaries
            url = "https://maps.googleapis.com/maps/api/geocode/json"
            params = {
                "address": "Czech Republic",
                "key": self.api_key
            }

            data = self.make_api_request(url, params)

            if data and data.get('status') == 'OK':
                # Extract administrative areas from the result
                regions = []
                for result in data.get('results', []):
                    for component in result.get('address_components', []):
                        if 'administrative_area_level_1' in component.get('types', []):
                            region_name = component.get('long_name')
                            if region_name and region_name not in regions:
                                regions.append(region_name)

                # If we couldn't find regions in the geocoding result, use a hardcoded list
                # This is not fallback data, but a fixed list of known regions in Czech Republic
                if not regions:
                    regions = [
                        "Prague", "Central Bohemian Region", "South Bohemian Region",
                        "Plzeň Region", "Karlovy Vary Region", "Ústí nad Labem Region",
                        "Liberec Region", "Hradec Králové Region", "Pardubice Region",
                        "Vysočina Region", "South Moravian Region", "Olomouc Region",
                        "Zlín Region", "Moravian-Silesian Region"
                    ]

                return regions
            else:
                print(f"Failed to get Czech regions: {data.get('status')}")
                return []
        except Exception as e:
            print(f"Error getting Czech regions: {e}")
            return []

    def get_city_boundaries(self, city_name, country="Czech Republic"):
        """
        Get the boundaries of a city using Google Maps API.

        Args:
            city_name (str): Name of the city
            country (str, optional): Country name to narrow down the search

        Returns:
            dict: Dictionary with bounds information (north, south, east, west)
                  or None if boundaries couldn't be determined
        """
        # Try to get from cache first
        cache_key = f"city_boundaries_{city_name.lower().replace(' ', '_')}_{country.lower().replace(' ', '_')}"

        # Check if we have a cache manager in the app
        if hasattr(self, 'app') and self.app and hasattr(self.app, 'cache_manager'):
            # Check if the city_boundaries cache exists
            if not self.app.cache_manager.cache_exists('city_boundaries'):
                # Create the cache with a 30-day expiry
                self.app.cache_manager.create_cache('city_boundaries', default_expiry=86400*30)

            # Try to get from cache
            cached_bounds = self.app.cache_manager.get('city_boundaries', cache_key)
            if cached_bounds:
                print(f"Using cached boundaries for {city_name}")
                return cached_bounds

        # Geocode the city
        if country and country.lower() not in city_name.lower():
            full_city_name = f"{city_name}, {country}"
        else:
            full_city_name = city_name

        geocode_result = self.geocode(full_city_name)

        if not geocode_result:
            print(f"Could not geocode city: {city_name}")
            return None

        # Check if this is actually a city/locality
        is_city = False
        for component in geocode_result.get('address_components', []):
            if 'locality' in component.get('types', []) or 'administrative_area_level_2' in component.get('types', []):
                is_city = True
                break

        if not is_city:
            print(f"{city_name} does not appear to be a city/locality")

        # Try to get bounds first
        if 'bounds' in geocode_result.get('geometry', {}):
            bounds = geocode_result['geometry']['bounds']
            result = {
                'north': bounds['northeast']['lat'],
                'south': bounds['southwest']['lat'],
                'east': bounds['northeast']['lng'],
                'west': bounds['southwest']['lng']
            }

            # Cache the result if we have a cache manager
            if hasattr(self, 'app') and self.app and hasattr(self.app, 'cache_manager'):
                self.app.cache_manager.set('city_boundaries', cache_key, result, expiry=86400*30)  # 30 days

            return result

        # Fall back to viewport if bounds not available
        elif 'viewport' in geocode_result.get('geometry', {}):
            viewport = geocode_result['geometry']['viewport']
            result = {
                'north': viewport['northeast']['lat'],
                'south': viewport['southwest']['lat'],
                'east': viewport['northeast']['lng'],
                'west': viewport['southwest']['lng']
            }

            # Cache the result if we have a cache manager
            if hasattr(self, 'app') and self.app and hasattr(self.app, 'cache_manager'):
                self.app.cache_manager.set('city_boundaries', cache_key, result, expiry=86400*30)  # 30 days

            return result

        print(f"Could not determine boundaries for {city_name}")
        return None

    def get_cities_in_region(self, region):
        """Get list of cities in a Czech region using Google Maps Places API"""
        if not self.api_key:
            print("Google Maps API key is required to get cities in a region")
            return []

        # Use the Google Maps API to get cities in the region
        try:
            # Use the Places API to find cities in the region
            url = "https://maps.googleapis.com/maps/api/place/textsearch/json"
            params = {
                "query": f"cities in {region} Czech Republic",
                "type": "locality",
                "key": self.api_key
            }

            data = self.make_api_request(url, params)

            if data and data.get('status') == 'OK':
                # Extract city names from the results
                cities = []
                for result in data.get('results', []):
                    city_name = result.get('name')
                    if city_name and city_name not in cities:
                        cities.append(city_name)

                return cities
            else:
                print(f"Failed to get cities in {region}: {data.get('status')}")
                return []
        except Exception as e:
            print(f"Error getting cities in {region}: {e}")
            return []

    def open_map_in_browser(self, lat=None, lng=None, address=None):
        """Open Google Maps in browser with specified location

        Args:
            lat (float): Latitude
            lng (float): Longitude
            address (str, optional): Address to search for. If provided, takes precedence over coordinates.

        Raises:
            ValueError: If neither valid coordinates nor address are provided
        """
        # If address is provided, geocode it to get coordinates
        if address and not (lat and lng):
            geocode_result = self.geocode_address(address)
            if geocode_result:
                lat = geocode_result.get('lat')
                lng = geocode_result.get('lng')

        # If we still don't have coordinates, raise an error
        if not (lat and lng):
            error_msg = "Valid coordinates or address are required to open a map"
            print(error_msg)
            raise ValueError(error_msg)

        # Construct the Google Maps URL
        url = f"https://www.google.com/maps/search/?api=1&query={lat},{lng}"

        # Open the URL in the default browser
        webbrowser.open(url)

        # Return the coordinates for potential caching
        return {
            'lat': lat,
            'lng': lng,
            'url': url
        }

    def make_api_request(self, url, params=None, callback=None, error_callback=None):
        """
        Make an API request with caching and rate limiting

        Args:
            url (str): The API URL
            params (dict, optional): Query parameters
            callback (function, optional): Function to call with the response data
            error_callback (function, optional): Function to call on error

        Returns:
            dict: The API response data
        """
        # Check cache first (memory cache is checked first, then disk)
        cached_data = self.cache.get(url, params)
        if cached_data:
            if callback:
                # Use threading to avoid blocking if callback is slow
                threading.Thread(target=lambda: callback(cached_data), daemon=True).start()
            return cached_data

        # Increment active requests counter
        with self.request_lock:
            self.active_requests += 1

        # Apply rate limiting
        self.rate_limiter.wait()

        try:
            # Make the API request
            if params:
                print(f"Making API request to {url} with params {params}")
                response = requests.get(url, params=params, timeout=10)  # Add timeout
            else:
                print(f"Making API request to {url}")
                response = requests.get(url, timeout=10)  # Add timeout

            # Check if the request was successful
            if response.status_code != 200:
                print(f"HTTP error: {response.status_code} - {response.reason}")
                print(f"Response content: {response.text[:200]}...")  # Print first 200 chars of response

                if error_callback:
                    error_callback(f"HTTP error: {response.status_code} - {response.reason}")

                # Return a minimal response with error information
                return {
                    'status': 'HTTP_ERROR',
                    'error_message': f"HTTP error: {response.status_code} - {response.reason}"
                }

            # Parse the response
            try:
                data = response.json()
            except ValueError as e:
                print(f"Error parsing JSON response: {e}")
                print(f"Response content: {response.text[:200]}...")  # Print first 200 chars of response

                if error_callback:
                    error_callback(f"Error parsing JSON response: {e}")

                # Return a minimal response with error information
                return {
                    'status': 'JSON_PARSE_ERROR',
                    'error_message': f"Error parsing JSON response: {e}"
                }

            # Cache the response
            self.cache.set(url, data, params)

            # Call the callback if provided
            if callback:
                # Use threading to avoid blocking if callback is slow
                threading.Thread(target=lambda: callback(data), daemon=True).start()

            return data
        except requests.exceptions.Timeout as e:
            print(f"API request timeout: {e}")
            if error_callback:
                error_callback(f"API request timeout: {e}")
            return {
                'status': 'TIMEOUT',
                'error_message': f"API request timeout: {e}"
            }
        except requests.exceptions.ConnectionError as e:
            print(f"API connection error: {e}")
            if error_callback:
                error_callback(f"API connection error: {e}")
            return {
                'status': 'CONNECTION_ERROR',
                'error_message': f"API connection error: {e}"
            }
        except Exception as e:
            print(f"API request error: {e}")
            import traceback
            traceback.print_exc()  # Print the full stack trace for debugging

            if error_callback:
                error_callback(str(e))

            return {
                'status': 'ERROR',
                'error_message': str(e)
            }
        finally:
            # Decrement active requests counter
            with self.request_lock:
                self.active_requests -= 1

    def _direct_geocode(self, address):
        """
        Direct geocoding method that bypasses the class's API request mechanism
        This is a fallback for when the regular method fails

        Args:
            address (str): The address to geocode

        Returns:
            dict: The geocoding result, or None if geocoding failed
        """
        print(f"Trying direct geocoding for '{address}'")

        try:
            # Properly encode the address for URL
            import urllib.parse
            encoded_address = urllib.parse.quote(address)
            url = f"https://maps.googleapis.com/maps/api/geocode/json?address={encoded_address}&key={self.api_key}"

            # Make a direct request
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()

                if data.get('status') == 'OK':
                    # Get the first result
                    result = data.get('results', [])[0]
                    location = result.get('geometry', {}).get('location', {})
                    formatted_address = result.get('formatted_address', '')

                    print(f"Direct geocoding successful for '{address}'")
                    print(f"Formatted address: {formatted_address}")
                    print(f"Coordinates: {location.get('lat')}, {location.get('lng')}")

                    geocode_result = {
                        'lat': location.get('lat'),
                        'lng': location.get('lng'),
                        'formatted_address': formatted_address
                    }

                    # Cache the result in our specialized geocoding cache
                    self.geocoding_cache.set(address, geocode_result)

                    return geocode_result
                else:
                    print(f"Direct geocoding failed with status: {data.get('status')}")
                    return None
            else:
                print(f"Direct geocoding HTTP error: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error in direct geocoding: {e}")
            return None

    def _direct_reverse_geocode(self, lat, lng):
        """
        Direct reverse geocoding method that bypasses the class's API request mechanism
        This is a fallback for when the regular method fails

        Args:
            lat (float): Latitude
            lng (float): Longitude

        Returns:
            dict: The reverse geocoding result, or None if reverse geocoding failed
        """
        print(f"Trying direct reverse geocoding for coordinates {lat}, {lng}")

        try:
            # Construct the URL
            url = f"https://maps.googleapis.com/maps/api/geocode/json?latlng={lat},{lng}&key={self.api_key}"

            # Make a direct request
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()

                if data.get('status') == 'OK':
                    # Get the first result
                    result = data.get('results', [])[0]
                    formatted_address = result.get('formatted_address', '')

                    # Extract address components
                    address_components = {}
                    for component in result.get('address_components', []):
                        for component_type in component.get('types', []):
                            address_components[component_type] = component.get('long_name')

                    print(f"Direct reverse geocoding successful")
                    print(f"Formatted address: {formatted_address}")

                    return {
                        'formatted_address': formatted_address,
                        'address_components': address_components
                    }
                else:
                    print(f"Direct reverse geocoding failed with status: {data.get('status')}")
                    return None
            else:
                print(f"Direct reverse geocoding HTTP error: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error in direct reverse geocoding: {e}")
            return None

    def show_map(self, lat, lng, address=None, zoom=15, parent_frame=None):
        """Show a map at the specified coordinates

        Args:
            lat (float): Latitude
            lng (float): Longitude
            address (str, optional): Address to display
            zoom (int, optional): Zoom level (1-20)
            parent_frame (Frame, optional): Parent frame to embed the map in
        """
        # If a parent frame is provided, use it
        if parent_frame:
            self.map_frame = parent_frame

        # If we have tkinterweb available, use it for embedded maps
        if TKINTERWEB_AVAILABLE and self.map_frame:
            self._show_embedded_map(lat, lng, zoom, address)
        else:
            # Fall back to opening in browser
            self._open_in_external_browser(lat, lng, address)

    def set_map_frame(self, frame):
        """Set the frame to use for embedded maps

        Args:
            frame (Frame): The tkinter frame to embed the map in
        """
        self.map_frame = frame
        # Clear any existing HTML frame
        self.html_frame = None
        print(f"Map frame set: {frame}")

    def _show_embedded_map(self, lat, lng, zoom=15, address=None):
        """Show an embedded map using tkinterweb

        Args:
            lat (float): Latitude
            lng (float): Longitude
            zoom (int, optional): Zoom level (1-20)
            address (str, optional): Address to display
        """
        # Clear the map frame
        if self.map_frame:
            for widget in self.map_frame.winfo_children():
                widget.pack_forget()

            # Create the HTML frame if it doesn't exist
            if not self.html_frame:
                self.html_frame = HtmlFrame(self.map_frame)

            # Pack the HTML frame
            self.html_frame.pack(fill="both", expand=True)

            # Create the HTML for the map
            if self.api_key:
                # Use Google Maps JavaScript API for interactive map
                html = self._create_interactive_map_html(lat, lng, zoom, address)
            else:
                # Use static map if no API key
                html = self._create_static_map_html(lat, lng)

            # Load the HTML
            self.html_frame.load_html(html)
            print(f"Showing embedded map at coordinates {lat}, {lng}")
        else:
            print("No map frame available for embedded map")
            # Fall back to opening in browser
            self._open_in_external_browser(lat, lng, address)

    def _open_in_external_browser(self, lat, lng, address=None):
        """Open Google Maps in external browser

        Args:
            lat (float): Latitude
            lng (float): Longitude
            address (str, optional): Address to display
        """
        if lat and lng:
            url = f"https://www.google.com/maps?q={lat},{lng}"
        elif address:
            url = f"https://www.google.com/maps/search/?api=1&query={address}"
        else:
            # Default to Czech Republic
            url = "https://www.google.com/maps/place/Czech+Republic/"

        print(f"Opening map in browser: {url}")
        webbrowser.open(url)

    def _create_interactive_map_html(self, lat, lng, zoom=15, address=None):
        """Create HTML for an interactive map using Google Maps JavaScript API

        Args:
            lat (float): Latitude
            lng (float): Longitude
            zoom (int, optional): Zoom level (1-20)
            address (str, optional): Address to display

        Returns:
            str: HTML for the map
        """
        # Try to use the template loader first
        try:
            return self.template_loader.format_template(
                'map_with_radius',
                lat=lat,
                lng=lng,
                radius=500,  # Default radius in meters
                api_key=self.api_key
            )
        except Exception as e:
            print(f"Error loading map template: {e}")

            # Fall back to interactive_map.html template
            title = address if address else f"Location ({lat}, {lng})"
            try:
                return self.template_loader.format_template(
                    'interactive_map',
                    lat=lat,
                    lng=lng,
                    zoom=zoom,
                    title=title,
                    api_key=self.api_key
                )
            except Exception as e:
                print(f"Error loading interactive_map template: {e}")
                # Fall back to inline HTML as a last resort
                return f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body {{ margin: 0; padding: 0; height: 100%; }}
                        #map {{ width: 100%; height: 100%; }}
                    </style>
                </head>
                <body>
                    <div id="map"></div>
                    <script>
                        function initMap() {{
                            var center = {{lat: {lat}, lng: {lng}}};
                            var map = new google.maps.Map(document.getElementById('map'), {{
                                zoom: {zoom},
                                center: center
                            }});

                            // Add a marker at the center
                            var marker = new google.maps.Marker({{
                                position: center,
                                map: map,
                                title: '{title}'
                            }});
                        }}
                    </script>
                    <script async defer
                        src="https://maps.googleapis.com/maps/api/js?key={self.api_key}&callback=initMap">
                    </script>
                </body>
                </html>
                """

    def _create_static_map_html(self, lat, lng):
        """Create HTML for a static map (when no API key is available)

        Args:
            lat (float): Latitude
            lng (float): Longitude

        Returns:
            str: HTML for the map
        """
        # Try to use the template loader first
        try:
            return self.template_loader.format_template(
                'map_placeholder',
                lat=lat,
                lng=lng
            )
        except Exception as e:
            print(f"Error loading map template: {e}")
            # Try using czech_property_map.html template
            try:
                return self.template_loader.format_template(
                    'czech_property_map',
                    lat=lat,
                    lng=lng,
                    static_map_url=f"https://www.google.com/maps/place/{lat},{lng}/staticmap?size=800x600"
                )
            except Exception as e:
                print(f"Error loading czech_property_map template: {e}")
                # Fall back to inline HTML as a last resort
                return f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body {{ margin: 0; padding: 0; font-family: Arial, sans-serif; }}
                        .map-placeholder {{
                            width: 100%;
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            background-color: #f5f5f5;
                            padding: 20px;
                            box-sizing: border-box;
                        }}
                        .button {{
                            display: inline-block;
                            background-color: #4285F4;
                            color: white;
                            padding: 10px 20px;
                            text-decoration: none;
                            border-radius: 4px;
                            margin-top: 20px;
                        }}
                    </style>
                </head>
                <body>
                    <div class="map-placeholder">
                        <h2>Google Maps API Key Required</h2>
                        <p>To display an interactive map, you need to add a Google Maps API key to your config.ini file.</p>
                        <p>Current coordinates: Lat: {lat}, Lng: {lng}</p>
                        <a class="button" href="https://www.google.com/maps?q={lat},{lng}" target="_blank">Open in Google Maps</a>
                    </div>
                </body>
                </html>
                """

    def clear_geocoding_cache(self):
        """
        Clear the geocoding cache to force fresh lookups

        Returns:
            int: Number of cache entries cleared
        """
        print("Clearing geocoding cache...")
        count = self.geocoding_cache.clear()
        print(f"Cleared {count} geocoding cache entries")
        return count

    def validate_api_key(self):
        """
        Validate the Google Maps API key by making a simple request

        Returns:
            bool: True if the API key is valid, False otherwise
        """
        if not self.api_key:
            print("No API key to validate")
            return False

        print("Validating Google Maps API key...")

        # Try a simple geocoding request
        test_url = f"https://maps.googleapis.com/maps/api/geocode/json?address=Prague&key={self.api_key}"
        try:
            response = requests.get(test_url, timeout=10)
            data = response.json()

            if data.get('status') == 'OK':
                print("Google Maps API key is valid and Geocoding API is enabled")
                return True
            elif data.get('status') == 'REQUEST_DENIED':
                error_message = data.get('error_message', 'Unknown error')
                print(f"Google Maps API key validation failed: {error_message}")

                # Check if it's a specific API not enabled error
                if "API" in error_message and "enabled" in error_message:
                    print("Please enable the required APIs in the Google Cloud Console:")
                    print("1. Geocoding API")
                    print("2. Places API")
                    print("3. Maps JavaScript API")
                else:
                    print("Please check that your Google Maps API key is valid")

                return False
            else:
                print(f"Google Maps API key validation returned unexpected status: {data.get('status')}")
                print(f"Error message: {data.get('error_message', 'None')}")
                return False

        except Exception as e:
            print(f"Error validating Google Maps API key: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_place_predictions(self, input_text, types=None, components=None):
        """
        Get place predictions from Google Maps Places API

        Args:
            input_text (str): The text to get predictions for
            types (str, optional): The types of predictions to return
            components (dict, optional): The components to restrict predictions to

        Returns:
            list: A list of prediction objects
        """
        if not self.api_key:
            print("Google Maps API key is required for place predictions")
            # Return an empty list instead of using fallback data
            return []

        # Add Czech Republic context if not already included
        if isinstance(input_text, str) and "czech" not in input_text.lower() and "česk" not in input_text.lower():
            # Check if the input is a Czech region or city name
            region_mapping = self.get_region_name_mapping()
            input_lower = input_text.lower()

            # If it's a Czech region name, use the English name
            if input_lower in region_mapping:
                input_text = region_mapping[input_lower]

            # Add Czech Republic context for better results
            if len(input_text) > 2:  # Only add context if the input is not too short
                input_text = f"{input_text}, Czech Republic"

        # Properly encode the input text
        import urllib.parse
        encoded_input = urllib.parse.quote(input_text)

        # Construct the URL
        url = f"https://maps.googleapis.com/maps/api/place/autocomplete/json?input={encoded_input}&key={self.api_key}&components=country:cz"

        # Add optional parameters
        if types:
            url += f"&types={types}"

        if components:
            components_str = "|".join([f"{k}:{v}" for k, v in components.items()])
            url += f"&components={components_str}"

        # Make the API request with caching and rate limiting
        try:
            data = self.make_api_request(url)

            if data and data.get('status') == 'OK':
                return data.get('predictions', [])
            elif data and data.get('status') == 'REQUEST_DENIED':
                print(f"Place predictions request denied: {data.get('error_message', '')}")
                # If this is the first REQUEST_DENIED, validate the API key
                if not hasattr(self, '_places_api_checked'):
                    self._places_api_checked = True
                    print("Checking if Places API is enabled...")
                    # This might be a Places API not enabled issue
                    print("Please make sure the Places API is enabled in your Google Cloud Console")
            elif data and data.get('status') == 'INVALID_REQUEST':
                print(f"Invalid place predictions request: {data.get('error_message', '')}")
            else:
                print(f"Place predictions error: {data.get('status', 'Unknown error')}")

            return []
        except Exception as e:
            print(f"Error getting place predictions: {e}")
            import traceback
            traceback.print_exc()
            return []

    def get_autocomplete_suggestions(self, text):
        """
        Get autocomplete suggestions for the given text.

        This is an alias for get_place_predictions to maintain compatibility with
        code that expects this method name.

        Args:
            text: Text to get suggestions for

        Returns:
            list: List of suggestion strings
        """
        print(f"DEBUG: get_autocomplete_suggestions called with text='{text}'")

        # Call get_place_predictions to get the raw suggestions
        raw_suggestions = self.get_place_predictions(text)

        # Convert the raw suggestions to a list of strings
        suggestions = []
        if raw_suggestions:
            for suggestion in raw_suggestions:
                if isinstance(suggestion, dict) and 'description' in suggestion:
                    suggestions.append(suggestion['description'])
                else:
                    suggestions.append(str(suggestion))

            print(f"DEBUG: Converted {len(raw_suggestions)} raw suggestions to {len(suggestions)} string suggestions")
            if suggestions:
                print(f"DEBUG: First few suggestions: {suggestions[:3]}")
        else:
            print(f"DEBUG: No suggestions found for '{text}'")

        return suggestions

    def get_place_autocomplete(self, text):
        """
        Get place autocomplete suggestions for the given text.

        This is an alias for get_autocomplete_suggestions to maintain compatibility with
        code that expects this method name.

        Args:
            text: Text to get suggestions for

        Returns:
            list: List of suggestion strings
        """
        print(f"DEBUG: get_place_autocomplete called with text='{text}'")

        # Call get_autocomplete_suggestions to get the suggestions
        return self.get_autocomplete_suggestions(text)
