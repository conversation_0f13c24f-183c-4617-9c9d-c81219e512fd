"""
Radius Search Table Demo

This script demonstrates searching for properties within different radiuses around an address
and displaying the results in a table format, including coordinates, RUIAN data, and CUZK URLs.

Usage:
    python examples/radius_search_table_demo.py [address]

Example:
    python examples/radius_search_table_demo.py "Prague, Czech Republic"
"""

import os
import sys
import logging
import argparse
import csv
from datetime import datetime
from tabulate import tabulate  # You may need to install this: pip install tabulate
from unittest.mock import MagicMock

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RadiusSearchTableDemo")

# Import required modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from api.cuzk.cuzk_integration import CUZKIntegration


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Radius Search Table Demo')
    parser.add_argument('address', nargs='?', default="Prague, Czech Republic",
                        help='Address to search around (default: Prague, Czech Republic)')
    parser.add_argument('--radiuses', nargs='+', type=float, default=[0.1, 0.5, 1.0],
                        help='Search radiuses in kilometers (default: 0.1 0.5 1.0)')
    parser.add_argument('--max-points', type=int, default=5,
                        help='Maximum number of points to check per radius (default: 5)')
    parser.add_argument('--export', action='store_true',
                        help='Export results to CSV file')
    return parser.parse_args()


def search_properties_within_radius(center_lat, center_lng, radius_meters, max_points, 
                                   cuzk_integration, osm_integration):
    """
    Search for properties within a radius around a center point.
    
    Args:
        center_lat (float): Center latitude in WGS84
        center_lng (float): Center longitude in WGS84
        radius_meters (int): Radius in meters
        max_points (int): Maximum number of points to check
        cuzk_integration: CUZK integration instance
        osm_integration: OSM integration instance
        
    Returns:
        list: List of property data dictionaries
    """
    import math
    
    # Convert radius from meters to degrees (approximate)
    # 1 degree of latitude is approximately 111,000 meters
    radius_lat = radius_meters / 111000
    # 1 degree of longitude varies with latitude
    radius_lng = radius_meters / (111000 * math.cos(math.radians(center_lat)))
    
    # Generate points in a grid around the center
    points = []
    
    # Add the center point first
    points.append({'lat': center_lat, 'lng': center_lng})
    
    # Generate concentric circles of points
    num_circles = min(3, max_points // 2)  # Maximum 3 circles
    
    for circle in range(1, num_circles + 1):
        # Calculate the radius of this circle
        circle_radius_lat = (radius_lat * circle) / num_circles
        circle_radius_lng = (radius_lng * circle) / num_circles
        
        # Calculate the number of points on this circle
        # More points on outer circles
        num_points_on_circle = min(max_points // num_circles, 4 * circle)
        
        for i in range(num_points_on_circle):
            angle = 2 * math.pi * i / num_points_on_circle
            
            point_lat = center_lat + circle_radius_lat * math.sin(angle)
            point_lng = center_lng + circle_radius_lng * math.cos(angle)
            
            points.append({'lat': point_lat, 'lng': point_lng})
            
            # Check if we've reached the maximum number of points
            if len(points) >= max_points:
                break
        
        if len(points) >= max_points:
            break
    
    # Search for properties at each point
    properties = []
    
    for point in points:
        # Get coordinates
        lat = point['lat']
        lng = point['lng']
        
        # Convert WGS84 to S-JTSK
        x, y = cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)
        
        # Try to get property data from CUZK
        property_data = cuzk_integration.get_property_by_coordinates(x, y)
        
        if property_data:
            # If we found a single property, convert it to a list
            if isinstance(property_data, dict):
                property_data = [property_data]
            
            for prop in property_data:
                # Add coordinates to the property data
                prop['lat'] = lat
                prop['lng'] = lng
                prop['x_sjtsk'] = x
                prop['y_sjtsk'] = y
                
                # Generate the MapaIdentifikace URL
                prop['url'] = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
                
                # Add to the results
                properties.append(prop)
        else:
            # If no property found, try OSM
            osm_buildings = osm_integration.find_buildings_by_coordinates(lat, lng, 50)
            
            if osm_buildings:
                for building in osm_buildings:
                    ruian_ref = building.get('ruian_ref')
                    
                    if ruian_ref:
                        # Create property data dictionary
                        prop = {
                            'ruian_id': ruian_ref,
                            'source': 'openstreetmap',
                            'lat': lat,
                            'lng': lng,
                            'x_sjtsk': x,
                            'y_sjtsk': y,
                            'property_type': building['tags'].get('building', 'Building'),
                            'parcel_number': building['tags'].get('addr:conscriptionnumber', ''),
                            'cadastral_territory': building['tags'].get('addr:city', 'Location'),
                            'owner_name': 'Property Owner (view details on CUZK)',
                            'owner_address': building['tags'].get('addr:street', '') + ' ' + 
                                            building['tags'].get('addr:housenumber', '') or 'Property Address',
                            'url': f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
                        }
                        
                        # Add to the results
                        properties.append(prop)
    
    return properties


def export_results_to_csv(all_results, location_name):
    """Export all results to a CSV file."""
    # Create a timestamp for the filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create the filename
    filename = f"property_search_{location_name.replace(' ', '_')}_{timestamp}.csv"
    
    logger.info(f"Exporting all results to {filename}")
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        # Create a CSV writer
        writer = csv.writer(csvfile)
        
        # Write the header row
        writer.writerow([
            "Radius (km)", "Property Type", "RUIAN ID", "Address", 
            "Latitude", "Longitude", "X (S-JTSK)", "Y (S-JTSK)", "CUZK URL"
        ])
        
        # Write the data rows for each radius
        for radius, properties in all_results.items():
            for prop in properties:
                writer.writerow([
                    radius,
                    prop.get('property_type', 'Building'),
                    prop.get('ruian_id', 'Unknown'),
                    prop.get('owner_address', 'Unknown'),
                    prop.get('lat', ''),
                    prop.get('lng', ''),
                    prop.get('x_sjtsk', ''),
                    prop.get('y_sjtsk', ''),
                    prop.get('url', '')
                ])
    
    logger.info(f"Results exported to {filename}")


def display_results_table(all_results, location_name):
    """Display results in a table format."""
    for radius, properties in all_results.items():
        print(f"\n=== Properties within {radius} km radius of {location_name} ===")
        
        if not properties:
            print("No properties found.")
            continue
        
        # Prepare table data
        table_data = []
        for prop in properties:
            table_data.append([
                prop.get('property_type', 'Building'),
                prop.get('ruian_id', 'Unknown'),
                prop.get('owner_address', 'Unknown'),
                f"{prop.get('lat', ''):.6f}",
                f"{prop.get('lng', ''):.6f}",
                prop.get('x_sjtsk', ''),
                prop.get('y_sjtsk', ''),
                prop.get('url', '')
            ])
        
        # Display table
        headers = ["Property Type", "RUIAN ID", "Address", "Latitude", "Longitude", "X (S-JTSK)", "Y (S-JTSK)", "CUZK URL"]
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
        print(f"Total: {len(properties)} properties\n")


def main():
    """Run the radius search table demo."""
    args = parse_arguments()
    
    logger.info(f"Radius Search Table Demo")
    logger.info(f"Address: {args.address}")
    logger.info(f"Radiuses: {args.radiuses} km")
    
    # Initialize components
    google_maps = GoogleMapsIntegration()
    cuzk_integration = CUZKIntegration()
    
    # Create a mock OSM integration
    osm_integration = MagicMock()
    osm_integration.find_buildings_by_coordinates = MagicMock(return_value=[
        {
            'type': 'way',
            'id': '123456',
            'lat': 50.0755,
            'lon': 14.4378,
            'tags': {
                'building': 'residential',
                'ref:ruian': '87654321',
                'addr:street': 'Test Street',
                'addr:housenumber': '123',
                'addr:city': 'Prague'
            },
            'ruian_ref': '87654321'
        }
    ])
    
    # Mock the CUZK integration's get_property_by_coordinates method
    cuzk_integration.get_property_by_coordinates = MagicMock(return_value={
        'property_type': 'Building',
        'ruian_id': '12345678',
        'owner_address': 'Test Address 123, Prague',
        'owner_name': 'Test Owner',
        'cadastral_territory': 'Prague',
        'parcel_number': '123/456'
    })
    
    # Get coordinates for the address
    geocode_result = google_maps.geocode(args.address)
    
    if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
        logger.error(f"Could not geocode address: {args.address}")
        return
    
    center_lat = geocode_result['lat']
    center_lng = geocode_result['lng']
    location_name = geocode_result.get('formatted_address', args.address)
    
    logger.info(f"Geocoded {args.address} to {center_lat}, {center_lng} ({location_name})")
    
    # Dictionary to store results for each radius
    all_results = {}
    
    # Search with each radius
    for radius in args.radiuses:
        logger.info(f"Searching with radius: {radius} km")
        
        # Search for properties within this radius
        radius_meters = int(radius * 1000)
        properties = search_properties_within_radius(
            center_lat, center_lng, radius_meters, args.max_points,
            cuzk_integration, osm_integration
        )
        
        # Store the results
        all_results[radius] = properties
        
        logger.info(f"Found {len(properties)} properties within {radius} km radius")
    
    # Display results in a table format
    display_results_table(all_results, location_name)
    
    # Export results to CSV if requested
    if args.export:
        export_results_to_csv(all_results, location_name)


if __name__ == '__main__':
    main()
