"""
Coordinate verification tool for the Czech Property Registry application.

This script provides a simple way to test coordinate conversion and verification.
It can be used to diagnose issues with coordinate conversion between WGS84 and S-JTSK.
"""

import os
import sys
import logging
import argparse
from typing import Tuple, Dict, Any, Optional

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CoordinateVerifier")

# Import coordinate helpers
from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk, convert_sjtsk_to_wgs84


def verify_coordinates(lat: float, lng: float) -> Dict[str, Any]:
    """
    Verify coordinates by converting to S-JTSK and back to WGS84.
    
    Args:
        lat (float): Latitude in WGS84
        lng (float): Longitude in WGS84
        
    Returns:
        dict: Dictionary with verification results
    """
    logger.info(f"Verifying coordinates: WGS84({lat}, {lng})")
    
    # Step 1: Convert WGS84 to S-JTSK
    sjtsk_x, sjtsk_y = convert_wgs84_to_sjtsk(lat, lng)
    logger.info(f"Converted to S-JTSK({sjtsk_x}, {sjtsk_y})")
    
    # Step 2: Convert back to WGS84
    reconverted_lat, reconverted_lng = convert_sjtsk_to_wgs84(sjtsk_x, sjtsk_y)
    
    # Calculate the difference
    lat_diff = abs(lat - reconverted_lat)
    lng_diff = abs(lng - reconverted_lng)
    logger.info(f"Reconverted to WGS84({reconverted_lat}, {reconverted_lng})")
    logger.info(f"Coordinate difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")
    
    # Check if the difference is significant (more than 0.001 degrees, roughly 100 meters)
    has_significant_error = lat_diff > 0.001 or lng_diff > 0.001
    
    if has_significant_error:
        logger.warning(f"Significant coordinate conversion error detected!")
        logger.warning(f"Original WGS84: {lat}, {lng}")
        logger.warning(f"Reconverted WGS84: {reconverted_lat}, {reconverted_lng}")
        logger.warning(f"Difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")
    
    # Generate CUZK URL
    cuzk_url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={int(sjtsk_x)}&y={int(sjtsk_y)}"
    logger.info(f"CUZK URL: {cuzk_url}")
    
    # Generate Google Maps URL
    google_maps_url = f"https://www.google.com/maps/search/?api=1&query={lat},{lng}"
    logger.info(f"Google Maps URL: {google_maps_url}")
    
    # Generate Google Maps URL for reconverted coordinates
    reconverted_google_maps_url = f"https://www.google.com/maps/search/?api=1&query={reconverted_lat},{reconverted_lng}"
    logger.info(f"Reconverted Google Maps URL: {reconverted_google_maps_url}")
    
    # Return results
    return {
        'original_wgs84': {'lat': lat, 'lng': lng},
        'sjtsk': {'x': sjtsk_x, 'y': sjtsk_y},
        'reconverted_wgs84': {'lat': reconverted_lat, 'lng': reconverted_lng},
        'difference': {'lat_diff': lat_diff, 'lng_diff': lng_diff},
        'has_significant_error': has_significant_error,
        'cuzk_url': cuzk_url,
        'google_maps_url': google_maps_url,
        'reconverted_google_maps_url': reconverted_google_maps_url
    }


def reverse_verify_coordinates(x: float, y: float) -> Dict[str, Any]:
    """
    Verify S-JTSK coordinates by converting to WGS84 and back to S-JTSK.
    
    Args:
        x (float): X coordinate in S-JTSK
        y (float): Y coordinate in S-JTSK
        
    Returns:
        dict: Dictionary with verification results
    """
    logger.info(f"Verifying S-JTSK coordinates: ({x}, {y})")
    
    # Step 1: Convert S-JTSK to WGS84
    wgs84_lat, wgs84_lng = convert_sjtsk_to_wgs84(x, y)
    logger.info(f"Converted to WGS84({wgs84_lat}, {wgs84_lng})")
    
    # Step 2: Convert back to S-JTSK
    reconverted_x, reconverted_y = convert_wgs84_to_sjtsk(wgs84_lat, wgs84_lng)
    
    # Calculate the difference
    x_diff = abs(x - reconverted_x)
    y_diff = abs(y - reconverted_y)
    logger.info(f"Reconverted to S-JTSK({reconverted_x}, {reconverted_y})")
    logger.info(f"Coordinate difference: x_diff={x_diff:.2f}, y_diff={y_diff:.2f}")
    
    # Check if the difference is significant (more than 100 meters)
    has_significant_error = x_diff > 100 or y_diff > 100
    
    if has_significant_error:
        logger.warning(f"Significant coordinate conversion error detected!")
        logger.warning(f"Original S-JTSK: {x}, {y}")
        logger.warning(f"Reconverted S-JTSK: {reconverted_x}, {reconverted_y}")
        logger.warning(f"Difference: x_diff={x_diff:.2f}, y_diff={y_diff:.2f}")
    
    # Generate CUZK URL
    cuzk_url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={int(x)}&y={int(y)}"
    logger.info(f"CUZK URL: {cuzk_url}")
    
    # Generate Google Maps URL
    google_maps_url = f"https://www.google.com/maps/search/?api=1&query={wgs84_lat},{wgs84_lng}"
    logger.info(f"Google Maps URL: {google_maps_url}")
    
    # Return results
    return {
        'original_sjtsk': {'x': x, 'y': y},
        'wgs84': {'lat': wgs84_lat, 'lng': wgs84_lng},
        'reconverted_sjtsk': {'x': reconverted_x, 'y': reconverted_y},
        'difference': {'x_diff': x_diff, 'y_diff': y_diff},
        'has_significant_error': has_significant_error,
        'cuzk_url': cuzk_url,
        'google_maps_url': google_maps_url
    }


def main():
    """Main function to run the coordinate verification tool."""
    parser = argparse.ArgumentParser(description='Verify coordinate conversion between WGS84 and S-JTSK.')
    
    # Create a mutually exclusive group for coordinate types
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--wgs84', nargs=2, type=float, metavar=('LAT', 'LNG'),
                      help='WGS84 coordinates (latitude, longitude)')
    group.add_argument('--sjtsk', nargs=2, type=float, metavar=('X', 'Y'),
                      help='S-JTSK coordinates (x, y)')
    
    args = parser.parse_args()
    
    if args.wgs84:
        lat, lng = args.wgs84
        results = verify_coordinates(lat, lng)
        
        # Print a summary
        print("\n=== Coordinate Verification Results ===")
        print(f"Original WGS84: {lat}, {lng}")
        print(f"Converted to S-JTSK: {results['sjtsk']['x']}, {results['sjtsk']['y']}")
        print(f"Reconverted to WGS84: {results['reconverted_wgs84']['lat']}, {results['reconverted_wgs84']['lng']}")
        print(f"Difference: lat_diff={results['difference']['lat_diff']:.6f}, lng_diff={results['difference']['lng_diff']:.6f}")
        print(f"Has significant error: {results['has_significant_error']}")
        print(f"CUZK URL: {results['cuzk_url']}")
        print(f"Google Maps URL: {results['google_maps_url']}")
        print(f"Reconverted Google Maps URL: {results['reconverted_google_maps_url']}")
        
    elif args.sjtsk:
        x, y = args.sjtsk
        results = reverse_verify_coordinates(x, y)
        
        # Print a summary
        print("\n=== Coordinate Verification Results ===")
        print(f"Original S-JTSK: {x}, {y}")
        print(f"Converted to WGS84: {results['wgs84']['lat']}, {results['wgs84']['lng']}")
        print(f"Reconverted to S-JTSK: {results['reconverted_sjtsk']['x']}, {results['reconverted_sjtsk']['y']}")
        print(f"Difference: x_diff={results['difference']['x_diff']:.2f}, y_diff={results['difference']['y_diff']:.2f}")
        print(f"Has significant error: {results['has_significant_error']}")
        print(f"CUZK URL: {results['cuzk_url']}")
        print(f"Google Maps URL: {results['google_maps_url']}")


if __name__ == "__main__":
    main()
