"""
Settings dialog for the Czech Property Registry application.

This module provides a dialog for configuring application settings.
"""

import tkinter as tk
from tkinter import ttk
import os

from config.config import config


class SettingsDialog:
    """
    A dialog for configuring application settings.
    
    This dialog allows users to configure various application settings,
    including batch operation settings.
    """
    
    def __init__(self, parent):
        """
        Initialize the settings dialog.
        
        Args:
            parent: Parent window
        """
        self.parent = parent
        self.dialog = None
        self.settings_changed = False
        
        # Create variables for settings
        self.max_batch_size_var = tk.StringVar()
        self.cache_expiry_var = tk.StringVar()
        self.font_scale_var = tk.StringVar()
        
        # Load current settings
        self._load_settings()
        
        # Create the dialog
        self._create_dialog()
        
    def _load_settings(self):
        """Load current settings from the configuration."""
        # Load batch size
        max_batch_size = config.get_int("DEFAULT", "max_batch_size", 5)
        self.max_batch_size_var.set(str(max_batch_size))
        
        # Load cache expiry
        cache_expiry = config.get_int("CACHE", "expiry_hours", 24)
        self.cache_expiry_var.set(str(cache_expiry))
        
        # Load font scale
        font_scale = config.get_float("DEFAULT", "font_scale", 1.0)
        self.font_scale_var.set(str(font_scale))
        
    def _create_dialog(self):
        """Create the settings dialog."""
        # Create the dialog window
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Settings")
        self.dialog.geometry("400x300")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.focus_set()
        self.dialog.resizable(False, False)
        
        # Make the dialog modal
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Create a notebook for tabs
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create tabs
        general_tab = ttk.Frame(notebook)
        batch_tab = ttk.Frame(notebook)
        cache_tab = ttk.Frame(notebook)
        
        notebook.add(general_tab, text="General")
        notebook.add(batch_tab, text="Batch Operations")
        notebook.add(cache_tab, text="Cache")
        
        # Populate the general tab
        self._create_general_tab(general_tab)
        
        # Populate the batch tab
        self._create_batch_tab(batch_tab)
        
        # Populate the cache tab
        self._create_cache_tab(cache_tab)
        
        # Create buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        ttk.Button(
            button_frame,
            text="Save",
            command=self._save_settings
        ).pack(side="right", padx=5)
        
        ttk.Button(
            button_frame,
            text="Cancel",
            command=self._on_close
        ).pack(side="right", padx=5)
        
    def _create_general_tab(self, parent):
        """
        Create the general settings tab.
        
        Args:
            parent: Parent frame
        """
        # Create a frame for the settings
        settings_frame = ttk.Frame(parent, padding=10)
        settings_frame.pack(fill="both", expand=True)
        
        # Font scale
        ttk.Label(
            settings_frame,
            text="Font Scale:"
        ).grid(row=0, column=0, sticky="w", pady=5)
        
        ttk.Entry(
            settings_frame,
            textvariable=self.font_scale_var,
            width=10
        ).grid(row=0, column=1, sticky="w", pady=5)
        
        ttk.Label(
            settings_frame,
            text="(Requires restart)"
        ).grid(row=0, column=2, sticky="w", pady=5, padx=5)
        
    def _create_batch_tab(self, parent):
        """
        Create the batch operations settings tab.
        
        Args:
            parent: Parent frame
        """
        # Create a frame for the settings
        settings_frame = ttk.Frame(parent, padding=10)
        settings_frame.pack(fill="both", expand=True)
        
        # Maximum batch size
        ttk.Label(
            settings_frame,
            text="Maximum Batch Size:"
        ).grid(row=0, column=0, sticky="w", pady=5)
        
        ttk.Entry(
            settings_frame,
            textvariable=self.max_batch_size_var,
            width=10
        ).grid(row=0, column=1, sticky="w", pady=5)
        
        ttk.Label(
            settings_frame,
            text="(Number of items before confirmation)"
        ).grid(row=0, column=2, sticky="w", pady=5, padx=5)
        
    def _create_cache_tab(self, parent):
        """
        Create the cache settings tab.
        
        Args:
            parent: Parent frame
        """
        # Create a frame for the settings
        settings_frame = ttk.Frame(parent, padding=10)
        settings_frame.pack(fill="both", expand=True)
        
        # Cache expiry
        ttk.Label(
            settings_frame,
            text="Cache Expiry (hours):"
        ).grid(row=0, column=0, sticky="w", pady=5)
        
        ttk.Entry(
            settings_frame,
            textvariable=self.cache_expiry_var,
            width=10
        ).grid(row=0, column=1, sticky="w", pady=5)
        
        # Clear cache button
        ttk.Button(
            settings_frame,
            text="Clear Cache",
            command=self._clear_cache
        ).grid(row=1, column=0, columnspan=2, sticky="w", pady=10)
        
    def _save_settings(self):
        """Save the settings to the configuration."""
        try:
            # Save batch size
            max_batch_size = int(self.max_batch_size_var.get())
            if max_batch_size < 1:
                max_batch_size = 1
            config.set("DEFAULT", "max_batch_size", str(max_batch_size))
            
            # Save cache expiry
            cache_expiry = int(self.cache_expiry_var.get())
            if cache_expiry < 1:
                cache_expiry = 1
            config.set("CACHE", "expiry_hours", str(cache_expiry))
            
            # Save font scale
            font_scale = float(self.font_scale_var.get())
            if font_scale < 0.5:
                font_scale = 0.5
            elif font_scale > 2.0:
                font_scale = 2.0
            config.set("DEFAULT", "font_scale", str(font_scale))
            
            # Save the configuration
            config.save_config()
            
            # Set the flag
            self.settings_changed = True
            
            # Close the dialog
            self._on_close()
            
        except Exception as e:
            # Show an error message
            from ui.message_boxes import MessageBoxes
            MessageBoxes.show_error("Error", f"Error saving settings: {str(e)}")
            
    def _clear_cache(self):
        """Clear the application cache."""
        try:
            # Get the cache directory
            cache_dir = config.get("PATHS", "cache_dir", "data/cache")
            
            # Check if the directory exists
            if os.path.exists(cache_dir):
                # Ask for confirmation
                from ui.message_boxes import MessageBoxes
                confirm = MessageBoxes.ask_yes_no(
                    "Clear Cache",
                    f"Are you sure you want to clear the cache? This will delete all cached data."
                )
                
                if confirm == 'yes':
                    # Delete all files in the cache directory
                    for filename in os.listdir(cache_dir):
                        file_path = os.path.join(cache_dir, filename)
                        if os.path.isfile(file_path):
                            os.unlink(file_path)
                    
                    # Show a success message
                    MessageBoxes.show_info("Cache Cleared", "The cache has been cleared successfully.")
            else:
                # Show an error message
                MessageBoxes.show_info("No Cache", "No cache directory found.")
                
        except Exception as e:
            # Show an error message
            from ui.message_boxes import MessageBoxes
            MessageBoxes.show_error("Error", f"Error clearing cache: {str(e)}")
            
    def _on_close(self):
        """Handle the close button."""
        # Close the dialog
        if self.dialog is not None:
            self.dialog.destroy()
            self.dialog = None
            
    def show(self):
        """Show the dialog and return whether settings were changed."""
        # Wait for the dialog to close
        self.parent.wait_window(self.dialog)
        
        # Return whether settings were changed
        return self.settings_changed
