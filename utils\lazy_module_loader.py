"""
Lazy module loading utilities for the Czech Property Registry application.
Provides mechanisms for loading modules only when they're needed.
"""

import sys
import importlib
import logging
import time
import threading
from types import ModuleType
from typing import Dict, Any, Optional, Set, List, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("LazyModuleLoader")


class LazyModule(ModuleType):
    """
    A proxy for lazily loading Python modules.
    The module is only imported when its attributes are accessed.
    """
    
    def __init__(self, name: str, loader: 'LazyModuleLoader'):
        """
        Initialize the lazy module.
        
        Args:
            name (str): Name of the module to load
            loader (LazyModuleLoader): The loader that created this module
        """
        # Initialize with a dummy module
        super().__init__(name)
        self._loader = loader
        self._real_module: Optional[ModuleType] = None
        self._loading = False
        self._loading_lock = threading.Lock()
        self._load_time: Optional[float] = None
        
        # Store the original module if it's already loaded
        if name in sys.modules:
            self._real_module = sys.modules[name]
    
    def __getattr__(self, name: str) -> Any:
        """
        Get an attribute from the module, loading it if necessary.
        
        Args:
            name (str): Name of the attribute
            
        Returns:
            The requested attribute
            
        Raises:
            AttributeError: If the attribute doesn't exist
        """
        # Load the real module if it's not loaded yet
        if self._real_module is None:
            with self._loading_lock:
                if not self._loading:
                    self._loading = True
                    try:
                        start_time = time.time()
                        logger.info(f"Lazily loading module: {self.__name__}")
                        self._real_module = importlib.import_module(self.__name__)
                        self._load_time = time.time() - start_time
                        logger.info(f"Loaded module {self.__name__} in {self._load_time:.4f} seconds")
                        
                        # Notify the loader that the module has been loaded
                        self._loader._on_module_loaded(self.__name__, self._load_time)
                    except Exception as e:
                        logger.error(f"Error loading module {self.__name__}: {e}")
                        raise
                    finally:
                        self._loading = False
        
        # Get the attribute from the real module
        return getattr(self._real_module, name)
    
    def __dir__(self) -> List[str]:
        """
        Get the list of attributes in the module.
        
        Returns:
            List of attribute names
        """
        if self._real_module is None:
            return super().__dir__()
        return dir(self._real_module)


class LazyModuleLoader:
    """
    A loader for lazily loading modules.
    Modules are only imported when their attributes are accessed.
    """
    
    def __init__(self):
        """Initialize the lazy module loader."""
        self._lazy_modules: Dict[str, LazyModule] = {}
        self._load_times: Dict[str, float] = {}
        self._load_order: List[str] = []
        self._callbacks: Dict[str, List[Callable[[str, float], None]]] = {}
        self._lock = threading.Lock()
    
    def lazy_import(self, module_name: str) -> ModuleType:
        """
        Import a module lazily.
        
        Args:
            module_name (str): Name of the module to import
            
        Returns:
            A proxy module that loads the real module when accessed
        """
        with self._lock:
            # Check if we already have a lazy module for this name
            if module_name in self._lazy_modules:
                return self._lazy_modules[module_name]
            
            # Create a new lazy module
            lazy_module = LazyModule(module_name, self)
            
            # Store it in our dictionary
            self._lazy_modules[module_name] = lazy_module
            
            # Replace the module in sys.modules if it's not already there
            if module_name not in sys.modules:
                sys.modules[module_name] = lazy_module
            
            return lazy_module
    
    def lazy_import_all(self, module_names: List[str]) -> Dict[str, ModuleType]:
        """
        Import multiple modules lazily.
        
        Args:
            module_names (List[str]): Names of the modules to import
            
        Returns:
            Dictionary mapping module names to proxy modules
        """
        return {name: self.lazy_import(name) for name in module_names}
    
    def _on_module_loaded(self, module_name: str, load_time: float) -> None:
        """
        Called when a module is loaded.
        
        Args:
            module_name (str): Name of the loaded module
            load_time (float): Time taken to load the module in seconds
        """
        with self._lock:
            self._load_times[module_name] = load_time
            self._load_order.append(module_name)
            
            # Call any registered callbacks
            for callback in self._callbacks.get('module_loaded', []):
                try:
                    callback(module_name, load_time)
                except Exception as e:
                    logger.error(f"Error in module loaded callback: {e}")
    
    def register_callback(self, event: str, callback: Callable[[str, float], None]) -> None:
        """
        Register a callback for module loading events.
        
        Args:
            event (str): Event name ('module_loaded')
            callback: Function to call when the event occurs
        """
        with self._lock:
            if event not in self._callbacks:
                self._callbacks[event] = []
            self._callbacks[event].append(callback)
    
    def get_load_times(self) -> Dict[str, float]:
        """
        Get the load times for all loaded modules.
        
        Returns:
            Dictionary mapping module names to load times in seconds
        """
        with self._lock:
            return self._load_times.copy()
    
    def get_load_order(self) -> List[str]:
        """
        Get the order in which modules were loaded.
        
        Returns:
            List of module names in the order they were loaded
        """
        with self._lock:
            return self._load_order.copy()
    
    def is_loaded(self, module_name: str) -> bool:
        """
        Check if a module has been loaded.
        
        Args:
            module_name (str): Name of the module
            
        Returns:
            True if the module has been loaded, False otherwise
        """
        with self._lock:
            if module_name not in self._lazy_modules:
                return False
            return self._lazy_modules[module_name]._real_module is not None
    
    def get_loaded_modules(self) -> List[str]:
        """
        Get a list of all loaded modules.
        
        Returns:
            List of loaded module names
        """
        with self._lock:
            return [name for name, module in self._lazy_modules.items() if module._real_module is not None]
    
    def get_unloaded_modules(self) -> List[str]:
        """
        Get a list of all unloaded modules.
        
        Returns:
            List of unloaded module names
        """
        with self._lock:
            return [name for name, module in self._lazy_modules.items() if module._real_module is None]


# Create a global instance for convenience
lazy_module_loader = LazyModuleLoader()


def lazy_import(module_name: str) -> ModuleType:
    """
    Import a module lazily.
    
    Args:
        module_name (str): Name of the module to import
        
    Returns:
        A proxy module that loads the real module when accessed
    """
    return lazy_module_loader.lazy_import(module_name)


def lazy_import_all(module_names: List[str]) -> Dict[str, ModuleType]:
    """
    Import multiple modules lazily.
    
    Args:
        module_names (List[str]): Names of the modules to import
        
    Returns:
        Dictionary mapping module names to proxy modules
    """
    return lazy_module_loader.lazy_import_all(module_names)
