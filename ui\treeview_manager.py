"""
Treeview management utilities for the Czech Property Registry application.

This module provides functionality for managing treeview widgets,
including scrolling and event handling.
"""

import tkinter as tk
from tkinter import ttk
import time


class TreeviewManager:
    """
    Manages treeview behavior for tkinter applications.
    
    Provides support for:
    - Treeview scrolling
    - Click event handling
    - Selection management
    """
    
    def __init__(self, app):
        """
        Initialize the TreeviewManager.
        
        Args:
            app: The main application instance for callbacks and UI access
        """
        self.app = app
        
        # Initialize processing flags
        self._processing_scroll = False
        self._last_scroll_time = 0
        self._scroll_debounce_ms = 10  # Debounce time in milliseconds
        
    def create_treeview(self, parent, columns, headings=None, column_widths=None, height=10, show="headings"):
        """
        Create a treeview with standard configuration.
        
        Args:
            parent: The parent widget
            columns (list): List of column IDs
            headings (dict, optional): Dictionary mapping column IDs to heading text
            column_widths (dict, optional): Dictionary mapping column IDs to column widths
            height (int): Height of the treeview in rows
            show (str): What to show in the treeview ("headings", "tree", "tree headings")
            
        Returns:
            ttk.Treeview: The created treeview
        """
        # Create the treeview
        treeview = ttk.Treeview(
            parent,
            columns=columns,
            show=show,
            height=height
        )
        
        # Set headings if provided
        if headings:
            for column_id, heading_text in headings.items():
                treeview.heading(column_id, text=heading_text)
                
        # Set column widths if provided
        if column_widths:
            for column_id, width in column_widths.items():
                treeview.column(column_id, width=width)
                
        # Add scrolling support
        self.add_scrolling_support(treeview)
        
        return treeview
        
    def add_scrolling_support(self, treeview):
        """
        Add scrolling support to a treeview.
        
        Args:
            treeview: The treeview to add scrolling support to
        """
        # Add mousewheel bindings
        treeview.bind("<MouseWheel>", lambda event: self.on_treeview_mousewheel(event, treeview))  # Windows
        treeview.bind("<Button-4>", lambda event: self.on_treeview_mousewheel(event, treeview))    # Linux scroll up
        treeview.bind("<Button-5>", lambda event: self.on_treeview_mousewheel(event, treeview))    # Linux scroll down
        
    def on_treeview_mousewheel(self, event, treeview=None):
        """
        Handle mousewheel events for treeviews.
        
        Args:
            event: The mousewheel event
            treeview: The treeview to scroll (if None, uses event.widget)
            
        Returns:
            str: "break" to prevent event propagation
        """
        # Use the provided treeview or get it from the event
        if treeview is None:
            treeview = event.widget
            
        # Check if we're already processing a scroll event to prevent stuttering
        if self._processing_scroll:
            return "break"
            
        # Apply debounce to prevent too many scroll events
        current_time = time.time() * 1000  # Convert to milliseconds
        if (current_time - self._last_scroll_time) < self._scroll_debounce_ms:
            return "break"
            
        self._last_scroll_time = current_time
        self._processing_scroll = True
        
        try:
            # Calculate delta (different handling for Windows and Linux)
            if hasattr(event, 'num') and event.num in (4, 5):
                # Linux scroll events
                delta = 1 if event.num == 4 else -1  # Up (4) or Down (5)
            elif hasattr(event, 'delta'):  # Windows
                # Get the raw delta value
                raw_delta = event.delta
                
                # For touchpads on Windows, the delta can be much smaller than 120
                if abs(raw_delta) < 120:
                    # This is likely a touchpad gesture - use reduced sensitivity
                    delta = raw_delta / 200
                else:
                    # This is likely a mouse wheel - use standard sensitivity
                    delta = raw_delta / 120
                    
                # Apply a minimum threshold for small movements
                if abs(delta) < 0.05:
                    return "break"
                    
                # Normalize to a reasonable range
                if delta > 0:
                    delta = min(3, max(0.1, delta))
                else:
                    delta = max(-3, min(-0.1, delta))
            else:
                # Unknown event type
                return "break"
                
            # Get the number of items in the treeview
            children_count = len(treeview.get_children())
            
            # Calculate a multiplier based on the number of items
            if children_count > 100:
                multiplier = 3  # Fast scrolling for many items
            elif children_count > 50:
                multiplier = 2  # Medium scrolling for moderate items
            else:
                multiplier = 1  # Slow scrolling for few items
                
            # Scroll the treeview
            treeview.yview_scroll(int(-delta * multiplier), "units")
            
            return "break"  # Prevent event propagation
        finally:
            # Clear the processing flag after a short delay
            if hasattr(self.app, 'root'):
                self.app.root.after(50, self._clear_processing_flag)
            else:
                self._processing_scroll = False
                
    def _clear_processing_flag(self):
        """Clear the processing flag"""
        self._processing_scroll = False
        
    def get_selected_item(self, treeview):
        """
        Get the selected item from a treeview.
        
        Args:
            treeview: The treeview to get the selected item from
            
        Returns:
            tuple: (item_id, values) of the selected item, or (None, None) if no item is selected
        """
        selected_items = treeview.selection()
        if not selected_items:
            return None, None
            
        item_id = selected_items[0]
        values = treeview.item(item_id, 'values')
        
        return item_id, values
        
    def get_selected_items(self, treeview):
        """
        Get all selected items from a treeview.
        
        Args:
            treeview: The treeview to get the selected items from
            
        Returns:
            list: List of (item_id, values) tuples for each selected item
        """
        selected_items = treeview.selection()
        if not selected_items:
            return []
            
        result = []
        for item_id in selected_items:
            values = treeview.item(item_id, 'values')
            result.append((item_id, values))
            
        return result
        
    def clear_treeview(self, treeview):
        """
        Clear all items from a treeview.
        
        Args:
            treeview: The treeview to clear
        """
        for item in treeview.get_children():
            treeview.delete(item)
            
    def add_item(self, treeview, values, tags=None):
        """
        Add an item to a treeview.
        
        Args:
            treeview: The treeview to add the item to
            values (tuple): Values for each column
            tags (tuple, optional): Tags to apply to the item
            
        Returns:
            str: The item ID of the added item
        """
        item_id = treeview.insert("", "end", values=values, tags=tags)
        return item_id
        
    def add_items(self, treeview, items_data):
        """
        Add multiple items to a treeview.
        
        Args:
            treeview: The treeview to add the items to
            items_data (list): List of dictionaries with 'values' and optional 'tags' keys
            
        Returns:
            list: List of item IDs of the added items
        """
        item_ids = []
        for item_data in items_data:
            values = item_data['values']
            tags = item_data.get('tags')
            item_id = self.add_item(treeview, values, tags)
            item_ids.append(item_id)
            
        return item_ids
        
    def setup_column_sorting(self, treeview):
        """
        Set up column sorting for a treeview.
        
        Args:
            treeview: The treeview to set up sorting for
        """
        # Store the current sort column and order
        treeview._sort_column = None
        treeview._sort_order = 'asc'
        
        # Add click handlers to column headings
        for column in treeview['columns']:
            treeview.heading(
                column,
                command=lambda col=column: self._sort_treeview(treeview, col)
            )
            
    def _sort_treeview(self, treeview, column):
        """
        Sort a treeview by a specific column.
        
        Args:
            treeview: The treeview to sort
            column: The column to sort by
        """
        # Get the current sort column and order
        current_sort_column = getattr(treeview, '_sort_column', None)
        current_sort_order = getattr(treeview, '_sort_order', 'asc')
        
        # Determine the new sort order
        if column == current_sort_column:
            # Toggle the sort order if clicking the same column
            new_sort_order = 'desc' if current_sort_order == 'asc' else 'asc'
        else:
            # Default to ascending order for a new column
            new_sort_order = 'asc'
            
        # Update the sort column and order
        treeview._sort_column = column
        treeview._sort_order = new_sort_order
        
        # Update the column heading to show the sort order
        for col in treeview['columns']:
            if col == column:
                direction = " ↑" if new_sort_order == 'asc' else " ↓"
                treeview.heading(col, text=treeview.heading(col, 'text').rstrip(' ↑↓') + direction)
            else:
                treeview.heading(col, text=treeview.heading(col, 'text').rstrip(' ↑↓'))
                
        # Get all items
        item_list = [(treeview.set(item, column), item) for item in treeview.get_children('')]
        
        # Sort the items
        try:
            # Try to sort numerically
            item_list.sort(key=lambda x: float(x[0]), reverse=(new_sort_order == 'desc'))
        except (ValueError, TypeError):
            # Fall back to string sorting
            item_list.sort(key=lambda x: x[0].lower(), reverse=(new_sort_order == 'desc'))
            
        # Rearrange items in the sorted order
        for index, (_, item) in enumerate(item_list):
            treeview.move(item, '', index)
            
    def setup_row_highlighting(self, treeview):
        """
        Set up row highlighting for a treeview.
        
        Args:
            treeview: The treeview to set up highlighting for
        """
        # Configure tags for highlighting
        treeview.tag_configure('highlight', background='#e0e0ff')
        
        # Add mouse motion binding
        treeview.bind('<Motion>', lambda event: self._highlight_row(event, treeview))
        
    def _highlight_row(self, event, treeview):
        """
        Highlight the row under the mouse cursor.
        
        Args:
            event: The mouse motion event
            treeview: The treeview to highlight the row in
        """
        # Get the item under the cursor
        item_id = treeview.identify_row(event.y)
        
        # Remove highlighting from all items
        for item in treeview.get_children():
            tags = list(treeview.item(item, 'tags') or [])
            if 'highlight' in tags:
                tags.remove('highlight')
                treeview.item(item, tags=tags)
                
        # Add highlighting to the item under the cursor
        if item_id:
            tags = list(treeview.item(item_id, 'tags') or [])
            if 'highlight' not in tags:
                tags.append('highlight')
                treeview.item(item_id, tags=tags)
