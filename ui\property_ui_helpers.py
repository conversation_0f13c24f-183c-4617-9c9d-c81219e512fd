"""
UI helper functions for the Czech Property Registry application.
"""

import tkinter as tk
from tkinter import ttk, filedialog
import csv
from typing import Dict, Any, List, Optional, Callable
import threading
from ui.message_boxes import MessageBoxes


class PropertyUIHelpers:
    """
    Class for UI helper functions related to property data.
    """

    @staticmethod
    def create_property_input_dialog(parent, title="Enter Property Details",
                                    initial_values=None) -> Optional[Dict[str, Any]]:
        """
        Create a dialog for entering property details.

        Args:
            parent: Parent window
            title (str): Dialog title
            initial_values (dict, optional): Initial values for the fields

        Returns:
            dict: Property data or None if canceled
        """
        # Create a dictionary to store the property data
        property_data = [None]  # Use a list to store the result

        # Create the dialog
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.geometry("500x600")
        dialog.transient(parent)
        dialog.grab_set()

        # Make the dialog modal
        dialog.focus_set()

        # Create a frame for the form
        form_frame = ttk.Frame(dialog, padding=10)
        form_frame.pack(fill="both", expand=True)

        # Create the form fields
        ttk.Label(form_frame, text="Owner Name:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        owner_name_entry = ttk.Entry(form_frame, width=40)
        owner_name_entry.grid(row=0, column=1, sticky="ew", padx=5, pady=5)

        ttk.Label(form_frame, text="Owner Address:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        owner_address_entry = ttk.Entry(form_frame, width=40)
        owner_address_entry.grid(row=1, column=1, sticky="ew", padx=5, pady=5)

        ttk.Label(form_frame, text="Property Type:").grid(row=2, column=0, sticky="w", padx=5, pady=5)
        property_type_entry = ttk.Entry(form_frame, width=40)
        property_type_entry.grid(row=2, column=1, sticky="ew", padx=5, pady=5)

        ttk.Label(form_frame, text="Parcel Number:").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        parcel_number_entry = ttk.Entry(form_frame, width=40)
        parcel_number_entry.grid(row=3, column=1, sticky="ew", padx=5, pady=5)

        ttk.Label(form_frame, text="Cadastral Territory:").grid(row=4, column=0, sticky="w", padx=5, pady=5)
        cadastral_territory_entry = ttk.Entry(form_frame, width=40)
        cadastral_territory_entry.grid(row=4, column=1, sticky="ew", padx=5, pady=5)

        ttk.Label(form_frame, text="Ownership Share:").grid(row=5, column=0, sticky="w", padx=5, pady=5)
        ownership_share_entry = ttk.Entry(form_frame, width=40)
        ownership_share_entry.grid(row=5, column=1, sticky="ew", padx=5, pady=5)

        ttk.Label(form_frame, text="LV Number:").grid(row=6, column=0, sticky="w", padx=5, pady=5)
        lv_number_entry = ttk.Entry(form_frame, width=40)
        lv_number_entry.grid(row=6, column=1, sticky="ew", padx=5, pady=5)

        ttk.Label(form_frame, text="Additional Notes:").grid(row=7, column=0, sticky="w", padx=5, pady=5)
        additional_notes_text = tk.Text(form_frame, width=40, height=5)
        additional_notes_text.grid(row=7, column=1, sticky="ew", padx=5, pady=5)

        # Set initial values if provided
        if initial_values:
            owner_name_entry.insert(0, initial_values.get('owner_name', ''))
            owner_address_entry.insert(0, initial_values.get('owner_address', ''))
            property_type_entry.insert(0, initial_values.get('property_type', ''))
            parcel_number_entry.insert(0, initial_values.get('parcel_number', ''))
            cadastral_territory_entry.insert(0, initial_values.get('cadastral_territory', ''))
            ownership_share_entry.insert(0, initial_values.get('ownership_share', ''))
            lv_number_entry.insert(0, initial_values.get('lv_number', ''))
            additional_notes_text.insert("1.0", initial_values.get('additional_notes', ''))

        # Create buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill="x", padx=10, pady=10)

        def on_ok():
            # Validate required fields
            owner_name = owner_name_entry.get().strip()
            owner_address = owner_address_entry.get().strip()

            if not owner_name or not owner_address:
                MessageBoxes.show_error(
                    "Missing Information",
                    "Please enter at least the owner name and address."
                )
                return

            # Get all the property details
            property_data[0] = {
                'owner_name': owner_name,
                'owner_address': owner_address,
                'property_type': property_type_entry.get().strip(),
                'parcel_number': parcel_number_entry.get().strip(),
                'cadastral_territory': cadastral_territory_entry.get().strip(),
                'ownership_share': ownership_share_entry.get().strip(),
                'lv_number': lv_number_entry.get().strip(),
                'additional_notes': additional_notes_text.get("1.0", tk.END).strip(),
                'source': 'direct_user_input'
            }

            # Close the dialog
            dialog.destroy()

        def on_cancel():
            # Close the dialog without saving
            dialog.destroy()

        ttk.Button(button_frame, text="OK", command=on_ok).pack(side="right", padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="right", padx=5)

        # Wait for the dialog to be closed
        parent.wait_window(dialog)

        # Return the property data
        return property_data[0]

    @staticmethod
    def export_properties_to_csv(parent, properties: List[Dict[str, Any]],
                                default_filename: str = "properties.csv") -> None:
        """
        Export properties to a CSV file.

        Args:
            parent: Parent window
            properties (list): List of property dictionaries
            default_filename (str): Default filename for the CSV file
        """
        if not properties:
            MessageBoxes.show_info("No Properties", "There are no properties to export.")
            return

        # Ask for the file path
        file_path = filedialog.asksaveasfilename(
            parent=parent,
            defaultextension=".csv",
            filetypes=[("CSV Files", "*.csv"), ("All Files", "*.*")],
            initialfile=default_filename
        )

        if not file_path:
            return  # User canceled

        try:
            # Open the file for writing
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                # Create a CSV writer
                writer = csv.writer(f)

                # Write the header row
                writer.writerow([
                    "Owner Name", "Owner Address", "Property Type", "Parcel Number",
                    "Cadastral Territory", "Ownership Share", "LV Number", "Additional Notes"
                ])

                # Write the property data
                for prop in properties:
                    writer.writerow([
                        prop.get('owner_name', ''),
                        prop.get('owner_address', ''),
                        prop.get('property_type', ''),
                        prop.get('parcel_number', ''),
                        prop.get('cadastral_territory', ''),
                        prop.get('ownership_share', ''),
                        prop.get('lv_number', ''),
                        prop.get('additional_notes', '')
                    ])

            MessageBoxes.show_info("Export Complete", f"Successfully exported {len(properties)} properties to {file_path}")

        except Exception as e:
            MessageBoxes.show_error("Export Error", f"Error exporting properties: {e}")

    @staticmethod
    def export_buildings_to_csv(parent, buildings: List[Dict[str, Any]],
                               default_filename: str = "buildings.csv") -> None:
        """
        Export buildings to a CSV file.

        Args:
            parent: Parent window
            buildings (list): List of building dictionaries
            default_filename (str): Default filename for the CSV file
        """
        if not buildings:
            MessageBoxes.show_info("No Buildings", "There are no buildings to export.")
            return

        # Ask for the file path
        file_path = filedialog.asksaveasfilename(
            parent=parent,
            defaultextension=".csv",
            filetypes=[("CSV Files", "*.csv"), ("All Files", "*.*")],
            initialfile=default_filename
        )

        if not file_path:
            return  # User canceled

        try:
            # Open the file for writing
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                # Create a CSV writer
                writer = csv.writer(f)

                # Write the header row
                writer.writerow([
                    "RUIAN ID", "Building Type", "Name", "Street", "House Number",
                    "City", "Postal Code", "Latitude", "Longitude"
                ])

                # Write the building data
                for building in buildings:
                    # Get coordinates
                    lat = building.get('lat', '')
                    lng = building.get('lon', '')

                    # Get building information
                    ruian_id = building.get('ruian_ref', '')
                    building_type = building.get('tags', {}).get('building', '')
                    name = building.get('tags', {}).get('name', '')
                    addr_street = building.get('tags', {}).get('addr:street', '')
                    addr_housenumber = building.get('tags', {}).get('addr:housenumber', '')
                    addr_city = building.get('tags', {}).get('addr:city', '')
                    addr_postcode = building.get('tags', {}).get('addr:postcode', '')

                    # Write the row
                    writer.writerow([
                        ruian_id, building_type, name, addr_street, addr_housenumber,
                        addr_city, addr_postcode, lat, lng
                    ])

            # Show success message
            MessageBoxes.show_info("Export Complete", f"Successfully exported {len(buildings)} buildings to {file_path}")

        except Exception as e:
            MessageBoxes.show_error("Export Error", f"Error exporting buildings: {e}")

    @staticmethod
    def show_status(parent, message: str, duration: int = 3000) -> None:
        """
        Show a status message in the status bar.

        Args:
            parent: Parent window with a status_var attribute
            message (str): Message to display
            duration (int): Duration in milliseconds (0 for permanent)
        """
        if hasattr(parent, 'status_var'):
            parent.status_var.set(message)

            # Clear the status after the specified duration
            if duration > 0:
                parent.after(duration, lambda: parent.status_var.set(""))

    @staticmethod
    def run_with_progress(parent, task: Callable, title: str = "Processing",
                         message: str = "Please wait...",
                         callback: Optional[Callable] = None) -> None:
        """
        Run a task with a progress dialog.

        Args:
            parent: Parent window
            task (callable): Task to run
            title (str): Dialog title
            message (str): Message to display
            callback (callable, optional): Callback to run when the task is complete
        """
        # Create a progress dialog
        progress_dialog = tk.Toplevel(parent)
        progress_dialog.title(title)
        progress_dialog.geometry("300x100")
        progress_dialog.transient(parent)
        progress_dialog.grab_set()
        progress_dialog.focus_set()
        progress_dialog.resizable(False, False)

        # Create a frame for the progress bar
        progress_frame = ttk.Frame(progress_dialog, padding=10)
        progress_frame.pack(fill="both", expand=True)

        # Create a label for the message
        ttk.Label(progress_frame, text=message).pack(pady=5)

        # Create a progress bar
        progress_bar = ttk.Progressbar(progress_frame, mode="indeterminate")
        progress_bar.pack(fill="x", pady=5)
        progress_bar.start()

        # Create a variable to store the result
        result = [None]

        # Run the task in a separate thread
        def run_task():
            try:
                result[0] = task()
            except Exception as e:
                print(f"Error running task: {e}")
                result[0] = None
            finally:
                # Close the progress dialog
                parent.after(0, progress_dialog.destroy)

                # Call the callback if provided
                if callback:
                    parent.after(0, lambda: callback(result[0]))

        # Start the thread
        threading.Thread(target=run_task, daemon=True).start()
