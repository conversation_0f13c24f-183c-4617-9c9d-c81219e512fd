"""
Clear Geocoding Cache Script for Czech Property Registry

This script clears the geocoding cache in the Czech Property Registry application
to ensure fresh geocoding data is fetched from the Google Maps API.
"""

import os
import sys
import logging
import shutil
from utils.geocoding_cache import GeocodingCache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clear_geocoding_cache():
    """Clear the geocoding cache"""
    try:
        # Create a new instance of the geocoding cache
        geocoding_cache = GeocodingCache(cache_dir='geocoding_cache')
        
        # Clear the cache
        count = geocoding_cache.clear()
        
        logger.info(f"Cleared {count} geocoding cache entries")
        return count
    except Exception as e:
        logger.error(f"Error clearing geocoding cache: {e}")
        return 0

def main():
    """Main function"""
    logger.info("Starting geocoding cache clearing process")
    
    # Clear the geocoding cache
    count = clear_geocoding_cache()
    
    logger.info(f"Geocoding cache clearing process completed. Cleared {count} entries.")
    print(f"Geocoding cache has been cleared. {count} entries were removed.")
    print("Please restart the application to ensure all caches are refreshed.")

if __name__ == "__main__":
    main()
