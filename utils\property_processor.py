"""
Property data processing utilities for the Czech Property Registry application.
"""

import os
import json
import logging
from typing import Dict, Any, List

# Configure logging
logger = logging.getLogger(__name__)


class PropertyDataProcessor:
    """
    Class for processing property data from various sources.
    """

    def __init__(self, app=None):
        """
        Initialize the property data processor

        Args:
            app: The main application instance (optional)
        """
        self.app = app

        # Load Czech cadastral data if available
        self.czech_cadastral = None

        # Try to use the data manager if available
        if app and hasattr(app, 'data_manager'):
            try:
                self.czech_cadastral = app.data_manager.load_czech_data('cadastral')
                logger.info("Loaded cadastral territories from data manager")
            except Exception as e:
                logger.warning(f"Error loading cadastral territories from data manager: {e}")
                # Continue to file-based loading

        # Try to load from file if data manager failed or is not available
        if not self.czech_cadastral and os.path.exists('czech_data/cadastral_territories.json'):
            try:
                with open('czech_data/cadastral_territories.json', 'r', encoding='utf-8') as f:
                    self.czech_cadastral = json.load(f)
                    logger.info("Loaded cadastral territories from file")
            except Exception as e:
                logger.error(f"Error loading cadastral territories: {e}")

    def property_type_matches(self, actual_type: str, filter_type: str) -> bool:
        """
        Check if a property type matches a filter type.

        Args:
            actual_type (str): The actual property type
            filter_type (str): The filter type to match against

        Returns:
            bool: True if the property type matches the filter, False otherwise
        """
        # Convert both to lowercase for case-insensitive matching
        actual_lower = actual_type.lower()
        filter_lower = filter_type.lower()

        # Direct match
        if actual_lower == filter_lower:
            return True

        # Check for partial matches based on keywords
        keywords = self._load_property_type_keywords()

        # If we have keywords for this filter type, check if any match
        if filter_lower in keywords:
            for keyword in keywords[filter_lower]:
                if keyword.lower() in actual_lower:
                    return True

        return False

    def _load_property_type_keywords(self) -> Dict[str, List[str]]:
        """
        Load property type keywords from data manager.

        Returns:
            dict: Dictionary mapping property types to keywords

        Raises:
            ValueError: If property type keywords cannot be loaded
        """
        try:
            # Use the data manager to load property types
            if hasattr(self.app, 'data_manager'):
                property_types = self.app.data_manager.load_czech_data('property_types')
                if property_types and "property_type_keywords" in property_types:
                    return property_types["property_type_keywords"]

            # Try to load directly from file as fallback
            if os.path.exists('czech_data/property_types.json'):
                try:
                    with open('czech_data/property_types.json', 'r', encoding='utf-8') as f:
                        property_types = json.load(f)
                        if "property_type_keywords" in property_types:
                            return property_types["property_type_keywords"]
                except Exception as e:
                    logger.error(f"Error loading property type keywords from file: {e}")
                    # Continue to error

            # If we get here, we couldn't load the data
            error_msg = "Property type keywords data not available"
            logger.error(error_msg)
            raise ValueError(error_msg)

        except Exception as e:
            logger.error(f"Error loading property type keywords: {e}")
            raise ValueError(f"Failed to load property type keywords: {e}")

    def _load_property_types(self) -> List[str]:
        """
        Load property types from data manager.

        Returns:
            list: List of property types

        Raises:
            ValueError: If property types cannot be loaded
        """
        try:
            # Use the data manager to load property types
            if hasattr(self.app, 'data_manager'):
                property_types = self.app.data_manager.load_czech_data('property_types')
                if property_types and "property_types" in property_types:
                    return property_types["property_types"]

            # Try to load directly from file as fallback
            if os.path.exists('czech_data/property_types.json'):
                try:
                    with open('czech_data/property_types.json', 'r', encoding='utf-8') as f:
                        property_types = json.load(f)
                        if "property_types" in property_types:
                            return property_types["property_types"]
                except Exception as e:
                    logger.error(f"Error loading property types from file: {e}")
                    # Continue to error

            # If we get here, we couldn't load the data
            error_msg = "Property types data not available"
            logger.error(error_msg)
            raise ValueError(error_msg)

        except Exception as e:
            logger.error(f"Error loading property types: {e}")
            raise ValueError(f"Failed to load property types: {e}")

    def get_real_property_data(self, ruian_id: str, api_client) -> Dict[str, Any]:
        """
        Get real property data from the API instead of generating sample data.

        Args:
            ruian_id (str): RUIAN ID of the property
            api_client: API client to use for fetching data

        Returns:
            dict: Property data from the API
        """
        if not api_client:
            error_msg = "API client is required to fetch real property data"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not ruian_id:
            error_msg = "RUIAN ID is required to fetch property data"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # This method should be implemented to use the API client
        # to fetch real property data instead of generating sample data
        logger.info(f"Fetching real property data for RUIAN ID: {ruian_id}")

        # The implementation would depend on the specific API client
        # and how it fetches property data

        # For now, we'll raise an error to indicate that sample data
        # is no longer supported
        error_msg = "Sample property data generation is no longer supported. Use real data from API."
        logger.error(error_msg)
        raise NotImplementedError(error_msg)
