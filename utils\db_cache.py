"""
Database-backed cache for the Czech Property Registry application.
Provides persistent caching with better performance than file-based caching.
"""

import sqlite3
import pickle
import time
import os
import threading
import logging
import json
import hashlib
from typing import Dict, Any, Optional, List, Tuple, Set, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DBCache")


class DBCache:
    """
    A database-backed cache using SQLite.
    Provides persistent caching with better performance than file-based caching.
    """
    
    def __init__(self, db_path: str = "cache.db", auto_vacuum: bool = True):
        """
        Initialize the database cache.
        
        Args:
            db_path (str): Path to the SQLite database file
            auto_vacuum (bool): Whether to automatically vacuum the database
        """
        self.db_path = db_path
        self.auto_vacuum = auto_vacuum
        self._conn = None
        self._lock = threading.Lock()
        self._init_db()
        
        # Statistics
        self._stats = {
            'hits': 0,
            'misses': 0,
            'writes': 0,
            'deletes': 0
        }
        self._stats_lock = threading.Lock()
        
        logger.info(f"DBCache initialized with database at {db_path}")
    
    def _init_db(self) -> None:
        """Initialize the database schema."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Create the cache table if it doesn't exist
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS cache (
                cache_name TEXT NOT NULL,
                key TEXT NOT NULL,
                value BLOB NOT NULL,
                metadata TEXT,
                created REAL NOT NULL,
                expires REAL,
                PRIMARY KEY (cache_name, key)
            )
            """)
            
            # Create an index on expiry time for faster cleanup
            cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_cache_expires ON cache (expires)
            """)
            
            # Create a table for cache metadata
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS cache_metadata (
                cache_name TEXT PRIMARY KEY,
                settings TEXT NOT NULL
            )
            """)
            
            conn.commit()
    
    def _get_connection(self) -> sqlite3.Connection:
        """
        Get a database connection.
        
        Returns:
            sqlite3.Connection: Database connection
        """
        with self._lock:
            if self._conn is None:
                self._conn = sqlite3.connect(self.db_path, check_same_thread=False)
                self._conn.row_factory = sqlite3.Row
            
            return self._conn
    
    def create_cache(self, cache_name: str, expiry: Optional[int] = None, settings: Dict[str, Any] = None) -> None:
        """
        Create a new cache with the given name and settings.
        
        Args:
            cache_name (str): Name of the cache to create
            expiry (int, optional): Default expiry time in seconds
            settings (dict, optional): Additional settings for the cache
        """
        settings = settings or {}
        if expiry is not None:
            settings['default_expiry'] = expiry
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Store cache metadata
            cursor.execute(
                "INSERT OR REPLACE INTO cache_metadata (cache_name, settings) VALUES (?, ?)",
                (cache_name, json.dumps(settings))
            )
            
            conn.commit()
        
        logger.info(f"Created cache '{cache_name}' with settings {settings}")
    
    def get(self, cache_name: str, key: str) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            
        Returns:
            Any: The cached value or None if not found or expired
        """
        # Hash the key if it's too long
        if len(key) > 100:
            key = hashlib.md5(key.encode()).hexdigest()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Get the value
            cursor.execute(
                "SELECT value, expires FROM cache WHERE cache_name = ? AND key = ?",
                (cache_name, key)
            )
            
            row = cursor.fetchone()
            
            if row is None:
                with self._stats_lock:
                    self._stats['misses'] += 1
                return None
            
            # Check if expired
            if row['expires'] is not None and time.time() >= row['expires']:
                # Delete expired entry
                cursor.execute(
                    "DELETE FROM cache WHERE cache_name = ? AND key = ?",
                    (cache_name, key)
                )
                conn.commit()
                
                with self._stats_lock:
                    self._stats['misses'] += 1
                    self._stats['deletes'] += 1
                
                return None
            
            # Deserialize the value
            try:
                value = pickle.loads(row['value'])
                
                with self._stats_lock:
                    self._stats['hits'] += 1
                
                return value
            except (pickle.PickleError, EOFError) as e:
                logger.warning(f"Error deserializing cache value for {cache_name}/{key}: {e}")
                
                # Delete corrupted entry
                cursor.execute(
                    "DELETE FROM cache WHERE cache_name = ? AND key = ?",
                    (cache_name, key)
                )
                conn.commit()
                
                with self._stats_lock:
                    self._stats['misses'] += 1
                    self._stats['deletes'] += 1
                
                return None
    
    def set(self, cache_name: str, key: str, value: Any, expiry: Optional[int] = None, metadata: Dict[str, Any] = None) -> None:
        """
        Set a value in the cache.
        
        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            value (Any): Value to cache
            expiry (int, optional): Expiry time in seconds. If None, uses cache-specific default.
            metadata (dict, optional): Additional metadata to store with the value
        """
        # Hash the key if it's too long
        if len(key) > 100:
            key = hashlib.md5(key.encode()).hexdigest()
        
        # Get cache settings
        settings = self._get_cache_settings(cache_name)
        
        # Calculate expiry timestamp
        expires = None
        if expiry is not None:
            expires = time.time() + expiry
        elif 'default_expiry' in settings:
            expires = time.time() + settings['default_expiry']
        
        # Serialize the value
        try:
            serialized_value = pickle.dumps(value, protocol=pickle.HIGHEST_PROTOCOL)
        except (pickle.PickleError, TypeError) as e:
            logger.warning(f"Error serializing cache value for {cache_name}/{key}: {e}")
            return
        
        # Serialize metadata
        serialized_metadata = None
        if metadata:
            serialized_metadata = json.dumps(metadata)
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Store the value
            cursor.execute(
                """
                INSERT OR REPLACE INTO cache (cache_name, key, value, metadata, created, expires)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (cache_name, key, serialized_value, serialized_metadata, time.time(), expires)
            )
            
            conn.commit()
        
        with self._stats_lock:
            self._stats['writes'] += 1
    
    def delete(self, cache_name: str, key: str) -> bool:
        """
        Delete a value from the cache.
        
        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            
        Returns:
            bool: True if the value was deleted, False otherwise
        """
        # Hash the key if it's too long
        if len(key) > 100:
            key = hashlib.md5(key.encode()).hexdigest()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Delete the value
            cursor.execute(
                "DELETE FROM cache WHERE cache_name = ? AND key = ?",
                (cache_name, key)
            )
            
            deleted = cursor.rowcount > 0
            conn.commit()
            
            if deleted:
                with self._stats_lock:
                    self._stats['deletes'] += 1
            
            return deleted
    
    def clear(self, cache_name: str) -> int:
        """
        Clear a specific cache.
        
        Args:
            cache_name (str): Name of the cache to clear
            
        Returns:
            int: Number of items cleared
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Delete all values in the cache
            cursor.execute(
                "DELETE FROM cache WHERE cache_name = ?",
                (cache_name,)
            )
            
            deleted = cursor.rowcount
            conn.commit()
            
            with self._stats_lock:
                self._stats['deletes'] += deleted
            
            logger.info(f"Cleared {deleted} items from cache '{cache_name}'")
            
            return deleted
    
    def clear_all(self) -> Dict[str, int]:
        """
        Clear all caches.
        
        Returns:
            Dict[str, int]: Dictionary mapping cache names to number of items cleared
        """
        result = {}
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Get all cache names
            cursor.execute("SELECT DISTINCT cache_name FROM cache")
            cache_names = [row['cache_name'] for row in cursor.fetchall()]
            
            # Clear each cache
            for cache_name in cache_names:
                result[cache_name] = self.clear(cache_name)
            
            # Vacuum the database if auto_vacuum is enabled
            if self.auto_vacuum:
                cursor.execute("VACUUM")
                conn.commit()
        
        return result
    
    def cleanup(self) -> Dict[str, int]:
        """
        Remove expired entries from all caches.
        
        Returns:
            Dict[str, int]: Dictionary mapping cache names to number of items removed
        """
        result = {}
        current_time = time.time()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Get all cache names with expired entries
            cursor.execute(
                """
                SELECT DISTINCT cache_name FROM cache
                WHERE expires IS NOT NULL AND expires < ?
                """,
                (current_time,)
            )
            
            cache_names = [row['cache_name'] for row in cursor.fetchall()]
            
            # Delete expired entries for each cache
            for cache_name in cache_names:
                cursor.execute(
                    """
                    DELETE FROM cache
                    WHERE cache_name = ? AND expires IS NOT NULL AND expires < ?
                    """,
                    (cache_name, current_time)
                )
                
                deleted = cursor.rowcount
                result[cache_name] = deleted
                
                with self._stats_lock:
                    self._stats['deletes'] += deleted
            
            conn.commit()
            
            # Vacuum the database if auto_vacuum is enabled and items were deleted
            if self.auto_vacuum and sum(result.values()) > 0:
                cursor.execute("VACUUM")
                conn.commit()
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the cache.
        
        Returns:
            Dict[str, Any]: Dictionary with cache statistics
        """
        stats = {}
        
        with self._stats_lock:
            stats.update(self._stats)
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Get total number of items
            cursor.execute("SELECT COUNT(*) as count FROM cache")
            stats['total_items'] = cursor.fetchone()['count']
            
            # Get number of items per cache
            cursor.execute(
                """
                SELECT cache_name, COUNT(*) as count FROM cache
                GROUP BY cache_name
                """
            )
            
            stats['items_per_cache'] = {row['cache_name']: row['count'] for row in cursor.fetchall()}
            
            # Get number of expired items
            current_time = time.time()
            cursor.execute(
                """
                SELECT COUNT(*) as count FROM cache
                WHERE expires IS NOT NULL AND expires < ?
                """,
                (current_time,)
            )
            
            stats['expired_items'] = cursor.fetchone()['count']
            
            # Get database size
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()['page_count']
            
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()['page_size']
            
            stats['db_size'] = page_count * page_size
            stats['db_size_mb'] = stats['db_size'] / (1024 * 1024)
        
        return stats
    
    def _get_cache_settings(self, cache_name: str) -> Dict[str, Any]:
        """
        Get settings for a specific cache.
        
        Args:
            cache_name (str): Name of the cache
            
        Returns:
            Dict[str, Any]: Cache settings
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT settings FROM cache_metadata WHERE cache_name = ?",
                (cache_name,)
            )
            
            row = cursor.fetchone()
            
            if row is None:
                return {}
            
            return json.loads(row['settings'])
    
    def close(self) -> None:
        """Close the database connection."""
        with self._lock:
            if self._conn is not None:
                self._conn.close()
                self._conn = None
        
        logger.info("DBCache closed")
    
    def __del__(self) -> None:
        """Close the database connection when the object is deleted."""
        self.close()


# Create a global instance for convenience
db_cache = DBCache()
