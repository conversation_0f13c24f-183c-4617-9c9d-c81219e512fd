"""
Virtualized tree widget for efficient rendering of large hierarchical datasets.
Only renders the visible nodes, significantly improving performance for large trees.
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import List, Dict, Any, Callable, Optional, Tuple, Set

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("VirtualizedTree")


class TreeNode:
    """Represents a node in the virtualized tree."""

    def __init__(self, id: str, data: Any, parent_id: Optional[str] = None):
        """
        Initialize a tree node.

        Args:
            id (str): Unique identifier for the node
            data (Any): Data associated with the node
            parent_id (str, optional): ID of the parent node
        """
        self.id = id
        self.data = data
        self.parent_id = parent_id
        self.children: List[str] = []
        self.is_expanded = False
        self.level = 0  # Depth in the tree
        self.visible_index = -1  # Position in the flattened visible list


class VirtualizedTree(ttk.Frame):
    """
    A virtualized tree widget that only renders visible nodes.
    Significantly improves performance for large hierarchical datasets.
    """

    def __init__(self, master,
                 item_height: int = 30,
                 buffer_items: int = 5,
                 width: int = 400,
                 height: int = 300,
                 indent_width: int = 20,
                 **kwargs):
        """
        Initialize the virtualized tree.

        Args:
            master: Parent widget
            item_height (int): Height of each item in pixels
            buffer_items (int): Number of items to render above and below the visible area
            width (int): Width of the tree
            height (int): Height of the tree
            indent_width (int): Width of each indentation level
            **kwargs: Additional arguments for the frame
        """
        super().__init__(master, **kwargs)

        self.item_height = item_height
        self.buffer_items = buffer_items
        self.width = width
        self.height = height
        self.indent_width = indent_width

        # Data
        self._nodes: Dict[str, TreeNode] = {}
        self._root_nodes: List[str] = []
        self._visible_nodes: List[str] = []  # Flattened list of visible node IDs
        self._visible_widgets: Dict[int, tk.Widget] = {}  # Widgets for visible nodes

        # Rendering functions
        self._create_node_widget_func: Optional[Callable[[], tk.Widget]] = None
        self._render_node_func: Optional[Callable[[TreeNode, tk.Widget, int], None]] = None

        # UI components
        self._canvas = tk.Canvas(
            self,
            width=width,
            height=height,
            highlightthickness=0
        )
        self._scrollbar = ttk.Scrollbar(
            self,
            orient="vertical",
            command=self._canvas.yview
        )
        self._canvas.configure(yscrollcommand=self._scrollbar.set)

        # Layout
        self._canvas.grid(row=0, column=0, sticky="nsew")
        self._scrollbar.grid(row=0, column=1, sticky="ns")
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # Content frame
        self._content_frame = tk.Frame(self._canvas)
        self._content_window = self._canvas.create_window(
            (0, 0),
            window=self._content_frame,
            anchor="nw",
            width=width
        )

        # Bind events
        self._canvas.bind("<Configure>", self._on_canvas_configure)
        self._canvas.bind("<MouseWheel>", self._on_mousewheel)
        self._canvas.bind("<Button-4>", self._on_mousewheel)
        self._canvas.bind("<Button-5>", self._on_mousewheel)

        # Track scroll position
        self._last_scroll_position = 0
        self._canvas.bind("<Motion>", self._check_scroll_position)

        logger.info("VirtualizedTree initialized")

    def set_node_renderer(self,
                         create_widget_func: Callable[[], tk.Widget],
                         render_func: Callable[[TreeNode, tk.Widget, int], None]) -> None:
        """
        Set the functions to create and render node widgets.

        Args:
            create_widget_func: Function that creates a new widget for a node
            render_func: Function that renders a node into a widget (node, widget, indent_level)
        """
        self._create_node_widget_func = create_widget_func
        self._render_node_func = render_func

    def add_node(self, id: str, data: Any, parent_id: Optional[str] = None) -> None:
        """
        Add a node to the tree.

        Args:
            id (str): Unique identifier for the node
            data (Any): Data associated with the node
            parent_id (str, optional): ID of the parent node
        """
        # Create the node
        node = TreeNode(id, data, parent_id)

        # Add to nodes dictionary
        self._nodes[id] = node

        # If it has a parent, add it to the parent's children
        if parent_id and parent_id in self._nodes:
            self._nodes[parent_id].children.append(id)
            node.level = self._nodes[parent_id].level + 1
        else:
            # It's a root node
            self._root_nodes.append(id)

        # Update the visible nodes list
        self._update_visible_nodes()

        # Update the content height
        self._update_content_height()

        # Render visible nodes
        self._render_visible_nodes()

    def remove_node(self, id: str) -> None:
        """
        Remove a node and all its children from the tree.

        Args:
            id (str): ID of the node to remove
        """
        if id not in self._nodes:
            return

        # Get the node
        node = self._nodes[id]

        # Remove from parent's children
        if node.parent_id and node.parent_id in self._nodes:
            self._nodes[node.parent_id].children.remove(id)

        # Remove from root nodes if it's a root
        if id in self._root_nodes:
            self._root_nodes.remove(id)

        # Remove all children recursively
        self._remove_node_recursive(id)

        # Update the visible nodes list
        self._update_visible_nodes()

        # Update the content height
        self._update_content_height()

        # Render visible nodes
        self._render_visible_nodes()

    def _remove_node_recursive(self, id: str) -> None:
        """
        Recursively remove a node and all its children.

        Args:
            id (str): ID of the node to remove
        """
        if id not in self._nodes:
            return

        # Get the node
        node = self._nodes[id]

        # Remove all children
        for child_id in node.children[:]:  # Copy to avoid modification during iteration
            self._remove_node_recursive(child_id)

        # Remove the node itself
        del self._nodes[id]

    def expand_node(self, id: str) -> None:
        """
        Expand a node to show its children.

        Args:
            id (str): ID of the node to expand
        """
        if id not in self._nodes:
            return

        # Set the node as expanded
        self._nodes[id].is_expanded = True

        # Update the visible nodes list
        self._update_visible_nodes()

        # Update the content height
        self._update_content_height()

        # Render visible nodes
        self._render_visible_nodes()

    def collapse_node(self, id: str) -> None:
        """
        Collapse a node to hide its children.

        Args:
            id (str): ID of the node to collapse
        """
        if id not in self._nodes:
            return

        # Set the node as collapsed
        self._nodes[id].is_expanded = False

        # Update the visible nodes list
        self._update_visible_nodes()

        # Update the content height
        self._update_content_height()

        # Render visible nodes
        self._render_visible_nodes()

    def toggle_node(self, id: str) -> None:
        """
        Toggle a node's expanded state.

        Args:
            id (str): ID of the node to toggle
        """
        if id not in self._nodes:
            return

        if self._nodes[id].is_expanded:
            self.collapse_node(id)
        else:
            self.expand_node(id)

    def _update_visible_nodes(self) -> None:
        """Update the list of visible nodes based on expanded state."""
        self._visible_nodes = []

        # Start with root nodes
        for root_id in self._root_nodes:
            self._add_visible_node_recursive(root_id)

        # Update visible indices
        for i, node_id in enumerate(self._visible_nodes):
            self._nodes[node_id].visible_index = i

    def _add_visible_node_recursive(self, node_id: str) -> None:
        """
        Recursively add a node and its visible children to the visible nodes list.

        Args:
            node_id (str): ID of the node to add
        """
        if node_id not in self._nodes:
            return

        # Add the node
        self._visible_nodes.append(node_id)

        # If expanded, add children
        node = self._nodes[node_id]
        if node.is_expanded:
            for child_id in node.children:
                self._add_visible_node_recursive(child_id)

    def _update_content_height(self) -> None:
        """Update the height of the content frame based on the number of visible nodes."""
        total_height = len(self._visible_nodes) * self.item_height
        self._canvas.configure(scrollregion=(0, 0, self.width, total_height))

        # If the content is smaller than the canvas, disable scrolling
        if total_height <= self.height:
            self._scrollbar.grid_remove()
        else:
            self._scrollbar.grid()

    def _get_visible_range(self) -> Tuple[int, int]:
        """
        Get the range of visible nodes.

        Returns:
            Tuple of (start_index, end_index)
        """
        # Get the current scroll position
        scroll_top = self._canvas.yview()[0] * len(self._visible_nodes) * self.item_height

        # Calculate the visible range
        start_idx = max(0, int(scroll_top / self.item_height) - self.buffer_items)
        visible_items = int(self.height / self.item_height) + 2 * self.buffer_items
        end_idx = min(len(self._visible_nodes), start_idx + visible_items)

        return start_idx, end_idx

    def _render_visible_nodes(self) -> None:
        """Render only the visible nodes."""
        if not self._create_node_widget_func or not self._render_node_func:
            logger.warning("Node renderer not set")
            return

        # Get the visible range
        start_idx, end_idx = self._get_visible_range()

        # Remove widgets that are no longer visible
        for idx in list(self._visible_widgets.keys()):
            if idx < start_idx or idx >= end_idx:
                self._visible_widgets[idx].destroy()
                del self._visible_widgets[idx]

        # Create widgets for newly visible nodes
        for idx in range(start_idx, end_idx):
            if idx >= len(self._visible_nodes):
                break

            if idx not in self._visible_widgets:
                # Create a new widget
                widget = self._create_node_widget_func()
                widget.place(x=0, y=idx * self.item_height, width=self.width, height=self.item_height)

                # Get the node
                node_id = self._visible_nodes[idx]
                node = self._nodes[node_id]

                # Render the node
                self._render_node_func(node, widget, node.level)

                # Store the widget
                self._visible_widgets[idx] = widget

    def _on_canvas_configure(self, event) -> None:
        """Handle canvas resize events."""
        # Update the width of the content window
        self.width = event.width
        self._canvas.itemconfig(self._content_window, width=event.width)

        # Re-render visible nodes
        self._render_visible_nodes()

    def _on_mousewheel(self, event) -> None:
        """Handle mousewheel events for scrolling."""
        # Different platforms have different mousewheel events
        if event.num == 4 or event.delta > 0:
            self._canvas.yview_scroll(-1, "units")
        elif event.num == 5 or event.delta < 0:
            self._canvas.yview_scroll(1, "units")

        # Check if we need to re-render
        self._check_scroll_position(event)

    def _check_scroll_position(self, event) -> None:
        """Check if the scroll position has changed enough to re-render."""
        current_pos = self._canvas.yview()[0]
        if abs(current_pos - self._last_scroll_position) > 0.01:
            self._last_scroll_position = current_pos
            self._render_visible_nodes()

    def update_node(self, id: str) -> None:
        """
        Update a specific node.

        Args:
            id (str): ID of the node to update
        """
        if id not in self._nodes:
            return

        node = self._nodes[id]
        if node.visible_index >= 0 and node.visible_index in self._visible_widgets:
            self._render_node_func(node, self._visible_widgets[node.visible_index], node.level)

    def update_all_visible(self) -> None:
        """Update all visible nodes."""
        for idx in self._visible_widgets:
            if idx < len(self._visible_nodes):
                node_id = self._visible_nodes[idx]
                node = self._nodes[node_id]
                self._render_node_func(node, self._visible_widgets[idx], node.level)

    def scroll_to_node(self, id: str) -> None:
        """
        Scroll to make a specific node visible.

        Args:
            id (str): ID of the node to scroll to
        """
        if id not in self._nodes:
            return

        node = self._nodes[id]
        if node.visible_index >= 0:
            # Calculate the position to scroll to
            pos = node.visible_index * self.item_height / (len(self._visible_nodes) * self.item_height)
            self._canvas.yview_moveto(pos)
            self._render_visible_nodes()

    def get_visible_nodes(self) -> List[TreeNode]:
        """
        Get the currently visible nodes.

        Returns:
            List of visible nodes
        """
        start_idx, end_idx = self._get_visible_range()
        return [self._nodes[self._visible_nodes[idx]] for idx in range(start_idx, end_idx) if idx < len(self._visible_nodes)]

    def clear(self) -> None:
        """Clear all nodes from the tree."""
        self._nodes = {}
        self._root_nodes = []
        self._visible_nodes = []

        # Remove all widgets
        for widget in self._visible_widgets.values():
            widget.destroy()
        self._visible_widgets = {}

        # Update the content height
        self._update_content_height()

        logger.info("Cleared virtualized tree")

    def get_node(self, id: str) -> Optional[TreeNode]:
        """
        Get a node by its ID.

        Args:
            id (str): ID of the node to get

        Returns:
            TreeNode or None if not found
        """
        return self._nodes.get(id)

    def get_all_nodes(self) -> Dict[str, TreeNode]:
        """
        Get all nodes in the tree.

        Returns:
            Dictionary of node IDs to nodes
        """
        return self._nodes.copy()

    def get_root_nodes(self) -> List[TreeNode]:
        """
        Get all root nodes.

        Returns:
            List of root nodes
        """
        return [self._nodes[id] for id in self._root_nodes if id in self._nodes]

    def get_children(self, id: str) -> List[TreeNode]:
        """
        Get the children of a node.

        Args:
            id (str): ID of the node

        Returns:
            List of child nodes
        """
        if id not in self._nodes:
            return []

        return [self._nodes[child_id] for child_id in self._nodes[id].children if child_id in self._nodes]

    def expand_all(self) -> None:
        """Expand all nodes in the tree."""
        for node in self._nodes.values():
            node.is_expanded = True

        # Update the visible nodes list
        self._update_visible_nodes()

        # Update the content height
        self._update_content_height()

        # Render visible nodes
        self._render_visible_nodes()

        logger.info("Expanded all nodes in tree")

    def collapse_all(self) -> None:
        """Collapse all nodes in the tree."""
        for node in self._nodes.values():
            node.is_expanded = False

        # Update the visible nodes list
        self._update_visible_nodes()

        # Update the content height
        self._update_content_height()

        # Render visible nodes
        self._render_visible_nodes()

        logger.info("Collapsed all nodes in tree")
