"""
Property search methods for the Czech Property Registry application.

This module provides methods for searching properties using various criteria.
"""

from ui.message_boxes import MessageBoxes


class PropertySearch:
    """
    Methods for searching properties using various criteria.
    """

    def __init__(self, app):
        """
        Initialize the property search methods.

        Args:
            app: The main application instance
        """
        self.app = app
        self.session = app.session

    def unified_search(self, search_type, **kwargs):
        """
        Unified search function that dispatches to the appropriate search method

        Args:
            search_type (str): Type of search ('coordinates', 'parcel', 'address', 'location')
            **kwargs: Additional arguments specific to the search type

        Returns:
            list: List of property data dictionaries or None if search failed
        """
        try:
            self.app.show_status(f"Performing {search_type} search...")

            # Dispatch to the appropriate search method
            if search_type == 'coordinates':
                # Extract coordinates
                lat = kwargs.get('lat')
                lng = kwargs.get('lng')

                if not lat or not lng:
                    MessageBoxes.show_error("Input Error", "Please provide valid coordinates")
                    return None

                # Perform the search
                return self.fetch_property_by_coordinates(lat, lng)

            elif search_type == 'parcel':
                # Extract parcel number
                parcel_number = kwargs.get('parcel_number')

                if not parcel_number:
                    MessageBoxes.show_error("Input Error", "Please provide a valid parcel number")
                    return None

                # Perform the search
                return self.fetch_property_by_parcel(parcel_number)

            elif search_type == 'address':
                # Extract address components
                address = kwargs.get('address')
                city = kwargs.get('city')
                street = kwargs.get('street')
                number = kwargs.get('number')
                postal = kwargs.get('postal')

                if not address and not (city or street or number):
                    MessageBoxes.show_error("Input Error", "Please provide a valid address")
                    return None

                # Perform the search
                if address:
                    return self.fetch_properties_by_address(address)
                else:
                    # Construct address from components
                    components = []
                    if street:
                        components.append(street)
                    if number:
                        components.append(number)
                    if city:
                        components.append(city)
                    if postal:
                        components.append(postal)

                    full_address = ", ".join(components)
                    return self.fetch_properties_by_address(full_address)

            elif search_type == 'location':
                # Extract location components
                region = kwargs.get('region')
                district = kwargs.get('district')
                city = kwargs.get('city')
                cadastral = kwargs.get('cadastral')

                if not any([region, district, city, cadastral]):
                    MessageBoxes.show_error("Input Error", "Please provide at least one location component")
                    return None

                # Perform the search
                return self.fetch_properties_by_location(region, district, city, cadastral)

            else:
                MessageBoxes.show_error("Input Error", f"Unknown search type: {search_type}")
                return None

        except Exception as e:
            MessageBoxes.show_error("Search Error", f"An error occurred during search: {str(e)}")
            print(f"Error in unified search: {e}")
            return None

    def fetch_property_by_coordinates(self, lat, lng):
        """
        Fetch property data by coordinates

        Args:
            lat (str): Latitude
            lng (str): Longitude

        Returns:
            dict: Property data or None if search failed
        """
        try:
            # Show status
            self.app.show_status(f"Searching for property at coordinates {lat}, {lng}...")

            # Convert coordinates if needed
            x_coord, y_coord = lat, lng

            # Use the CUZK scraper
            property_data = self.app.cuzk_scraper.search_by_coordinates(x_coord, y_coord)

            if property_data:
                return property_data

            # If the CUZK scraper failed, try the CUZK integration
            property_data = self.app.cuzk_integration.get_property_by_coordinates(x_coord, y_coord)

            if property_data:
                return property_data

            # If all searches failed, show an error message
            self.app.show_status("No property data found")

            # Show notification in the banner if available
            if hasattr(self.app, 'show_notification'):
                self.app.show_notification(
                    message="No property data found at these coordinates.",
                    notification_type="error",
                    auto_hide=True,
                    duration=8000,
                    key="no_property_data"
                )
            else:
                # Fall back to message box if notification banner is not available
                MessageBoxes.show_error(
                    "No Data Found",
                    "No property data could be found at these coordinates."
                )

            return None

        except Exception as e:
            print(f"Error fetching property by coordinates: {e}")
            return None

    def fetch_property_by_parcel(self, parcel_number):
        """
        Fetch property data by parcel number

        Args:
            parcel_number (str): Parcel number

        Returns:
            dict: Property data or None if search failed
        """
        try:
            # Show status
            self.app.show_status(f"Searching for property with parcel number {parcel_number}...")

            # Use the CUZK scraper
            property_data = self.app.cuzk_scraper.search_by_parcel_number(parcel_number)

            if property_data:
                return property_data

            # If the CUZK scraper failed, try the CUZK integration
            property_data = self.app.cuzk_integration.get_property_by_parcel(parcel_number)

            if property_data:
                return property_data

            # If all searches failed, show an error message
            self.app.show_status("No property data found")

            # Show notification in the banner if available
            if hasattr(self.app, 'show_notification'):
                self.app.show_notification(
                    message=f"No property data found for parcel number: {parcel_number}",
                    notification_type="error",
                    auto_hide=True,
                    duration=8000,
                    key="no_property_data"
                )
            else:
                # Fall back to message box if notification banner is not available
                MessageBoxes.show_error(
                    "No Data Found",
                    f"No property data could be found for parcel number: {parcel_number}"
                )

            return None

        except Exception as e:
            print(f"Error fetching property by parcel: {e}")
            return None

    def fetch_properties_by_location(self, region, district, city, cadastral):
        """
        Fetch properties in a specific location

        Args:
            region (str): Region name
            district (str): District name
            city (str): City name
            cadastral (str): Cadastral territory name

        Returns:
            list: List of property dictionaries or None if search failed
        """
        try:
            # Show status
            location_parts = []
            if city:
                location_parts.append(city)
            if district:
                location_parts.append(district)
            if region:
                location_parts.append(region)
            if cadastral:
                location_parts.append(f"cadastral: {cadastral}")

            location_str = ", ".join(location_parts)
            self.app.show_status(f"Searching for properties in {location_str}...")

            # Use the CUZK integration
            properties = self.app.cuzk_integration.get_properties_by_location(region, district, city, cadastral)

            if properties:
                return properties

            # If all searches failed, show an error message
            self.app.show_status("No property data found")

            # Show notification in the banner if available
            location_parts = []
            if city:
                location_parts.append(city)
            if district:
                location_parts.append(district)
            if region:
                location_parts.append(region)
            if cadastral:
                location_parts.append(f"cadastral: {cadastral}")

            location_str = ", ".join(location_parts)

            if hasattr(self.app, 'show_notification'):
                self.app.show_notification(
                    message=f"No property data found for location: {location_str}",
                    notification_type="error",
                    auto_hide=True,
                    duration=8000,
                    key="no_property_data"
                )
            else:
                # Fall back to message box if notification banner is not available
                MessageBoxes.show_error(
                    "No Data Found",
                    f"No property data could be found for location: {location_str}"
                )

            return []

        except Exception as e:
            print(f"Error fetching properties by location: {e}")
            return None

    def fetch_properties_by_address(self, address):
        """
        Fetch properties at a specific address

        Args:
            address (str): Address to search for

        Returns:
            list: List of property dictionaries or None if search failed
        """
        try:
            # Show status
            self.app.show_status(f"Searching for properties at address: {address}...")

            # Use the CUZK integration
            properties = self.app.cuzk_integration.get_properties_by_address(address)

            if properties:
                return properties

            # If the CUZK integration failed, try the Google Maps API to get coordinates
            coordinates = self.app.google_maps.geocode_address(address)

            if coordinates:
                lat, lng = coordinates

                # Use the coordinates to search for properties
                property_data = self.fetch_property_by_coordinates(lat, lng)

                if property_data:
                    return [property_data]

            # If all searches failed, show an error message
            self.app.show_status("No property data found")

            # Show notification in the banner if available
            if hasattr(self.app, 'show_notification'):
                self.app.show_notification(
                    message=f"No property data found for address: {address}",
                    notification_type="error",
                    auto_hide=True,
                    duration=8000,
                    key="no_property_data"
                )
            else:
                # Fall back to message box if notification banner is not available
                MessageBoxes.show_error(
                    "No Data Found",
                    f"No property data could be found for address: {address}"
                )

            return []

        except Exception as e:
            print(f"Error fetching properties by address: {e}")
            return None
