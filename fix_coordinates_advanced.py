"""
Advanced fix for coordinate conversion issues in the Czech Property Registry application.

This script:
1. Updates the coordinate conversion constants in both files to ensure they're identical
2. Clears all caches
3. Restarts the application
"""

import os
import shutil
import sys
import subprocess
import logging
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants for the coordinate conversion
# These are the correct constants that should be used in both files
CORRECT_LAT_0 = 49.5
CORRECT_LNG_0 = 15.0
CORRECT_LAT_SCALE = 111320
CORRECT_LNG_SCALE = 73000
CORRECT_X_OFFSET = 850000
CORRECT_Y_OFFSET = 1000000

def update_coordinate_conversion_code():
    """Update the coordinate conversion code in both files."""
    files_to_update = [
        'api/cuzk/cuzk_integration.py',
        'utils/helpers/coordinate_helpers.py',
    ]
    
    updated_count = 0
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            try:
                # Read the file
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Update the constants
                # This is a simple pattern matching approach - it might need to be adjusted
                # based on the actual code structure
                
                # Update lat_0 and lng_0
                content = re.sub(r'lat_0\s*=\s*[\d\.]+', f'lat_0 = {CORRECT_LAT_0}', content)
                content = re.sub(r'lng_0\s*=\s*[\d\.]+', f'lng_0 = {CORRECT_LNG_0}', content)
                
                # Update scale factors
                content = re.sub(r'lat_scale\s*=\s*[\d\.]+', f'lat_scale = {CORRECT_LAT_SCALE}', content)
                content = re.sub(r'lng_scale\s*=\s*[\d\.]+', f'lng_scale = {CORRECT_LNG_SCALE}', content)
                
                # Update offsets in convert_wgs84_to_sjtsk
                content = re.sub(r'x\s*=\s*(?:int\()?\s*-1?\s*\*\s*lng_offset\s*-\s*[\d\.]+\)?', 
                                f'x = -1 * lng_offset - {CORRECT_X_OFFSET}', content)
                content = re.sub(r'y\s*=\s*(?:int\()?\s*-1?\s*\*\s*lat_offset\s*-\s*[\d\.]+\)?', 
                                f'y = -1 * lat_offset - {CORRECT_Y_OFFSET}', content)
                
                # Update offsets in convert_sjtsk_to_wgs84
                content = re.sub(r'lat_offset\s*=\s*-\(y\s*\+\s*[\d\.]+\)\s*\/\s*lat_scale', 
                                f'lat_offset = -(y + {CORRECT_Y_OFFSET}) / lat_scale', content)
                content = re.sub(r'lng_offset\s*=\s*-\(x\s*\+\s*[\d\.]+\)\s*\/\s*lng_scale', 
                                f'lng_offset = -(x + {CORRECT_X_OFFSET}) / lng_scale', content)
                
                # Write the updated content back to the file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"Updated coordinate conversion code in: {file_path}")
                updated_count += 1
            except Exception as e:
                logger.error(f"Error updating coordinate conversion code in {file_path}: {e}")
    
    return updated_count

def clear_cache_directories():
    """Clear all cache directories."""
    cache_dirs = [
        'cache',
        '.cache',
        'czech_data/cache',
        'data/cache',
    ]
    
    cleared_count = 0
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir) and os.path.isdir(cache_dir):
            try:
                # Remove the directory and all its contents
                shutil.rmtree(cache_dir)
                logger.info(f"Cleared cache directory: {cache_dir}")
                cleared_count += 1
                
                # Recreate the empty directory
                os.makedirs(cache_dir, exist_ok=True)
            except Exception as e:
                logger.error(f"Error clearing cache directory {cache_dir}: {e}")
    
    return cleared_count

def clear_cache_files():
    """Clear all cache files."""
    cache_files = [
        'czech_data/cache.json',
        'czech_data/city_cache.json',
        'czech_data/address_cache.json',
        'czech_data/property_cache.json',
        'czech_data/suggestions_cache.json',
        'czech_data/encrypted_urls.json',
    ]
    
    cleared_count = 0
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            try:
                # Remove the file
                os.remove(cache_file)
                logger.info(f"Cleared cache file: {cache_file}")
                cleared_count += 1
            except Exception as e:
                logger.error(f"Error clearing cache file {cache_file}: {e}")
    
    return cleared_count

def restart_application():
    """Restart the main application."""
    try:
        # Get the path to the main.py file
        main_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
        
        # Check if the file exists
        if not os.path.exists(main_path):
            logger.error(f"Main file not found at: {main_path}")
            return False
        
        # Launch the application with the current Python interpreter
        cmd = [sys.executable, main_path]
        
        # Use Popen to launch the process without waiting for it to complete
        subprocess.Popen(cmd)
        
        return True
    except Exception as e:
        logger.error(f"Error restarting application: {e}")
        return False

def main():
    """Main function to fix coordinate conversion issues."""
    logger.info("Starting advanced coordinate conversion fix...")
    
    # Update the coordinate conversion code
    updated_count = update_coordinate_conversion_code()
    logger.info(f"Updated coordinate conversion code in {updated_count} files")
    
    # Clear cache directories
    dir_count = clear_cache_directories()
    logger.info(f"Cleared {dir_count} cache directories")
    
    # Clear cache files
    file_count = clear_cache_files()
    logger.info(f"Cleared {file_count} cache files")
    
    # Restart the application
    if restart_application():
        logger.info("Application restarted successfully")
    else:
        logger.error("Failed to restart application")
        logger.info("Please restart the application manually by running 'python main.py'")
    
    logger.info("Advanced coordinate conversion fix completed")

if __name__ == "__main__":
    main()
