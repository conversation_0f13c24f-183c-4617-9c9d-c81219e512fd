"""
Test script to verify that Mapy.cz coordinates are being used correctly for CUZK URLs.

This script:
1. Initializes the Mapy.cz integration
2. Tests generating CUZK URLs for several known locations
3. Compares the results with expected values
"""

import logging
import sys
import os
import traceback

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Changed to DEBUG level for more detailed logs
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("MapyCZTest")

# Add a console handler for immediate output
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.DEBUG)
logger.addHandler(console_handler)

# Print startup message
print("Starting Mapy.cz coordinate test script...")
logger.info("Logger initialized")

# Import the necessary modules
try:
    from api.mapycz.mapycz_integration import MapyCZIntegration
    from api.cuzk.cuzk_integration import CUZKIntegration
    from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk, convert_sjtsk_to_wgs84
except ImportError as e:
    logger.error(f"Error importing modules: {e}")
    logger.error("Make sure you're running this script from the project root directory")
    sys.exit(1)

def test_mapycz_coordinates():
    """Test Mapy.cz coordinate integration for CUZK URLs"""
    logger.info("Testing Mapy.cz coordinate integration for CUZK URLs")

    # Initialize the Mapy.cz integration
    mapycz = MapyCZIntegration()

    # Initialize the CUZK integration
    cuzk = CUZKIntegration()

    # Test locations
    test_locations = [
        # Known locations (exact matches in the lookup table)
        {
            "name": "Prague (Václavské náměstí)",
            "wgs84": (50.0811, 14.428),
            "expected_sjtsk": (1064688, 774041)
        },
        {
            "name": "Brno",
            "wgs84": (49.1951, 16.6068),
            "expected_sjtsk": (1160744, 598248)
        },
        {
            "name": "Bušovice",
            "wgs84": (49.7955, 13.5347),
            "expected_sjtsk": (1032895, 743033)
        },
        {
            "name": "Klecany",
            "wgs84": (50.1754, 14.4141),
            "expected_sjtsk": (1075185, 807229)
        },

        # Locations that should use interpolation
        {
            "name": "Prague (Žižkov)",
            "wgs84": (50.0833, 14.4500),
            "expected_sjtsk": None  # We don't know the exact expected value
        },
        {
            "name": "Pilsen (Plzeň)",
            "wgs84": (49.7474, 13.3775),
            "expected_sjtsk": (1066000, 820000)
        },
        {
            "name": "Ostrava",
            "wgs84": (49.8350, 18.2823),
            "expected_sjtsk": (1104000, 470000)
        },
        {
            "name": "Olomouc",
            "wgs84": (49.5938, 17.2510),
            "expected_sjtsk": (1121000, 549000)
        },

        # Locations far from any known point (should use weighted interpolation)
        {
            "name": "Jihlava",
            "wgs84": (49.3961, 15.5903),
            "expected_sjtsk": None  # We don't know the exact expected value
        },
        {
            "name": "Zlín",
            "wgs84": (49.2248, 17.6627),
            "expected_sjtsk": None  # We don't know the exact expected value
        }
    ]

    # Test each location
    for location in test_locations:
        logger.info(f"\nTesting location: {location['name']}")
        lat, lng = location["wgs84"]
        expected_sjtsk = location["expected_sjtsk"]

        # Generate CUZK URL using Mapy.cz integration
        url = mapycz.generate_cuzk_url(lat, lng)
        logger.info(f"Generated CUZK URL: {url}")

        # Extract S-JTSK coordinates from the URL
        import re
        match = re.search(r'x=(\d+)&y=(\d+)', url)
        if match:
            x_sjtsk, y_sjtsk = int(match.group(1)), int(match.group(2))
            logger.info(f"Extracted S-JTSK coordinates: ({x_sjtsk}, {y_sjtsk})")

            # Compare with expected values if available
            if expected_sjtsk:
                expected_x, expected_y = expected_sjtsk
                x_diff = abs(x_sjtsk - expected_x)
                y_diff = abs(y_sjtsk - expected_y)
                logger.info(f"Difference from expected: x_diff={x_diff}, y_diff={y_diff}")

                if x_diff > 10 or y_diff > 10:
                    logger.warning(f"Coordinates differ significantly from expected values!")
                else:
                    logger.info(f"Coordinates match expected values within tolerance")
            else:
                logger.info(f"No expected S-JTSK coordinates available for comparison")
        else:
            logger.error(f"Could not extract S-JTSK coordinates from URL: {url}")

        # Generate CUZK URL using CUZK integration
        cuzk_url = cuzk.generate_cuzk_url(lat, lng, use_mapa_identifikace=True)
        logger.info(f"CUZK integration URL: {cuzk_url}")

        # Compare the two URLs
        if url == cuzk_url:
            logger.info(f"URLs match: Mapy.cz integration is being used correctly")
        else:
            logger.warning(f"URLs do not match!")
            logger.warning(f"Mapy.cz URL: {url}")
            logger.warning(f"CUZK URL: {cuzk_url}")

        # For comparison, also convert using PyProj
        pyproj_x, pyproj_y = convert_wgs84_to_sjtsk(lat, lng)
        logger.info(f"PyProj S-JTSK: ({int(pyproj_x)}, {int(pyproj_y)})")

        # Extract S-JTSK coordinates from the URL again (in case the match variable is out of scope)
        match = re.search(r'x=(\d+)&y=(\d+)', url)
        if match:
            x_sjtsk, y_sjtsk = int(match.group(1)), int(match.group(2))

            # Convert back to WGS84
            reconverted_lat, reconverted_lng = convert_sjtsk_to_wgs84(x_sjtsk, y_sjtsk)
            logger.info(f"Reconverted WGS84: ({reconverted_lat}, {reconverted_lng})")

            # Calculate difference
            lat_diff = abs(lat - reconverted_lat)
            lng_diff = abs(lng - reconverted_lng)
            logger.info(f"WGS84 difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")

            # Compare with PyProj coordinates
            pyproj_diff_x = abs(x_sjtsk - int(pyproj_x))
            pyproj_diff_y = abs(y_sjtsk - int(pyproj_y))
            logger.info(f"Difference from PyProj: x_diff={pyproj_diff_x}, y_diff={pyproj_diff_y}")
        else:
            logger.error(f"Could not extract S-JTSK coordinates from URL for reconversion")

if __name__ == "__main__":
    test_mapycz_coordinates()
