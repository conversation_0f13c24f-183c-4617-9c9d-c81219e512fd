"""
Test the CUZK MapaIdentifikace URL integration.

This test verifies that the CUZK integration can generate MapaIdentifikace URLs
similar to how ikatastr works, and that these URLs can be used to open the CUZK
website with property information.
"""

import unittest
import logging
import webbrowser
from unittest.mock import patch, MagicMock

from api.cuzk.cuzk_integration import CUZKIntegration

# Set up logging
logger = logging.getLogger(__name__)


class TestCUZKMapaIdentifikace(unittest.TestCase):
    """Test the CUZK MapaIdentifikace URL integration."""

    def setUp(self):
        """Set up the test environment."""
        self.cuzk_integration = CUZKIntegration()
        
        # Test coordinates (Prague)
        self.test_lat = 50.0755
        self.test_lng = 14.4378
        self.test_radius = 500  # meters

    @patch('webbrowser.open')
    def test_generate_mapa_identifikace_url(self, mock_open):
        """Test generating a MapaIdentifikace URL from coordinates."""
        logger.info("Testing MapaIdentifikace URL generation")

        # Generate a MapaIdentifikace URL from coordinates
        url = self.cuzk_integration.generate_cuzk_url(
            self.test_lat, self.test_lng, use_mapa_identifikace=True
        )

        # Verify that a URL was generated
        self.assertIsNotNone(url, "No URL generated")
        self.assertTrue(url.startswith("http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"), 
                       "URL does not use MapaIdentifikace format")
        self.assertTrue("l=KN" in url, "URL does not include the layer parameter")

        logger.info(f"Generated MapaIdentifikace URL: {url}")

        # Test opening the URL in the browser
        self.cuzk_integration.open_cuzk_in_browser(
            self.test_lat, self.test_lng, use_mapa_identifikace=True
        )

        # Verify that webbrowser.open was called with the correct URL
        mock_open.assert_called_once()
        called_url = mock_open.call_args[0][0]
        self.assertTrue("MapaIdentifikace.aspx" in called_url, 
                       "URL does not use MapaIdentifikace format")

        return url

    @patch('webbrowser.open')
    def test_batch_cuzk_urls(self, mock_open):
        """Test generating batch CUZK URLs for properties around a central point."""
        logger.info("Testing batch CUZK URL generation")

        # Generate batch CUZK URLs
        points = self.cuzk_integration.generate_batch_cuzk_urls(
            self.test_lat, self.test_lng, self.test_radius, num_points=5
        )

        # Verify that points were generated
        self.assertIsNotNone(points, "No points generated")
        self.assertEqual(len(points), 5, "Wrong number of points generated")

        # Verify that each point has the required fields
        for point in points:
            self.assertIn('lat', point, "Point missing latitude")
            self.assertIn('lng', point, "Point missing longitude")
            self.assertIn('x', point, "Point missing x coordinate")
            self.assertIn('y', point, "Point missing y coordinate")
            self.assertIn('url', point, "Point missing URL")
            self.assertTrue(point['url'].startswith("http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"), 
                           "URL does not use MapaIdentifikace format")

        logger.info(f"Generated {len(points)} batch CUZK URLs")

        # Test opening batch CUZK URLs in the browser
        self.cuzk_integration.open_batch_cuzk_in_browser(
            self.test_lat, self.test_lng, self.test_radius, 
            num_points=3, max_tabs=2  # Only open 2 tabs
        )

        # Verify that webbrowser.open was called twice (max_tabs=2)
        self.assertEqual(mock_open.call_count, 2, "webbrowser.open not called the expected number of times")

        return points


if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the tests
    unittest.main()
