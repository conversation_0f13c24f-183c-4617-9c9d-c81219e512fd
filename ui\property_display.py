"""
Property display methods for the Czech Property Registry application.

This module provides methods for displaying property data in the UI.
"""

import tkinter as tk
from tkinter import ttk
from ui.message_boxes import MessageBoxes
from utils.template_loader import TemplateLoader


class PropertyDisplay:
    """
    Methods for displaying property data in the UI.
    """

    def __init__(self, app):
        """
        Initialize the property display methods.

        Args:
            app: The main application instance
        """
        self.app = app
        self.template_loader = TemplateLoader()

    def display_property_details(self, property_data):
        """
        Display property details in the UI

        Args:
            property_data (dict): Property data to display
        """
        try:
            # Extract owner information
            self.app.owner_name = property_data.get('owner_name', '')
            self.app.owner_address = property_data.get('owner_address', '')

            # Determine gender if available
            if 'owner_gender' in property_data:
                self.app.owner_gender = property_data['owner_gender']
            else:
                # Try to determine gender from name
                self.app.owner_gender = self.determine_gender(self.app.owner_name)

            # Update the UI with the property details
            self.update_property_ui(property_data)

            # Show a success message
            self.app.show_status(f"Found property owned by {self.app.owner_name}")
            MessageBoxes.show_info("Success", f"Found property owned by {self.app.owner_name}")

            # Generate a letter if requested
            if hasattr(self.app, 'auto_generate_letter') and self.app.auto_generate_letter.get():
                self.app.generate_letter()
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred while displaying property details: {str(e)}")
            print(f"Error displaying property details: {e}")

    def update_property_ui(self, property_data):
        """
        Update the UI with property details

        Args:
            property_data (dict): Property data to display
        """
        try:
            # Update buyer information fields
            self.app.buyer_name.delete(0, tk.END)
            self.app.buyer_name.insert(0, self.app.owner_name)

            self.app.buyer_address.delete(0, tk.END)
            self.app.buyer_address.insert(0, self.app.owner_address)

            # Update the letter text with a template
            self.app.letter_text.delete(1.0, tk.END)

            try:
                # Use the template loader to load and format the letter template
                letter_template = self.template_loader.format_template(
                    'property_letter_template',
                    owner_name=self.app.owner_name,
                    buyer_name=self.app.buyer_name.get(),
                    buyer_contact=self.app.buyer_contact.get()
                )
            except Exception as e:
                print(f"Error loading property letter template: {e}")
                # Fallback to hardcoded template if template loading fails
                letter_template = f"Dear {self.app.owner_name},\n\n"
                letter_template += "I am writing to express my interest in your property. "
                letter_template += "Please contact me at your earliest convenience.\n\n"
                letter_template += "Best regards,\n"
                letter_template += f"{self.app.buyer_name.get()}\n"
                letter_template += f"{self.app.buyer_contact.get()}"

            self.app.letter_text.insert(tk.END, letter_template)

            # If we have a treeview for displaying properties, update it
            if hasattr(self.app, 'property_treeview'):
                self.update_property_treeview([property_data])
        except Exception as e:
            print(f"Error updating property UI: {e}")

    def update_property_treeview(self, properties):
        """
        Update the property treeview with the given properties

        Args:
            properties (list): List of property dictionaries to display
        """
        try:
            # Clear the existing treeview
            if hasattr(self.app, 'property_treeview'):
                for item in self.app.property_treeview.get_children():
                    self.app.property_treeview.delete(item)

                # Add the properties to the treeview
                for i, prop in enumerate(properties):
                    # Extract property details
                    owner_name = prop.get('owner_name', 'Unknown')
                    owner_address = prop.get('owner_address', 'Unknown')
                    property_type = prop.get('property_type', 'Unknown')
                    parcel_number = prop.get('parcel_number', 'Unknown')
                    cadastral_territory = prop.get('cadastral_territory', 'Unknown')
                    ownership_share = prop.get('ownership_share', 'Unknown')
                    lv_number = prop.get('lv_number', 'Unknown')

                    # Add the property to the treeview
                    self.app.property_treeview.insert(
                        '',
                        'end',
                        text=str(i+1),
                        values=(
                            owner_name,
                            owner_address,
                            property_type,
                            parcel_number,
                            cadastral_territory,
                            ownership_share,
                            lv_number
                        )
                    )
        except Exception as e:
            print(f"Error updating property treeview: {e}")

    def display_properties(self, properties):
        """
        Display multiple properties in the UI

        Args:
            properties (list): List of property dictionaries to display
        """
        try:
            # Store the properties
            self.app.property_list = properties

            # Update the status
            self.app.show_status(f"Found {len(properties)} properties")

            # If we have a treeview for displaying properties, update it
            if hasattr(self.app, 'property_treeview'):
                self.update_property_treeview(properties)
            else:
                # Create a treeview for displaying properties
                self.create_property_treeview(properties)

            # Show a success message
            MessageBoxes.show_info("Success", f"Found {len(properties)} properties")
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred while displaying properties: {str(e)}")
            print(f"Error displaying properties: {e}")

    def create_property_treeview(self, properties):
        """
        Create a treeview for displaying properties

        Args:
            properties (list): List of property dictionaries to display
        """
        try:
            # Create a frame for the treeview
            treeview_frame = ttk.Frame(self.app.results_frame)
            treeview_frame.pack(fill="both", expand=True, padx=5, pady=5)

            # Create the treeview
            columns = (
                "owner_name",
                "owner_address",
                "property_type",
                "parcel_number",
                "cadastral_territory",
                "ownership_share",
                "lv_number"
            )

            self.app.property_treeview = ttk.Treeview(
                treeview_frame,
                columns=columns,
                show="headings",
                selectmode="browse"
            )

            # Add the columns
            self.app.property_treeview.heading("owner_name", text="Owner Name")
            self.app.property_treeview.heading("owner_address", text="Owner Address")
            self.app.property_treeview.heading("property_type", text="Property Type")
            self.app.property_treeview.heading("parcel_number", text="Parcel Number")
            self.app.property_treeview.heading("cadastral_territory", text="Cadastral Territory")
            self.app.property_treeview.heading("ownership_share", text="Ownership Share")
            self.app.property_treeview.heading("lv_number", text="LV Number")

            # Set column widths
            self.app.property_treeview.column("owner_name", width=150)
            self.app.property_treeview.column("owner_address", width=200)
            self.app.property_treeview.column("property_type", width=100)
            self.app.property_treeview.column("parcel_number", width=100)
            self.app.property_treeview.column("cadastral_territory", width=100)
            self.app.property_treeview.column("ownership_share", width=100)
            self.app.property_treeview.column("lv_number", width=100)

            # Add a scrollbar
            scrollbar = ttk.Scrollbar(treeview_frame, orient="vertical", command=self.app.property_treeview.yview)
            self.app.property_treeview.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side="right", fill="y")

            # Pack the treeview
            self.app.property_treeview.pack(side="left", fill="both", expand=True)

            # Add the properties to the treeview
            self.update_property_treeview(properties)

            # Add a double-click handler
            self.app.property_treeview.bind("<Double-1>", self.on_property_double_click)

            # Add a right-click menu
            self.add_property_treeview_menu()
        except Exception as e:
            print(f"Error creating property treeview: {e}")

    def on_property_double_click(self, _):
        """
        Handle double-click on a property in the treeview

        Args:
            event: The event that triggered this handler
        """
        try:
            # Get the selected item
            item = self.app.property_treeview.selection()[0]

            # Get the property index
            index = int(self.app.property_treeview.item(item, "text")) - 1

            # Get the property data
            property_data = self.app.property_list[index]

            # Display the property details
            self.display_property_details(property_data)
        except Exception as e:
            print(f"Error handling property double-click: {e}")

    def add_property_treeview_menu(self):
        """
        Add a right-click menu to the property treeview
        """
        try:
            # Create the menu
            self.app.property_menu = tk.Menu(self.app.property_treeview, tearoff=0)

            # Add menu items
            self.app.property_menu.add_command(label="View Details", command=self.view_selected_property)
            self.app.property_menu.add_command(label="Generate Letter", command=self.generate_letter_for_selected)
            self.app.property_menu.add_separator()
            self.app.property_menu.add_command(label="Export to CSV", command=self.export_properties_to_csv)

            # Add the right-click handler
            self.app.property_treeview.bind("<Button-3>", self.show_property_menu)
        except Exception as e:
            print(f"Error adding property treeview menu: {e}")

    def show_property_menu(self, event):
        """
        Show the property menu on right-click

        Args:
            event: The event that triggered this handler
        """
        try:
            # Select the item under the cursor
            item = self.app.property_treeview.identify_row(event.y)
            if item:
                self.app.property_treeview.selection_set(item)
                self.app.property_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"Error showing property menu: {e}")

    def view_selected_property(self):
        """
        View the selected property
        """
        try:
            # Get the selected item
            item = self.app.property_treeview.selection()[0]

            # Get the property index
            index = int(self.app.property_treeview.item(item, "text")) - 1

            # Get the property data
            property_data = self.app.property_list[index]

            # Display the property details
            self.display_property_details(property_data)
        except Exception as e:
            print(f"Error viewing selected property: {e}")

    def generate_letter_for_selected(self):
        """
        Generate a letter for the selected property
        """
        try:
            # Get the selected item
            item = self.app.property_treeview.selection()[0]

            # Get the property index
            index = int(self.app.property_treeview.item(item, "text")) - 1

            # Get the property data
            property_data = self.app.property_list[index]

            # Display the property details
            self.display_property_details(property_data)

            # Generate the letter
            self.app.generate_letter()
        except Exception as e:
            print(f"Error generating letter for selected property: {e}")

    def export_properties_to_csv(self):
        """
        Export properties to a CSV file
        """
        try:
            # Use the PropertyUIHelpers class to export the properties
            self.app.property_ui_helpers.export_properties_to_csv(self.app.root, self.app.property_list)
        except Exception as e:
            print(f"Error exporting properties to CSV: {e}")

    def determine_gender(self, name):
        """
        Determine the gender of a person from their name

        Args:
            name (str): The person's name

        Returns:
            str: 'male', 'female', or 'unknown'
        """
        try:
            # Check if we have GPT available and it's enabled
            if hasattr(self.app, 'gpt') and self.app.gpt and hasattr(self.app, 'use_gpt_gender') and self.app.use_gpt_gender.get():
                # Try to use GPT to determine gender
                gender = self.app.gpt.determine_gender(name)
                if gender:
                    return gender

            # If GPT is not available or failed, use rule-based approach
            # Check if the name is in the cache
            if hasattr(self.app, '_gender_cache') and name in self.app._gender_cache:
                return self.app._gender_cache[name]

            # Check if the name is in the cache manager
            if hasattr(self.app, 'cache_manager') and self.app.cache_manager.get('gender', name):
                return self.app.cache_manager.get('gender', name)

            # Use rule-based approach for Czech names
            # Most Czech female surnames end with 'ová'
            if name.lower().endswith('ová'):
                gender = 'female'
            # Most Czech male first names end with consonants
            elif any(name.lower().endswith(c) for c in 'bcdfghjklmnpqrstvwxz'):
                gender = 'male'
            else:
                gender = 'unknown'

            # Cache the result
            if hasattr(self.app, '_gender_cache'):
                self.app._gender_cache[name] = gender

            # Cache the result in the cache manager
            if hasattr(self.app, 'cache_manager'):
                self.app.cache_manager.set('gender', name, gender)

            return gender
        except Exception as e:
            print(f"Error determining gender: {e}")
            return 'unknown'
