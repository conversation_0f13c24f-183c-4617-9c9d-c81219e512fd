# Czech Property Registry Tests

This directory contains tests for the Czech Property Registry application.

## Test Files

- `test_osm_cuzk_integration.py`: Tests the integration between OpenStreetMap and CUZK
- `test_batch_operations.py`: Tests the batch operations functionality
- `test_smart_batch_search.py`: Tests the smart batch search functionality with random addresses and radius
- `run_osm_cuzk_test.py`: Simple script to test OSM and CUZK integration
- `run_smart_batch_test.py`: <PERSON>ript to run the smart batch search test
- `run_all_tests.py`: Script to run all tests

## Running Tests

### Running All Tests

To run all tests, use the following command:

```bash
python tests/run_all_tests.py
```

### Running a Specific Test

To run a specific test module, use the following command:

```bash
python tests/run_all_tests.py test_osm_cuzk_integration
```

Replace `test_osm_cuzk_integration` with the name of the test module you want to run.

### Running a Simple Integration Test

To run a simple integration test that fetches buildings from OSM and opens the CUZK website, use the following command:

```bash
python tests/run_osm_cuzk_test.py
```

This script will:
1. Fetch buildings from OpenStreetMap at a predefined location (Prague)
2. Find buildings with RUIAN references
3. Ask if you want to open the CUZK website for a building
4. Open the CUZK website if you confirm

### Running the Smart Batch Search Test

To run the smart batch search test that verifies the functionality of searching for properties by address with a specified radius, use the following command:

```bash
python tests/run_smart_batch_test.py
```

This script will:
1. Test searching for properties using a random Czech address and radius
2. Verify that RUIAN IDs are found and processed correctly
3. Test handling of invalid radius input

## Test Coverage

The tests cover the following functionality:

1. **OSM Integration**:
   - Fetching buildings from OpenStreetMap
   - Extracting RUIAN references from buildings

2. **CUZK Integration**:
   - Generating CUZK URLs from coordinates
   - Opening the CUZK website with RUIAN IDs
   - Verifying CUZK website accessibility

3. **Batch Operations**:
   - Selecting multiple buildings
   - Opening multiple buildings in CUZK
   - Fetching owner information for multiple buildings
   - Exporting property information to CSV
   - Smart batch search with address and radius selection
   - Finding RUIAN IDs for buildings in a specified area

4. **UI Components**:
   - Progress dialog for batch operations
   - Settings dialog for configuring batch size

## Adding New Tests

To add a new test:

1. Create a new file named `test_*.py` in the tests directory
2. Import the necessary modules
3. Create a test class that inherits from `unittest.TestCase`
4. Add test methods that start with `test_`
5. Run the tests using `run_all_tests.py`

Example:

```python
import unittest

class TestMyFeature(unittest.TestCase):
    def test_my_feature(self):
        # Test code here
        self.assertTrue(True)
```
