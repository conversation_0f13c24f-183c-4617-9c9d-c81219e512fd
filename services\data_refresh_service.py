"""
Data Refresh Service for the Czech Property Registry application.

This service is responsible for refreshing data from external APIs
and storing it in JSON files for faster access. It ensures data is
fresh while minimizing API calls.
"""

import os
import json
import time
import logging
import threading
import datetime
from typing import Dict, Any, List, Optional, Callable

# Configure logging
logger = logging.getLogger(__name__)

class DataRefreshService:
    """
    Service for refreshing data from external APIs and storing it in JSON files.

    This service implements a hybrid approach:
    1. Data is stored in JSON files for fast access
    2. Data is refreshed from APIs on a configurable schedule
    3. Data freshness is tracked and reported to the UI
    """

    def __init__(self, app):
        """
        Initialize the data refresh service.

        Args:
            app: The main application instance
        """
        self.app = app
        self.data_dir = "czech_data"
        self.refresh_thread = None
        self.stop_refresh = False
        self.refresh_interval = 24 * 60 * 60  # Default: 24 hours in seconds
        self.last_refresh = {}
        self.refresh_in_progress = {}
        self.refresh_lock = threading.Lock()
        self.always_prefer_api = False  # Changed to False to prioritize cache for static data
        self.max_cache_age = 30 * 24 * 60 * 60  # Maximum cache age in seconds (30 days for static data)

        # Define data priorities (lower number = higher priority)
        self.data_priorities = {
            "regions": 1,       # Highest priority - needed for basic UI
            "property_types": 2, # High priority - needed for property search
            "cities": 3,        # Medium priority
            "districts": 4,     # Lower priority
            "cadastral_areas": 5, # Lower priority
            "streets": 6        # Lowest priority
        }

        # Define cache expiration times for different data types (in seconds)
        # Static data can have longer expiration times
        self.cache_expiration = {
            "regions": 90 * 24 * 60 * 60,      # 90 days for regions (very static)
            "cities": 60 * 24 * 60 * 60,       # 60 days for cities (very static)
            "districts": 60 * 24 * 60 * 60,    # 60 days for districts (very static)
            "cadastral_areas": 30 * 24 * 60 * 60, # 30 days for cadastral areas (somewhat static)
            "property_types": 30 * 24 * 60 * 60,  # 30 days for property types (somewhat static)
            "streets": 14 * 24 * 60 * 60       # 14 days for streets (more likely to change)
        }

        # Track which data types have been loaded
        self.loaded_data_types = set()

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # Load refresh timestamps
        self._load_refresh_timestamps()

    def _load_refresh_timestamps(self):
        """Load the last refresh timestamps from file."""
        timestamp_file = os.path.join(self.data_dir, "refresh_timestamps.json")
        if os.path.exists(timestamp_file):
            try:
                with open(timestamp_file, 'r', encoding='utf-8') as f:
                    self.last_refresh = json.load(f)
                logger.info(f"Loaded refresh timestamps: {self.last_refresh}")
            except Exception as e:
                logger.error(f"Error loading refresh timestamps: {e}")
                self.last_refresh = {}

    def _save_refresh_timestamps(self):
        """Save the last refresh timestamps to file."""
        timestamp_file = os.path.join(self.data_dir, "refresh_timestamps.json")
        try:
            with open(timestamp_file, 'w', encoding='utf-8') as f:
                json.dump(self.last_refresh, f, indent=2)
            logger.info(f"Saved refresh timestamps: {self.last_refresh}")
        except Exception as e:
            logger.error(f"Error saving refresh timestamps: {e}")

    def start_refresh_thread(self):
        """Start the background refresh thread."""
        if self.refresh_thread is None or not self.refresh_thread.is_alive():
            self.stop_refresh = False
            self.refresh_thread = threading.Thread(
                target=self._refresh_thread,
                daemon=True
            )
            self.refresh_thread.start()
            logger.info("Started data refresh thread")

    def stop_refresh_thread(self):
        """Stop the background refresh thread."""
        self.stop_refresh = True
        if self.refresh_thread and self.refresh_thread.is_alive():
            self.refresh_thread.join(timeout=1.0)
            logger.info("Stopped data refresh thread")

    def _refresh_thread(self):
        """Background thread for refreshing data."""
        while not self.stop_refresh:
            try:
                # Check if any data needs refreshing
                self._check_and_refresh_data()

                # Sleep for a while before checking again
                for _ in range(60):  # Check every minute if we should stop
                    if self.stop_refresh:
                        break
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Error in refresh thread: {e}")
                time.sleep(60)  # Sleep for a minute before retrying

    def _check_and_refresh_data(self):
        """Check if any data needs refreshing and refresh it if needed."""
        # Data types to refresh, sorted by priority
        data_types = sorted(
            [
                "regions", "cities", "districts", "cadastral_areas",
                "property_types", "streets"
            ],
            key=lambda x: self.data_priorities.get(x, 999)
        )

        for data_type in data_types:
            # Skip if refresh is already in progress for this data type
            if self.refresh_in_progress.get(data_type, False):
                continue

            # Check if data should be refreshed from API
            if self._should_refresh_from_api(data_type):
                logger.info(f"Scheduling refresh of {data_type} data from API")
                # Start refresh in a separate thread to avoid blocking
                threading.Thread(
                    target=self._refresh_data,
                    args=(data_type,),
                    daemon=True
                ).start()
            else:
                logger.debug(f"Data for {data_type} is still fresh, no refresh needed")

    def load_essential_data(self):
        """
        Load only essential data needed for the application to function.
        This is called during application startup to minimize loading time.

        For static data, we prioritize loading from cache for speed.

        NOTE: This method now only loads from cache and does not trigger API calls
        to prevent automatic data loading at startup.
        """
        logger.info("Loading essential data from cache only")

        # Create a loading indicator if the app has a root window
        loading_indicator = None
        if hasattr(self.app, 'root'):
            try:
                # Import the loading indicator
                from ui.loading_indicator import LoadingIndicator
                loading_indicator = LoadingIndicator(self.app.root, "Loading Data", "Loading essential data...")
                logger.info("Created loading indicator for essential data")
            except Exception as e:
                logger.error(f"Error creating loading indicator: {e}")

        # Only load high priority data types initially
        high_priority_types = ["regions", "property_types"]

        for data_type in high_priority_types:
            # Skip if already loaded or in progress
            if data_type in self.loaded_data_types or self.refresh_in_progress.get(data_type, False):
                continue

            # Update the loading indicator
            if loading_indicator:
                loading_indicator.update(f"Loading {data_type}...", data_type, "Loading")

            # Try to load from cache only - no API calls
            logger.info(f"Loading essential data: {data_type} (from cache only)")

            try:
                # Check if we have a file for this data type
                file_path = os.path.join(self.data_dir, f"czech_{data_type}.json")
                if data_type == "property_types":
                    file_path = os.path.join(self.data_dir, "property_types.json")

                if os.path.exists(file_path):
                    # Load from file
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # Update the last refresh timestamp if not already set
                    if data_type not in self.last_refresh:
                        # Use file modification time as last refresh time
                        self.last_refresh[data_type] = os.path.getmtime(file_path)
                        self._save_refresh_timestamps()

                    logger.info(f"Loaded {data_type} from cache file: {file_path}")

                    # Mark as loaded
                    self.loaded_data_types.add(data_type)

                    # No background refresh - we'll load data when needed
                else:
                    # No cache file, but we won't load from API automatically
                    logger.info(f"No cache file for {data_type}, but skipping API load at startup")
                    # Mark as not loaded so it will be loaded when needed
                    if data_type in self.loaded_data_types:
                        self.loaded_data_types.remove(data_type)
            except Exception as e:
                logger.error(f"Error loading {data_type} from cache: {e}")
                # Don't fall back to API - we'll load when needed
                if data_type in self.loaded_data_types:
                    self.loaded_data_types.remove(data_type)

            # Update the loading indicator
            if loading_indicator:
                loading_indicator.update(None, data_type, "Loaded")

        # Close the loading indicator
        if loading_indicator:
            loading_indicator.close()

        logger.info("Essential data loading from cache complete")

    def load_remaining_data_in_background(self):
        """
        Load all remaining data in the background after the application has started.
        This is called after the UI is displayed to improve perceived performance.

        For static data, we prioritize loading from cache for speed and only
        check against the API in the background if needed.

        NOTE: This method is now disabled to prevent automatic data loading at startup.
        Data will only be loaded when explicitly requested.
        """
        logger.info("Background data loading is disabled to prevent automatic data loading at startup")

        # Show a notification that background loading is disabled
        if hasattr(self.app, 'notification_banner'):
            self.app.notification_banner.show(
                message="Background data loading is disabled. Data will be loaded when needed.",
                notification_type="info",
                auto_hide=True,
                duration=5000,
                key="background_loading_disabled"
            )

        # Reset status
        if hasattr(self.app, 'show_status'):
            self.app.show_status("Ready")

    def _refresh_data(self, data_type: str):
        """
        Refresh data for a specific data type.

        NOTE: This method has been modified to prevent automatic API calls during startup.
        It will only run when explicitly requested by the user.

        Args:
            data_type: Type of data to refresh
        """
        # Check if this is being called during startup
        if hasattr(self.app, '_is_startup') and self.app._is_startup:
            logger.info(f"Skipping {data_type} refresh during startup")
            return

        with self.refresh_lock:
            self.refresh_in_progress[data_type] = True

        try:
            # Log whether we're using API data
            if self.always_prefer_api:
                logger.info(f"Refreshing {data_type} data from API (always_prefer_api=True)")
            else:
                logger.info(f"Refreshing {data_type} data")

            # Call the appropriate refresh method based on data type
            if data_type == "regions":
                success = self._refresh_regions()
            elif data_type == "cities":
                success = self._refresh_cities()
            elif data_type == "districts":
                success = self._refresh_districts()
            elif data_type == "cadastral_areas":
                success = self._refresh_cadastral_areas()
            elif data_type == "property_types":
                success = self._refresh_property_types()
            elif data_type == "streets":
                success = self._refresh_streets()
            else:
                logger.warning(f"Unknown data type: {data_type}")
                success = False

            # Update refresh timestamp if successful
            if success:
                self.last_refresh[data_type] = time.time()
                self._save_refresh_timestamps()
                logger.info(f"Successfully refreshed {data_type} data from API")

                # Show notification that API data is being used
                if hasattr(self.app, 'show_api_data_notification'):
                    self.app.show_api_data_notification(data_type)
            else:
                logger.warning(f"Failed to refresh {data_type} data from API")

        except Exception as e:
            logger.error(f"Error refreshing {data_type} data from API: {e}")

        finally:
            with self.refresh_lock:
                self.refresh_in_progress[data_type] = False

    def get_data_freshness(self, data_type: str) -> Dict[str, Any]:
        """
        Get the freshness information for a specific data type.

        Args:
            data_type: Type of data to check

        Returns:
            dict: Dictionary with freshness information
        """
        last_refresh = self.last_refresh.get(data_type, 0)
        current_time = time.time()
        age_seconds = current_time - last_refresh

        if last_refresh == 0:
            status = "unknown"
        elif age_seconds > self.max_cache_age:
            status = "expired"
        elif age_seconds > self.refresh_interval:
            status = "stale"
        else:
            status = "fresh"

        return {
            "status": status,
            "last_refresh": last_refresh,
            "age_seconds": age_seconds,
            "age_hours": age_seconds / 3600,
            "age_days": age_seconds / (3600 * 24),
            "refresh_in_progress": self.refresh_in_progress.get(data_type, False),
            "should_refresh_from_api": self._should_refresh_from_api(data_type)
        }

    def _should_refresh_from_api(self, data_type: str) -> bool:
        """
        Determine if data should be refreshed from API rather than using cached data.

        For static data (regions, cities, etc.), we prefer cache and only refresh
        in the background if the data is stale or expired.

        Args:
            data_type: Type of data to check

        Returns:
            bool: True if data should be refreshed from API, False otherwise
        """
        # Always prefer API if the flag is set (but this is now False by default)
        if self.always_prefer_api:
            logger.info(f"Always preferring API data for {data_type} (always_prefer_api=True)")
            return True

        # Check if data is already being refreshed
        if self.refresh_in_progress.get(data_type, False):
            logger.info(f"Refresh already in progress for {data_type}")
            return False

        # Check if data has never been refreshed (no cache available)
        last_refresh = self.last_refresh.get(data_type, 0)
        if last_refresh == 0:
            logger.info(f"Data for {data_type} has never been refreshed, using API")
            return True

        # Get the appropriate cache expiration time for this data type
        expiration_time = self.cache_expiration.get(data_type, self.refresh_interval)

        # Check if data is expired
        current_time = time.time()
        age_seconds = current_time - last_refresh

        if age_seconds > expiration_time:
            logger.info(f"Data for {data_type} is expired (age: {age_seconds/86400:.1f} days, expiration: {expiration_time/86400:.1f} days), using API")
            return True

        # For static data types, we don't need to refresh as frequently
        if data_type in ["regions", "cities", "districts", "cadastral_areas"]:
            logger.info(f"Data for {data_type} is static and still valid (age: {age_seconds/86400:.1f} days), using cache")
            return False

        # For other data types, check if data is stale
        if age_seconds > self.refresh_interval:
            logger.info(f"Data for {data_type} is stale (age: {age_seconds/86400:.1f} days), using API")
            return True

        # Data is fresh, using cache
        logger.info(f"Data for {data_type} is fresh (age: {age_seconds/86400:.1f} days), using cache")
        return False

    def force_refresh(self, data_type: str, use_api: bool = True) -> bool:
        """
        Force a refresh of a specific data type.

        Args:
            data_type: Type of data to refresh
            use_api: Whether to force using API data (default: True)

        Returns:
            bool: True if refresh was started, False otherwise
        """
        # Skip if refresh is already in progress for this data type
        if self.refresh_in_progress.get(data_type, False):
            logger.warning(f"Refresh already in progress for {data_type}")
            return False

        # Log the refresh request
        logger.info(f"Forcing refresh of {data_type} data (use_api={use_api})")

        # Temporarily set the always_prefer_api flag if requested
        original_value = self.always_prefer_api
        if use_api:
            self.always_prefer_api = True

        try:
            # Start refresh in a separate thread
            thread = threading.Thread(
                target=self._refresh_data,
                args=(data_type,),
                daemon=True
            )
            thread.start()

            return True
        finally:
            # Restore the original value after starting the thread
            if use_api:
                self.always_prefer_api = original_value

    def _refresh_regions(self) -> bool:
        """
        Refresh regions data from Google Maps API.

        NOTE: This method has been modified to prevent automatic API calls during startup.
        It will only run when explicitly requested by the user.

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if this is being called during startup
        if hasattr(self.app, '_is_startup') and self.app._is_startup:
            logger.info("Skipping regions refresh during startup")
            return False

        try:
            # Check if Google Maps API is available
            if not hasattr(self.app, 'google_maps') or not self.app.google_maps:
                logger.error("Google Maps API not available")
                return False

            # Get regions from Google Maps API
            regions = self.app.google_maps.get_czech_regions()
            if not regions:
                logger.warning("No regions returned from Google Maps API")
                return False

            # Create data structure
            regions_data = {
                "regions_english": regions,
                "regions_czech": self._translate_regions_to_czech(regions),
                "region_mapping": self.app.google_maps.get_region_name_mapping(),
                "last_updated": datetime.datetime.now().isoformat()
            }

            # Save to file
            file_path = os.path.join(self.data_dir, "czech_regions.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(regions_data, f, indent=2)

            logger.info(f"Saved {len(regions)} regions to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error refreshing regions data: {e}")
            return False

    def _translate_regions_to_czech(self, english_regions: List[str]) -> List[str]:
        """
        Translate English region names to Czech.

        Args:
            english_regions: List of English region names

        Returns:
            list: List of Czech region names
        """
        # Get region mapping (English to Czech)
        if hasattr(self.app, 'google_maps') and self.app.google_maps:
            mapping = self.app.google_maps.get_region_name_mapping()

            # Invert the mapping (Czech to English -> English to Czech)
            inverted_mapping = {}
            for czech, english in mapping.items():
                inverted_mapping[english.lower()] = czech.title()

            # Translate regions
            czech_regions = []
            for region in english_regions:
                czech_region = inverted_mapping.get(region.lower())
                if czech_region:
                    czech_regions.append(czech_region)
                else:
                    # If no mapping found, use the English name
                    czech_regions.append(region)

            return czech_regions

        # If no mapping available, return the English names
        return english_regions

    def _refresh_cities(self) -> bool:
        """
        Refresh cities data from Google Maps API.

        NOTE: This method has been modified to prevent automatic API calls during startup.
        It will only run when explicitly requested by the user.

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if this is being called during startup
        if hasattr(self.app, '_is_startup') and self.app._is_startup:
            logger.info("Skipping cities refresh during startup")
            return False

        try:
            # Check if Google Maps API is available
            if not hasattr(self.app, 'google_maps') or not self.app.google_maps:
                logger.error("Google Maps API not available")
                return False

            # Get regions first
            regions = self.app.google_maps.get_czech_regions()
            if not regions:
                logger.warning("No regions available for city refresh")
                return False

            # Get cities for each region
            cities_by_region = {}
            for region in regions:
                cities = self.app.google_maps.get_cities_in_region(region)
                if cities:
                    cities_by_region[region] = cities
                    logger.info(f"Got {len(cities)} cities for {region}")
                else:
                    logger.warning(f"No cities returned for {region}")

            # Create data structure
            cities_data = {
                "cities_by_region": cities_by_region,
                "last_updated": datetime.datetime.now().isoformat()
            }

            # Save to file
            file_path = os.path.join(self.data_dir, "czech_cities.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cities_data, f, indent=2)

            logger.info(f"Saved cities for {len(cities_by_region)} regions to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error refreshing cities data: {e}")
            return False

    def _refresh_districts(self) -> bool:
        """
        Refresh districts data from Google Maps API.

        NOTE: This method has been modified to prevent automatic API calls during startup.
        It will only run when explicitly requested by the user.

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if this is being called during startup
        if hasattr(self.app, '_is_startup') and self.app._is_startup:
            logger.info("Skipping districts refresh during startup")
            return False

        try:
            # Check if Google Maps API is available
            if not hasattr(self.app, 'google_maps') or not self.app.google_maps:
                logger.error("Google Maps API not available")
                return False

            # Get cities first (we need to get districts for each city)
            cities_data = {}
            cities_file_path = os.path.join(self.data_dir, "czech_cities.json")
            if os.path.exists(cities_file_path):
                try:
                    with open(cities_file_path, 'r', encoding='utf-8') as f:
                        cities_data = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading cities data: {e}")
                    return False

            # If no cities data, refresh cities first
            if not cities_data or "cities_by_region" not in cities_data:
                logger.info("No cities data available, refreshing cities first")
                if not self._refresh_cities():
                    logger.error("Failed to refresh cities data")
                    return False

                # Load the newly refreshed cities data
                try:
                    with open(cities_file_path, 'r', encoding='utf-8') as f:
                        cities_data = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading refreshed cities data: {e}")
                    return False

            # Get districts for each city in each region
            districts_by_region = {}
            districts_by_city = {}

            # Process each region and its cities
            for region, cities in cities_data.get("cities_by_region", {}).items():
                districts_by_region[region] = []

                for city in cities:
                    # Use Google Maps API to get districts in the city
                    try:
                        # Construct a query for districts in the city
                        query = f"districts in {city}, {region}, Czech Republic"

                        # Use the Places API to find districts
                        url = "https://maps.googleapis.com/maps/api/place/textsearch/json"
                        params = {
                            "query": query,
                            "key": self.app.google_maps.api_key
                        }

                        data = self.app.google_maps.make_api_request(url, params)

                        if data and data.get('status') == 'OK':
                            # Extract district names from the results
                            districts = []
                            for result in data.get('results', []):
                                district_name = result.get('name')
                                if district_name and district_name not in districts:
                                    districts.append(district_name)

                            # Add to the dictionaries
                            if districts:
                                districts_by_city[city] = districts
                                # Add unique districts to the region list
                                for district in districts:
                                    if district not in districts_by_region[region]:
                                        districts_by_region[region].append(district)

                                logger.info(f"Got {len(districts)} districts for {city}")
                            else:
                                logger.warning(f"No districts returned for {city}")
                        else:
                            logger.warning(f"Failed to get districts for {city}: {data.get('status')}")
                    except Exception as e:
                        logger.error(f"Error getting districts for {city}: {e}")
                        # Continue with the next city
                        continue

                    # Add a small delay to avoid rate limiting
                    time.sleep(0.5)

            # Create data structure
            districts_data = {
                "districts_by_region": districts_by_region,
                "districts_by_city": districts_by_city,
                "last_updated": datetime.datetime.now().isoformat()
            }

            # Save to file
            file_path = os.path.join(self.data_dir, "czech_districts.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(districts_data, f, indent=2)

            # Count total districts
            total_districts = sum(len(districts) for districts in districts_by_city.values())
            logger.info(f"Saved {total_districts} districts for {len(districts_by_city)} cities to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error refreshing districts data: {e}")
            return False

    def _refresh_cadastral_areas(self) -> bool:
        """
        Refresh cadastral areas data from RUIAN API.

        NOTE: This method has been modified to prevent automatic URL generation during startup.
        It will only run when explicitly requested by the user.

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if this is being called during startup
        if hasattr(self.app, '_is_startup') and self.app._is_startup:
            logger.info("Skipping cadastral areas refresh during startup")
            return False

        try:
            # Check if CUZK integration is available
            if not hasattr(self.app, 'cuzk_integration') or not self.app.cuzk_integration:
                logger.error("CUZK integration not available")
                return False

            # Get cities first (we need to get cadastral areas for each city)
            cities_data = {}
            cities_file_path = os.path.join(self.data_dir, "czech_cities.json")
            if os.path.exists(cities_file_path):
                try:
                    with open(cities_file_path, 'r', encoding='utf-8') as f:
                        cities_data = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading cities data: {e}")
                    return False

            # If no cities data, refresh cities first
            if not cities_data or "cities_by_region" not in cities_data:
                logger.info("No cities data available, refreshing cities first")
                if not self._refresh_cities():
                    logger.error("Failed to refresh cities data")
                    return False

                # Load the newly refreshed cities data
                try:
                    with open(cities_file_path, 'r', encoding='utf-8') as f:
                        cities_data = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading refreshed cities data: {e}")
                    return False

            # Get cadastral areas for each city
            cadastral_areas_by_city = {}
            cadastral_areas_by_region = {}

            # Process each region and its cities
            for region, cities in cities_data.get("cities_by_region", {}).items():
                cadastral_areas_by_region[region] = []

                # Limit to a few cities per region to avoid overwhelming the API
                cities_to_process = cities[:5]  # Process only 5 cities per region

                for city in cities_to_process:
                    # Use Google Maps to get coordinates for the city
                    try:
                        # Geocode the city
                        geocode_result = self.app.google_maps.geocode_address(f"{city}, {region}, Czech Republic")

                        if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
                            logger.warning(f"Could not geocode city: {city}, {region}")
                            continue

                        lat = geocode_result['lat']
                        lng = geocode_result['lng']

                        # Convert WGS84 to S-JTSK coordinates
                        x, y = self.app.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)

                        # Use CUZK API to get cadastral area for these coordinates
                        property_data = self.app.cuzk_integration.get_property_by_coordinates(x, y)

                        if property_data:
                            # Extract cadastral area from property data
                            cadastral_area = property_data.get('cadastral_area')
                            if cadastral_area:
                                # Add to the dictionaries
                                if city not in cadastral_areas_by_city:
                                    cadastral_areas_by_city[city] = []

                                if cadastral_area not in cadastral_areas_by_city[city]:
                                    cadastral_areas_by_city[city].append(cadastral_area)

                                # Add to region list if not already there
                                if cadastral_area not in cadastral_areas_by_region[region]:
                                    cadastral_areas_by_region[region].append(cadastral_area)

                                logger.info(f"Got cadastral area '{cadastral_area}' for {city}")
                            else:
                                logger.warning(f"No cadastral area found for {city}")
                        else:
                            logger.warning(f"No property data returned for {city}")

                        # We'll skip the additional cadastral areas search to prevent excessive URL generation

                    except Exception as e:
                        logger.error(f"Error getting cadastral areas for {city}: {e}")
                        # Continue with the next city
                        continue

                    # Add a small delay to avoid overwhelming the CUZK API
                    time.sleep(1.0)

            # Create data structure
            cadastral_data = {
                "cadastral_areas_by_region": cadastral_areas_by_region,
                "cadastral_areas_by_city": cadastral_areas_by_city,
                "last_updated": datetime.datetime.now().isoformat()
            }

            # Save to file
            file_path = os.path.join(self.data_dir, "cadastral_areas.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cadastral_data, f, indent=2)

            # Count total cadastral areas
            total_areas = sum(len(areas) for areas in cadastral_areas_by_city.values())
            logger.info(f"Saved {total_areas} cadastral areas for {len(cadastral_areas_by_city)} cities to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error refreshing cadastral areas data: {e}")
            return False

    def _refresh_property_types(self) -> bool:
        """
        Refresh property types data.

        NOTE: This method has been modified to prevent automatic API calls during startup.
        It will only run when explicitly requested by the user.

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if this is being called during startup
        if hasattr(self.app, '_is_startup') and self.app._is_startup:
            logger.info("Skipping property types refresh during startup")
            return False

        try:
            # Define standard property types and their keywords
            property_types = [
                "Residential Building", "Commercial Building", "Industrial Building",
                "Agricultural Land", "Building Land", "Forest Land", "Garden", "Apartment",
                "House", "Villa", "Cottage", "Garage", "Office", "Shop", "Warehouse",
                "Factory", "Workshop", "Restaurant", "Hotel", "Pension"
            ]

            # Define keywords for property types
            property_type_keywords = {
                "residential building": ["obytná", "bytový", "rodinný", "byt", "dům"],
                "commercial building": ["komerční", "obchodní", "nebytový"],
                "building land": ["stavební", "pozemek", "parcela"],
                "agricultural land": ["zemědělský", "pole", "orná půda"],
                "forest land": ["les", "lesní"],
                "garden": ["zahrada"],
                "apartment": ["byt", "bytová jednotka"],
                "house": ["dům", "rodinný dům"],
                "villa": ["vila"],
                "cottage": ["chata", "chalupa"],
                "garage": ["garáž"]
            }

            # Create data structure
            property_types_data = {
                "property_types": property_types,
                "property_type_keywords": property_type_keywords,
                "last_updated": datetime.datetime.now().isoformat()
            }

            # Save to file
            file_path = os.path.join(self.data_dir, "property_types.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(property_types_data, f, indent=2)

            logger.info(f"Saved {len(property_types)} property types to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error refreshing property types data: {e}")
            return False

    def _refresh_streets(self) -> bool:
        """
        Refresh streets data from Google Maps API.

        NOTE: This method has been modified to prevent automatic API calls during startup.
        It will only run when explicitly requested by the user.

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if this is being called during startup
        if hasattr(self.app, '_is_startup') and self.app._is_startup:
            logger.info("Skipping streets refresh during startup")
            return False

        try:
            # Check if Google Maps API is available
            if not hasattr(self.app, 'google_maps') or not self.app.google_maps:
                logger.error("Google Maps API not available")
                return False

            # Get cities first (we need to get streets for each city)
            cities_data = {}
            cities_file_path = os.path.join(self.data_dir, "czech_cities.json")
            if os.path.exists(cities_file_path):
                try:
                    with open(cities_file_path, 'r', encoding='utf-8') as f:
                        cities_data = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading cities data: {e}")
                    return False

            # If no cities data, refresh cities first
            if not cities_data or "cities_by_region" not in cities_data:
                logger.info("No cities data available, refreshing cities first")
                if not self._refresh_cities():
                    logger.error("Failed to refresh cities data")
                    return False

                # Load the newly refreshed cities data
                try:
                    with open(cities_file_path, 'r', encoding='utf-8') as f:
                        cities_data = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading refreshed cities data: {e}")
                    return False

            # Get streets for each city
            streets_by_city = {}
            all_streets = set()  # Keep track of all unique streets

            # Process each region and its cities
            for region, cities in cities_data.get("cities_by_region", {}).items():
                # Limit to a reasonable number of cities per region to avoid overwhelming the API
                # We'll prioritize larger cities
                cities_to_process = cities[:20]  # Process up to 20 cities per region

                for city in cities_to_process:
                    # Use Google Maps API to get streets in the city
                    try:
                        # Construct a query for streets in the city
                        query = f"streets in {city}, {region}, Czech Republic"

                        # Use the Places API to find streets
                        url = "https://maps.googleapis.com/maps/api/place/textsearch/json"
                        params = {
                            "query": query,
                            "key": self.app.google_maps.api_key
                        }

                        data = self.app.google_maps.make_api_request(url, params)

                        if data and data.get('status') == 'OK':
                            # Extract street names from the results
                            streets = []
                            for result in data.get('results', []):
                                # Check if the result is likely a street
                                address = result.get('formatted_address', '')
                                name = result.get('name', '')

                                # Skip if it doesn't look like a street
                                if not self._is_likely_street(name, address):
                                    continue

                                if name and name not in streets:
                                    streets.append(name)
                                    all_streets.add(name)

                            # Add to the dictionary
                            if streets:
                                streets_by_city[city] = streets
                                logger.info(f"Got {len(streets)} streets for {city}")
                            else:
                                logger.warning(f"No streets returned for {city}")
                        else:
                            logger.warning(f"Failed to get streets for {city}: {data.get('status')}")

                        # Try an alternative approach - search for common street types
                        street_types = ["ulice", "náměstí", "třída", "nábřeží", "most"]
                        for street_type in street_types:
                            try:
                                # Construct a query for streets of this type in the city
                                query = f"{street_type} in {city}, {region}, Czech Republic"

                                params = {
                                    "query": query,
                                    "key": self.app.google_maps.api_key
                                }

                                data = self.app.google_maps.make_api_request(url, params)

                                if data and data.get('status') == 'OK':
                                    # Extract street names from the results
                                    for result in data.get('results', []):
                                        name = result.get('name', '')
                                        address = result.get('formatted_address', '')

                                        # Skip if it doesn't look like a street
                                        if not self._is_likely_street(name, address):
                                            continue

                                        if name and name not in streets_by_city.get(city, []):
                                            if city not in streets_by_city:
                                                streets_by_city[city] = []

                                            streets_by_city[city].append(name)
                                            all_streets.add(name)

                                    logger.info(f"Got additional streets of type '{street_type}' for {city}")
                            except Exception as e:
                                logger.error(f"Error getting streets of type '{street_type}' for {city}: {e}")
                                continue

                            # Add a small delay to avoid rate limiting
                            time.sleep(0.5)

                    except Exception as e:
                        logger.error(f"Error getting streets for {city}: {e}")
                        # Continue with the next city
                        continue

                    # Add a small delay to avoid rate limiting
                    time.sleep(1.0)

            # Create data structure
            streets_data = {
                "streets_by_city": streets_by_city,
                "all_streets": list(all_streets),
                "last_updated": datetime.datetime.now().isoformat()
            }

            # Save to file
            file_path = os.path.join(self.data_dir, "czech_streets.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(streets_data, f, indent=2)

            # Also save to addresses.json for backward compatibility
            addresses_data = {
                "streets": list(all_streets),
                "last_updated": datetime.datetime.now().isoformat()
            }
            addresses_file_path = os.path.join(self.data_dir, "addresses.json")
            with open(addresses_file_path, 'w', encoding='utf-8') as f:
                json.dump(addresses_data, f, indent=2)

            logger.info(f"Saved {len(all_streets)} unique streets for {len(streets_by_city)} cities to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error refreshing streets data: {e}")
            return False

    def set_api_preference(self, prefer_api: bool = True) -> None:
        """
        Set whether to always prefer API data over cached data.

        Args:
            prefer_api: Whether to always prefer API data (default: True)
        """
        previous_value = self.always_prefer_api
        self.always_prefer_api = prefer_api
        logger.info(f"API preference changed: {previous_value} -> {prefer_api}")

        # If we're now preferring API, trigger a check for stale data
        if prefer_api and not previous_value:
            logger.info("Now preferring API data, checking for stale data to refresh")
            threading.Thread(
                target=self._check_and_refresh_data,
                daemon=True
            ).start()

    def _is_likely_street(self, name: str, address: str) -> bool:
        """
        Check if a name is likely to be a street.

        Args:
            name: Name to check
            address: Full address

        Returns:
            bool: True if the name is likely a street, False otherwise
        """
        # Common Czech street indicators
        street_indicators = ["ulice", "náměstí", "třída", "nábřeží", "most", "alej", "bulvár", "cesta"]

        # Check if any indicator is in the name or address
        name_lower = name.lower()
        address_lower = address.lower()

        for indicator in street_indicators:
            if indicator in name_lower or indicator in address_lower:
                return True

        # Check if the name contains numbers (common for addresses)
        if any(char.isdigit() for char in name):
            return False

        # Check if the name is too short (likely not a street)
        if len(name) < 5:
            return False

        # If we're not sure, assume it's a street
        return True
