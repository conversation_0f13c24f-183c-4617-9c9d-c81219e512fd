"""
Run the smart batch search test.

This script runs the smart batch search test to verify that the application
can search for properties by address with a specified radius and find RUIAN IDs.
"""

import os
import sys
import logging
import unittest

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RunSmartBatchTest")

# Import the test module
from tests.test_smart_batch_search import TestSmartBatchSearch


def run_test():
    """Run the smart batch search test."""
    logger.info("Running smart batch search test...")

    # Create a test suite with the smart batch search test
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestSmartBatchSearch)

    # Run the test suite
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)

    # Log the results
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Failures: {len(result.failures)}")
    logger.info(f"Skipped: {len(result.skipped)}")

    # Return True if all tests passed
    return len(result.errors) == 0 and len(result.failures) == 0


if __name__ == '__main__':
    success = run_test()
    sys.exit(0 if success else 1)
