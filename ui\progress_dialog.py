"""
Progress dialog for the Czech Property Registry application.

This module provides a progress dialog for batch operations.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time


class ProgressDialog:
    """
    A progress dialog for batch operations.
    
    This dialog shows progress for batch operations and allows cancellation.
    It supports both determinate and indeterminate progress modes.
    """
    
    def __init__(self, parent, title="Processing", message="Please wait...", 
                 max_value=100, determinate=True, cancelable=True):
        """
        Initialize the progress dialog.
        
        Args:
            parent: Parent window
            title (str): Dialog title
            message (str): Message to display
            max_value (int): Maximum value for the progress bar
            determinate (bool): Whether to use determinate mode
            cancelable (bool): Whether the operation can be canceled
        """
        self.parent = parent
        self.title = title
        self.message = message
        self.max_value = max_value
        self.determinate = determinate
        self.cancelable = cancelable
        
        # Create variables
        self.current_value = 0
        self.current_item = ""
        self.canceled = False
        self.dialog = None
        self.progress_bar = None
        self.message_label = None
        self.item_label = None
        
        # Create the dialog
        self._create_dialog()
        
    def _create_dialog(self):
        """Create the progress dialog."""
        # Create the dialog window
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x150")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.focus_set()
        self.dialog.resizable(False, False)
        
        # Make the dialog modal
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Create a frame for the progress bar
        progress_frame = ttk.Frame(self.dialog, padding=10)
        progress_frame.pack(fill="both", expand=True)
        
        # Create a label for the message
        self.message_label = ttk.Label(progress_frame, text=self.message)
        self.message_label.pack(pady=5, anchor="w")
        
        # Create a label for the current item
        self.item_label = ttk.Label(progress_frame, text="")
        self.item_label.pack(pady=2, anchor="w")
        
        # Create a progress bar
        mode = "determinate" if self.determinate else "indeterminate"
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            mode=mode, 
            maximum=self.max_value,
            length=380
        )
        self.progress_bar.pack(fill="x", pady=10)
        
        # Start the progress bar in indeterminate mode
        if not self.determinate:
            self.progress_bar.start()
        
        # Create a cancel button if cancelable
        if self.cancelable:
            button_frame = ttk.Frame(progress_frame)
            button_frame.pack(fill="x", pady=5)
            
            cancel_button = ttk.Button(
                button_frame, 
                text="Cancel", 
                command=self.cancel
            )
            cancel_button.pack(side="right")
    
    def update(self, value=None, message=None, item=None):
        """
        Update the progress dialog.
        
        Args:
            value (int, optional): New progress value
            message (str, optional): New message
            item (str, optional): Current item being processed
        """
        if self.dialog is None or not self.dialog.winfo_exists():
            return
            
        # Update the progress value
        if value is not None and self.determinate:
            self.current_value = value
            self.progress_bar["value"] = value
            
        # Update the message
        if message is not None:
            self.message = message
            self.message_label.config(text=message)
            
        # Update the current item
        if item is not None:
            self.current_item = item
            self.item_label.config(text=f"Processing: {item}")
            
        # Update the dialog
        self.dialog.update_idletasks()
    
    def cancel(self):
        """Cancel the operation."""
        self.canceled = True
        
        # Update the message
        self.message_label.config(text="Canceling...")
        
        # Disable the cancel button
        for widget in self.dialog.winfo_children():
            if isinstance(widget, ttk.Button):
                widget.config(state="disabled")
    
    def is_canceled(self):
        """Check if the operation was canceled."""
        return self.canceled
    
    def close(self):
        """Close the progress dialog."""
        if self.dialog is not None and self.dialog.winfo_exists():
            self.dialog.destroy()
            self.dialog = None
    
    def _on_close(self):
        """Handle the close button."""
        if self.cancelable:
            self.cancel()
        
    def __enter__(self):
        """Support for context manager."""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Support for context manager."""
        self.close()
