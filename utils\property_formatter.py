"""
Property formatting utilities for the Czech Property Registry application.
"""

from typing import Dict, Any, List, Optional


class PropertyDetailsFormatter:
    """
    Class for formatting property details in various formats.
    """

    @staticmethod
    def format_property_for_display(property_data: Dict[str, Any], index: int = 0) -> str:
        """
        Format property information for display in the UI.

        Args:
            property_data (dict): Property data dictionary
            index (int): Index of the property in a list (for numbering)

        Returns:
            str: Formatted property information
        """
        # Extract property information with defaults
        owner_name = property_data.get('owner_name', 'Unknown')
        owner_address = property_data.get('owner_address', 'Unknown')
        prop_type = property_data.get('property_type', 'Unknown')
        parcel_number = property_data.get('parcel_number', 'Unknown')
        cadastral_territory = property_data.get('cadastral_territory', 'Unknown')

        # Build the result text
        result_text = f"Property {index+1}:\n"
        result_text += f"Owner: {owner_name}\n"
        result_text += f"Address: {owner_address}\n"
        result_text += f"Type: {prop_type}\n"
        result_text += f"Parcel: {parcel_number}\n"
        result_text += f"Cadastral Territory: {cadastral_territory}\n\n"

        return result_text

    @staticmethod
    def format_property_for_letter(property_data: Dict[str, Any], include_details: bool = True) -> str:
        """
        Format property information for inclusion in a letter.

        Args:
            property_data (dict): Property data dictionary
            include_details (bool): Whether to include detailed property information

        Returns:
            str: Formatted property information for a letter
        """
        # Extract property information with defaults
        owner_name = property_data.get('owner_name', 'Unknown')
        owner_address = property_data.get('owner_address', 'Unknown')
        prop_type = property_data.get('property_type', 'Unknown')
        parcel_number = property_data.get('parcel_number', 'Unknown')
        cadastral_territory = property_data.get('cadastral_territory', 'Unknown')

        # Build the result text
        result_text = f"Property Owner: {owner_name}\n"
        result_text += f"Address: {owner_address}\n"

        if include_details:
            result_text += f"Property Type: {prop_type}\n"
            result_text += f"Parcel Number: {parcel_number}\n"
            result_text += f"Cadastral Territory: {cadastral_territory}\n"

        return result_text

    @staticmethod
    def format_property_for_csv(property_data: Dict[str, Any]) -> List[str]:
        """
        Format property information for CSV export.

        Args:
            property_data (dict): Property data dictionary

        Returns:
            list: List of property values in the order for CSV export
        """
        return [
            property_data.get('owner_name', 'Unknown'),
            property_data.get('owner_address', 'Unknown'),
            property_data.get('property_type', 'Unknown'),
            property_data.get('parcel_number', 'Unknown'),
            property_data.get('cadastral_territory', 'Unknown'),
            property_data.get('ownership_share', 'Unknown'),
            property_data.get('lv_number', 'Unknown'),
            property_data.get('additional_notes', '')
        ]

    @staticmethod
    def format_building_for_display(building: Dict[str, Any]) -> Dict[str, str]:
        """
        Format building information from OSM for display.

        Args:
            building (dict): Building data from OpenStreetMap

        Returns:
            dict: Formatted building information
        """
        tags = building.get('tags', {})
        
        # Format address
        address = ""
        if 'addr:street' in tags and 'addr:housenumber' in tags:
            address = f"{tags['addr:street']} {tags['addr:housenumber']}"
        if 'addr:city' in tags:
            address += f", {tags['addr:city']}"
        if not address:
            address = "Unknown"
            
        return {
            'type': tags.get('building', 'Unknown'),
            'address': address,
            'ruian_id': building.get('ruian_ref', 'Unknown')
        }

    @staticmethod
    def format_sample_property_for_display(property_data: Dict[str, Any], index: int = 0) -> str:
        """
        Format sample property information for display in the UI.

        Args:
            property_data (dict): Property data dictionary
            index (int): Index of the property in a list (for numbering)

        Returns:
            str: Formatted property information with sample indicator
        """
        # Extract property information with defaults
        owner_name = property_data.get('owner_name', 'Unknown')
        owner_address = property_data.get('owner_address', 'Unknown')
        prop_type = property_data.get('property_type', 'Unknown')

        # Build the result text
        result_text = f"Property {index+1} ⚠️ [SAMPLE DATA]:\n"
        result_text += f"Owner: {owner_name}\n"
        result_text += f"Address: {owner_address}\n"
        result_text += f"Type: {prop_type}\n\n"

        return result_text
