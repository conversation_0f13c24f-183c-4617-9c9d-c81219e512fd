#!/usr/bin/env python3
"""
Test script for the RegionManager class.
This script tests the functionality of the RegionManager class.
"""

import os
import json
import unittest
import tkinter as tk
from unittest.mock import MagicMock, patch

# Add the parent directory to the path to ensure imports work correctly
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from utils.region_manager import RegionManager
# No fallback data is used - removed DemoData import


class TestRegionManager(unittest.TestCase):
    """Test cases for the RegionManager class."""

    def setUp(self):
        """Set up the test environment."""
        # Create a mock app
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window

        self.app = MagicMock()
        self.app.root = self.root

        # Create a temporary directory for test data
        if not os.path.exists('czech_data'):
            os.makedirs('czech_data')

        # Initialize the RegionManager
        self.region_manager = RegionManager(self.app)

    def tearDown(self):
        """Clean up after the test."""
        self.root.destroy()

        # Clean up any test files
        test_files = [
            'czech_data/regions.json',
            'czech_data/cities.json',
            'czech_data/districts.json',
            'czech_data/cadastral_areas.json'
        ]

        for file_path in test_files:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    print(f"Error removing {file_path}: {e}")

    def test_initialize_empty_regions_data(self):
        """Test initializing empty regions data."""
        # Call the method
        self.region_manager._initialize_empty_regions_data()

        # Check that the czech_regions dictionary has been populated
        self.assertIsNotNone(self.region_manager.czech_regions)
        self.assertIn('regions_czech', self.region_manager.czech_regions)
        self.assertIn('regions_english', self.region_manager.czech_regions)
        self.assertIn('region_codes', self.region_manager.czech_regions)
        self.assertIn('region_coordinates', self.region_manager.czech_regions)

        # Check that the data has been initialized with basic Czech regions data
        self.assertIsInstance(self.region_manager.czech_regions['regions_czech'], list)
        self.assertIsInstance(self.region_manager.czech_regions['regions_english'], list)
        self.assertIsInstance(self.region_manager.czech_regions['region_codes'], dict)
        self.assertIsInstance(self.region_manager.czech_regions['region_coordinates'], dict)

        # Check that Prague is in the regions
        self.assertIn("Praha", self.region_manager.czech_regions['regions_czech'])
        self.assertIn("Prague", self.region_manager.czech_regions['regions_english'])

    def test_load_czech_regions_data_no_files(self):
        """Test loading Czech regions data when no files exist."""
        # Ensure no files exist
        test_files = [
            'czech_data/regions.json',
            'czech_data/cities.json',
            'czech_data/districts.json',
            'czech_data/cadastral_areas.json'
        ]

        for file_path in test_files:
            if os.path.exists(file_path):
                os.remove(file_path)

        # Call the method
        self.region_manager._load_czech_regions_data()

        # Check that the data has been initialized with demo data
        self.assertIsNotNone(self.region_manager.czech_regions)
        self.assertIn('regions_czech', self.region_manager.czech_regions)

        # Check that the cities, districts, and cadastral data has been initialized
        self.assertTrue(hasattr(self.region_manager, 'czech_cities'))
        self.assertTrue(hasattr(self.region_manager, 'czech_districts'))
        self.assertTrue(hasattr(self.region_manager, 'czech_cadastral'))

    def test_load_czech_regions_data_with_files(self):
        """Test loading Czech regions data when files exist."""
        # Create a new instance of RegionManager without calling _load_czech_regions_data
        with patch.object(RegionManager, '_load_czech_regions_data'):
            new_region_manager = RegionManager(self.app)
            # Clear the czech_regions data
            new_region_manager.czech_regions = {}

        # Create test files
        test_data = {
            'czech_data/regions.json': {
                'regions_czech': ['Praha', 'Brno'],
                'regions_english': ['Prague', 'Brno'],
                'region_codes': {'Praha': 'CZ010', 'Brno': 'CZ064'},
                'region_coordinates': {'Praha': [50.0755, 14.4378], 'Brno': [49.1951, 16.6068]},
                'region_name_mapping': {'praha': 'Prague', 'brno': 'Brno'}
            },
            'czech_data/cities.json': {
                'cities_by_region': {'Prague': ['Prague 1', 'Prague 2'], 'Brno': ['Brno-střed', 'Brno-sever']},
                'cities_by_region_code': {'CZ010': ['Prague 1', 'Prague 2'], 'CZ064': ['Brno-střed', 'Brno-sever']},
                'city_name_mapping': {'praha 1': 'Prague 1', 'brno-střed': 'Brno-střed'}
            },
            'czech_data/districts.json': {
                'districts_by_city': {'Prague': ['Prague 1', 'Prague 2'], 'Brno': ['Brno-střed', 'Brno-sever']},
                'districts_by_region_code': {'CZ010': ['Prague 1', 'Prague 2'], 'CZ064': ['Brno-střed', 'Brno-sever']}
            },
            'czech_data/cadastral_areas.json': {
                'cadastral_areas_by_city': {'Prague': ['Staré Město', 'Nové Město'], 'Brno': ['Brno-město', 'Brno-Židenice']},
                'cadastral_areas_by_district': {'Prague 1': ['Staré Město', 'Nové Město'], 'Brno-střed': ['Brno-město']},
                'cadastral_template': {'id': '', 'name': '', 'code': '', 'region': '', 'district': ''}
            }
        }

        for file_path, data in test_data.items():
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        # Call the method
        new_region_manager._load_czech_regions_data()

        # Check that the data has been loaded from the files
        self.assertEqual(new_region_manager.czech_regions['regions_czech'], ['Praha', 'Brno'])
        self.assertEqual(new_region_manager.czech_regions['regions_english'], ['Prague', 'Brno'])
        self.assertEqual(new_region_manager.czech_cities['cities_by_region']['Prague'], ['Prague 1', 'Prague 2'])
        self.assertEqual(new_region_manager.czech_districts['districts_by_city']['Prague'], ['Prague 1', 'Prague 2'])
        self.assertEqual(new_region_manager.czech_cadastral['cadastral_areas_by_city']['Prague'], ['Staré Město', 'Nové Město'])

    def test_get_regions(self):
        """Test getting regions."""
        # Mock the data source manager
        self.app.data_source_manager.get_regions.return_value = (['Prague', 'Brno'], 'test_source')

        # Call the method
        regions = self.region_manager.get_regions()

        # Check that the method returns the expected regions
        self.assertEqual(regions, ['Prague', 'Brno'])

        # Check that the regions_data has been updated
        self.assertEqual(self.region_manager.regions_data, {'Prague': 'R1', 'Brno': 'R2'})

        # Check that the data source manager was called
        self.app.data_source_manager.get_regions.assert_called_once_with(language="english")

        # Check that the status was updated
        self.app.show_status.assert_called_once_with("Loaded regions from test_source")

    def test_get_regions_empty(self):
        """Test getting regions when no regions are found."""
        # Mock the data source manager to return empty regions
        self.app.data_source_manager.get_regions.return_value = ([], 'test_source')

        # Call the method
        regions = self.region_manager.get_regions()

        # Check that the method returns an empty list
        self.assertEqual(regions, [])

        # Check that the regions_data has not been updated
        self.assertEqual(self.region_manager.regions_data, {})

        # Check that the data source manager was called
        self.app.data_source_manager.get_regions.assert_called_once_with(language="english")

    def test_get_regions_exception(self):
        """Test getting regions when an exception occurs."""
        # Mock the data source manager to raise an exception
        self.app.data_source_manager.get_regions.side_effect = Exception("Test exception")

        # Call the method
        regions = self.region_manager.get_regions()

        # Check that the method returns an empty list
        self.assertEqual(regions, [])

        # Check that the regions_data has not been updated
        self.assertEqual(self.region_manager.regions_data, {})

        # Check that the data source manager was called
        self.app.data_source_manager.get_regions.assert_called_once_with(language="english")

    def test_load_districts_for_region(self):
        """Test loading districts for a region."""
        # Mock the data source manager
        self.app.data_source_manager.get_cities_in_region.return_value = (['City1', 'City2'], 'test_source')

        # Add a mock for the UI update method
        self.region_manager._update_districts_ui = MagicMock()

        # Call the method
        self.region_manager._load_districts_for_region('Test Region')

        # Check that the data source manager was called
        self.app.data_source_manager.get_cities_in_region.assert_called_once_with('Test Region')

        # Check that the UI update method was called via root.after
        # We can't directly check this because it's called via root.after, but we can check that
        # the method exists and was not called directly
        self.region_manager._update_districts_ui.assert_not_called()

    def test_load_districts_for_region_with_region_districts(self):
        """Test loading districts for a region when region_districts is available."""
        # Set up region_districts in czech_regions
        self.region_manager.czech_regions['region_districts'] = {'Test Region': ['District1', 'District2']}

        # Add a mock for the UI update method
        self.region_manager._update_districts_ui = MagicMock()

        # Call the method
        self.region_manager._load_districts_for_region('Test Region')

        # Check that the data source manager was not called
        self.app.data_source_manager.get_cities_in_region.assert_not_called()

        # Check that the UI update method was called via root.after
        # We can't directly check this because it's called via root.after, but we can check that
        # the method exists and was not called directly
        self.region_manager._update_districts_ui.assert_not_called()


if __name__ == "__main__":
    unittest.main()
