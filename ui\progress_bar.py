"""
Progress Bar for the Czech Property Registry application.

This module provides a progress bar component that can be used to show
progress for long-running operations like searches.
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import logging

# Configure logging
logger = logging.getLogger(__name__)

class ProgressBar:
    """
    A progress bar component that shows progress for long-running operations.
    
    This component displays a progress bar, percentage, and status messages
    to inform the user about the progress of operations like searches.
    """
    
    def __init__(self, parent, width=400, height=80):
        """
        Initialize the progress bar component.
        
        Args:
            parent: Parent widget
            width: Width of the component
            height: Height of the component
        """
        self.parent = parent
        
        # Create the frame
        self.frame = ttk.LabelFrame(parent, text="Progress", padding=10)
        
        # Progress variables
        self.progress_value = 0
        self.progress_max = 100
        self.is_visible = False
        self.is_indeterminate = False
        self.start_time = None
        
        # Create UI elements
        self._create_ui()
        
    def _create_ui(self):
        """Create the UI elements for the progress bar."""
        # Task label
        self.task_label = ttk.Label(
            self.frame, 
            text="Ready",
            font=("Arial", 10)
        )
        self.task_label.pack(pady=(0, 5), anchor=tk.W, fill=tk.X)
        
        # Progress bar and percentage in the same row
        progress_frame = ttk.Frame(self.frame)
        progress_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            orient=tk.HORIZONTAL, 
            length=350, 
            mode="determinate"
        )
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        # Percentage label
        self.percentage_label = ttk.Label(
            progress_frame, 
            text="0%",
            width=5,
            anchor=tk.E
        )
        self.percentage_label.pack(side=tk.RIGHT)
        
        # Status and time in the same row
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill=tk.X)
        
        # Status message
        self.status_label = ttk.Label(
            status_frame, 
            text="",
            font=("Arial", 9, "italic")
        )
        self.status_label.pack(side=tk.LEFT, anchor=tk.W)
        
        # Elapsed time
        self.time_label = ttk.Label(
            status_frame, 
            text="",
            font=("Arial", 9)
        )
        self.time_label.pack(side=tk.RIGHT, anchor=tk.E)
        
        # Cancel button
        self.cancel_button = ttk.Button(
            self.frame,
            text="Cancel",
            command=self._on_cancel,
            width=10
        )
        self.cancel_button.pack(anchor=tk.E, pady=(5, 0))
        
        # Cancel callback
        self.cancel_callback = None
        
    def show(self):
        """Show the progress bar."""
        if not self.is_visible:
            self.frame.pack(fill=tk.X, padx=10, pady=10)
            self.is_visible = True
            self.start_time = time.time()
            self._update_elapsed_time()
            
    def hide(self):
        """Hide the progress bar."""
        if self.is_visible:
            self.frame.pack_forget()
            self.is_visible = False
            self.start_time = None
            
    def set_progress(self, value, max_value=100):
        """
        Set the progress bar value.
        
        Args:
            value: Current progress value
            max_value: Maximum progress value
        """
        self.progress_value = value
        self.progress_max = max_value
        
        # Calculate percentage
        if max_value > 0:
            percentage = min(100, int((value / max_value) * 100))
        else:
            percentage = 0
            
        # Update the UI
        if self.is_indeterminate:
            self.progress_bar.stop()
            self.progress_bar["mode"] = "determinate"
            self.is_indeterminate = False
            
        self.progress_bar["value"] = percentage
        self.percentage_label.config(text=f"{percentage}%")
        
    def set_indeterminate(self, indeterminate=True):
        """
        Set the progress bar to indeterminate mode.
        
        Args:
            indeterminate: Whether to use indeterminate mode
        """
        if indeterminate and not self.is_indeterminate:
            self.progress_bar["mode"] = "indeterminate"
            self.progress_bar.start()
            self.percentage_label.config(text="")
            self.is_indeterminate = True
        elif not indeterminate and self.is_indeterminate:
            self.progress_bar.stop()
            self.progress_bar["mode"] = "determinate"
            self.percentage_label.config(text="0%")
            self.is_indeterminate = False
            
    def set_task(self, task_name):
        """
        Set the current task name.
        
        Args:
            task_name: Name of the current task
        """
        self.task_label.config(text=task_name)
        
    def set_status(self, message):
        """
        Set the status message.
        
        Args:
            message: Status message to display
        """
        self.status_label.config(text=message)
        
    def _update_elapsed_time(self):
        """Update the elapsed time label."""
        if self.is_visible and self.start_time:
            elapsed = time.time() - self.start_time
            minutes = int(elapsed // 60)
            seconds = int(elapsed % 60)
            
            if minutes > 0:
                time_text = f"Time: {minutes}m {seconds}s"
            else:
                time_text = f"Time: {seconds}s"
                
            self.time_label.config(text=time_text)
            
            # Schedule the next update
            self.parent.after(1000, self._update_elapsed_time)
            
    def set_cancel_callback(self, callback):
        """
        Set the callback function for the cancel button.
        
        Args:
            callback: Function to call when cancel is clicked
        """
        self.cancel_callback = callback
        
    def _on_cancel(self):
        """Handle cancel button click."""
        if self.cancel_callback:
            self.cancel_callback()
            
    def reset(self):
        """Reset the progress bar to its initial state."""
        self.set_progress(0)
        self.set_task("Ready")
        self.set_status("")
        self.time_label.config(text="")
        self.start_time = time.time() if self.is_visible else None
