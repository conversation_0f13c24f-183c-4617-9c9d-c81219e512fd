"""
Real batch search UI components for the Czech Property Registry application.

This module provides UI components for batch searching of buildings
within a radius of a given address, with no limit on the number of results.
"""

import tkinter as tk
from tkinter import ttk
import webbrowser
import logging
import threading
from typing import List, Dict, Any, Optional
from utils.autocomplete import AutocompleteEntry
from ui.message_boxes import MessageBoxes
from ui.progress_bar import ProgressBar
from utils.improved_thread_manager import ImprovedThreadManager

# Setup logging
logger = logging.getLogger("RealBatchSearchUI")

class RealBatchSearchUI:
    """
    UI components for real batch search functionality.

    This class creates and manages the UI elements for searching
    buildings within a radius of a given address, with no limit
    on the number of results.
    """

    def __init__(self, parent, app):
        """
        Initialize the real batch search UI.

        Args:
            parent: The parent frame or window
            app: The main application instance
        """
        self.parent = parent
        self.app = app

        # Print debug information
        print("Initializing RealBatchSearchUI")
        print(f"Parent: {parent}")
        print(f"App: {app}")

        # Create the main frame
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add a label to indicate this is the real batch search UI
        ttk.Label(
            self.frame,
            text="Real Batch Search",
            font=("Arial", 16, "bold"),
            foreground="#4CAF50"
        ).pack(side="top", pady=10)

        # Create the search frame
        self.create_search_frame()

        # Create the progress bar
        self.progress_bar = ProgressBar(self.frame)
        self.progress_bar.frame.pack(fill=tk.X, padx=5, pady=5)
        self.progress_bar.hide()  # Hide initially

        # Create a status and debug frame
        self.create_debug_frame()

        # Create the results frame
        self.create_results_frame()

        # Initialize variables
        self.buildings = []
        self.search_in_progress = False

        # Create a thread manager for background tasks
        self.thread_manager = ImprovedThreadManager(max_workers=3)

    def create_search_frame(self):
        """Create the search frame with address input and radius selection."""
        search_frame = ttk.LabelFrame(self.frame, text="Search Parameters")
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        # Address input with autocomplete
        ttk.Label(search_frame, text="Address:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        # Create a function to get address suggestions
        def get_address_suggestions(text):
            if hasattr(self.app, 'google_maps'):
                suggestions = self.app.google_maps.get_place_predictions(text, components={"country": "cz"})
                if not suggestions and text and len(text) > 2:
                    # Show a status message when no suggestions are found
                    self.status_label.config(text=f"No address suggestions found for '{text}'")
                return suggestions
            else:
                self.status_label.config(text="Google Maps API not available")
                return []

        # Create the autocomplete entry
        self.address_entry = AutocompleteEntry(
            search_frame,
            autocomplete_function=get_address_suggestions,
            width=40
        )
        self.address_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)

        # Bind the Return key to trigger search
        self.address_entry.entry.bind("<Return>", lambda _: self.on_search())

        # Radius selection
        ttk.Label(search_frame, text="Radius (km):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.radius_var = tk.StringVar(value="1.0")
        radius_values = ["0.0", "0.2", "0.5", "1.0", "2.0", "5.0"]
        self.radius_combobox = ttk.Combobox(search_frame, textvariable=self.radius_var, values=radius_values, width=10)
        self.radius_combobox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Add a note about radius 0
        radius_note = ttk.Label(search_frame, text="Note: Radius 0 will search the entire city if the address is a city",
                               font=("Arial", 8), foreground="blue")
        radius_note.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        # Add a label to explain radius 0
        ttk.Label(
            search_frame,
            text="Use radius 0 to search only within city boundaries",
            font=("Arial", 8),
            foreground="#666666"
        ).grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)

        # Search button
        self.search_button = ttk.Button(search_frame, text="Search", command=self.on_search)
        self.search_button.grid(row=2, column=0, columnspan=2, padx=5, pady=10)

        # Cancel button
        self.cancel_button = ttk.Button(search_frame, text="Cancel", command=self.on_cancel, state=tk.DISABLED)
        self.cancel_button.grid(row=2, column=1, padx=5, pady=10, sticky=tk.E)

        # Status label
        self.status_label = ttk.Label(search_frame, text="Ready")
        self.status_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Configure grid
        search_frame.columnconfigure(1, weight=1)

    def create_debug_frame(self):
        """Create a frame with debug tools and status information."""
        debug_frame = ttk.LabelFrame(self.frame, text="Debug Tools")
        debug_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create a status label
        self.status_label = ttk.Label(debug_frame, text="Ready", anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)

        # Create a standalone mode button
        self.standalone_btn = ttk.Button(
            debug_frame,
            text="Switch to Standalone Mode",
            command=self.on_switch_to_standalone
        )
        self.standalone_btn.pack(side=tk.LEFT, padx=5, pady=5)

        # Create buttons frame
        buttons_frame = ttk.Frame(debug_frame)
        buttons_frame.pack(side=tk.RIGHT, padx=5, pady=5)

        # Create a clear cache button
        self.clear_cache_btn = ttk.Button(
            buttons_frame,
            text="Clear All Cache",
            command=self.on_clear_cache
        )
        self.clear_cache_btn.pack(side=tk.RIGHT, padx=5, pady=5)

        # Create a clear current city cache button
        self.clear_city_cache_btn = ttk.Button(
            buttons_frame,
            text="Clear Current City Cache",
            command=self.on_clear_city_cache
        )
        self.clear_city_cache_btn.pack(side=tk.RIGHT, padx=5, pady=5)

        # Create a specific button to clear Mariánské Lázně cache
        # This is added to help with the specific issue reported by users
        self.clear_marianske_btn = ttk.Button(
            buttons_frame,
            text="Fix Mariánské Lázně",
            command=self.on_fix_marianske_lazne
        )
        self.clear_marianske_btn.pack(side=tk.RIGHT, padx=5, pady=5)

        # Create a button to reset all city caches
        # This is a more drastic solution that clears all city-related caches
        self.reset_city_caches_btn = ttk.Button(
            buttons_frame,
            text="Reset ALL City Caches",
            command=self.on_reset_all_city_caches,
            style="Accent.TButton"  # Use accent style to indicate this is a more drastic action
        )
        self.reset_city_caches_btn.pack(side=tk.RIGHT, padx=5, pady=5)

        # Create a button to fix coordinate conversion issues
        # This is a specific fix for the coordinate conversion issues
        self.fix_coordinates_btn = ttk.Button(
            buttons_frame,
            text="Fix Coordinate Conversion",
            command=self.on_fix_coordinate_conversion,
            style="Accent.TButton"  # Use accent style to indicate this is a more drastic action
        )
        self.fix_coordinates_btn.pack(side=tk.RIGHT, padx=5, pady=5)

    def create_results_frame(self):
        """Create the results frame with a treeview for displaying buildings."""
        results_frame = ttk.LabelFrame(self.frame, text="Search Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a treeview for displaying buildings
        columns = ("id", "lat", "lng", "x", "y", "address", "ruian_id", "type", "url")
        self.buildings_tree = ttk.Treeview(results_frame, columns=columns, show="headings")

        # Define column headings
        self.buildings_tree.heading("id", text="#")
        self.buildings_tree.heading("lat", text="Latitude")
        self.buildings_tree.heading("lng", text="Longitude")
        self.buildings_tree.heading("x", text="X (S-JTSK)")
        self.buildings_tree.heading("y", text="Y (S-JTSK)")
        self.buildings_tree.heading("address", text="Address")
        self.buildings_tree.heading("ruian_id", text="RUIAN ID")
        self.buildings_tree.heading("type", text="Type")
        self.buildings_tree.heading("url", text="CUZK URL")

        # Define column widths
        self.buildings_tree.column("id", width=50, anchor=tk.CENTER)
        self.buildings_tree.column("lat", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("lng", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("x", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("y", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("address", width=200, anchor=tk.W)
        self.buildings_tree.column("ruian_id", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("type", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("url", width=250, anchor=tk.W)

        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.buildings_tree.yview)
        self.buildings_tree.configure(yscrollcommand=y_scrollbar.set)

        x_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.buildings_tree.xview)
        self.buildings_tree.configure(xscrollcommand=x_scrollbar.set)

        # Pack the treeview and scrollbars
        self.buildings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind double-click event to open URL
        self.buildings_tree.bind("<Double-1>", self.on_building_double_click)

        # Create buttons frame
        buttons_frame = ttk.Frame(results_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # Open selected button
        self.open_selected_button = ttk.Button(buttons_frame, text="Open Selected", command=self.on_open_selected)
        self.open_selected_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Open all button
        self.open_all_button = ttk.Button(buttons_frame, text="Open All", command=self.on_open_all)
        self.open_all_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Export button
        self.export_button = ttk.Button(buttons_frame, text="Export Results", command=self.on_export_results)
        self.export_button.pack(side=tk.RIGHT, padx=5, pady=5)

        # Results count label
        self.results_count_label = ttk.Label(buttons_frame, text="0 buildings found")
        self.results_count_label.pack(side=tk.RIGHT, padx=5, pady=5)

    def on_search(self):
        """Handle the search button click."""
        # Get the address and radius
        address = self.address_entry.get()  # AutocompleteEntry has a get() method
        radius = self.radius_var.get()

        # Print debug information
        print(f"RealBatchSearchUI.on_search called with address: {address}, radius: {radius}")
        logger.info(f"RealBatchSearchUI.on_search called with address: {address}, radius: {radius}")

        if not address:
            MessageBoxes.show_warning("Missing Information", "Please enter an address to search for buildings.")
            return

        try:
            radius_float = float(radius)
            if radius_float < 0:
                MessageBoxes.show_warning("Invalid Radius", "Radius must be 0 or greater.")
                return
        except ValueError:
            MessageBoxes.show_warning("Invalid Radius", "Please enter a valid number for the radius.")
            return

        # Update UI state
        self.search_button.config(state=tk.DISABLED)
        self.cancel_button.config(state=tk.NORMAL)
        self.search_in_progress = True

        # Show and initialize the progress bar
        self.progress_bar.show()
        self.progress_bar.set_indeterminate(True)

        # Special handling for radius 0 (city-wide search)
        if radius_float == 0:
            task_name = f"Searching for all buildings in {address}..."
            self.status_label.config(text=task_name)
            self.progress_bar.set_task(task_name)
            self.progress_bar.set_status("Initializing city-wide search...")

            MessageBoxes.show_info("City-Wide Search",
                              f"You've selected a radius of 0, which will search the entire city area of {address}.\n\n"
                              f"This may take longer than a regular radius search.")
        else:
            task_name = f"Searching for buildings near {address}..."
            self.status_label.config(text=task_name)
            self.progress_bar.set_task(task_name)
            self.progress_bar.set_status(f"Searching within {radius_float} km radius...")

        # Set up cancel callback
        self.progress_bar.set_cancel_callback(self.on_cancel)

        # Clear previous results
        self.clear_results()

        # Call the batch search manager
        if hasattr(self.app, 'real_batch_search_manager'):
            print("Using real_batch_search_manager for search")
            logger.info("Using real_batch_search_manager for search")

            # Add a try-except block to catch any errors
            try:
                # Set up progress callback
                def progress_callback(current, total, status_message=None):
                    if not self.search_in_progress:
                        return

                    # Use after() to update UI from the main thread
                    def update_ui():
                        # Update the progress bar
                        if total > 0:
                            self.progress_bar.set_indeterminate(False)
                            self.progress_bar.set_progress(current, total)

                            if status_message:
                                self.progress_bar.set_status(status_message)
                            else:
                                self.progress_bar.set_status(f"Processing {current} of {total} coordinates...")
                        else:
                            self.progress_bar.set_indeterminate(True)

                            if status_message:
                                self.progress_bar.set_status(status_message)

                    # Schedule the UI update on the main thread
                    self.frame.after(0, update_ui)

                # Start the search with progress callback in a separate thread
                def search_thread():
                    try:
                        self.app.real_batch_search_manager.batch_search_by_address(
                            address, None, radius_float, None, self.display_search_results, progress_callback
                        )
                    except Exception as e:
                        # Handle errors in the thread
                        error_msg = f"Error in batch search thread: {str(e)}"
                        print(error_msg)
                        logger.error(error_msg, exc_info=True)

                        # Update UI on the main thread
                        self.frame.after(0, lambda: self._handle_search_error(error_msg))

                # Start the search thread
                threading.Thread(target=search_thread, daemon=True).start()
            except Exception as e:
                error_msg = f"Error in batch search: {str(e)}"
                print(error_msg)
                logger.error(error_msg, exc_info=True)
                MessageBoxes.show_error("Error", error_msg)

                # Reset UI state
                self.search_button.config(state=tk.NORMAL)
                self.cancel_button.config(state=tk.DISABLED)
                self.status_label.config(text="Error in search")
                self.progress_bar.hide()
                self.search_in_progress = False
        else:
            # Print all available attributes of the app for debugging
            print("Available app attributes:")
            logger.error("Real batch search manager not initialized")

            for attr in dir(self.app):
                if not attr.startswith('__'):
                    print(f"  - {attr}")
                    logger.info(f"App attribute: {attr}")

            MessageBoxes.show_error("Error", "Real batch search manager not initialized.")

            # Reset UI state
            self.search_button.config(state=tk.NORMAL)
            self.cancel_button.config(state=tk.DISABLED)
            self.status_label.config(text="Ready")
            self.progress_bar.hide()
            self.search_in_progress = False

    def on_cancel(self):
        """Handle the cancel button click."""
        if hasattr(self.app, 'real_batch_search_manager'):
            self.app.real_batch_search_manager.cancel_current_search()

        # Update UI state
        self.search_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.status_label.config(text="Search cancelled")

        # Update progress bar
        self.progress_bar.set_task("Search cancelled")
        self.progress_bar.set_status("Operation was cancelled by user")
        self.progress_bar.set_progress(0, 100)

        # Hide progress bar after a short delay
        self.frame.after(2000, self.progress_bar.hide)

        # Update search state
        self.search_in_progress = False

    def clear_results(self):
        """Clear the results treeview."""
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)

        self.buildings = []
        self.results_count_label.config(text="0 buildings found")

    def display_search_results(self, buildings):
        """
        Display the search results in the treeview.

        Args:
            buildings (list): List of building data dictionaries
        """
        # Print debug information
        print(f"RealBatchSearchUI.display_search_results called with {len(buildings)} buildings")
        logger.info(f"RealBatchSearchUI.display_search_results called with {len(buildings)} buildings")

        # Update progress bar
        self.progress_bar.set_indeterminate(False)
        self.progress_bar.set_progress(100, 100)
        self.progress_bar.set_status("Processing search results...")

        if not buildings:
            print("No buildings found")
            logger.warning("No buildings found")
            MessageBoxes.show_info("No Results", "No buildings found in the specified area.")

            # Update UI state
            self.search_button.config(state=tk.NORMAL)
            self.cancel_button.config(state=tk.DISABLED)
            self.status_label.config(text="No buildings found")

            # Update progress bar
            self.progress_bar.set_task("Search completed")
            self.progress_bar.set_status("No buildings found in the specified area")

            # Hide progress bar after a short delay
            self.frame.after(2000, self.progress_bar.hide)

            # Update search state
            self.search_in_progress = False
            return

        # Print the first building for debugging
        print(f"First building: {buildings[0]}")
        logger.info(f"First building: {buildings[0]}")

        # Store the buildings
        self.buildings = buildings

        # Clear previous results
        self.clear_results()

        # Update progress bar
        self.progress_bar.set_task("Processing results")
        self.progress_bar.set_status(f"Adding {len(buildings)} buildings to the results table...")

        # Add buildings to the treeview
        for building in buildings:
            try:
                # Construct address string if available
                address = ""

                # Check if we have street and housenumber
                if 'street' in building and building['street'] and 'housenumber' in building and building['housenumber']:
                    address = f"{building['street']} {building['housenumber']}"

                    # Add city if available
                    if 'city' in building and building['city']:
                        address += f", {building['city']}"

                    # Add postcode if available
                    if 'postcode' in building and building['postcode']:
                        address += f" {building['postcode']}"

                # If we don't have street/housenumber but have a name, use that
                elif 'name' in building and building['name']:
                    # If we also have a city, include it
                    if 'city' in building and building['city']:
                        address = f"{building['name']}, {building['city']}"
                    else:
                        address = building['name']

                # If we only have city, use that
                elif 'city' in building and building['city']:
                    if 'postcode' in building and building['postcode']:
                        address = f"{building['city']} {building['postcode']}"
                    else:
                        address = building['city']

                    # If we have a building type, add it
                    if 'building_type' in building and building['building_type']:
                        building_type = building['building_type']
                        # Capitalize the first letter of the building type
                        if building_type:
                            building_type = building_type[0].upper() + building_type[1:]
                        address = f"{building_type} in {address}"

                # If we have tags, try to construct an address from them
                elif 'tags' in building and building['tags']:
                    tags = building['tags']
                    addr_parts = []

                    # Try to get street and housenumber from tags
                    if 'addr:street' in tags and tags['addr:street'] and 'addr:housenumber' in tags and tags['addr:housenumber']:
                        addr_parts.append(f"{tags['addr:street']} {tags['addr:housenumber']}")
                    elif 'addr:street' in tags and tags['addr:street']:
                        addr_parts.append(tags['addr:street'])

                    # Try alternative address components
                    elif 'addr:place' in tags and tags['addr:place']:
                        addr_parts.append(tags['addr:place'])
                    elif 'addr:hamlet' in tags and tags['addr:hamlet']:
                        addr_parts.append(tags['addr:hamlet'])
                    elif 'addr:suburb' in tags and tags['addr:suburb']:
                        addr_parts.append(tags['addr:suburb'])
                    elif 'addr:neighbourhood' in tags and tags['addr:neighbourhood']:
                        addr_parts.append(tags['addr:neighbourhood'])

                    # Try to get city from tags
                    if 'addr:city' in tags and tags['addr:city']:
                        addr_parts.append(tags['addr:city'])

                    # Try to get postcode from tags
                    if 'addr:postcode' in tags and tags['addr:postcode']:
                        addr_parts.append(tags['addr:postcode'])

                    # Join all parts with commas
                    if addr_parts:
                        address = ", ".join(addr_parts)
                    # If no address parts but we have a name in tags, use that
                    elif 'name' in tags and tags['name']:
                        address = tags['name']
                    # If we have a building type, use that
                    elif tags.get('building') or tags.get('building:part') or tags.get('amenity') or tags.get('shop') or tags.get('office'):
                        building_type = tags.get('building') or tags.get('building:part') or tags.get('amenity') or tags.get('shop') or tags.get('office')
                        # Capitalize the first letter of the building type
                        if building_type:
                            building_type = building_type[0].upper() + building_type[1:]
                        address = building_type

                # If we still don't have an address, use the building type if available
                if not address and 'building_type' in building and building['building_type']:
                    building_type = building['building_type']
                    # Capitalize the first letter of the building type
                    if building_type:
                        building_type = building_type[0].upper() + building_type[1:]
                    address = building_type

                # If we still don't have an address, use a default value
                if not address:
                    address = "Building"

                # Get RUIAN ID if available
                ruian_id = building.get('ruian_id', "")

                # Get building type if available
                building_type = building.get('building_type', "")

                # Get URL if available
                url = building.get('url', "")

                # Log the URL for debugging
                logger.info(f"Building URL: {url}")

                self.buildings_tree.insert("", tk.END, values=(
                    building['id'],
                    f"{building['lat']:.6f}",
                    f"{building['lng']:.6f}",
                    building['x'],
                    building['y'],
                    address,
                    ruian_id,
                    building_type,
                    url
                ))
            except Exception as e:
                error_msg = f"Error adding building to treeview: {str(e)}, building: {building}"
                print(error_msg)
                logger.error(error_msg)

        # Update the results count label
        self.results_count_label.config(text=f"{len(buildings)} buildings found")

        # Update UI state
        self.search_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.status_label.config(text="Search completed")

        # Update progress bar
        self.progress_bar.set_task("Search completed")
        self.progress_bar.set_status(f"Found {len(buildings)} buildings")
        self.progress_bar.set_progress(100, 100)

        # Hide progress bar after a short delay
        self.frame.after(3000, self.progress_bar.hide)

        # Update search state
        self.search_in_progress = False

    def on_building_double_click(self, _):
        """Handle double-click on a building in the treeview."""
        # Get the selected item
        selected_item = self.buildings_tree.selection()
        if not selected_item:
            logger.warning("Double-click event but no item selected")
            return

        # Log the selected item
        logger.info(f"Double-click on item: {selected_item[0]}")

        try:
            # Import the BrowserSpoofer for adding human-like delays
            try:
                from utils.browser_spoofer import BrowserSpoofer
                use_browser_spoofer = True
            except ImportError:
                use_browser_spoofer = False

            # Get the values from the treeview
            values = self.buildings_tree.item(selected_item[0], "values")
            logger.info(f"Selected item values: {values}")

            # Get the URL directly from the values (last column)
            url = values[8]  # URL is the 9th column (index 8)
            logger.info(f"URL from treeview: {url}")

            # Open the URL in the browser
            if url:
                logger.info(f"Opening URL: {url}")

                # Add a human-like delay before opening the URL
                if use_browser_spoofer:
                    BrowserSpoofer.add_human_like_delay("click", verbose=True)

                webbrowser.open(url)
            else:
                # Fallback to the old method if URL is not in the treeview
                item_id = values[0]
                logger.info(f"URL not found in treeview, falling back to buildings list. Item ID: {item_id}")

                building_idx = int(item_id) - 1

                if 0 <= building_idx < len(self.buildings):
                    building = self.buildings[building_idx]
                    logger.info(f"Building data: {building}")

                    if 'url' in building:
                        url = building['url']
                        logger.info(f"Opening URL from buildings list: {url}")

                        # Add a human-like delay before opening the URL
                        if use_browser_spoofer:
                            BrowserSpoofer.add_human_like_delay("click", verbose=True)

                        webbrowser.open(url)
                    else:
                        logger.error(f"No URL found in building data: {building}")
                        MessageBoxes.show_warning("Missing URL", "No URL available for this building.")
                else:
                    logger.error(f"Building index out of range: {building_idx}, buildings length: {len(self.buildings)}")
                    MessageBoxes.show_warning("Error", "Could not find building data for the selected item.")
        except Exception as e:
            logger.error(f"Error in on_building_double_click: {str(e)}", exc_info=True)
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")

    def on_open_selected(self):
        """Open the selected buildings in the browser."""
        # Get the selected items
        selected_items = self.buildings_tree.selection()
        if not selected_items:
            logger.info("Open Selected clicked but no items selected")
            MessageBoxes.show_info("No Selection", "Please select one or more buildings.")
            return

        logger.info(f"Open Selected clicked with {len(selected_items)} items selected")

        # Import the BrowserSpoofer for adding human-like delays
        try:
            from utils.browser_spoofer import BrowserSpoofer
            use_browser_spoofer = True
        except ImportError:
            use_browser_spoofer = False

        # Open each selected building
        for i, item in enumerate(selected_items):
            try:
                # Add a delay between opening multiple tabs to avoid triggering bot detection
                if i > 0 and use_browser_spoofer:
                    BrowserSpoofer.add_human_like_delay("navigation", verbose=True)

                values = self.buildings_tree.item(item, "values")
                logger.info(f"Selected item values: {values}")

                # Get the URL directly from the values (last column)
                url = values[8]  # URL is the 9th column (index 8)
                logger.info(f"URL from treeview: {url}")

                # Open the URL in the browser
                if url:
                    logger.info(f"Opening URL: {url}")
                    webbrowser.open(url)
                else:
                    # Fallback to the old method if URL is not in the treeview
                    item_id = values[0]
                    logger.info(f"URL not found in treeview, falling back to buildings list. Item ID: {item_id}")

                    building_idx = int(item_id) - 1

                    if 0 <= building_idx < len(self.buildings):
                        building = self.buildings[building_idx]
                        logger.info(f"Building data: {building}")

                        if 'url' in building:
                            url = building['url']
                            logger.info(f"Opening URL from buildings list: {url}")
                            webbrowser.open(url)
                        else:
                            logger.error(f"No URL found in building data: {building}")
                            MessageBoxes.show_warning("Missing URL", "No URL available for this building.")
                    else:
                        logger.error(f"Building index out of range: {building_idx}, buildings length: {len(self.buildings)}")
                        MessageBoxes.show_warning("Error", "Could not find building data for the selected item.")
            except Exception as e:
                logger.error(f"Error in on_open_selected for item {item}: {str(e)}", exc_info=True)
                MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")

    def on_open_all(self):
        """Open all buildings in the browser."""
        if not self.buildings:
            MessageBoxes.show_info("No Buildings", "No buildings to open.")
            return

        # Ask for confirmation if there are many buildings
        if len(self.buildings) > 10:
            confirm = MessageBoxes.ask_yes_no(
                "Confirm Open All",
                f"This will open {len(self.buildings)} browser tabs. Are you sure you want to continue?"
            )
            if not confirm:
                return

        # Open all buildings
        for building in self.buildings:
            webbrowser.open(building['url'])

    def on_export_results(self):
        """Export the search results to a file."""
        # To be implemented
        MessageBoxes.show_info("Not Implemented", "Export functionality is not yet implemented.")

    def on_clear_cache(self):
        """Clear all address suggestions cache entries."""
        try:
            # Check if the region manager has the clear_address_cache method
            if hasattr(self.app, 'region_manager') and hasattr(self.app.region_manager, 'clear_address_cache'):
                # Ask for confirmation
                confirm = MessageBoxes.ask_yes_no(
                    "Clear All Cache",
                    "Are you sure you want to clear the entire address cache? This will remove all cached address suggestions."
                )

                if confirm:
                    # Clear the cache
                    prefix = None  # Clear all cache entries
                    cleared_count = self.app.region_manager.clear_address_cache(prefix)

                    # Show a success message
                    self.status_label.config(text=f"Cleared {cleared_count} cache entries")
                    MessageBoxes.show_info("Cache Cleared", f"Successfully cleared {cleared_count} cache entries.")
            else:
                self.status_label.config(text="Region manager not available")
                MessageBoxes.show_error("Error", "Region manager not available or does not have clear_address_cache method.")
        except Exception as e:
            error_msg = f"Error clearing cache: {e}"
            self.status_label.config(text=error_msg)
            MessageBoxes.show_error("Error", error_msg)

    def on_clear_city_cache(self):
        """Clear the address suggestions cache for the current city."""
        try:
            # Get the current city from the address entry
            address = self.address_entry.get()
            if not address:
                MessageBoxes.show_warning("No City Selected", "Please enter a city in the address field first.")
                return

            # Check if the region manager has the clear_city_cache method
            if hasattr(self.app, 'region_manager') and hasattr(self.app.region_manager, 'clear_city_cache'):
                # Ask for confirmation
                confirm = MessageBoxes.ask_yes_no(
                    "Clear City Cache",
                    f"Are you sure you want to clear the cache for '{address}'? This will remove all cached suggestions for this city."
                )

                if confirm:
                    # Use the new clear_city_cache method which is more thorough
                    cleared_count = self.app.region_manager.clear_city_cache(address)

                    # Show a success message
                    self.status_label.config(text=f"Cleared {cleared_count} cache entries for '{address}'")
            else:
                # Fall back to the old method if clear_city_cache is not available
                if hasattr(self.app, 'region_manager') and hasattr(self.app.region_manager, 'clear_address_cache'):
                    # Ask for confirmation
                    confirm = MessageBoxes.ask_yes_no(
                        "Clear City Cache",
                        f"Are you sure you want to clear the cache for '{address}'? This will remove all cached suggestions for this city."
                    )

                    if confirm:
                        # Clear the cache for this city
                        # Use both batch_city and city prefixes to catch all possible entries
                        prefixes = [f"batch_city:{address}", f"city:{address}"]
                        total_cleared = 0

                        for prefix in prefixes:
                            cleared_count = self.app.region_manager.clear_address_cache(prefix)
                            total_cleared += cleared_count

                        # Show a success message
                        self.status_label.config(text=f"Cleared {total_cleared} cache entries for '{address}'")
                        MessageBoxes.show_info("Cache Cleared", f"Successfully cleared {total_cleared} cache entries for '{address}'.")
                else:
                    self.status_label.config(text="Region manager not available")
                    MessageBoxes.show_error("Error", "Region manager not available or does not have cache clearing methods.")
        except Exception as e:
            error_msg = f"Error clearing city cache: {e}"
            self.status_label.config(text=error_msg)
            MessageBoxes.show_error("Error", error_msg)

    def on_switch_to_standalone(self):
        """Switch to the standalone batch search application."""
        try:
            # Ask for confirmation
            confirm = MessageBoxes.ask_yes_no(
                "Switch to Standalone Mode",
                "Are you sure you want to switch to the standalone batch search application? This will close the current application."
            )

            if confirm:
                # Get the current city from the address entry
                city = self.address_entry.get()

                # Show a status message
                self.status_label.config(text="Launching standalone application...")

                # Launch the standalone application using the launcher
                import subprocess
                import sys
                import os

                # Get the path to the launcher.py file
                launcher_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "launcher.py")

                # Check if the file exists
                if not os.path.exists(launcher_path):
                    MessageBoxes.show_error("Error", f"Launcher not found at: {launcher_path}")
                    return

                # Launch the standalone application with the current city
                cmd = [sys.executable, launcher_path]
                if city:
                    cmd.extend(["--city", city])

                # Use Popen to launch the process without waiting for it to complete
                subprocess.Popen(cmd)

                # Close the current application
                self.app.root.after(1000, self.app.root.destroy)

        except Exception as e:
            error_msg = f"Error switching to standalone mode: {e}"
            self.status_label.config(text=error_msg)
            MessageBoxes.show_error("Error", error_msg)

    def _handle_search_error(self, error_msg):
        """
        Handle errors that occur during the search process.

        Args:
            error_msg: Error message to display
        """
        # Show error message
        MessageBoxes.show_error("Error", error_msg)

        # Reset UI state
        self.search_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.status_label.config(text="Error in search")
        self.progress_bar.hide()
        self.search_in_progress = False

    def on_fix_marianske_lazne(self):
        """
        Fix the specific issue with Mariánské Lázně by clearing its cache.

        This is a targeted fix for the reported issue where searching for
        Mariánské Lázně returns Choťovice due to cache issues.
        """
        try:
            # Check if the region manager has the clear_city_cache method
            if hasattr(self.app, 'region_manager') and hasattr(self.app.region_manager, 'clear_city_cache'):
                # Ask for confirmation
                confirm = MessageBoxes.ask_yes_no(
                    "Fix Mariánské Lázně",
                    "This will clear the cache for Mariánské Lázně to fix the issue where it returns Choťovice. Continue?"
                )

                if confirm:
                    # Clear the cache for Mariánské Lázně
                    city_name = "Mariánské Lázně"
                    cleared_count = self.app.region_manager.clear_city_cache(city_name)

                    # Also clear any cache for Choťovice to be thorough
                    choťovice_count = self.app.region_manager.clear_city_cache("Choťovice")

                    # Show a success message
                    total_cleared = cleared_count + choťovice_count
                    self.status_label.config(text=f"Fixed Mariánské Lázně issue. Cleared {total_cleared} cache entries.")

                    # Set the address field to Mariánské Lázně for convenience
                    self.address_entry.set_text("Mariánské Lázně")
            else:
                self.status_label.config(text="Region manager not available")
                MessageBoxes.show_error("Error", "Region manager not available or does not have clear_city_cache method.")
        except Exception as e:
            error_msg = f"Error fixing Mariánské Lázně issue: {e}"
            self.status_label.config(text=error_msg)
            MessageBoxes.show_error("Error", error_msg)

    def on_reset_all_city_caches(self):
        """
        Reset ALL city-related caches in the application.

        This is a more drastic solution that completely clears all city-related caches,
        including both the old-style address cache and the new cache manager.
        """
        try:
            # Ask for confirmation with a strong warning
            confirm = MessageBoxes.ask_yes_no(
                "Reset ALL City Caches",
                "WARNING: This will completely reset ALL city-related caches in the application.\n\n"
                "This is a drastic solution that should only be used when you're experiencing\n"
                "persistent issues with city searches returning incorrect results.\n\n"
                "Are you sure you want to continue?"
            )

            if not confirm:
                return

            # Clear all city-related caches
            cleared_count = 0

            # First, check if we have a region manager with the clear_city_cache method
            if hasattr(self.app, 'region_manager') and hasattr(self.app.region_manager, 'clear_city_cache'):
                # Clear all city caches
                cleared_count = self.app.region_manager.clear_city_cache()

            # Also clear the cache manager if available
            if hasattr(self.app, 'cache_manager'):
                try:
                    # Clear city suggestions cache
                    city_count = self.app.cache_manager.clear_cache('city_suggestions')
                    cleared_count += city_count

                    # Clear address suggestions that might contain city data
                    address_keys = self.app.cache_manager.get_all_keys('address_suggestions')
                    for key in address_keys:
                        if 'city' in key.lower() or 'batch_city' in key.lower():
                            self.app.cache_manager.delete('address_suggestions', key)
                            cleared_count += 1
                except Exception as e:
                    print(f"Error clearing cache with cache manager: {e}")

            # Also clear the old-style address cache
            if hasattr(self.app, '_address_suggestions_cache') and hasattr(self.app, '_address_cache_lock'):
                with self.app._address_cache_lock:
                    # Get a list of keys to avoid modifying the dictionary during iteration
                    keys = list(self.app._address_suggestions_cache.keys())

                    for key in keys:
                        # Clear all city-related entries
                        if 'city' in key.lower() or 'batch_city' in key.lower():
                            del self.app._address_suggestions_cache[key]
                            cleared_count += 1

            # Show a success message
            self.status_label.config(text=f"Reset ALL city caches. Cleared {cleared_count} cache entries.")
            MessageBoxes.show_info(
                "Caches Reset",
                f"Successfully reset ALL city caches. Cleared {cleared_count} cache entries.\n\n"
                "City searches should now return correct results."
            )

            # Suggest restarting the application for a clean slate
            restart = MessageBoxes.ask_yes_no(
                "Restart Recommended",
                "For best results, it's recommended to restart the application.\n\n"
                "Would you like to restart the application now?"
            )

            if restart:
                # Restart the application
                import sys
                import os
                import subprocess

                # Get the path to the main.py file
                main_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "main.py")

                # Check if the file exists
                if not os.path.exists(main_path):
                    MessageBoxes.show_error("Error", f"Main file not found at: {main_path}")
                    return

                # Launch the application with the current Python interpreter
                cmd = [sys.executable, main_path]

                # Use Popen to launch the process without waiting for it to complete
                subprocess.Popen(cmd)

                # Close the current application
                self.app.root.after(1000, self.app.root.destroy)

        except Exception as e:
            error_msg = f"Error resetting city caches: {e}"
            self.status_label.config(text=error_msg)
            MessageBoxes.show_error("Error", error_msg)

    def on_fix_coordinate_conversion(self):
        """
        Fix coordinate conversion issues by clearing all caches and restarting the application.

        This is a more drastic solution that should be used when city searches are returning
        incorrect locations due to coordinate conversion issues.
        """
        try:
            # Ask for confirmation with a strong warning
            confirm = MessageBoxes.ask_yes_no(
                "Fix Coordinate Conversion",
                "WARNING: This will clear all caches and restart the application.\n\n"
                "This is a drastic solution that should only be used when you're experiencing\n"
                "persistent issues with city searches returning incorrect locations.\n\n"
                "Are you sure you want to continue?"
            )

            if not confirm:
                return

            # Clear all caches
            cleared_count = 0

            # First, check if we have a region manager with the clear_city_cache method
            if hasattr(self.app, 'region_manager') and hasattr(self.app.region_manager, 'clear_city_cache'):
                # Clear all city caches
                cleared_count = self.app.region_manager.clear_city_cache()

            # Also clear the cache manager if available
            if hasattr(self.app, 'cache_manager'):
                try:
                    # Clear all caches
                    for cache_name in ['city_suggestions', 'address_suggestions', 'city_boundaries', 'property_data']:
                        try:
                            count = self.app.cache_manager.clear_cache(cache_name)
                            cleared_count += count
                        except Exception as e:
                            print(f"Error clearing cache {cache_name}: {e}")
                except Exception as e:
                    print(f"Error clearing cache with cache manager: {e}")

            # Also clear the old-style address cache
            if hasattr(self.app, '_address_suggestions_cache') and hasattr(self.app, '_address_cache_lock'):
                with self.app._address_cache_lock:
                    # Get a list of keys to avoid modifying the dictionary during iteration
                    keys = list(self.app._address_suggestions_cache.keys())
                    cleared_count += len(keys)
                    self.app._address_suggestions_cache.clear()

            # Show a success message
            self.status_label.config(text=f"Fixed coordinate conversion issues. Cleared {cleared_count} cache entries.")
            MessageBoxes.show_info(
                "Coordinate Conversion Fixed",
                f"Successfully fixed coordinate conversion issues. Cleared {cleared_count} cache entries.\n\n"
                "The application will now restart to apply the changes."
            )

            # Restart the application
            import sys
            import os
            import subprocess

            # Get the path to the main.py file
            main_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "main.py")

            # Check if the file exists
            if not os.path.exists(main_path):
                MessageBoxes.show_error("Error", f"Main file not found at: {main_path}")
                return

            # Launch the application with the current Python interpreter
            cmd = [sys.executable, main_path]

            # Use Popen to launch the process without waiting for it to complete
            subprocess.Popen(cmd)

            # Close the current application
            self.app.root.after(1000, self.app.root.destroy)

        except Exception as e:
            error_msg = f"Error fixing coordinate conversion issues: {e}"
            self.status_label.config(text=error_msg)
            MessageBoxes.show_error("Error", error_msg)

    def update_status(self, status_text):
        """Update the status label."""
        self.status_label.config(text=status_text)
