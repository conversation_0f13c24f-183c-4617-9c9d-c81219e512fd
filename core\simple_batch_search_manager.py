"""
Simple Batch Search Manager for the Czech Property Registry application.

This module provides a simplified batch search manager that uses the Overpass API
to fetch real building data.
"""

import threading
import time
import logging
import math
import requests
import json
from typing import List, Dict, Any, Optional, Callable

# Configure logging
logger = logging.getLogger(__name__)

class SimpleBatchSearchManager:
    """
    Simple batch search manager that uses the Overpass API to fetch building data.
    
    This class provides methods for searching buildings within a radius of a given
    address or coordinates. It uses the Overpass API to fetch real building data.
    """
    
    def __init__(self, app):
        """
        Initialize the simple batch search manager.
        
        Args:
            app: The main application instance
        """
        self.app = app
        self.search_in_progress = False
        self.cancel_search = False
        
        logger.info("Simple Batch Search Manager initialized")
    
    def batch_search_by_address(self, address, property_types, radius, max_results, callback, progress_callback=None):
        """
        Perform a batch search for buildings by address.
        
        Args:
            address (str): Address to search around
            property_types (list): List of property types to include
            radius (float): Search radius in kilometers
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
            progress_callback (callable, optional): Function to call with progress updates
        """
        if self.search_in_progress:
            logger.warning("Search already in progress, ignoring new request")
            return
        
        self.search_in_progress = True
        self.cancel_search = False
        
        # Check if this is a city-level search (radius 0)
        is_city_search = radius == 0
        if is_city_search:
            logger.info(f"Simple batch searching for entire city: {address}")
            if hasattr(self.app, 'show_status'):
                self.app.show_status(f"Searching for all buildings in {address}...")
        else:
            logger.info(f"Simple batch searching by address: {address} with radius {radius}km")
            if hasattr(self.app, 'show_status'):
                self.app.show_status(f"Searching for buildings near {address}...")
        
        # Convert radius from km to meters
        radius_meters = int(float(radius) * 1000)
        
        # Start the search in a background thread
        threading.Thread(
            target=self._batch_search_thread,
            args=(address, property_types, radius_meters, max_results, callback, progress_callback),
            daemon=True
        ).start()
    
    def _batch_search_thread(self, address, property_types, radius_meters, max_results, callback, progress_callback=None):
        """
        Background thread for batch searching buildings by address.
        
        Args:
            address (str): Full address to search in
            property_types (list): List of property types to include
            radius_meters (int): Search radius in meters
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
            progress_callback (callable, optional): Function to call with progress updates
        """
        try:
            # Update progress
            if progress_callback:
                self.app.root.after(0, lambda: progress_callback(0, 100, "Geocoding address..."))
            
            # Get coordinates for the address using Google Maps
            geocode_result = self.app.google_maps.geocode(address)
            
            if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
                logger.error(f"Could not geocode address: {address}")
                if hasattr(self.app, 'show_status'):
                    self.app.root.after(0, lambda: self.app.show_status("Ready"))
                from tkinter import messagebox
                self.app.root.after(0, lambda: messagebox.showerror(
                    "Geocoding Error", f"Could not find coordinates for address: {address}"))
                self.search_in_progress = False
                return
            
            lat = geocode_result['lat']
            lng = geocode_result['lng']
            logger.info(f"Geocoded coordinates: lat={lat}, lng={lng}")
            
            # Fetch buildings using the Overpass API
            buildings = self._fetch_real_buildings(lat, lng, radius_meters, address, radius_meters/1000, progress_callback)
            
            # Process the buildings and prepare results
            if buildings and len(buildings) > 0:
                logger.info(f"Successfully found {len(buildings)} real buildings from OpenStreetMap")
                
                # Process buildings and generate URLs
                processed_buildings = self._process_buildings(buildings, progress_callback)
                
                # Call the callback function with the results
                if callback:
                    self.app.root.after(0, lambda: callback(processed_buildings))
                
                # Update status
                if hasattr(self.app, 'show_status'):
                    self.app.root.after(0, lambda: self.app.show_status(
                        f"Found {len(buildings)} buildings near {address}"))
            else:
                logger.warning(f"No buildings found near {address}")
                if hasattr(self.app, 'show_status'):
                    self.app.root.after(0, lambda: self.app.show_status("Ready"))
                from tkinter import messagebox
                self.app.root.after(0, lambda: messagebox.showwarning(
                    "No Buildings Found", f"No buildings found within {radius_meters/1000:.1f}km of {address}"))
        
        except Exception as e:
            logger.error(f"Error in batch search thread: {e}", exc_info=True)
            if hasattr(self.app, 'show_status'):
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
            from tkinter import messagebox
            self.app.root.after(0, lambda: messagebox.showerror("Error", f"An error occurred: {str(e)}"))
        
        finally:
            self.search_in_progress = False
    
    def _fetch_real_buildings(self, lat, lng, radius_meters, location_name=None, radius_km=None, progress_callback=None):
        """
        Fetch real building data from OpenStreetMap using the Overpass API.
        
        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius_meters (int): Search radius in meters
            location_name (str, optional): Name of the location (for logging purposes)
            radius_km (float, optional): Radius in kilometers (for logging purposes)
            progress_callback (callable, optional): Function to call with progress updates
            
        Returns:
            list: List of building coordinates
        """
        try:
            # Update progress
            if progress_callback:
                self.app.root.after(0, lambda: progress_callback(10, 100, "Querying OpenStreetMap API..."))
            
            # Construct the Overpass API query for buildings
            overpass_url = "https://overpass-api.de/api/interpreter"
            
            # Calculate a slightly larger radius to ensure we capture all buildings
            extended_radius = int(radius_meters * 1.1)  # 10% larger radius
            
            overpass_query = f"""
            [out:json][timeout:180];
            (
              // Standard building tags
              way["building"](around:{extended_radius},{lat},{lng});
              relation["building"](around:{extended_radius},{lat},{lng});
              node["building"](around:{extended_radius},{lat},{lng});
            );
            out center;
            """
            
            # Make the request
            response = requests.post(overpass_url, data={"data": overpass_query}, timeout=180)
            
            # Update progress
            if progress_callback:
                self.app.root.after(0, lambda: progress_callback(30, 100, "Processing OpenStreetMap data..."))
            
            # Check if the request was successful
            if response.status_code == 200:
                # Parse the response
                data = response.json()
                
                # Extract building coordinates
                points = []
                processed_ids = set()  # To avoid duplicates
                
                for element in data.get('elements', []):
                    element_id = f"{element.get('type')}_{element.get('id')}"
                    
                    # Skip if we've already processed this element
                    if element_id in processed_ids:
                        continue
                    
                    # Extract coordinates based on element type
                    if element.get('type') == 'way' and 'center' in element:
                        points.append({
                            'lat': element['center']['lat'],
                            'lng': element['center']['lon'],
                            'tags': element.get('tags', {}),
                            'id': element.get('id'),
                            'type': element.get('type')
                        })
                        processed_ids.add(element_id)
                    elif element.get('type') == 'relation' and 'center' in element:
                        points.append({
                            'lat': element['center']['lat'],
                            'lng': element['center']['lon'],
                            'tags': element.get('tags', {}),
                            'id': element.get('id'),
                            'type': element.get('type')
                        })
                        processed_ids.add(element_id)
                    elif element.get('type') == 'node' and 'lat' in element and 'lon' in element:
                        # Only include nodes that are actually buildings
                        tags = element.get('tags', {})
                        if tags.get('building') or tags.get('building:part') or tags.get('addr:housenumber'):
                            points.append({
                                'lat': element['lat'],
                                'lng': element['lon'],
                                'tags': tags,
                                'id': element.get('id'),
                                'type': element.get('type')
                            })
                            processed_ids.add(element_id)
                
                # Update progress
                if progress_callback:
                    self.app.root.after(0, lambda: progress_callback(50, 100, f"Found {len(points)} buildings..."))
                
                return points
            else:
                error_msg = f"Error fetching buildings from OpenStreetMap: {response.status_code}"
                logger.error(error_msg)
                raise ValueError(error_msg)
        
        except Exception as e:
            error_msg = f"Error fetching real buildings: {e}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg)
    
    def _process_buildings(self, buildings, progress_callback=None):
        """
        Process the buildings found and generate URLs for each one.
        
        Args:
            buildings (list): List of building coordinates
            progress_callback (callable, optional): Function to call with progress updates
            
        Returns:
            list: List of processed building data
        """
        processed_buildings = []
        total_buildings = len(buildings)
        
        # Update progress
        if progress_callback:
            self.app.root.after(0, lambda: progress_callback(60, 100, f"Processing {total_buildings} buildings..."))
        
        for idx, point in enumerate(buildings):
            try:
                # Update progress every 10 buildings
                if progress_callback and idx % 10 == 0:
                    progress_pct = 60 + (idx / total_buildings) * 40
                    self.app.root.after(0, lambda: progress_callback(
                        int(progress_pct), 100, f"Processing building {idx+1} of {total_buildings}..."))
                
                # Convert WGS84 to S-JTSK
                x, y = self.app.cuzk_integration.convert_wgs84_to_sjtsk(point['lat'], point['lng'])
                
                # Generate MapaIdentifikace URL
                url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={int(x)}&y={int(y)}"
                
                # Create building data dictionary
                building_data = {
                    'id': idx + 1,
                    'lat': point['lat'],
                    'lng': point['lng'],
                    'x': int(x),
                    'y': int(y),
                    'url': url
                }
                
                # Add additional data if available
                if 'tags' in point:
                    # Extract useful tags
                    tags = point.get('tags', {})
                    
                    # Extract name
                    if tags.get('name'):
                        building_data['name'] = tags.get('name')
                    
                    # Extract address components
                    if tags.get('addr:street'):
                        building_data['street'] = tags.get('addr:street')
                    if tags.get('addr:housenumber'):
                        building_data['housenumber'] = tags.get('addr:housenumber')
                    if tags.get('addr:city'):
                        building_data['city'] = tags.get('addr:city')
                    if tags.get('addr:postcode'):
                        building_data['postcode'] = tags.get('addr:postcode')
                    
                    # Extract building type
                    if tags.get('building'):
                        building_data['building_type'] = tags.get('building')
                    elif tags.get('building:part'):
                        building_data['building_type'] = tags.get('building:part')
                    elif tags.get('amenity'):
                        building_data['building_type'] = tags.get('amenity')
                    elif tags.get('shop'):
                        building_data['building_type'] = tags.get('shop')
                    elif tags.get('office'):
                        building_data['building_type'] = tags.get('office')
                    
                    # Extract RUIAN ID
                    if tags.get('ref:ruian'):
                        building_data['ruian_id'] = tags.get('ref:ruian')
                    elif tags.get('ref:ruian:building'):
                        building_data['ruian_id'] = tags.get('ref:ruian:building')
                
                processed_buildings.append(building_data)
            
            except Exception as e:
                logger.error(f"Error processing building {idx+1}: {e}")
        
        # Update progress
        if progress_callback:
            self.app.root.after(0, lambda: progress_callback(100, 100, "Search completed"))
        
        return processed_buildings
    
    def cancel_current_search(self):
        """Cancel the current search operation if one is in progress."""
        if self.search_in_progress:
            logger.info("Cancelling current search operation")
            self.cancel_search = True
            if hasattr(self.app, 'show_status'):
                self.app.show_status("Cancelling search...")
        else:
            logger.info("No search in progress to cancel")
