"""
Thread management utilities for the Czech Property Registry application.
Provides centralized thread management and execution.
"""

import threading
import time
import queue
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from typing import Dict, Any, Callable, List, Optional, Tuple, Set
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ThreadManager")


class ThreadManager:
    """
    Manages thread creation, execution, and lifecycle for the application.
    Provides a unified interface for running tasks asynchronously.
    """

    def __init__(self, max_workers: int = 5, thread_name_prefix: str = "PropertyScraper"):
        """
        Initialize the thread manager.

        Args:
            max_workers (int): Maximum number of worker threads (default: 5)
            thread_name_prefix (str): Prefix for thread names
        """
        self._executor = ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix=thread_name_prefix
        )
        self._futures: Dict[str, Future] = {}
        self._futures_lock = threading.Lock()
        self._task_queue = queue.PriorityQueue()
        self._running = True
        self._active_tasks: Set[str] = set()
        self._active_tasks_lock = threading.Lock()
        
        # Start the task processor thread
        self._processor_thread = threading.Thread(
            target=self._process_tasks,
            name=f"{thread_name_prefix}-Processor",
            daemon=True
        )
        self._processor_thread.start()
        
        logger.info(f"ThreadManager initialized with {max_workers} workers")

    def submit(self, task_id: str, func: Callable, *args, priority: int = 1, **kwargs) -> Future:
        """
        Submit a task for execution.

        Args:
            task_id (str): Unique identifier for the task
            func (Callable): Function to execute
            *args: Arguments to pass to the function
            priority (int): Task priority (lower number = higher priority)
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Future: Future object representing the task
        """
        with self._futures_lock:
            # Cancel existing task with the same ID if it exists and is not done
            if task_id in self._futures and not self._futures[task_id].done():
                self._futures[task_id].cancel()
                logger.info(f"Cancelled existing task {task_id}")
            
            # Create a wrapper function that updates task status
            def task_wrapper():
                try:
                    with self._active_tasks_lock:
                        self._active_tasks.add(task_id)
                    logger.debug(f"Starting task {task_id}")
                    result = func(*args, **kwargs)
                    logger.debug(f"Completed task {task_id}")
                    return result
                except Exception as e:
                    logger.error(f"Error in task {task_id}: {e}")
                    raise
                finally:
                    with self._active_tasks_lock:
                        self._active_tasks.discard(task_id)
            
            # Submit the task to the executor
            future = self._executor.submit(task_wrapper)
            self._futures[task_id] = future
            logger.info(f"Submitted task {task_id} with priority {priority}")
            return future

    def submit_prioritized(self, task_id: str, func: Callable, *args, priority: int = 1, **kwargs) -> None:
        """
        Submit a task to the priority queue for execution.

        Args:
            task_id (str): Unique identifier for the task
            func (Callable): Function to execute
            *args: Arguments to pass to the function
            priority (int): Task priority (lower number = higher priority)
            **kwargs: Keyword arguments to pass to the function
        """
        # Add the task to the priority queue
        self._task_queue.put((priority, (task_id, func, args, kwargs)))
        logger.info(f"Queued task {task_id} with priority {priority}")

    def _process_tasks(self) -> None:
        """Process tasks from the priority queue."""
        while self._running:
            try:
                # Get the next task from the queue
                priority, (task_id, func, args, kwargs) = self._task_queue.get(timeout=1.0)
                
                # Submit the task
                self.submit(task_id, func, *args, **kwargs)
                
                # Mark the task as done
                self._task_queue.task_done()
            except queue.Empty:
                # No tasks in the queue, just continue
                pass
            except Exception as e:
                logger.error(f"Error processing task: {e}")

    def cancel(self, task_id: str) -> bool:
        """
        Cancel a task.

        Args:
            task_id (str): ID of the task to cancel

        Returns:
            bool: True if the task was cancelled, False otherwise
        """
        with self._futures_lock:
            if task_id in self._futures and not self._futures[task_id].done():
                result = self._futures[task_id].cancel()
                if result:
                    logger.info(f"Cancelled task {task_id}")
                return result
        return False

    def cancel_all(self) -> int:
        """
        Cancel all running tasks.

        Returns:
            int: Number of tasks cancelled
        """
        count = 0
        with self._futures_lock:
            for task_id, future in self._futures.items():
                if not future.done() and future.cancel():
                    count += 1
        
        logger.info(f"Cancelled {count} tasks")
        return count

    def wait_for(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """
        Wait for a task to complete and return its result.

        Args:
            task_id (str): ID of the task to wait for
            timeout (float, optional): Maximum time to wait in seconds

        Returns:
            Any: The result of the task

        Raises:
            KeyError: If the task ID is not found
            TimeoutError: If the timeout expires before the task completes
        """
        with self._futures_lock:
            if task_id not in self._futures:
                raise KeyError(f"Task {task_id} not found")
            future = self._futures[task_id]
        
        try:
            return future.result(timeout=timeout)
        except TimeoutError:
            logger.warning(f"Timeout waiting for task {task_id}")
            raise

    def is_running(self, task_id: str) -> bool:
        """
        Check if a task is currently running.

        Args:
            task_id (str): ID of the task to check

        Returns:
            bool: True if the task is running, False otherwise
        """
        with self._active_tasks_lock:
            return task_id in self._active_tasks

    def get_active_tasks(self) -> List[str]:
        """
        Get a list of currently active task IDs.

        Returns:
            List[str]: List of active task IDs
        """
        with self._active_tasks_lock:
            return list(self._active_tasks)

    def shutdown(self, wait: bool = True) -> None:
        """
        Shut down the thread manager.

        Args:
            wait (bool): Whether to wait for tasks to complete
        """
        self._running = False
        self._executor.shutdown(wait=wait)
        logger.info(f"ThreadManager shut down (wait={wait})")
