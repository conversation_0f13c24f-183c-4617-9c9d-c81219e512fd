"""
Test script for the real batch search functionality.

This script tests the real batch search manager by fetching buildings
within a radius of a given address using the Overpass API.
"""

import os
import sys
import json
import logging
import requests
from typing import List, Dict, Any, Optional, Tuple

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TestRealBatchSearch")

def fetch_real_buildings(lat: float, lng: float, radius_meters: int) -> List[Dict[str, float]]:
    """
    Fetch real building data from OpenStreetMap using the Overpass API.
    
    Args:
        lat (float): Latitude of the center point
        lng (float): Longitude of the center point
        radius_meters (int): Search radius in meters
        
    Returns:
        list: List of building coordinates
        
    Raises:
        ValueError: If no real buildings are found or API request fails
    """
    try:
        # Always fetch fresh data from the Overpass API
        logger.info("Fetching fresh data from Overpass API")
        
        # Construct the Overpass API query for buildings
        overpass_url = "https://overpass-api.de/api/interpreter"
        overpass_query = f"""
        [out:json][timeout:60];
        (
          way["building"](around:{radius_meters},{lat},{lng});
          relation["building"](around:{radius_meters},{lat},{lng});
          node["building"](around:{radius_meters},{lat},{lng});
        );
        out center;
        """
        
        logger.info(f"Querying Overpass API for buildings within {radius_meters}m of {lat}, {lng}")
        
        # Define cache file path for saving results
        cache_file = f"overpass_result_{lat:.5f}_{lng:.5f}_{radius_meters}.json"
        
        # Make the request
        response = requests.post(overpass_url, data={"data": overpass_query}, timeout=60)
        
        # Check if the request was successful
        if response.status_code == 200:
            # Parse the response
            data = response.json()
            
            # Save the results to a cache file for future reference (not for fallback)
            try:
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f)
                logger.info(f"Saved Overpass API results to cache file {cache_file}")
            except Exception as e:
                logger.error(f"Error saving Overpass API results to cache: {e}")
            
            # Log the number of elements found
            num_elements = len(data.get('elements', []))
            logger.info(f"Overpass API returned {num_elements} elements")
            
            # Extract building coordinates
            points = []
            for element in data.get('elements', []):
                if element.get('type') == 'way' and 'center' in element:
                    points.append({
                        'lat': element['center']['lat'],
                        'lng': element['center']['lon']
                    })
                elif element.get('type') == 'relation' and 'center' in element:
                    points.append({
                        'lat': element['center']['lat'],
                        'lng': element['center']['lon']
                    })
                elif element.get('type') == 'node' and 'lat' in element and 'lon' in element:
                    points.append({
                        'lat': element['lat'],
                        'lng': element['lon']
                    })
            
            # Log the number of points extracted
            logger.info(f"Extracted {len(points)} building coordinates from Overpass API response")
            
            if len(points) > 0:
                return points
            else:
                error_msg = f"No buildings found at coordinates {lat}, {lng} with radius {radius_meters}m"
                logger.error(error_msg)
                raise ValueError(error_msg)
        else:
            error_msg = f"Error fetching buildings from OpenStreetMap: {response.status_code}"
            logger.error(error_msg)
            raise ValueError(error_msg)
            
    except requests.exceptions.RequestException as e:
        error_msg = f"Error making Overpass API call: {e}"
        logger.error(error_msg)
        raise ValueError(error_msg)
    except Exception as e:
        error_msg = f"Error fetching real buildings: {e}"
        logger.error(error_msg, exc_info=True)
        raise ValueError(error_msg)

def convert_wgs84_to_sjtsk(lat: float, lng: float) -> Tuple[int, int]:
    """
    Convert WGS84 coordinates to S-JTSK.
    
    Args:
        lat (float): Latitude in WGS84
        lng (float): Longitude in WGS84
        
    Returns:
        tuple: (x, y) coordinates in S-JTSK as integers
    """
    # Constants for the Czech Republic region
    lat_0 = 49.5
    lng_0 = 15.0
    
    # Scale factors
    lat_scale = 111320  # meters per degree of latitude
    lng_scale = 73000   # approximate meters per degree of longitude at Czech latitude
    
    # Calculate offsets from the central point
    lat_offset = (lat - lat_0) * lat_scale
    lng_offset = (lng - lng_0) * lng_scale
    
    # Convert to S-JTSK coordinates
    # Note: S-JTSK has a different orientation than WGS84
    # The negative signs account for the different orientation
    x = int(-1 * lng_offset - 850000)
    y = int(-1 * lat_offset - 1000000)
    
    # CUZK MapaIdentifikace.aspx expects integers
    return (x, y)

def process_buildings(buildings: List[Dict[str, float]]) -> List[Dict[str, Any]]:
    """
    Process the buildings found and generate URLs for each one.
    
    Args:
        buildings (list): List of building coordinates
        
    Returns:
        list: List of processed building data
    """
    processed_buildings = []
    total_buildings = len(buildings)
    
    logger.info(f"Processing {total_buildings} buildings...")
    
    for idx, point in enumerate(buildings):
        try:
            # Convert WGS84 to S-JTSK
            x, y = convert_wgs84_to_sjtsk(point['lat'], point['lng'])
            
            # Ensure coordinates are integers
            x_int = int(x)
            y_int = int(y)
            
            # Generate MapaIdentifikace URL
            url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"
            
            # Create building data dictionary
            building_data = {
                'id': idx + 1,
                'lat': point['lat'],
                'lng': point['lng'],
                'x': x_int,
                'y': y_int,
                'url': url
            }
            
            processed_buildings.append(building_data)
            
        except Exception as e:
            logger.error(f"Error processing building {idx+1}: {e}")
    
    logger.info(f"Completed processing {total_buildings} buildings")
    
    return processed_buildings

def main():
    """Main function to test the real batch search functionality."""
    try:
        # Test with Prague coordinates
        lat = 50.0755381
        lng = 14.4378005
        radius_km = 0.5
        
        logger.info(f"Testing with coordinates: lat={lat}, lng={lng}")
        logger.info(f"Selected radius: {radius_km} km")
        
        # Convert radius from km to meters
        radius_meters = int(radius_km * 1000)
        
        # Fetch buildings using the Overpass API
        buildings = fetch_real_buildings(lat, lng, radius_meters)
        
        # Process the buildings and prepare results
        if buildings and len(buildings) > 0:
            logger.info(f"Successfully found {len(buildings)} real buildings from OpenStreetMap")
            
            # Process buildings and generate URLs
            processed_buildings = process_buildings(buildings)
            
            # Print the first 10 buildings
            logger.info("First 10 buildings:")
            for i, building in enumerate(processed_buildings[:10]):
                logger.info(f"Building {i+1}: {building}")
            
            logger.info(f"Total buildings found: {len(processed_buildings)}")
        else:
            logger.warning(f"No buildings found near coordinates {lat}, {lng}")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)

if __name__ == "__main__":
    main()
