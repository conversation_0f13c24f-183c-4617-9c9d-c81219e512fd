"""
Mapy.cz API client for the Czech Property Registry application.

This module provides a client for interacting with the Mapy.cz API.
"""

import os
import json
import requests
import logging
from typing import Dict, Any, List, Optional, Union
from urllib.parse import quote
import time

# Configure logging
logger = logging.getLogger(__name__)

class MapyCZClient:
    """
    Client for interacting with the Mapy.cz API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Mapy.cz API client.

        Args:
            api_key (str, optional): Mapy.cz API key
        """
        self.api_key = api_key
        self.base_url = "https://api.mapy.cz"
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9,cs;q=0.8",
            "Referer": "https://mapy.cz/",
            "Origin": "https://mapy.cz"
        })

        # Load API key from config if not provided
        if not self.api_key:
            self._load_api_key_from_config()

        logger.info("Initialized Mapy.cz API client")

    def _load_api_key_from_config(self):
        """
        Load the Mapy.cz API key from the configuration file.
        """
        try:
            if os.path.exists('config/api_keys.json'):
                with open('config/api_keys.json', 'r') as f:
                    config = json.load(f)
                    self.api_key = config.get('mapycz_api_key')
                    if self.api_key:
                        logger.info("Loaded Mapy.cz API key from config")
                    else:
                        logger.warning("No Mapy.cz API key found in config")
            else:
                logger.warning("No API keys config file found")
        except Exception as e:
            logger.error(f"Error loading API key from config: {e}")

    def geocode(self, address: str) -> Dict[str, Any]:
        """
        Geocode an address using Mapy.cz API.

        Args:
            address (str): The address to geocode

        Returns:
            dict: Dictionary with lat, lng, and other location data
        """
        try:
            # Encode the address for URL
            encoded_address = quote(address)

            # Use Mapy.cz geocoding API
            url = f"{self.base_url}/v1/geocode"

            params = {
                "query": address
            }

            if self.api_key:
                params["apiKey"] = self.api_key

            # Make the request
            response = self.session.get(url, params=params)

            # Check if the request was successful
            if response.status_code == 200:
                data = response.json()

                # Process the response
                if data and "items" in data and len(data["items"]) > 0:
                    item = data["items"][0]

                    # Extract coordinates
                    position = item.get("position", {})
                    lat = position.get("lat")
                    lng = position.get("lon")

                    # Extract address components
                    address_components = {}
                    if "address" in item:
                        address_data = item["address"]
                        address_components = {
                            "street": address_data.get("street"),
                            "house_number": address_data.get("house_number"),
                            "city": address_data.get("city"),
                            "postal_code": address_data.get("postal_code"),
                            "country": address_data.get("country")
                        }

                    # Create formatted address
                    formatted_address = item.get("name", address)

                    return {
                        "lat": lat,
                        "lng": lng,
                        "formatted_address": formatted_address,
                        "address_components": address_components,
                        "place_id": item.get("id")
                    }

                logger.warning(f"No results found for address: {address}")
                return {}

            logger.error(f"Error geocoding address: {response.status_code} - {response.text}")
            return {}

        except Exception as e:
            logger.error(f"Error geocoding address with Mapy.cz: {e}")
            return {}

    def reverse_geocode(self, lat: float, lng: float) -> Dict[str, Any]:
        """
        Reverse geocode coordinates using Mapy.cz API.

        Args:
            lat (float): Latitude
            lng (float): Longitude

        Returns:
            dict: Dictionary with address information
        """
        try:
            # Use Mapy.cz geocode API with coordinates
            # The public API doesn't have a direct reverse geocode endpoint, so we use the geocode endpoint with coordinates
            url = f"https://api.mapy.cz/geocode"

            params = {
                "query": f"{lat},{lng}"
            }

            if self.api_key:
                params["apiKey"] = self.api_key

            # Make the request
            response = self.session.get(url, params=params)

            # Check if the request was successful
            if response.status_code == 200:
                try:
                    data = response.json()

                    # Process the response
                    if data and "items" in data and len(data["items"]) > 0:
                        item = data["items"][0]

                        # Extract address components
                        address_components = {}
                        if "address" in item:
                            address_data = item["address"]
                            address_components = {
                                "street": address_data.get("street"),
                                "house_number": address_data.get("house_number"),
                                "city": address_data.get("city"),
                                "postal_code": address_data.get("postal_code"),
                                "country": address_data.get("country")
                            }

                        # Create formatted address
                        formatted_address = item.get("name", f"{lat}, {lng}")

                        return {
                            "formatted_address": formatted_address,
                            "address_components": address_components,
                            "place_id": item.get("id")
                        }

                    logger.warning(f"No results found for coordinates: {lat}, {lng}")
                except Exception as e:
                    logger.error(f"Error parsing reverse geocoding response: {e}")

                # If we couldn't parse the response or no results were found, return a basic result
                return {
                    "formatted_address": f"{lat}, {lng}",
                    "address_components": {}
                }

            logger.error(f"Error reverse geocoding: {response.status_code} - {response.text}")
            return {
                "formatted_address": f"{lat}, {lng}",
                "address_components": {}
            }

        except Exception as e:
            logger.error(f"Error reverse geocoding with Mapy.cz: {e}")
            return {
                "formatted_address": f"{lat}, {lng}",
                "address_components": {}
            }
