"""
Batch Processing Manager for the Czech Property Registry application.

This module provides functionality for processing multiple properties
in batch, including filtering by property type and retrieving owner information.
"""

import threading
import time
import logging
from typing import List, Dict, Any, Optional, Callable

from ui.message_boxes import MessageBoxes
from models.ruian_id import RuianID

# Set up logging
logger = logging.getLogger(__name__)


class BatchProcessingManager:
    """
    Manager for batch processing of properties.

    This class provides methods for processing multiple properties in batch,
    including filtering by property type and retrieving owner information.
    """

    def __init__(self, app):
        """
        Initialize the batch processing manager.

        Args:
            app: The main application instance for callbacks and UI access
        """
        self.app = app
        self.processing_thread = None
        self.stop_processing = False
        self.processed_properties = []
        self.current_batch = []
        self.progress_callback = None
        self.completion_callback = None
        logger.info("BatchProcessingManager initialized")

    def process_selected_buildings(self, selected_buildings: List[Dict[str, Any]],
                                  progress_callback: Optional[Callable[[int, int], None]] = None,
                                  completion_callback: Optional[Callable[[List[Dict[str, Any]]], None]] = None) -> None:
        """
        Process selected buildings to retrieve owner information.

        Args:
            selected_buildings: List of selected building dictionaries
            progress_callback: Callback function for progress updates (current, total)
            completion_callback: Callback function for completion notification
        """
        # Store the callbacks
        self.progress_callback = progress_callback
        self.completion_callback = completion_callback

        # Store the current batch
        self.current_batch = selected_buildings
        self.processed_properties = []
        self.stop_processing = False

        # Start processing in a separate thread
        self.processing_thread = threading.Thread(
            target=self._process_buildings_thread,
            args=(selected_buildings,),
            daemon=True
        )
        self.processing_thread.start()

    def _process_buildings_thread(self, buildings: List[Dict[str, Any]]) -> None:
        """
        Thread function for processing buildings.

        Args:
            buildings: List of building dictionaries to process
        """
        try:
            total = len(buildings)
            processed = 0
            batch_size = 5  # Process buildings in batches for better performance

            # Update status
            self.app.show_status(f"Processing {total} buildings...")

            # Process buildings in batches
            for i in range(0, total, batch_size):
                # Check if processing should be stopped
                if self.stop_processing:
                    logger.info("Batch processing stopped by user")
                    break

                # Get the current batch
                batch = buildings[i:i+batch_size]

                # Process each building in the batch
                for building in batch:
                    # Check if processing should be stopped
                    if self.stop_processing:
                        logger.info("Batch processing stopped by user")
                        break

                    # Process the building
                    property_data = self._process_single_building(building)

                    # Add to processed properties if successful
                    if property_data:
                        self.processed_properties.append(property_data)

                    # Update progress
                    processed += 1
                    if self.progress_callback:
                        self.app.root.after(0, lambda p=processed: self.progress_callback(p, total))

                # Add a small delay between batches to prevent UI freezing
                time.sleep(0.1)

            # Update status
            self.app.show_status(f"Processed {len(self.processed_properties)} of {total} buildings")

            # Call completion callback
            if self.completion_callback:
                self.app.root.after(0, lambda: self.completion_callback(self.processed_properties))

        except Exception as e:
            logger.error(f"Error in batch processing thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error("Processing Error", f"Error processing buildings: {str(e)}"))
            self.app.show_status("Ready")

    def _process_single_building(self, building: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process a single building to retrieve owner information.

        Args:
            building: Building dictionary to process

        Returns:
            dict: Property data dictionary with owner information, or None if processing failed
        """
        try:
            # Extract RUIAN ID
            ruian_ref = building.get('ruian_ref')

            # Validate RUIAN ID
            ruian_id = RuianID.from_string(ruian_ref)
            if not ruian_id:
                logger.warning(f"Invalid RUIAN ID: {ruian_ref}")
                return None

            # Get property data from CUZK
            property_data = self._get_property_data_from_cuzk(ruian_id.value)

            # If property data was retrieved successfully, add building information
            if property_data:
                property_data.update({
                    'building_name': building.get('name', ''),
                    'building_address': building.get('address', ''),
                    'building_type': building.get('building_type', 'unknown'),
                    'ruian_id': ruian_id.value,
                    'osm_id': building.get('osm_id', ''),
                    'osm_type': building.get('osm_type', ''),
                    'lat': building.get('lat', ''),
                    'lng': building.get('lng', ''),
                })
                return property_data

            return None

        except Exception as e:
            logger.error(f"Error processing building: {e}", exc_info=True)
            return None

    def _get_property_data_from_cuzk(self, ruian_id: str) -> Optional[Dict[str, Any]]:
        """
        Get property data from CUZK using the RUIAN ID.

        Args:
            ruian_id: RUIAN ID of the property

        Returns:
            dict: Property data dictionary with owner information, or None if retrieval failed

        Raises:
            ValueError: If property data cannot be retrieved and no API is available
        """
        # Check if we should always use API data
        always_prefer_api = True
        if hasattr(self.app, 'data_manager'):
            always_prefer_api = getattr(self.app.data_manager, 'always_prefer_api', True)

        # If we're always preferring API data, skip the cache
        if always_prefer_api:
            logger.info(f"Always preferring API data for RUIAN ID: {ruian_id}")
        # Otherwise, try to get from cache first
        elif hasattr(self.app, 'data_manager'):
            try:
                # Check if we have a property cache
                property_cache_key = f"property_{ruian_id}"
                property_data = self.app.data_manager.get_cached_data(property_cache_key)
                if property_data:
                    logger.info(f"Using cached property data for RUIAN ID: {ruian_id}")
                    # Show notification that cached data is being used
                    self._show_cache_notification(ruian_id)
                    return property_data
            except Exception as e:
                logger.warning(f"Error getting property data from cache: {e}")
                # Continue to API sources

        try:
            # Use the CUZK integration to get property data
            if hasattr(self.app, 'cuzk_integration'):
                property_data = self.app.cuzk_integration.get_property_by_ruian_id(ruian_id)

                # Cache the data if available
                if property_data and hasattr(self.app, 'data_manager'):
                    try:
                        property_cache_key = f"property_{ruian_id}"
                        self.app.data_manager.cache_data(property_cache_key, property_data)
                    except Exception as e:
                        logger.warning(f"Error caching property data: {e}")

                return property_data

            # If CUZK integration is not available, use the property search
            if hasattr(self.app, 'property_search'):
                property_data = self.app.property_search.unified_search('ruian', ruian_id=ruian_id)

                # Cache the data if available
                if property_data and hasattr(self.app, 'data_manager'):
                    try:
                        property_cache_key = f"property_{ruian_id}"
                        self.app.data_manager.cache_data(property_cache_key, property_data)
                    except Exception as e:
                        logger.warning(f"Error caching property data: {e}")

                return property_data

            # If neither is available, raise an error
            error_msg = "No CUZK integration or property search available"
            logger.error(error_msg)
            raise ValueError(error_msg)

        except Exception as e:
            logger.error(f"Error getting property data from CUZK: {e}", exc_info=True)
            return None

    def get_real_property_data(self, ruian_id: str) -> Dict[str, Any]:
        """
        Get real property data from CUZK using the RUIAN ID.
        This method retrieves real property data from the API.

        Args:
            ruian_id: RUIAN ID of the property

        Returns:
            dict: Property data dictionary with owner information

        Raises:
            ValueError: If property data cannot be retrieved
        """
        # Always log that we're using real API data
        logger.info(f"Getting real property data from API for RUIAN ID: {ruian_id}")

        # Use the CUZK integration to get property data
        if hasattr(self.app, 'cuzk_integration'):
            data = self.app.cuzk_integration.get_property_by_ruian_id(ruian_id)
            if data:
                # Cache the data if available
                if hasattr(self.app, 'data_manager'):
                    try:
                        property_cache_key = f"property_{ruian_id}"
                        self.app.data_manager.cache_data(property_cache_key, data)
                    except Exception as e:
                        logger.warning(f"Error caching property data: {e}")
                return data
            else:
                error_msg = f"Failed to get property data for RUIAN ID: {ruian_id}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        # If CUZK integration is not available, use the property search
        if hasattr(self.app, 'property_search'):
            data = self.app.property_search.unified_search('ruian', ruian_id=ruian_id)
            if data:
                return data
            else:
                error_msg = f"Failed to get property data for RUIAN ID: {ruian_id}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        # If neither is available, raise an error
        error_msg = "No CUZK integration or property search available"
        logger.error(error_msg)
        raise ValueError(error_msg)

    def _show_cache_notification(self, ruian_id: str) -> None:
        """
        Show a notification that cached property data is being used.

        Args:
            ruian_id: RUIAN ID of the property
        """
        # Only show notification if we have a notification banner
        if hasattr(self.app, 'notification_banner'):
            # Create the notification message
            message = f"Using cached property data for RUIAN ID: {ruian_id}. Real API data is preferred but not available."

            # Show the notification
            self.app.notification_banner.show(
                message=message,
                notification_type="fallback",
                auto_hide=True,
                duration=5000,
                key=f"cache_property_{ruian_id}"
            )

            # Also update the status bar
            self.app.show_status(f"Using cached property data for RUIAN ID: {ruian_id}")

    def stop_processing_batch(self) -> None:
        """Stop the current batch processing."""
        self.stop_processing = True
        logger.info("Batch processing stop requested")
