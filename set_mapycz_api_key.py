"""
<PERSON><PERSON><PERSON> to set the Mapy.cz API key in the configuration file.
"""

import os
import json
import sys
import argparse

def set_api_key(api_key):
    """
    Set the Mapy.cz API key in the configuration file.
    
    Args:
        api_key (str): The API key to set
    """
    # Create config directory if it doesn't exist
    if not os.path.exists('config'):
        os.makedirs('config')
    
    # Load existing config if it exists
    config = {}
    if os.path.exists('config/api_keys.json'):
        try:
            with open('config/api_keys.json', 'r') as f:
                config = json.load(f)
        except Exception as e:
            print(f"Error loading existing config: {e}")
    
    # Set the API key
    config['mapycz_api_key'] = api_key
    
    # Save the config
    try:
        with open('config/api_keys.json', 'w') as f:
            json.dump(config, f, indent=4)
        print(f"API key set successfully in config/api_keys.json")
    except Exception as e:
        print(f"Error saving config: {e}")
        return False
    
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Set the Mapy.cz API key in the configuration file.')
    parser.add_argument('api_key', help='The Mapy.cz API key to set')
    
    args = parser.parse_args()
    
    if not args.api_key:
        print("Error: API key is required")
        parser.print_help()
        return 1
    
    if set_api_key(args.api_key):
        print("You can now use the Mapy.cz API for coordinate conversion.")
        return 0
    else:
        print("Failed to set API key.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
