"""
UI initialization for the Czech Property Registry application.
"""

import tkinter as tk
from tkinter import ttk
import webbrowser
from utils import AutocompleteEntry

class AppUI:
    """
    Handles the initialization and setup of the application's user interface.
    This class is responsible for creating the main UI components and layout.
    """

    def __init__(self, app):
        """
        Initialize the UI components.

        Args:
            app: The main application instance
        """
        self.app = app
        self.root = app.root

        # Set up the style
        self._setup_style()

        # Create the main UI structure
        self._create_main_structure()

        # Create the login and buyer UI
        self._create_login_ui()

        # Create the search tabs
        self._create_search_tabs()

        # Create the map and results frames
        self._create_map_and_results_frames()

        # Create the letter output frame
        self._create_letter_output_frame()

        # Create the status bar
        self._create_status_bar()

    def _setup_style(self):
        """Set up the application style"""
        self.app.style = ttk.Style()
        self.app.style.configure("TFrame", background="#f0f0f0")
        self.app.style.configure("TLabel", background="#f0f0f0")
        self.app.style.configure("TButton", padding=6)
        self.app.style.configure("TNotebook", background="#f0f0f0")
        self.app.style.configure("TNotebook.Tab", padding=[10, 5])

        # Create an accent button style
        self.app.style.configure("Accent.TButton",
                            background="#4CAF50",
                            foreground="black",
                            padding=8)

        # Apply font scaling to all styles
        self.app.font_scaler.apply_to_style(self.app.style)

    def _create_main_structure(self):
        """Create the main UI structure with scrollbars"""
        # Create a main frame with scrollbars
        self.app.main_frame = ttk.Frame(self.root)
        self.app.main_frame.pack(fill="both", expand=True)

        # Create a canvas with scrollbars
        self.app.canvas = tk.Canvas(self.app.main_frame, highlightthickness=0)

        # Add vertical scrollbar to canvas - pack it first so it's always visible
        self.app.vsb = ttk.Scrollbar(self.app.main_frame, orient="vertical", command=self.app.canvas.yview)
        self.app.vsb.pack(side="right", fill="y")

        # Pack the canvas after the scrollbar
        self.app.canvas.pack(side="left", fill="both", expand=True)

        # Configure the canvas to use the scrollbar
        self.app.canvas.configure(yscrollcommand=self.app.vsb.set)

        # Add horizontal scrollbar to canvas - pack it first so it's always visible
        self.app.hsb = ttk.Scrollbar(self.root, orient="horizontal", command=self.app.canvas.xview)
        self.app.hsb.pack(side="bottom", fill="x")

        # Configure the canvas to use the horizontal scrollbar
        self.app.canvas.configure(xscrollcommand=self.app.hsb.set)

        # Create a frame inside the canvas for all content
        self.app.content_frame = ttk.Frame(self.app.canvas)
        # Create window with only the supported options
        self.app.canvas_frame = self.app.canvas.create_window(0, 0, anchor="nw", window=self.app.content_frame)

        # Create main container frames inside the content frame with a more compact layout
        # Main horizontal split - left side for search/input, right side for map
        self.app.main_paned = ttk.PanedWindow(self.app.content_frame, orient=tk.HORIZONTAL)
        self.app.main_paned.pack(fill="both", expand=True, padx=5, pady=5)  # Small padding

        # Left side contains search options and buyer info
        self.app.left_side = ttk.Frame(self.app.main_paned)

        # Right side contains map and results
        self.app.right_side = ttk.Frame(self.app.main_paned)

        # Add the sides to the paned window with appropriate weights - make left side narrower
        self.app.main_paned.add(self.app.left_side, weight=30)  # 30% of width
        self.app.main_paned.add(self.app.right_side, weight=70)  # 70% of width

        # Create vertical sections in the left side with minimal padding
        self.app.top_left = ttk.Frame(self.app.left_side)
        self.app.top_left.pack(fill="both", expand=True, padx=1, pady=1)  # Minimal padding, expands to fill space

        self.app.bottom_left = ttk.Frame(self.app.left_side)
        self.app.bottom_left.pack(fill="x", expand=False, padx=1, pady=1)  # Minimal padding, fixed height

        # Create vertical sections in the right side with better proportions
        # Create a paned window for the right side to allow user to adjust the split
        self.app.right_paned = ttk.PanedWindow(self.app.right_side, orient=tk.VERTICAL)
        self.app.right_paned.pack(fill="both", expand=True, padx=5, pady=5)  # Small padding

        # Top right for map (larger portion)
        self.app.top_right = ttk.Frame(self.app.right_paned)

        # Bottom right for results (smaller portion)
        self.app.bottom_right = ttk.Frame(self.app.right_paned)

        # Add frames to the paned window with appropriate weights - make map taller
        self.app.right_paned.add(self.app.top_right, weight=80)  # 80% of height for map
        self.app.right_paned.add(self.app.bottom_right, weight=20)  # 20% of height for results

        # For backward compatibility, map these to the old names
        self.app.left_column = self.app.top_left  # Search tabs go in top_left
        self.app.right_column = self.app.bottom_left  # Buyer info goes in bottom_left
        self.app.middle_right = self.app.top_right  # Map goes in top_right
        self.app.middle_left = self.app.bottom_right  # Results go in bottom_right

        # Create a frame for the letter output with minimal height
        self.app.bottom_frame = ttk.Frame(self.app.content_frame)
        self.app.bottom_frame.pack(fill="both", expand=False, padx=1, pady=1)  # Minimal padding and not expanding

        # Create a horizontal container for login and buyer info with minimal padding
        info_container = ttk.Frame(self.app.left_column)
        info_container.pack(fill="x", padx=0, pady=0)

        # Create login frame in the container - ultra compact
        self.app.login_frame = ttk.LabelFrame(info_container, text="ČÚZK Portal")
        self.app.login_frame.pack(side="left", fill="both", expand=True, padx=0, pady=0)

        # Create buyer frame in the container - ultra compact
        self.app.buyer_frame = ttk.LabelFrame(info_container, text="Buyer Information")
        self.app.buyer_frame.pack(side="right", fill="both", expand=True, padx=0, pady=0)

    def _create_search_tabs(self):
        """Create the search tabs - simplified to include real batch search and batch search"""
        # Create a frame for the real batch search
        self.app.real_batch_frame = ttk.Frame(self.app.left_column)
        self.app.real_batch_frame.pack(fill="both", expand=True, padx=0, pady=1)  # Minimal padding

        # Create a frame for the batch search
        self.app.batch_frame = ttk.Frame(self.app.left_column)
        self.app.batch_frame.pack(fill="both", expand=True, padx=0, pady=1)  # Minimal padding

        # Create the real batch search tab
        self._create_real_batch_search_tab()

        # Create the batch search tab
        self._create_batch_search_tab()

    def _create_osm_search_tab(self):
        """Create the OpenStreetMap search tab"""
        # Set up the OpenStreetMap search tab
        self.app.osm_search_frame = ttk.LabelFrame(self.app.osm_frame, text="Search Buildings with RUIAN References")
        self.app.osm_search_frame.pack(fill="both", expand=True, padx=1, pady=1)

        # Create a frame for the search options
        osm_options_frame = ttk.Frame(self.app.osm_search_frame)
        osm_options_frame.pack(fill="x", padx=2, pady=2)

        # Create a notebook for different OSM search methods
        self.app.osm_notebook = ttk.Notebook(self.app.osm_search_frame)
        self.app.osm_notebook.pack(fill="both", expand=True, padx=1, pady=1)

        # Create tabs for different OSM search methods
        self.app.osm_address_frame = ttk.Frame(self.app.osm_notebook)
        self.app.osm_coords_frame = ttk.Frame(self.app.osm_notebook)

        self.app.osm_notebook.add(self.app.osm_address_frame, text="Search by Address")
        self.app.osm_notebook.add(self.app.osm_coords_frame, text="Search by Coordinates")

        # === OSM Address Search Tab ===
        # Create a frame for the address search
        osm_address_search_frame = ttk.Frame(self.app.osm_address_frame)
        osm_address_search_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Full address with autocomplete
        ttk.Label(osm_address_search_frame, text="Full Address:").grid(row=0, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_full_address = AutocompleteEntry(
            osm_address_search_frame,
            width=30,
            autocomplete_function=self.app.get_osm_address_suggestions
        )
        self.app.osm_full_address.grid(row=0, column=1, columnspan=2, sticky="ew", padx=2, pady=2)

        # Add a description label
        ttk.Label(
            osm_address_search_frame,
            text="e.g., 'Václavské náměstí 1, Prague'",
            foreground="gray",
            font=("Arial", 7)
        ).grid(row=1, column=0, columnspan=3, padx=2, pady=1, sticky="w")

        # Add a separator
        ttk.Separator(osm_address_search_frame, orient="horizontal").grid(
            row=2, column=0, columnspan=3, sticky="ew", padx=2, pady=3
        )

        # Individual address components
        ttk.Label(osm_address_search_frame, text="Or enter components:", font=("Arial", 8)).grid(
            row=3, column=0, columnspan=3, padx=2, pady=1, sticky="w"
        )

        # City input with autocomplete
        ttk.Label(osm_address_search_frame, text="City:").grid(row=4, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_city_entry = AutocompleteEntry(
            osm_address_search_frame,
            width=30,
            autocomplete_function=self.app.get_osm_city_suggestions
        )
        self.app.osm_city_entry.grid(row=4, column=1, columnspan=2, sticky="ew", padx=2, pady=2)

        # Street input with autocomplete
        ttk.Label(osm_address_search_frame, text="Street:").grid(row=5, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_street_entry = AutocompleteEntry(
            osm_address_search_frame,
            width=30,
            autocomplete_function=self.app.get_osm_street_suggestions
        )
        self.app.osm_street_entry.grid(row=5, column=1, columnspan=2, sticky="ew", padx=2, pady=2)

        # House number input
        ttk.Label(osm_address_search_frame, text="Number:").grid(row=6, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_number_entry = ttk.Entry(osm_address_search_frame, width=10)
        self.app.osm_number_entry.grid(row=6, column=1, sticky="w", padx=2, pady=2)

        # Postal code input
        ttk.Label(osm_address_search_frame, text="PSČ:").grid(row=7, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_postal_entry = AutocompleteEntry(
            osm_address_search_frame,
            width=10,
            autocomplete_function=self.app.get_osm_postal_suggestions
        )
        self.app.osm_postal_entry.grid(row=7, column=1, sticky="w", padx=2, pady=2)

        # Add event handlers for field interaction
        # When full address is selected, update component fields
        self.app.osm_full_address.entry.bind("<FocusOut>", self.app.update_osm_component_fields)

        # When component fields are updated, update full address
        self.app.osm_city_entry.entry.bind("<FocusOut>", self.app.update_osm_full_address)
        self.app.osm_street_entry.entry.bind("<FocusOut>", self.app.update_osm_full_address)
        self.app.osm_number_entry.bind("<FocusOut>", self.app.update_osm_full_address)
        self.app.osm_postal_entry.entry.bind("<FocusOut>", self.app.update_osm_full_address)

        # Search radius
        ttk.Label(osm_address_search_frame, text="Search Radius (m):").grid(row=8, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_address_radius_var = tk.StringVar(value="2000")
        radius_entry = ttk.Entry(osm_address_search_frame, textvariable=self.app.osm_address_radius_var, width=10)
        radius_entry.grid(row=8, column=1, sticky="w", padx=2, pady=2)

        # Search button
        self.app.osm_address_search_btn = ttk.Button(
            osm_address_search_frame,
            text="Search Buildings in OSM",
            command=self.app.search_buildings_in_osm_by_address,
            style="Accent.TButton"
        )
        self.app.osm_address_search_btn.grid(row=9, column=0, columnspan=3, padx=2, pady=4, sticky="ew")

        # === OSM Coordinates Search Tab ===
        # Create a frame for the coordinates search
        osm_coords_search_frame = ttk.Frame(self.app.osm_coords_frame)
        osm_coords_search_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Latitude input
        ttk.Label(osm_coords_search_frame, text="Latitude:").grid(row=0, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_lat_entry = ttk.Entry(osm_coords_search_frame, width=20)
        self.app.osm_lat_entry.grid(row=0, column=1, sticky="ew", padx=2, pady=2)

        # Longitude input
        ttk.Label(osm_coords_search_frame, text="Longitude:").grid(row=1, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_lng_entry = ttk.Entry(osm_coords_search_frame, width=20)
        self.app.osm_lng_entry.grid(row=1, column=1, sticky="ew", padx=2, pady=2)

        # Search radius
        ttk.Label(osm_coords_search_frame, text="Search Radius (m):").grid(row=2, column=0, sticky="w", padx=2, pady=2)
        self.app.osm_coords_radius_var = tk.StringVar(value="2000")
        radius_entry = ttk.Entry(osm_coords_search_frame, textvariable=self.app.osm_coords_radius_var, width=10)
        radius_entry.grid(row=2, column=1, sticky="w", padx=2, pady=2)

        # Search button
        self.app.osm_coords_search_btn = ttk.Button(
            osm_coords_search_frame,
            text="Search Buildings in OSM",
            command=self.app.search_buildings_in_osm_by_coordinates_from_osm_tab,
            style="Accent.TButton"
        )
        self.app.osm_coords_search_btn.grid(row=3, column=0, columnspan=2, padx=2, pady=4, sticky="ew")

        # Add a separator
        ttk.Separator(self.app.osm_search_frame, orient="horizontal").pack(fill="x", padx=5, pady=5)

        # Add a frame for additional options
        osm_additional_options = ttk.Frame(self.app.osm_search_frame)
        osm_additional_options.pack(fill="x", padx=2, pady=2)

        # Add a label with information about the OSM search
        ttk.Label(
            osm_additional_options,
            text="OpenStreetMap Search finds buildings with RUIAN references.\n"
                 "These can be used to retrieve property owner information from the CUZK website.",
            wraplength=300,
            justify="center"
        ).pack(pady=5)

    def _create_real_batch_search_tab(self):
        """Create the real batch search tab as the main focus of the application"""
        # Set up the real batch property owner search tab with a clear title
        self.app.real_batch_search_frame = ttk.Frame(self.app.real_batch_frame)
        self.app.real_batch_search_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Add a prominent title at the top
        title_frame = ttk.Frame(self.app.real_batch_search_frame)
        title_frame.pack(fill="x", padx=5, pady=10)

        ttk.Label(
            title_frame,
            text="Czech Property Registry - Real Batch Search",
            font=("Arial", 14, "bold"),
            foreground="#4CAF50"
        ).pack(side="top", pady=5)

        # Add a description
        ttk.Label(
            title_frame,
            text="Search for all buildings within a radius of an address with no limit on results.",
            wraplength=400,
            justify="center"
        ).pack(side="top", pady=5)

        # The real batch search UI will be created by the UI manager
        # We don't need to create it here

    def _create_batch_search_tab(self):
        """Create the batch search tab"""
        # Set up the batch property owner search tab with a clear title
        self.app.batch_search_frame = ttk.Frame(self.app.batch_frame)
        self.app.batch_search_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Add a prominent title at the top
        title_frame = ttk.Frame(self.app.batch_search_frame)
        title_frame.pack(fill="x", padx=5, pady=10)

        ttk.Label(
            title_frame,
            text="Czech Property Registry - Smart Batch Search",
            font=("Arial", 14, "bold"),
            foreground="#4CAF50"
        ).pack(side="top", pady=5)

        # Add a description
        ttk.Label(
            title_frame,
            text="Search for properties in a batch using smart search features.",
            wraplength=400,
            justify="center"
        ).pack(side="top", pady=5)

    def _create_location_search_tab(self):
        """Create the location search tab"""
        # Set up the location search tab - more compact
        self.app.location_search_frame = ttk.LabelFrame(self.app.location_frame, text="Search by Location")
        self.app.location_search_frame.pack(fill="both", expand=True, padx=1, pady=1)

    def _create_address_search_tab(self):
        """Create the address search tab"""
        # Set up the address search tab - more compact
        self.app.address_search_frame = ttk.LabelFrame(self.app.address_frame, text="Search by Address")
        self.app.address_search_frame.pack(fill="both", expand=True, padx=1, pady=1)

    def _create_coordinate_search_tab(self):
        """Create the coordinate search tab"""
        # Set up the coordinate/parcel search tab - more compact
        self.app.input_frame = ttk.LabelFrame(self.app.coord_frame, text="Property Information")
        self.app.input_frame.pack(fill="both", expand=True, padx=1, pady=1)

    def _create_login_ui(self):
        """Create the login and buyer UI"""
        # Create a notebook for login and GPT settings - more compact
        login_notebook = ttk.Notebook(self.app.login_frame)
        login_notebook.pack(fill="both", expand=True, padx=0, pady=0)

        # Create tabs for login and GPT settings
        login_tab = ttk.Frame(login_notebook)
        gpt_tab = ttk.Frame(login_notebook)

        # Add GPT tab first, then ČÚZK tab
        login_notebook.add(gpt_tab, text="GPT")
        login_notebook.add(login_tab, text="ČÚZK")

        # Select the GPT tab by default
        login_notebook.select(gpt_tab)

        # === ČÚZK Login Tab ===
        # ČÚZK info - ultra compact
        cuzk_info_frame = ttk.Frame(login_tab)
        cuzk_info_frame.pack(fill="x", padx=0, pady=0)

        ttk.Label(
            cuzk_info_frame,
            text="Bank identity required",
            foreground="orange",
            font=("Arial", 7)
        ).pack(side="top", fill="x", pady=0)

        # Create a button frame for better layout - ultra compact
        button_frame = ttk.Frame(login_tab)
        button_frame.pack(fill="x", padx=0, pady=0)

        self.app.open_website_btn = ttk.Button(
            button_frame,
            text="Open Website",
            command=lambda: webbrowser.open("https://nahlizenidokn.cuzk.cz/"),
            width=8
        )
        self.app.open_website_btn.pack(side="left", padx=0, fill="x", expand=True)

        self.app.login_btn = ttk.Button(
            button_frame,
            text="Info",
            command=self.app.login_to_cuzk,
            width=8
        )
        self.app.login_btn.pack(side="right", padx=0, fill="x", expand=True)

        # Keep these fields for backward compatibility but hide them
        self.app.username = ttk.Entry(login_tab, width=30, state="disabled")
        self.app.password = ttk.Entry(login_tab, width=30, show="*", state="disabled")
        self.app.login_status = ttk.Label(login_tab)  # Just for compatibility

        # === GPT Settings Tab === (ultra compact)
        # Add a label with instructions - compact
        gpt_status = "Available" if hasattr(self.app, 'gpt') and self.app.gpt else "Not Available"
        status_color = "green" if hasattr(self.app, 'gpt') and self.app.gpt else "red"
        ttk.Label(
            gpt_tab,
            text=f"GPT: {gpt_status}",
            wraplength=150,
            font=self.app.font_scaler.get_font("medium", is_bold=True),
            foreground=status_color
        ).pack(pady=1)

        # Add checkboxes for enabling/disabling GPT features - enabled by default
        gender_checkbox = ttk.Checkbutton(
            gpt_tab,
            text="Gender determination",
            variable=self.app.use_gpt_gender,
            state="normal" if hasattr(self.app, 'gpt') and self.app.gpt else "disabled"
        )
        gender_checkbox.pack(anchor="w", padx=2, pady=0)

        letter_checkbox = ttk.Checkbutton(
            gpt_tab,
            text="Letter generation",
            variable=self.app.use_gpt_letter,
            state="normal" if hasattr(self.app, 'gpt') and self.app.gpt else "disabled"
        )
        letter_checkbox.pack(anchor="w", padx=2, pady=0)

        # Add a separator
        ttk.Separator(gpt_tab, orient="horizontal").pack(fill="x", padx=2, pady=2)

        # Add a label for CAPTCHA solving options
        ttk.Label(
            gpt_tab,
            text="CAPTCHA Solving:",
            font=self.app.font_scaler.get_font("small", is_bold=True)
        ).pack(anchor="w", padx=2, pady=0)

        # GPT CAPTCHA solving option
        captcha_checkbox = ttk.Checkbutton(
            gpt_tab,
            text="Use GPT Vision",
            variable=self.app.use_gpt_captcha,
            state="normal" if hasattr(self.app, 'gpt') and self.app.gpt else "disabled"
        )
        captcha_checkbox.pack(anchor="w", padx=2, pady=0)

        # Tesseract CAPTCHA solving option
        tesseract_status = "Available" if hasattr(self.app, 'TESSERACT_AVAILABLE') and self.app.TESSERACT_AVAILABLE else "Not Available"
        tesseract_checkbox = ttk.Checkbutton(
            gpt_tab,
            text=f"Use Tesseract OCR ({tesseract_status})",
            variable=self.app.use_tesseract_captcha,
            state="normal" if hasattr(self.app, 'TESSERACT_AVAILABLE') and self.app.TESSERACT_AVAILABLE else "disabled"
        )
        tesseract_checkbox.pack(anchor="w", padx=2, pady=0)

        # Create buyer information UI
        self._create_buyer_ui()

    def _create_buyer_ui(self):
        """Create the buyer information UI"""
        # Create a frame for buyer information
        buyer_info_frame = ttk.Frame(self.app.buyer_frame)
        buyer_info_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Buyer name
        ttk.Label(buyer_info_frame, text="Name:").grid(row=0, column=0, sticky="w", padx=2, pady=2)
        self.app.buyer_name = ttk.Entry(buyer_info_frame, width=30)
        self.app.buyer_name.grid(row=0, column=1, sticky="ew", padx=2, pady=2)

        # Buyer address
        ttk.Label(buyer_info_frame, text="Address:").grid(row=1, column=0, sticky="w", padx=2, pady=2)
        self.app.buyer_address = ttk.Entry(buyer_info_frame, width=30)
        self.app.buyer_address.grid(row=1, column=1, sticky="ew", padx=2, pady=2)

        # Buyer contact
        ttk.Label(buyer_info_frame, text="Contact:").grid(row=2, column=0, sticky="w", padx=2, pady=2)
        self.app.buyer_contact = ttk.Entry(buyer_info_frame, width=30)
        self.app.buyer_contact.grid(row=2, column=1, sticky="ew", padx=2, pady=2)

    def _create_map_and_results_frames(self):
        """Create the map and results frames"""
        # Create map frame in the top right section with minimal padding
        self.app.map_frame = ttk.LabelFrame(self.app.top_right, text="Map View")
        self.app.map_frame.pack(fill="both", expand=True, padx=1, pady=1)

        # Create results frame in the bottom right section with minimal padding
        self.app.results_frame = ttk.LabelFrame(self.app.bottom_right, text="Search Results")
        self.app.results_frame.pack(fill="both", expand=True, padx=1, pady=1)

    def _create_letter_output_frame(self):
        """Create the letter output frame"""
        # Create letter output frame with minimal padding
        self.app.output_frame = ttk.LabelFrame(self.app.bottom_frame, text="Generated Letter")
        self.app.output_frame.pack(fill="both", expand=True, padx=1, pady=1)

        # Output text area with scrollbars - ultra compact
        letter_frame = ttk.Frame(self.app.output_frame)
        letter_frame.pack(fill="both", expand=True, padx=1, pady=1)  # Minimal padding

        self.app.letter_text = tk.Text(letter_frame, wrap="none", height=8)  # Reduced height from 12 to 8

        # Add vertical scrollbar
        letter_vsb = ttk.Scrollbar(letter_frame, orient="vertical", command=self.app.letter_text.yview)
        self.app.letter_text.configure(yscrollcommand=letter_vsb.set)
        letter_vsb.pack(side="right", fill="y")

        # Add horizontal scrollbar
        letter_hsb = ttk.Scrollbar(letter_frame, orient="horizontal", command=self.app.letter_text.xview)
        self.app.letter_text.configure(xscrollcommand=letter_hsb.set)
        letter_hsb.pack(side="bottom", fill="x")

        self.app.letter_text.pack(side="left", fill="both", expand=True)

    def _create_status_bar(self):
        """Create the status bar"""
        # Create status bar (place it outside the scrollable area)
        self.app.status_bar = ttk.Label(self.root, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.app.status_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.app.hsb)
