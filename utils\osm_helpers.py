"""
Helper functions for OpenStreetMap integration.

This module provides utility functions for working with OpenStreetMap data.
"""

import re


def update_osm_component_fields(app, event=None):
    """
    Update the OSM component fields based on the full address.
    
    Args:
        app: The application instance or UI component with address fields
        event: The event that triggered this function (optional)
    """
    # Get the full address
    full_address = app.osm_full_address.get().strip()
    
    if not full_address:
        return
    
    # Try to parse the address into components
    # This is a simple parser and may not work for all addresses
    
    # Check for postal code
    postal_match = re.search(r'(\d{3}\s*\d{2}|\d{5})', full_address)
    postal_code = postal_match.group(1) if postal_match else ""
    
    # Remove postal code from the address
    if postal_match:
        full_address = full_address.replace(postal_match.group(0), "").strip()
    
    # Split the address by commas
    parts = [p.strip() for p in full_address.split(",")]
    
    # The last part is usually the city
    city = parts[-1] if len(parts) > 0 else ""
    
    # The first part is usually the street and number
    street_and_number = parts[0] if len(parts) > 0 else ""
    
    # Try to extract the number from the street
    number_match = re.search(r'(\d+\w*|\w+\d+)$', street_and_number)
    number = number_match.group(1) if number_match else ""
    
    # Remove the number from the street
    if number_match:
        street = street_and_number[:number_match.start()].strip()
    else:
        street = street_and_number
    
    # Update the component fields
    app.osm_city_entry.delete(0, "end")
    app.osm_city_entry.insert(0, city)
    
    app.osm_street_entry.delete(0, "end")
    app.osm_street_entry.insert(0, street)
    
    app.osm_number_entry.delete(0, "end")
    app.osm_number_entry.insert(0, number)
    
    app.osm_postal_entry.delete(0, "end")
    app.osm_postal_entry.insert(0, postal_code)


def update_osm_full_address(app, event=None):
    """
    Update the OSM full address field based on the component fields.
    
    Args:
        app: The application instance or UI component with address fields
        event: The event that triggered this function (optional)
    """
    # Get the component values
    city = app.osm_city_entry.get().strip()
    street = app.osm_street_entry.get().strip()
    number = app.osm_number_entry.get().strip()
    postal = app.osm_postal_entry.get().strip()
    
    # Construct the full address
    full_address = ""
    
    if street:
        full_address += street
        
        if number:
            full_address += f" {number}"
    
    if city:
        if full_address:
            full_address += f", {city}"
        else:
            full_address = city
    
    if postal:
        if full_address:
            full_address += f", {postal}"
        else:
            full_address = postal
    
    # Update the full address field
    app.osm_full_address.delete(0, "end")
    app.osm_full_address.insert(0, full_address)


def get_osm_address_suggestions(app, text):
    """
    Get address suggestions for OSM search.
    
    Args:
        app: The application instance
        text: The text to get suggestions for
        
    Returns:
        list: List of address suggestions
    """
    if hasattr(app, 'get_address_suggestions'):
        return app.get_address_suggestions(text)
    return []


def get_osm_city_suggestions(app, text):
    """
    Get city suggestions for OSM search.
    
    Args:
        app: The application instance
        text: The text to get suggestions for
        
    Returns:
        list: List of city suggestions
    """
    if hasattr(app, 'get_city_suggestions'):
        return app.get_city_suggestions(text)
    return []


def get_osm_street_suggestions(app, text):
    """
    Get street suggestions for OSM search.
    
    Args:
        app: The application instance
        text: The text to get suggestions for
        
    Returns:
        list: List of street suggestions
    """
    if hasattr(app, 'get_street_suggestions'):
        return app.get_street_suggestions(text)
    return []


def get_osm_postal_suggestions(app, text):
    """
    Get postal code suggestions for OSM search.
    
    Args:
        app: The application instance
        text: The text to get suggestions for
        
    Returns:
        list: List of postal code suggestions
    """
    if hasattr(app, 'get_postal_suggestions'):
        return app.get_postal_suggestions(text)
    return []
