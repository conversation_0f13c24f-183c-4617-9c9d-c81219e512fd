"""
String-related helper functions for the Czech Property Registry application.

This module provides functions for string operations, such as formatting,
validation, and conversion.
"""

import re
import logging
from typing import Any, Dict, List, Optional, Tuple, Union

# Configure logging
logger = logging.getLogger(__name__)


def normalize_string(text: str) -> str:
    """
    Normalize a string by removing diacritics and converting to lowercase.

    Args:
        text (str): The string to normalize

    Returns:
        str: The normalized string
    """
    import unicodedata
    # Normalize to NFKD form and remove diacritics
    normalized = unicodedata.normalize('NFKD', text).encode('ASCII', 'ignore').decode('ASCII')
    # Convert to lowercase
    normalized = normalized.lower()
    # Remove extra whitespace
    normalized = ' '.join(normalized.split())
    return normalized


def format_address(address_parts: Dict[str, str]) -> str:
    """
    Format address parts into a full address string.

    Args:
        address_parts (dict): Dictionary of address parts

    Returns:
        str: Formatted address string
    """
    parts = []

    # Add street and number
    if address_parts.get('street'):
        street_part = address_parts['street']
        if address_parts.get('number'):
            street_part += f" {address_parts['number']}"
        parts.append(street_part)

    # Add city
    if address_parts.get('city'):
        parts.append(address_parts['city'])

    # Add postal code
    if address_parts.get('postal_code'):
        parts.append(address_parts['postal_code'])

    # Add country
    if address_parts.get('country'):
        parts.append(address_parts['country'])

    return ", ".join(parts)


def parse_address(address: str) -> Dict[str, str]:
    """
    Parse an address string into components.

    Args:
        address (str): The address to parse

    Returns:
        dict: Dictionary of address components
    """
    result = {
        'street': '',
        'number': '',
        'city': '',
        'postal_code': '',
        'country': 'Czech Republic'
    }

    # Try to extract the postal code (PSČ)
    postal_match = re.search(r'(\d{3}\s*\d{2})', address)
    if postal_match:
        result['postal_code'] = postal_match.group(1).replace(" ", "")
        # Remove postal code from the address
        address = address.replace(postal_match.group(0), "").strip()

    # Split the address by commas
    parts = [p.strip() for p in address.split(",")]

    # The last part is usually the country or city
    if parts and parts[-1].lower() in ('czech republic', 'czechia', 'česká republika', 'česko'):
        parts.pop()  # Remove the country

    # Now the last part should be the city
    if parts:
        result['city'] = parts.pop()

    # The first part is usually the street and number
    if parts:
        street_and_number = parts[0]
        # Try to extract the number from the street
        number_match = re.search(r'(\d+\w*|\w+\d+)$', street_and_number)
        if number_match:
            result['number'] = number_match.group(1)
            result['street'] = street_and_number[:number_match.start()].strip()
        else:
            result['street'] = street_and_number

    return result


def format_phone_number(phone: str) -> str:
    """
    Format a phone number.

    Args:
        phone (str): The phone number to format

    Returns:
        str: The formatted phone number
    """
    # Remove non-digit characters
    digits = re.sub(r'\D', '', phone)

    # Check if it's a Czech phone number
    if len(digits) == 9:
        # Format as XXX XXX XXX
        return f"{digits[:3]} {digits[3:6]} {digits[6:]}"
    elif len(digits) > 9 and digits.startswith('420'):
        # Format as +420 XXX XXX XXX
        return f"+420 {digits[3:6]} {digits[6:9]} {digits[9:]}"
    else:
        # Return as is
        return phone


def format_postal_code(postal_code: str) -> str:
    """
    Format a postal code.

    Args:
        postal_code (str): The postal code to format

    Returns:
        str: The formatted postal code
    """
    # Remove non-digit characters
    digits = re.sub(r'\D', '', postal_code)

    # Check if it's a Czech postal code
    if len(digits) == 5:
        # Format as XXX XX
        return f"{digits[:3]} {digits[3:]}"
    else:
        # Return as is
        return postal_code


def truncate_string(text: str, max_length: int, suffix: str = '...') -> str:
    """
    Truncate a string to a maximum length.

    Args:
        text (str): The string to truncate
        max_length (int): The maximum length
        suffix (str, optional): The suffix to add if truncated

    Returns:
        str: The truncated string
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def is_valid_email(email: str) -> bool:
    """
    Check if a string is a valid email address.

    Args:
        email (str): The email address to check

    Returns:
        bool: True if the email is valid, False otherwise
    """
    # Simple email validation regex
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def is_valid_phone(phone: str) -> bool:
    """
    Check if a string is a valid phone number.

    Args:
        phone (str): The phone number to check

    Returns:
        bool: True if the phone number is valid, False otherwise
    """
    # Remove non-digit characters
    digits = re.sub(r'\D', '', phone)

    # Check if it's a valid phone number
    return len(digits) >= 9


def is_valid_postal_code(postal_code: str) -> bool:
    """
    Check if a string is a valid Czech postal code.

    Args:
        postal_code (str): The postal code to check

    Returns:
        bool: True if the postal code is valid, False otherwise
    """
    # Remove non-digit characters
    digits = re.sub(r'\D', '', postal_code)

    # Check if it's a valid Czech postal code
    return len(digits) == 5


def capitalize_name(name: str) -> str:
    """
    Capitalize a name.

    Args:
        name (str): The name to capitalize

    Returns:
        str: The capitalized name
    """
    # Split the name into parts
    parts = name.split()

    # Capitalize each part
    capitalized_parts = []
    for part in parts:
        # Handle hyphenated names
        if '-' in part:
            subparts = part.split('-')
            capitalized_subparts = [subpart.capitalize() for subpart in subparts]
            capitalized_parts.append('-'.join(capitalized_subparts))
        else:
            capitalized_parts.append(part.capitalize())

    return ' '.join(capitalized_parts)


def strip_html_tags(text: str) -> str:
    """
    Remove HTML tags from a string.

    Args:
        text (str): The string to process

    Returns:
        str: The string without HTML tags
    """
    return re.sub(r'<[^>]+>', '', text)


__all__ = [
    'normalize_string',
    'format_address',
    'parse_address',
    'format_phone_number',
    'format_postal_code',
    'truncate_string',
    'is_valid_email',
    'is_valid_phone',
    'is_valid_postal_code',
    'capitalize_name',
    'strip_html_tags'
]
