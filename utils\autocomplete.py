"""
Autocomplete widgets for the Czech Property Registry application.
"""

import time
import threading
import tkinter as tk
from tkinter import ttk


class AutocompleteEntry(ttk.Frame):
    """A custom frame containing an entry widget with autocomplete functionality"""

    def __init__(self, master=None, autocomplete_function=None, width=20, **kwargs):
        """
        Create an AutocompleteEntry widget

        Args:
            master: The parent widget
            autocomplete_function: Function that returns autocomplete values
            width: Width of the entry widget
            **kwargs: Additional arguments for the frame
        """
        super().__init__(master, **kwargs)

        # Create the entry widget
        self.entry = ttk.Entry(self, width=width)
        self.entry.pack(fill="x", expand=True)

        # Create the listbox for suggestions
        self.listbox = tk.Listbox(self, width=width, height=5)

        # Store the autocomplete function
        self.autocomplete_function = autocomplete_function

        # Create a variable to track changes
        self.var = tk.StringVar()
        self.entry.config(textvariable=self.var)

        # Bind events
        self.var.trace_add("write", self.on_text_changed)
        self.entry.bind("<FocusOut>", self.on_focus_out)
        self.entry.bind("<Return>", self.on_return)
        self.entry.bind("<Up>", self.on_up)
        self.entry.bind("<Down>", self.on_down)
        self.entry.bind("<space>", self.on_space)
        self.listbox.bind("<ButtonRelease-1>", self.on_listbox_select)

        # Store the current suggestions
        self.current_suggestions = []

        # Flag to track if the listbox is visible
        self.listbox_visible = False

        # Last time we fetched suggestions
        self.last_fetch_time = 0

    def on_text_changed(self, *_):
        """Handle text changes in the entry widget"""
        # Get the current text
        text = self.var.get()

        # Hide the listbox if the text is too short
        if not text or len(text) < 2:
            self.hide_listbox()
            return

        # Throttle API calls with a more efficient approach
        current_time = time.time()

        # If we have a pending fetch, cancel it
        if hasattr(self, '_pending_fetch_id'):
            self.after_cancel(self._pending_fetch_id)

        # Calculate wait time based on time since last fetch
        # Minimum wait time is 100ms, maximum is 500ms
        time_since_last_fetch = current_time - self.last_fetch_time
        wait_time = max(100, min(500, 500 - time_since_last_fetch * 1000))

        # Update last fetch time for next calculation
        self.last_fetch_time = current_time

        # Schedule the fetch with calculated delay
        self._pending_fetch_id = self.after(int(wait_time), lambda: self.fetch_suggestions(text))

    def fetch_suggestions(self, text):
        """Fetch suggestions from the autocomplete function"""
        if not self.autocomplete_function:
            print(f"DEBUG: No autocomplete function available")
            return

        print(f"DEBUG: Fetching suggestions for text: '{text}'")

        # Store the text we're fetching for to avoid race conditions
        self._current_fetch_text = text

        # Use a thread to fetch suggestions to avoid blocking the UI
        def fetch_in_thread():
            try:
                # Get suggestions
                print(f"DEBUG: Calling autocomplete function for text: '{text}'")
                suggestions = self.autocomplete_function(text)
                print(f"DEBUG: Received {len(suggestions) if suggestions else 0} suggestions")

                if suggestions:
                    print(f"DEBUG: First few suggestions: {suggestions[:3]}")
                else:
                    print(f"DEBUG: No suggestions returned")

                # Check if this is still the current search text
                # This prevents outdated results from appearing
                def update_ui():
                    if not hasattr(self, '_current_fetch_text') or self._current_fetch_text == text:
                        print(f"DEBUG: Processing suggestions for UI update")
                        self.process_suggestions(suggestions)
                    else:
                        print(f"DEBUG: Skipping UI update - text changed from '{text}' to '{self._current_fetch_text}'")

                # Use after to update UI from the main thread
                self.after(10, update_ui)
            except Exception as e:
                print(f"ERROR: Error fetching suggestions: {e}")
                import traceback
                print(f"ERROR: {traceback.format_exc()}")
                self.after(10, lambda: self.hide_listbox())

        # Use thread pool or limit concurrent threads
        threading.Thread(target=fetch_in_thread, daemon=True).start()

    def process_suggestions(self, suggestions):
        """Process suggestions and update UI (called from main thread)"""
        print(f"DEBUG: Processing {len(suggestions) if suggestions else 0} suggestions")
        if suggestions:
            self.current_suggestions = suggestions
            print(f"DEBUG: Showing suggestions in UI")
            self.show_suggestions(suggestions)
        else:
            print(f"DEBUG: No suggestions to show, hiding listbox")
            self.hide_listbox()

    def show_suggestions(self, suggestions):
        """Show suggestions in the listbox"""
        if not suggestions:
            print(f"DEBUG: No suggestions to show")
            return

        print(f"DEBUG: Showing {len(suggestions)} suggestions in listbox")

        # Store current selection if any
        current_selection = None
        if self.listbox.curselection():
            current_selection = self.listbox.get(self.listbox.curselection()[0])
            print(f"DEBUG: Current selection: '{current_selection}'")

        # Clear the listbox - more efficient to delete all at once
        self.listbox.delete(0, tk.END)
        print(f"DEBUG: Cleared listbox")

        # Add suggestions to the listbox (limit to 8 for better performance)
        try:
            for i, suggestion in enumerate(suggestions[:8]):
                try:
                    if isinstance(suggestion, dict) and 'description' in suggestion:
                        description = suggestion['description']
                    else:
                        description = str(suggestion)

                    print(f"DEBUG: Adding suggestion {i+1}: '{description}'")
                    self.listbox.insert(tk.END, description)

                    # Add alternating background colors for better readability
                    # Only configure items that need coloring (even rows)
                    if i % 2 == 0:
                        self.listbox.itemconfig(i, {'bg': '#f0f0f0'})

                    # Try to restore previous selection if it still exists
                    if current_selection and description == current_selection:
                        self.listbox.selection_set(i)
                        self.listbox.see(i)
                        print(f"DEBUG: Restored selection to item {i}")
                except Exception as e:
                    print(f"ERROR: Error processing suggestion {i}: {e}")
        except Exception as e:
            print(f"ERROR: Error processing suggestions: {e}")
            import traceback
            print(f"ERROR: {traceback.format_exc()}")

        # Configure the listbox height - only if needed
        height = min(5, self.listbox.size())
        current_height = int(self.listbox.cget('height'))
        if height != current_height:
            self.listbox.config(height=height)
            print(f"DEBUG: Set listbox height to {height}")

        # Show the listbox if it's not already visible
        # Avoid unnecessary update_idletasks calls
        if not self.listbox_visible:
            print(f"DEBUG: Showing listbox")
            self.listbox.pack(fill="x", expand=True)
            self.listbox_visible = True
            # Only update if we're showing the listbox for the first time
            self.update_idletasks()
            print(f"DEBUG: Listbox should now be visible")
        else:
            print(f"DEBUG: Listbox was already visible")

    def hide_listbox(self):
        """Hide the listbox"""
        print(f"DEBUG: hide_listbox called, listbox_visible={self.listbox_visible}")
        if self.listbox_visible:
            print(f"DEBUG: Hiding listbox")
            self.listbox.pack_forget()
            self.listbox_visible = False
            # Clear listbox contents to free memory
            self.listbox.delete(0, tk.END)
            print(f"DEBUG: Listbox hidden and cleared")

    def _ensure_listbox_hidden(self):
        """Make absolutely sure the listbox is hidden"""
        if hasattr(self, 'listbox'):
            # First check if it's already hidden to avoid unnecessary operations
            if self.listbox_visible:
                self.listbox.pack_forget()
                self.listbox_visible = False
                # Clear listbox contents to free memory
                self.listbox.delete(0, tk.END)
                # Only update if we actually changed something
                self.update_idletasks()

    def on_listbox_select(self, _=None):
        """Handle selection from the listbox"""
        if self.listbox.curselection():
            index = self.listbox.curselection()[0]
            selected_text = self.listbox.get(index)

            # Get the current text
            current_text = self.var.get()

            # Split the current text by spaces to get the last word being typed
            words = current_text.split()
            current_word = words[-1] if words else ""
            prefix_text = " ".join(words[:-1]) + (" " if words else "")

            # Check if this looks like a street with a number (e.g., "Zbrojnická 229")
            # If so, we should use the full suggestion including city
            has_number = False
            if len(words) >= 2:
                # Check if the last word is a number or contains a number
                if current_word.isdigit() or any(c.isdigit() for c in current_word):
                    has_number = True

            # If we have a street with number, use the full suggestion
            if has_number and "," in selected_text:
                # Use the full suggestion including city
                if self.var.get() != selected_text:
                    self.var.set(selected_text)
            # Try to extract just the part that matches the current word
            elif current_word and len(words) > 0:
                # Find where the current word appears in the suggestion
                word_parts = selected_text.split()
                completed_word = None

                # Try to find the matching word in the suggestion
                for part in word_parts:
                    if part.lower().startswith(current_word.lower()):
                        completed_word = part
                        break

                if completed_word:
                    # Update only the current word, keeping the rest of the text
                    new_text = prefix_text + completed_word

                    # Update the entry text - only if different to avoid triggering change events
                    if self.var.get() != new_text:
                        self.var.set(new_text)
                else:
                    # If we couldn't find a matching word, use the full suggestion
                    if self.var.get() != selected_text:
                        self.var.set(selected_text)
            else:
                # If there's no current word or no words, use the full suggestion
                if self.var.get() != selected_text:
                    self.var.set(selected_text)

            # Hide the listbox immediately - use our optimized method
            self.hide_listbox()

            # Set focus back to the entry
            self.entry.focus_set()

            # No need for a full update() - more efficient to use update_idletasks
            self.update_idletasks()

            # Cancel any pending fetches since we've made a selection
            if hasattr(self, '_pending_fetch_id'):
                self.after_cancel(self._pending_fetch_id)

    def on_return(self, _=None):
        """Handle Return key press"""
        if self.listbox_visible:
            # Get the current text
            current_text = self.var.get()

            # Split the current text by spaces to get the last word being typed
            words = current_text.split()
            current_word = words[-1] if words else ""
            prefix_text = " ".join(words[:-1]) + (" " if words else "")

            # Check if this looks like a street with a number (e.g., "Zbrojnická 229")
            # If so, we should use the full suggestion including city
            has_number = False
            if len(words) >= 2:
                # Check if the last word is a number or contains a number
                if current_word.isdigit() or any(c.isdigit() for c in current_word):
                    has_number = True

            # If a suggestion is selected, use it
            if self.listbox.curselection():
                index = self.listbox.curselection()[0]
                selected_text = self.listbox.get(index)

                # If we have a street with number, use the full suggestion
                if has_number and "," in selected_text:
                    # Use the full suggestion including city
                    self.var.set(selected_text)

                    # Move cursor to the end
                    self.entry.icursor(tk.END)

                    # Hide the listbox
                    self.hide_listbox()
                # Otherwise, just complete the current word
                elif current_word and selected_text.lower().startswith(current_word.lower()):
                    # Find where the current word appears in the suggestion
                    word_parts = selected_text.split()
                    completed_word = None

                    # Try to find the matching word in the suggestion
                    for part in word_parts:
                        if part.lower().startswith(current_word.lower()):
                            completed_word = part
                            break

                    if completed_word:
                        # Update only the current word, keeping the rest of the text
                        new_text = prefix_text + completed_word
                        self.var.set(new_text)

                        # Move cursor to the end
                        self.entry.icursor(tk.END)

                        # Hide the listbox
                        self.hide_listbox()
                    else:
                        # If we couldn't find a matching word, use the full suggestion
                        self.on_listbox_select()
                else:
                    # If the current text is not a prefix of the selected text, use the full suggestion
                    self.on_listbox_select()
            # If no suggestion is selected but there are suggestions available, use the first one
            elif self.listbox.size() > 0 and current_word:
                first_suggestion = self.listbox.get(0)

                # If we have a street with number, use the full suggestion
                if has_number and "," in first_suggestion:
                    # Use the full suggestion including city
                    self.var.set(first_suggestion)

                    # Move cursor to the end
                    self.entry.icursor(tk.END)

                    # Hide the listbox
                    self.hide_listbox()
                else:
                    # Try to extract just the part that matches the current word
                    word_parts = first_suggestion.split()
                    completed_word = None

                    # Try to find the matching word in the suggestion
                    for part in word_parts:
                        if part.lower().startswith(current_word.lower()):
                            completed_word = part
                            break

                    if completed_word:
                        # Update only the current word, keeping the rest of the text
                        new_text = prefix_text + completed_word
                        self.var.set(new_text)

                        # Move cursor to the end
                        self.entry.icursor(tk.END)

                        # Hide the listbox
                        self.hide_listbox()
                    else:
                        # If we couldn't find a matching word, hide the listbox
                        self.hide_listbox()
            else:
                # If no selection but listbox is visible, hide it
                self.hide_listbox()

            # Return "break" to prevent the default behavior
            return "break"

    def on_up(self, _=None):
        """Handle Up arrow key press"""
        if self.listbox_visible:
            size = self.listbox.size()
            if size == 0:
                return "break"

            if self.listbox.curselection():
                index = self.listbox.curselection()[0]
                # Move selection up if not at the top
                if index > 0:
                    new_index = index - 1
                    # More efficient to do operations in batch
                    self.listbox.selection_clear(0, tk.END)
                    self.listbox.selection_set(new_index)
                    self.listbox.see(new_index)
            else:
                # Select the last item if nothing is selected
                last_index = size - 1
                self.listbox.selection_set(last_index)
                self.listbox.see(last_index)

            return "break"

    def on_down(self, _=None):
        """Handle Down arrow key press"""
        if self.listbox_visible:
            size = self.listbox.size()
            if size == 0:
                return "break"

            if self.listbox.curselection():
                index = self.listbox.curselection()[0]
                # Move selection down if not at the bottom
                if index < size - 1:
                    new_index = index + 1
                    # More efficient to do operations in batch
                    self.listbox.selection_clear(0, tk.END)
                    self.listbox.selection_set(new_index)
                    self.listbox.see(new_index)
            else:
                # Select the first item if nothing is selected
                self.listbox.selection_set(0)
                self.listbox.see(0)

            return "break"

    def on_space(self, _=None):
        """Handle Space key press - accept current suggestion but allow continued typing"""
        if self.listbox_visible:
            # Get the current text
            current_text = self.var.get()

            # Split the current text by spaces to get the last word being typed
            words = current_text.split()
            current_word = words[-1] if words else ""
            prefix_text = " ".join(words[:-1]) + (" " if words else "")

            # Check if this looks like a street with a number (e.g., "Zbrojnická 229")
            # If so, we should use the full suggestion including city
            has_number = False
            if len(words) >= 2:
                # Check if the last word is a number or contains a number
                if current_word.isdigit() or any(c.isdigit() for c in current_word):
                    has_number = True

            # If a suggestion is selected, use it
            if self.listbox.curselection():
                index = self.listbox.curselection()[0]
                selected_text = self.listbox.get(index)

                # If we have a street with number, use the full suggestion
                if has_number and "," in selected_text:
                    # Use the full suggestion including city
                    self.var.set(selected_text)

                    # Move cursor to the end
                    self.entry.icursor(tk.END)

                    # Hide the listbox
                    self.hide_listbox()

                    # Return "break" to prevent the default space behavior
                    return "break"
                # Otherwise, just complete the current word
                elif current_word and selected_text.lower().startswith(current_word.lower()):
                    # Find where the current word appears in the suggestion
                    word_parts = selected_text.split()
                    completed_word = None

                    # Try to find the matching word in the suggestion
                    for part in word_parts:
                        if part.lower().startswith(current_word.lower()):
                            completed_word = part
                            break

                    if completed_word:
                        # Update only the current word, keeping the rest of the text
                        new_text = prefix_text + completed_word
                        self.var.set(new_text)

                        # Move cursor to the end
                        self.entry.icursor(tk.END)

                        # Hide the listbox
                        self.hide_listbox()

                        # Add a space at the end
                        self.entry.insert(tk.END, " ")

                        # Return "break" to prevent the default space behavior
                        return "break"

            # If no suggestion is selected but there are suggestions available, use the first one
            elif self.listbox.size() > 0 and current_word:
                first_suggestion = self.listbox.get(0)

                # If we have a street with number, use the full suggestion
                if has_number and "," in first_suggestion:
                    # Use the full suggestion including city
                    self.var.set(first_suggestion)

                    # Move cursor to the end
                    self.entry.icursor(tk.END)

                    # Hide the listbox
                    self.hide_listbox()

                    # Return "break" to prevent the default space behavior
                    return "break"
                # Otherwise, just complete the current word
                else:
                    # Try to extract just the part that matches the current word
                    word_parts = first_suggestion.split()
                    completed_word = None

                    # Try to find the matching word in the suggestion
                    for part in word_parts:
                        if part.lower().startswith(current_word.lower()):
                            completed_word = part
                            break

                    if completed_word:
                        # Update only the current word, keeping the rest of the text
                        new_text = prefix_text + completed_word
                        self.var.set(new_text)

                        # Move cursor to the end
                        self.entry.icursor(tk.END)

                        # Hide the listbox
                        self.hide_listbox()

                        # Add a space at the end
                        self.entry.insert(tk.END, " ")

                        # Return "break" to prevent the default space behavior
                        return "break"

        # Allow the default space behavior if no suggestion is available or applicable
        return None

    def on_focus_out(self, _=None):
        """Handle focus out event"""
        # Use a shorter delay for better responsiveness
        # This delay is necessary to allow the click to be processed
        # before hiding the listbox

        # Cancel any previous delayed hide to prevent multiple calls
        if hasattr(self, '_delayed_hide_id'):
            self.after_cancel(self._delayed_hide_id)

        def delayed_hide():
            # Check if the focus is now on the listbox or back on the entry
            current_focus = self.focus_get()
            if self.listbox_visible and current_focus != self.listbox and current_focus != self.entry:
                self.hide_listbox()
            # Remove the reference to the delayed hide ID
            if hasattr(self, '_delayed_hide_id'):
                delattr(self, '_delayed_hide_id')

        # Store the ID so we can cancel it if needed
        self._delayed_hide_id = self.after(200, delayed_hide)

    def get(self):
        """Get the current text in the entry widget"""
        return self.entry.get()

    def delete(self, first, last=None):
        """Delete text from the entry widget"""
        return self.entry.delete(first, last)

    def insert(self, index, string):
        """Insert text into the entry widget"""
        return self.entry.insert(index, string)

    def set_text(self, text):
        """Set the text in the entry widget"""
        self.var.set(text)
        # Move cursor to the end
        self.entry.icursor(tk.END)
