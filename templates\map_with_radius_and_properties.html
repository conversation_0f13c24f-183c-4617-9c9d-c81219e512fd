<!DOCTYPE html>
<html>
<head>
    <style>
        body { margin: 0; padding: 0; height: 100%; }
        #map { width: 100%; height: 100%; }
        .info-window { max-width: 300px; }
        .legend {
            background: white;
            padding: 10px;
            margin: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            position: absolute;
            bottom: 30px;
            right: 10px;
            z-index: 1000;
        }
        .legend-item {
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 5px;
            display: inline-block;
        }
        .legend-label {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(255, 0, 0, 0.1); border: 2px solid rgba(255, 0, 0, 0.8);"></div>
            <div class="legend-label">Search Radius</div>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: blue; border-radius: 50%;"></div>
            <div class="legend-label">Search Center</div>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: red; border-radius: 50%;"></div>
            <div class="legend-label">Property</div>
        </div>
    </div>
    <script>
        function initMap() {
            var center = {lat: {center_lat}, lng: {center_lng}};
            var map = new google.maps.Map(document.getElementById('map'), {
                zoom: 14,
                center: center
            });

            // Add a marker at the center
            var centerMarker = new google.maps.Marker({
                position: center,
                map: map,
                title: 'Search Location: {location_name}',
                icon: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png'
            });

            // Add a circle with the specified radius
            var circle = new google.maps.Circle({
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.1,
                map: map,
                center: center,
                radius: {radius}
            });

            // Create bounds to fit all markers
            var bounds = new google.maps.LatLngBounds();
            bounds.extend(center);

            // Add markers for each property
            var properties = {properties_json};
            var infoWindow = new google.maps.InfoWindow();
            
            properties.forEach(function(property) {
                if (property.lat && property.lng) {
                    var position = {lat: parseFloat(property.lat), lng: parseFloat(property.lng)};
                    var marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        title: property.property_type || 'Property'
                    });
                    
                    bounds.extend(position);
                    
                    // Create info window content
                    var content = '<div class="info-window">';
                    content += '<h3>' + (property.property_type || 'Property') + '</h3>';
                    if (property.ruian_id) {
                        content += '<p><strong>RUIAN ID:</strong> ' + property.ruian_id + '</p>';
                    }
                    if (property.address) {
                        content += '<p><strong>Address:</strong> ' + property.address + '</p>';
                    }
                    if (property.url) {
                        content += '<p><a href="' + property.url + '" target="_blank">View on CUZK</a></p>';
                    }
                    content += '</div>';
                    
                    marker.addListener('click', function() {
                        infoWindow.setContent(content);
                        infoWindow.open(map, marker);
                    });
                }
            });
            
            // Adjust bounds to fit all markers and the circle
            map.fitBounds(bounds);
            
            // Make sure the circle is visible
            var circleBounds = circle.getBounds();
            if (circleBounds) {
                bounds.union(circleBounds);
                map.fitBounds(bounds);
            }
        }
    </script>
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={api_key}&callback=initMap">
    </script>
</body>
</html>
