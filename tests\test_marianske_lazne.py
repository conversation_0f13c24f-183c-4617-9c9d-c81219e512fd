"""
Test for searching the city of Mariánské Lázně for properties.

This test specifically targets Mariánské Lázně, which was reported to have issues
with finding enough URLs when searching the entire city.

Usage:
    python tests/test_marianske_lazne.py
"""

import os
import sys
import json
import time
import logging
import argparse
import requests
from typing import Dict, List, Any, Optional

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from api.cuzk.cuzk_integration import CUZKIntegration
from api.google_maps.city_boundaries import get_city_boundaries
from core.real_batch_search_manager import RealBatchSearchManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('marianske_lazne_test.log', mode='w')
    ]
)

logger = logging.getLogger("MarianskeTest")

class MarianskeTest:
    """Test class for searching Mariánské Lázně"""

    def __init__(self):
        """Initialize the test"""
        # Load API keys from config
        self.api_key = self._load_api_key()

        # Initialize Google Maps integration
        self.google_maps = GoogleMapsIntegration()

        # Initialize CUZK integration
        self.cuzk_integration = CUZKIntegration()

        # City name to test
        self.city_name = "Mariánské Lázně"

        # Create a proper mock app class
        class MockApp:
            def __init__(self, test_instance):
                self.google_maps = test_instance.google_maps
                self.cuzk_integration = test_instance.cuzk_integration
                self.root = self  # Use self as root for simplicity

            def after(self, delay, func, *args, **kwargs):
                # Execute the function immediately with any args
                if args or kwargs:
                    return func(*args, **kwargs)
                else:
                    return func()

            def show_status(self, msg):
                # Log status messages
                logger.info(f"Status: {msg}")

        # Create the mock app instance
        self.mock_app = MockApp(self)

        # Initialize batch search manager
        self.batch_search_manager = RealBatchSearchManager(self.mock_app)

    def _load_api_key(self):
        """Load Google Maps API key from config file"""
        import configparser
        config = configparser.ConfigParser()
        if os.path.exists('config.ini'):
            config.read('config.ini')
            if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
                return config['GOOGLE_MAPS']['api_key']
        return None

    def get_city_boundaries(self):
        """Get the boundaries of Mariánské Lázně"""
        logger.info(f"Getting boundaries for {self.city_name}")

        # Get city boundaries
        boundaries = get_city_boundaries(self.city_name, "Czech Republic", self.google_maps)

        if not boundaries:
            raise AssertionError(f"Could not get boundaries for {self.city_name}")

        logger.info(f"Boundaries for {self.city_name}: {boundaries}")
        return boundaries

    def generate_coordinates_in_city(self, boundaries, density=200):
        """Generate coordinates within the city boundaries"""
        logger.info(f"Generating coordinates within {self.city_name} with density {density}")

        # Extract boundaries
        north = boundaries['north']
        south = boundaries['south']
        east = boundaries['east']
        west = boundaries['west']

        # Calculate area dimensions
        width = east - west
        height = north - south
        area = width * height

        logger.info(f"City area dimensions: Width={width:.6f}, Height={height:.6f}, Area={area:.6f}")

        # Calculate step sizes
        lat_step = height / density
        lng_step = width / density

        # Generate coordinates
        coordinates = []
        for i in range(density + 1):
            for j in range(density + 1):
                lat = south + i * lat_step
                lng = west + j * lng_step
                coordinates.append({'lat': lat, 'lng': lng})

        logger.info(f"Generated {len(coordinates)} coordinates within {self.city_name}")
        return coordinates

    def convert_coordinates_and_create_urls(self, coordinates):
        """Convert coordinates to S-JTSK and create CUZK URLs"""
        logger.info(f"Converting {len(coordinates)} coordinates and creating CUZK URLs")

        results = []
        for coord in coordinates:
            lat = coord['lat']
            lng = coord['lng']

            # Convert WGS84 to S-JTSK
            x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)

            # Ensure coordinates are integers
            x_int = int(x)
            y_int = int(y)

            # Generate CUZK URL
            url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"

            # Create result dictionary
            result = {
                'lat': lat,
                'lng': lng,
                'x_sjtsk': x_int,
                'y_sjtsk': y_int,
                'url': url
            }

            results.append(result)

            # Add a small delay to avoid overwhelming the API
            time.sleep(0.01)

        logger.info(f"Created {len(results)} CUZK URLs")
        return results

    def test_city_search(self):
        """Test searching Mariánské Lázně using the batch search manager"""
        logger.info(f"Testing batch search for {self.city_name}")

        # Define a callback to receive the results
        results = []
        def callback(buildings):
            nonlocal results
            results = buildings
            logger.info(f"Received {len(buildings)} buildings from batch search")

        # Start the batch search with radius 0 (city-level search)
        self.batch_search_manager.batch_search_by_address(
            self.city_name, None, 0, None, callback
        )

        # Wait for the search to complete
        while self.batch_search_manager.search_in_progress:
            time.sleep(0.5)

        logger.info(f"Batch search completed with {len(results)} results")

        # Save the results to a file for analysis
        with open('marianske_lazne_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)

        logger.info(f"Results saved to marianske_lazne_results.json")

        # Verify that we found a reasonable number of buildings
        assert len(results) > 50, f"Expected more than 50 buildings, but found only {len(results)}"

        return results

    def run_test(self):
        """Run the test"""
        try:
            # Test city search
            results = self.test_city_search()

            # Print summary
            logger.info(f"Test completed successfully")
            logger.info(f"Found {len(results)} buildings in {self.city_name}")

            # Print some sample results
            logger.info("Sample results:")
            for i, result in enumerate(results[:5]):
                logger.info(f"  {i+1}. WGS84({result['lat']}, {result['lng']}) -> S-JTSK({result['x']}, {result['y']})")
                logger.info(f"     URL: {result['url']}")

            return True
        except Exception as e:
            logger.error(f"Test failed: {e}", exc_info=True)
            return False

if __name__ == "__main__":
    test = MarianskeTest()
    success = test.run_test()
    sys.exit(0 if success else 1)
