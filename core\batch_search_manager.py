"""
Batch search manager for the Czech Property Scraper application.
Handles all batch search-related functionality including:
- Searching for properties by location and type
- Fetching property data from OpenStreetMap
- Processing search results
- Handling both direct browser scraping and automatic API-based searches
"""

import tkinter as tk
import threading
import logging
import time
import webbrowser
import math
from typing import List, Dict, Any, Optional, Union, Callable, Tuple

from ui.message_boxes import MessageBoxes
from core.batch_search.batch_search_manager_base import BatchSearchManagerBase

# Set up logging
logger = logging.getLogger(__name__)


class BatchSearchManager(BatchSearchManagerBase):
    """
    Manages batch search functionality for the application.

    Provides methods for searching properties by location, address, or coordinates,
    and handles both direct browser scraping and automatic API-based searches.
    """

    def __init__(self, app):
        """
        Initialize the batch search manager.

        Args:
            app: The main application instance with access to Google Maps, OSM, and CUZK APIs
        """
        # Call the parent class constructor
        super().__init__(app)

    def batch_search_properties(self, property_type, location, radius, max_results):
        """
        Perform a batch search for properties by location and type.

        Asks user whether to use direct browser scraping or automatic API-based search,
        then delegates to the appropriate method.

        Args:
            property_type (str): Type of property to search for (e.g., "residential", "commercial")
            location (str): Location to search in (city, region, or full address)
            radius (float): Search radius in kilometers
            max_results (int): Maximum number of results to return
        """
        try:
            logger.info(f"Batch searching for {property_type} properties in {location} with radius {radius}km")

            # Convert radius from km to meters
            radius_meters = int(float(radius) * 1000)

            # Parse the location into city and region
            city = location
            region = ""
            if "," in location:
                parts = location.split(",", 1)
                city = parts[0].strip()
                region = parts[1].strip()

            # Ask the user if they want to use direct browser scraping
            use_direct_scrape = MessageBoxes.ask_scraping_method()

            if use_direct_scrape:
                # Use direct browser scraping
                self.direct_batch_scrape(city, region, property_type, radius_meters, max_results)
            else:
                # Use automatic scraping
                self.automatic_batch_scrape(city, region, property_type, radius_meters, max_results)
        except Exception as e:
            logger.error(f"Error in batch search: {e}", exc_info=True)
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")

    def batch_search(self):
        """
        Legacy method for backward compatibility with older UI components.

        Extracts search parameters from UI fields and calls batch_search_properties.
        """
        try:
            # Get search parameters
            city = self.app.batch_city.get().strip() if hasattr(self.app, 'batch_city') else ""
            region = self.app.batch_region.get().strip() if hasattr(self.app, 'batch_region') else ""
            property_type = self.app.batch_property_type.get().strip() if hasattr(self.app, 'batch_property_type') else "All"
            radius = self.app.batch_radius.get().strip() if hasattr(self.app, 'batch_radius') else "2"
            max_results = self.app.batch_max_results.get().strip() if hasattr(self.app, 'batch_max_results') else "50"

            # Validate inputs
            if not city and not region:
                MessageBoxes.show_error("Input Error", "Please enter either a city or a region")
                return

            # Set default values if not provided
            if not radius:
                radius = "2"  # Default radius in kilometers

            if not max_results:
                max_results = "50"  # Default maximum number of results

            # Construct the location string
            location = city
            if region and not city:
                location = region
            elif region and city:
                location = f"{city}, {region}"

            # Call the new method
            self.batch_search_properties(property_type, location, float(radius), int(max_results))
        except Exception as e:
            logger.error(f"Error in legacy batch search: {e}", exc_info=True)
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")

    def direct_batch_scrape(self, city, region, property_type, radius, max_results):
        """
        Use direct browser scraping for batch property search.

        Opens browser windows to directly access property data from CUZK website.
        Delegates to BatchSearchMethods class for implementation.

        Args:
            city (str): City name (e.g., "Praha")
            region (str): Region name (e.g., "Středočeský kraj")
            property_type (str): Type of property to search for
            radius (int): Search radius in meters
            max_results (int): Maximum number of results to return
        """
        # Ask the user if they want to use the MapaIdentifikace format (like ikatastr)
        use_mapa_identifikace = MessageBoxes.ask_yes_no(
            "CUZK URL Format",
            "Do you want to use the MapaIdentifikace format (like ikatastr)?\n\n"
            "Yes - Use http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx\n"
            "No - Use standard CUZK search URLs"
        )

        # If using MapaIdentifikace format, use our new batch CUZK method
        if use_mapa_identifikace and hasattr(self.app, 'cuzk_integration'):
            # Get coordinates for the location
            location = city
            if region:
                location = f"{city}, {region}" if city else region

            # Show status
            self.app.show_status(f"Searching for properties in {location}...")

            # Get coordinates for the location
            result = self.app.google_maps.geocode(location)

            if result:
                lat = result.get('lat')
                lng = result.get('lng')

                # Show the location on the map
                if hasattr(self.app, "show_embedded_map"):
                    self.app.show_embedded_map(lat, lng)

                # Open batch CUZK URLs in browser
                num_points = min(max_results, 9)  # Limit to 9 points by default
                points = self.app.cuzk_integration.open_batch_cuzk_in_browser(
                    lat, lng, radius, num_points=num_points, use_mapa_identifikace=True
                )

                # Show status
                self.app.show_status(f"Opened {len(points)} CUZK property pages in your browser")

                # Show a message to the user
                MessageBoxes.show_info(
                    "CUZK Property Pages",
                    f"Opened {len(points)} CUZK property pages in your browser.\n\n"
                    "Please check each page for property information."
                )

                # Also display the URLs in the batch results text if available
                if hasattr(self.app, 'batch_results_text'):
                    self.app.batch_results_text.config(state="normal")
                    self.app.batch_results_text.delete(1.0, tk.END)
                    self.app.batch_results_text.insert(tk.END, f"Generated {len(points)} CUZK property URLs for {location}:\n\n")

                    # Add each URL to the results
                    for i, point in enumerate(points):
                        url = point['url']
                        x = point['x']
                        y = point['y']

                        # Add property information
                        self.app.batch_results_text.insert(tk.END, f"Property {i+1} 🌐 [CUZK URL]:\n")
                        self.app.batch_results_text.insert(tk.END, f"Owner: Property Owner (view details on CUZK)\n")
                        self.app.batch_results_text.insert(tk.END, f"Address: {location} (Property {i+1})\n")
                        self.app.batch_results_text.insert(tk.END, f"Coordinates (S-JTSK): x={x}, y={y}\n")
                        self.app.batch_results_text.insert(tk.END, f"Type: Building\n")
                        self.app.batch_results_text.insert(tk.END, "CUZK URL: ")

                        # Add the clickable URL
                        url_start_pos = self.app.batch_results_text.index(tk.END)
                        self.app.batch_results_text.insert(tk.END, url)
                        url_end_pos = self.app.batch_results_text.index(tk.END)

                        # Make the URL clickable
                        try:
                            self.app.batch_results_text.tag_add(f"batch_url_{i}", url_start_pos, url_end_pos)
                            self.app.batch_results_text.tag_config(f"batch_url_{i}", foreground="blue", underline=1)
                            self.app.batch_results_text.tag_bind(f"batch_url_{i}", "<Button-1>",
                                                               lambda _, u=url: webbrowser.open(u))

                            # Add hover effect
                            self.app.batch_results_text.tag_bind(f"batch_url_{i}", "<Enter>",
                                                               lambda _: self.app.batch_results_text.config(cursor="hand2"))
                            self.app.batch_results_text.tag_bind(f"batch_url_{i}", "<Leave>",
                                                               lambda _: self.app.batch_results_text.config(cursor="arrow"))
                        except Exception as tag_error:
                            logger.error(f"Error making batch URL clickable: {tag_error}")

                        # Add newlines after the URL
                        self.app.batch_results_text.insert(tk.END, "\n\n")

                    self.app.batch_results_text.config(state="disabled")

                # Store the properties in the property list for later use
                self.app.property_list = []
                for i, point in enumerate(points):
                    # Create a more descriptive property entry
                    property_data = {
                        'lat': point['lat'],
                        'lng': point['lng'],
                        'x_sjtsk': point['x'],
                        'y_sjtsk': point['y'],
                        'url': point['url'],
                        'property_type': 'Building',
                        'address': location,
                        'source': 'CUZK_MapaIdentifikace',
                        'owner_name': 'Click URL to view property details',
                        'owner_address': point['url'],  # Store the URL in the owner_address field
                        'original_address': f"{location} (Property {i+1})",
                        'original_owner': 'Property Owner (view details on CUZK)',
                        'ruian_id': 'N/A'  # No RUIAN ID for MapaIdentifikace URLs
                    }
                    self.app.property_list.append(property_data)
            else:
                MessageBoxes.show_error("Location Error", f"Could not find coordinates for {location}")
        else:
            # Delegate to the BatchSearchMethods class for standard behavior
            if hasattr(self.app, 'batch_search_methods'):
                self.app.batch_search_methods.direct_batch_scrape(city, region, property_type, radius, max_results)
            else:
                print("Batch search methods not initialized")

    def automatic_batch_scrape(self, city, region, property_type, radius, max_results):
        """
        Use automatic API-based scraping for batch property search.

        Uses Google Maps for geocoding and OpenStreetMap for property data.
        Runs search in a background thread to avoid freezing the UI.

        Args:
            city (str): City name (e.g., "Praha")
            region (str): Region name (e.g., "Středočeský kraj")
            property_type (str): Type of property to search for
            radius (int): Search radius in meters
            max_results (int): Maximum number of results to return
        """
        try:
            # Clear the results text
            if hasattr(self.app, 'batch_results_text'):
                self.app.batch_results_text.config(state="normal")
                self.app.batch_results_text.delete(1.0, tk.END)
                self.app.batch_results_text.insert(tk.END, f"Searching for {property_type} properties in {city or region}...\n\n")
                self.app.batch_results_text.config(state="disabled")

            # Show status
            self.app.show_status(f"Searching for {property_type} properties in {city or region}...")

            # Get coordinates for the city or region
            if city:
                # Get coordinates for the city
                result = self.app.google_maps.geocode(city)
                location_name = city
            else:
                # Get coordinates for the region
                result = self.app.google_maps.geocode(region)
                location_name = region

            if not result:
                MessageBoxes.show_error("Error", f"Could not find coordinates for {city or region}")
                return

            lat = result['lat']
            lng = result['lng']

            # Update the batch results text
            if hasattr(self.app, 'batch_results_text'):
                self.app.batch_results_text.config(state="normal")
                self.app.batch_results_text.insert(tk.END, f"Found coordinates for {location_name}: {lat}, {lng}\n\n")
                self.app.batch_results_text.config(state="disabled")

            # Use threading to avoid freezing the UI
            def search_thread():
                try:
                    # Now search for properties in the area
                    if hasattr(self.app, 'batch_results_text'):
                        self.app.root.after(10, lambda: self.app.batch_results_text.config(state="normal"))
                        self.app.root.after(10, lambda: self.app.batch_results_text.insert(tk.END, "Searching for properties in the area...\n"))
                        self.app.root.after(10, lambda: self.app.batch_results_text.config(state="disabled"))

                    # Try to search for real properties using the CUZK website
                    properties = self.fetch_properties_in_area(lat, lng, int(radius), int(max_results), property_type)

                    # Update the UI in the main thread
                    self.app.root.after(0, lambda: self.display_batch_results(properties, location_name))
                except Exception as e:
                    print(f"Error searching for properties: {e}")
                    self.app.root.after(0, lambda: MessageBoxes.show_error("Search Error", f"Error searching for properties: {str(e)}"))
                    self.app.show_status("Ready")

            # Start the thread
            threading.Thread(target=search_thread, daemon=True).start()
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def fetch_properties_from_cuzk(self, lat: float, lng: float, radius: int, max_results: int) -> List[Dict[str, Any]]:
        """
        Fetch properties directly from CUZK/RUIAN based on coordinates and radius.

        This method uses the CUZK integration to search for properties in the specified area.
        It handles rate limiting and CAPTCHA challenges by using a combination of direct API
        calls and browser-based scraping when necessary.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            radius (int): Search radius in meters
            max_results (int): Maximum number of results to return

        Returns:
            list: List of property data dictionaries with RUIAN IDs and owner information
        """
        try:
            logger.info(f"Fetching properties directly from CUZK at coordinates {lat}, {lng} with radius {radius}m")

            # Show status
            self.app.show_status(f"Searching for properties in CUZK database...")

            properties = []

            # Check if we have the CUZK scraper available
            if hasattr(self.app, 'cuzk_scraper'):
                # Use the CUZK scraper to fetch properties in the area
                cuzk_properties = self.app.cuzk_scraper.fetch_properties_in_area(lat, lng, radius // 1000, max_results)

                if cuzk_properties:
                    logger.info(f"Found {len(cuzk_properties)} properties from CUZK scraper")
                    return cuzk_properties

            # If we have the CUZK integration available, try that as well
            if hasattr(self.app, 'cuzk_integration'):
                # Convert WGS84 coordinates to S-JTSK
                x, y = self.app.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)

                # Search for property by coordinates
                property_data = self.app.cuzk_integration.search_property_by_coordinates(x, y)

                if property_data:
                    # Add coordinates to the property data
                    property_data['lat'] = lat
                    property_data['lng'] = lng
                    property_data['source'] = 'cuzk'

                    # Extract RUIAN ID if available
                    if 'building_number' in property_data:
                        property_data['ruian_id'] = property_data['building_number']

                    properties.append(property_data)
                    logger.info(f"Found 1 property from CUZK integration")

            # If we still don't have enough properties, try the property service
            if len(properties) < max_results and hasattr(self.app, 'property_service'):
                # Get the location name from reverse geocoding
                location_info = self.app.google_maps.reverse_geocode(lat, lng)
                address = ""

                if location_info:
                    address = location_info.get('formatted_address', '')

                # Fetch properties by address
                service_properties = self.app.property_service.fetch_properties_by_address(address)

                if service_properties:
                    # Add coordinates to the property data
                    for prop in service_properties:
                        prop['lat'] = lat
                        prop['lng'] = lng
                        prop['source'] = 'cuzk_service'

                    properties.extend(service_properties[:max_results - len(properties)])
                    logger.info(f"Found {len(service_properties)} properties from property service")

            return properties

        except Exception as e:
            logger.error(f"Error fetching properties from CUZK: {e}", exc_info=True)
            return []

    def _batch_search_by_city_thread(self, city, property_types, max_results, callback, use_mapycz=True):
        """
        Background thread for batch search by city.

        Args:
            city (str): City name
            property_types (list): List of property types to include
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
            use_mapycz (bool): Whether to use Mapy.cz for city boundaries
        """
        try:
            # Get city boundaries from either Mapy.cz or Google Maps
            if use_mapycz and hasattr(self.app, 'mapycz_integration'):
                logger.info(f"Getting city boundaries for {city} from Mapy.cz")
                self.app.show_status(f"Getting city boundaries for {city} from Mapy.cz...")

                # Get city boundaries from Mapy.cz
                boundaries = self.app.mapycz_integration.get_city_boundaries(city)

                if boundaries:
                    # Convert to format expected by fetch_properties_in_city
                    mapycz_boundaries = {
                        'center': {
                            'lat': (boundaries['north'] + boundaries['south']) / 2,
                            'lng': (boundaries['east'] + boundaries['west']) / 2
                        },
                        'viewport': {
                            'northeast': {
                                'lat': boundaries['north'],
                                'lng': boundaries['east']
                            },
                            'southwest': {
                                'lat': boundaries['south'],
                                'lng': boundaries['west']
                            }
                        }
                    }

                    # Get the center of the city
                    center = mapycz_boundaries.get('center', {})
                    lat = center.get('lat')
                    lng = center.get('lng')

                    if lat and lng:
                        # Generate coordinates within the city
                        coordinates = self.app.mapycz_integration.generate_coordinates_in_city(boundaries)

                        if coordinates:
                            logger.info(f"Generated {len(coordinates)} coordinates within {city} using Mapy.cz")

                            # Extract just the coordinates
                            points = [(coord['lat'], coord['lng']) for coord in coordinates]

                            # Limit to max_results
                            if len(points) > max_results:
                                import random
                                # Randomly sample points to reduce to max_results
                                points = random.sample(points, max_results)

                            # Fetch property data for each point
                            properties = []
                            for i, (point_lat, point_lng) in enumerate(points):
                                try:
                                    # Update status
                                    self.app.show_status(f"Fetching property data for point {i+1}/{len(points)}")

                                    # Get property data for this point
                                    property_data = self.fetch_property_at_point(point_lat, point_lng, property_types)

                                    if property_data:
                                        properties.append(property_data)
                                except Exception as e:
                                    logger.error(f"Error fetching property data for point {i+1}: {e}")
                                    # Continue with the next point

                            # Update the UI in the main thread
                            self.app.root.after(0, lambda: self.display_batch_results(properties, city))

                            # Call the callback if provided
                            if callback:
                                self.app.root.after(0, lambda: callback(properties))

                            # Reset search flag
                            self.search_in_progress = False
                            return
                        else:
                            logger.warning(f"Could not generate coordinates for {city} using Mapy.cz, falling back to Google Maps")
                    else:
                        logger.warning(f"Could not get center coordinates for {city} from Mapy.cz, falling back to Google Maps")
                else:
                    logger.warning(f"Could not get boundaries for {city} from Mapy.cz, falling back to Google Maps")

            # Fall back to Google Maps if Mapy.cz failed or was not requested
            logger.info(f"Getting city boundaries for {city} from Google Maps")
            self.app.show_status(f"Getting city boundaries for {city} from Google Maps...")

            # Get city boundaries from Google Maps
            boundaries = self.app.google_maps.get_city_boundaries(city)

            if not boundaries:
                logger.error(f"Could not find boundaries for city: {city}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "City Error", f"Could not find boundaries for city: {city}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            # Get the center of the city
            center = boundaries.get('center', {})
            lat = center.get('lat')
            lng = center.get('lng')

            if not lat or not lng:
                logger.error(f"Could not find center coordinates for city: {city}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "City Error", f"Could not find center coordinates for city: {city}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            # Get the viewport of the city
            viewport = boundaries.get('viewport', {})
            northeast = viewport.get('northeast', {})
            southwest = viewport.get('southwest', {})

            # Calculate the radius of the city (approximate)
            from utils.helpers.coordinate_helpers import haversine_distance
            radius_km = haversine_distance(
                northeast.get('lat', lat),
                northeast.get('lng', lng),
                southwest.get('lat', lat),
                southwest.get('lng', lng)
            ) / 2

            logger.info(f"City radius: {radius_km}km")

            # Convert radius to meters
            radius_meters = int(radius_km * 1000)

            # Fetch properties in the area
            properties = self.fetch_properties_in_area(lat, lng, radius_meters, max_results, property_types)

            # Update the UI in the main thread
            self.app.root.after(0, lambda: self.display_batch_results(properties, city))

            # Call the callback if provided
            if callback:
                self.app.root.after(0, lambda: callback(properties))

            # Reset search flag
            self.search_in_progress = False

        except Exception as e:
            logger.error(f"Error in batch search by city thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"An error occurred: {str(e)}"))
            self.app.root.after(0, lambda: self.app.show_status("Ready"))
            self.search_in_progress = False

    def fetch_property_at_point(self, lat, lng, property_types=None):
        """
        Fetch property data for a specific point.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            property_types (list, optional): List of property types to include

        Returns:
            dict: Property data dictionary or None if no property found
        """
        try:
            # Check if we have RUIAN integration
            if hasattr(self.app, 'ruian_integration'):
                # Get RUIAN data for this point
                ruian_data = self.app.ruian_integration.get_ruian_data_by_coordinates(lat, lng)

                if ruian_data:
                    # Extract property information
                    property_data = {
                        'lat': lat,
                        'lng': lng,
                        'property_type': ruian_data.get('property_type', 'Unknown'),
                        'address': ruian_data.get('address', 'Unknown'),
                        'owner_name': ruian_data.get('owner_name', 'Unknown'),
                        'owner_address': ruian_data.get('owner_address', 'Unknown'),
                        'ruian_id': ruian_data.get('ruian_id', 'Unknown'),
                        'source': 'RUIAN'
                    }

                    return property_data

            # Fallback to OSM if available
            if hasattr(self.app, 'osm_integration'):
                # Get OSM data for this point
                osm_data = self.app.osm_integration.get_osm_data_by_coordinates(lat, lng)

                if osm_data:
                    # Extract property information
                    property_data = {
                        'lat': lat,
                        'lng': lng,
                        'property_type': osm_data.get('property_type', 'Unknown'),
                        'address': osm_data.get('address', 'Unknown'),
                        'owner_name': 'Unknown',  # OSM doesn't have owner information
                        'owner_address': 'Unknown',  # OSM doesn't have owner information
                        'ruian_id': 'Unknown',  # OSM doesn't have RUIAN IDs
                        'source': 'OSM'
                    }

                    return property_data

            # No property data available, just add the coordinates
            property_data = {
                'lat': lat,
                'lng': lng,
                'property_type': 'Unknown',
                'address': 'Unknown',
                'owner_name': 'Unknown',
                'owner_address': 'Unknown',
                'ruian_id': 'Unknown',
                'source': 'Coordinates'
            }

            return property_data

        except Exception as e:
            logger.error(f"Error fetching property at point {lat}, {lng}: {e}")
            return None

    def fetch_properties_in_area(self, lat, lng, radius, max_results, property_type, callback=None):
        """
        Fetch properties in the specified area using RUIAN/CUZK directly.

        Uses the CUZK integration to generate MapaIdentifikace URLs for properties in the area.
        Does not fall back to other sources, focusing exclusively on RUIAN.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            radius (int): Search radius in meters
            max_results (int): Maximum number of results to return
            property_type (str): Type of property to filter for (or None for all types)
            callback (callable, optional): Function to call with the results

        Returns:
            list: List of property data dictionaries with RUIAN IDs and metadata
        """
        logger.info(f"Fetching properties in area at coordinates {lat}, {lng} with radius {radius}m")

        # Show status
        self.app.show_status(f"Searching for properties...")

        # Generate points in the area
        import math
        import random

        # Convert radius from meters to degrees (approximate)
        # 1 degree of latitude is approximately 111,000 meters
        radius_lat = radius / 111000
        # 1 degree of longitude varies with latitude
        radius_lng = radius / (111000 * math.cos(math.radians(lat)))

        # Generate points in a grid pattern
        points = []

        # Add the center point first
        points.append({'lat': lat, 'lng': lng})

        # Calculate step size based on radius
        if radius <= 500:  # 500 meters or less
            # Use a dense grid with points every 50 meters
            step_size_lat = 50 / 111000  # 50 meters in degrees latitude
            step_size_lng = 50 / (111000 * math.cos(math.radians(lat)))  # 50 meters in degrees longitude
        elif radius <= 1000:  # 1 km or less
            # Use a medium grid with points every 100 meters
            step_size_lat = 100 / 111000  # 100 meters in degrees latitude
            step_size_lng = 100 / (111000 * math.cos(math.radians(lat)))  # 100 meters in degrees longitude
        else:
            # Use a sparse grid with points every 200 meters
            step_size_lat = 200 / 111000  # 200 meters in degrees latitude
            step_size_lng = 200 / (111000 * math.cos(math.radians(lat)))  # 200 meters in degrees longitude

        # Calculate the number of steps in each direction
        num_steps_lat = min(int(radius_lat / step_size_lat) + 1, 10)  # Limit to 10 steps
        num_steps_lng = min(int(radius_lng / step_size_lng) + 1, 10)  # Limit to 10 steps

        # Generate grid points
        for i in range(-num_steps_lat, num_steps_lat + 1):
            for j in range(-num_steps_lng, num_steps_lng + 1):
                # Skip the center point (already added)
                if i == 0 and j == 0:
                    continue

                # Calculate the coordinates of this point
                point_lat = lat + i * step_size_lat
                point_lng = lng + j * step_size_lng

                # Calculate the distance from the center
                dlat = point_lat - lat
                dlng = point_lng - lng
                distance = math.sqrt(dlat * dlat + dlng * dlng)

                # Only include points within the radius
                if distance <= radius_lat:
                    points.append({'lat': point_lat, 'lng': point_lng})

                    # Check if we've reached the maximum number of points
                    if len(points) >= max_results:
                        break

            # Check if we've reached the maximum number of points
            if len(points) >= max_results:
                break

        # Generate property data for each point
        properties = []

        for point in points:
            # Convert WGS84 to S-JTSK
            x, y = self.app.cuzk_integration.convert_wgs84_to_sjtsk(point['lat'], point['lng'])

            # Ensure coordinates are integers
            x_int = int(x)
            y_int = int(y)

            # Generate MapaIdentifikace URL
            url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"

            # Create a RUIAN ID that includes the coordinates
            ruian_id = f"RUIAN_{x_int}_{y_int}"

            # Create property data
            property_data = {
                'property_type': 'Building',
                'ruian_id': ruian_id,
                'source': 'ruian',
                'lat': point['lat'],
                'lng': point['lng'],
                'x_sjtsk': x_int,
                'y_sjtsk': y_int,
                'url': url,
                'owner_name': 'Click URL to view property details',
                'owner_address': url
            }

            # Add to properties list
            properties.append(property_data)

            # Add a small delay to avoid overwhelming the RUIAN API
            import time
            time.sleep(0.1)

        # Filter by property type if specified
        if property_type and property_type.lower() != 'all':
            filtered_properties = []
            for prop in properties:
                prop_type = prop.get('property_type', '').lower()
                if property_type.lower() in prop_type:
                    filtered_properties.append(prop)
            properties = filtered_properties

        # Update the buildings treeview in the batch search UI if it exists
        # First check if we have a callback function
        if callback:
            # Convert properties to the format expected by the buildings treeview
            buildings_format = []
            for prop in properties:
                building = {
                    'ruian_ref': prop.get('ruian_id', ''),
                    'name': prop.get('property_type', 'Building'),
                    'address': prop.get('url', ''),
                    'tags': {
                        'building': prop.get('property_type', 'Building'),
                        'addr:city': 'Location',
                        'addr:conscriptionnumber': ''
                    },
                    'lat': prop.get('lat'),
                    'lng': prop.get('lng'),
                    'source': 'ruian'
                }
                buildings_format.append(building)

            # Call the callback function with the buildings data
            self.app.root.after(0, lambda: callback(buildings_format))
            logger.info(f"Called callback with {len(buildings_format)} buildings from RUIAN")
        # If no callback, try to find the batch_search_ui
        elif hasattr(self.app, 'batch_search_ui') and hasattr(self.app.batch_search_ui, 'update_buildings_treeview'):
            # Convert properties to the format expected by the buildings treeview
            buildings_format = []
            for prop in properties:
                building = {
                    'ruian_ref': prop.get('ruian_id', ''),
                    'name': prop.get('property_type', 'Building'),
                    'address': prop.get('url', ''),
                    'tags': {
                        'building': prop.get('property_type', 'Building'),
                        'addr:city': 'Location',
                        'addr:conscriptionnumber': ''
                    },
                    'lat': prop.get('lat'),
                    'lng': prop.get('lng'),
                    'source': 'ruian'
                }
                buildings_format.append(building)

            # Update the buildings treeview directly in the batch_search_ui
            self.app.root.after(0, lambda: self.app.batch_search_ui.update_buildings_treeview(buildings_format))
            logger.info(f"Updated buildings treeview with {len(buildings_format)} buildings from RUIAN")
        # Try to find the UI component in the ui_manager
        elif hasattr(self.app, 'ui_manager') and hasattr(self.app.ui_manager, 'batch_search') and hasattr(self.app.ui_manager.batch_search, 'update_buildings_treeview'):
            # Convert properties to the format expected by the buildings treeview
            buildings_format = []
            for prop in properties:
                building = {
                    'ruian_ref': prop.get('ruian_id', ''),
                    'name': prop.get('property_type', 'Building'),
                    'address': prop.get('url', ''),
                    'tags': {
                        'building': prop.get('property_type', 'Building'),
                        'addr:city': 'Location',
                        'addr:conscriptionnumber': ''
                    },
                    'lat': prop.get('lat'),
                    'lng': prop.get('lng'),
                    'source': 'ruian'
                }
                buildings_format.append(building)

            # Update the buildings treeview through the ui_manager
            self.app.root.after(0, lambda: self.app.ui_manager.batch_search.update_buildings_treeview(buildings_format))
            logger.info(f"Updated buildings treeview through ui_manager with {len(buildings_format)} buildings from RUIAN")
        else:
            logger.warning("batch_search_ui not found or doesn't have update_buildings_treeview method")

        logger.info(f"Generated {len(properties)} RUIAN property URLs")
        return properties

    def _batch_search_thread(self, address, property_types, radius_meters, max_results, callback):
        """
        Background thread for batch search by address.

        Args:
            address (str): Full address to search in
            property_types (list): List of property types to include
            radius_meters (int): Search radius in meters
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
        """
        try:
            # Get coordinates for the address
            result = self.app.google_maps.geocode(address)

            if not result or 'lat' not in result or 'lng' not in result:
                logger.error(f"Could not geocode address: {address}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "Geocoding Error", f"Could not find coordinates for address: {address}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            lat = result['lat']
            lng = result['lng']
            formatted_address = result.get('formatted_address', address)

            # Fetch properties in the area
            properties = self.fetch_properties_in_area(lat, lng, radius_meters, max_results, property_types)

            # Update the UI in the main thread
            self.app.root.after(0, lambda: self.display_batch_results(properties, formatted_address))

            # Call the callback if provided
            if callback:
                self.app.root.after(0, lambda: callback(properties))

            # Reset search flag
            self.search_in_progress = False

        except Exception as e:
            logger.error(f"Error in batch search thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"An error occurred: {str(e)}"))
            self.app.root.after(0, lambda: self.app.show_status("Ready"))
            self.search_in_progress = False

    def _batch_search_by_coordinates_thread(self, lat, lng, property_types, radius_meters, max_results, callback):
        """
        Background thread for batch search by coordinates.

        Args:
            lat (float): Latitude
            lng (float): Longitude
            property_types (list): List of property types to include
            radius_meters (int): Search radius in meters
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
        """
        try:
            # Fetch properties in the area
            properties = self.fetch_properties_in_area(lat, lng, radius_meters, max_results, property_types)

            # Update the UI in the main thread
            location_name = f"Coordinates ({lat:.6f}, {lng:.6f})"
            self.app.root.after(0, lambda: self.display_batch_results(properties, location_name))

            # Call the callback if provided
            if callback:
                self.app.root.after(0, lambda: callback(properties))

            # Reset search flag
            self.search_in_progress = False

        except Exception as e:
            logger.error(f"Error in batch search by coordinates thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"An error occurred: {str(e)}"))
            self.app.root.after(0, lambda: self.app.show_status("Ready"))
            self.search_in_progress = False

    def _batch_search_by_city_thread(self, city, property_types, max_results, callback):
        """
        Background thread for batch search by city.

        Args:
            city (str): City name
            property_types (list): List of property types to include
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
        """
        try:
            # Get city boundaries from Google Maps
            boundaries = self.app.google_maps.get_city_boundaries(city)

            if not boundaries:
                logger.error(f"Could not find boundaries for city: {city}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "City Error", f"Could not find boundaries for city: {city}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            # Get the center of the city
            center = boundaries.get('center', {})
            lat = center.get('lat')
            lng = center.get('lng')

            if not lat or not lng:
                logger.error(f"Could not find center coordinates for city: {city}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "City Error", f"Could not find center coordinates for city: {city}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            # Fetch properties in the city area
            properties = self.fetch_properties_in_city(city, boundaries, max_results, property_types)

            # Update the UI in the main thread
            self.app.root.after(0, lambda: self.display_batch_results(properties, city))

            # Call the callback if provided
            if callback:
                self.app.root.after(0, lambda: callback(properties))

            # Reset search flag
            self.search_in_progress = False

        except Exception as e:
            logger.error(f"Error in batch search by city thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"An error occurred: {str(e)}"))
            self.app.root.after(0, lambda: self.app.show_status("Ready"))
            self.search_in_progress = False





        except Exception as e:
            logger.error(f"Error in batch search by address: {e}", exc_info=True)
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.app.show_status("Ready")

    def display_batch_results(self, properties, location_name):
        """
        Display the batch search results in the UI.

        Updates the batch results text widget with property information
        and enables action buttons if properties were found.

        Args:
            properties (list): List of property data dictionaries with owner info
            location_name (str): Name of the location for display purposes
        """
        try:
            # Clear the property list
            self.app.property_list = []

            # Update the batch results text
            if hasattr(self.app, 'batch_results_text'):
                self.app.batch_results_text.config(state="normal")
                self.app.batch_results_text.delete(1.0, tk.END)  # Clear existing content
                self.app.batch_results_text.insert(tk.END, f"Found {len(properties)} properties in {location_name}\n\n")

                # Process each property
                for i, property_data in enumerate(properties):
                    # Extract property information and check for "Unknown" values
                    owner_name = property_data.get('owner_name', '')
                    owner_address = property_data.get('owner_address', '')
                    prop_type = property_data.get('property_type', '')
                    source = property_data.get('source', '').lower()
                    url = property_data.get('url', '')

                    # Check for "Unknown" values and log them
                    unknown_fields = []
                    if owner_name == 'Unknown':
                        unknown_fields.append('owner_name')
                    if owner_address == 'Unknown':
                        unknown_fields.append('owner_address')
                    if prop_type == 'Unknown':
                        unknown_fields.append('property_type')

                    # Log the unknown fields for debugging
                    if unknown_fields:
                        logger.warning(f"Property {i+1} has 'Unknown' values for fields: {', '.join(unknown_fields)}")
                        logger.warning(f"Property data: {property_data}")

                        # Show a notification to the user
                        self.app.root.after(0, lambda: MessageBoxes.show_warning(
                            "Unknown Values Detected",
                            f"Property {i+1} has 'Unknown' values for fields: {', '.join(unknown_fields)}.\n\n"
                            "These will be replaced with default values for display purposes.\n"
                            "Please check the application logs for more details."
                        ))

                        # Replace "Unknown" values with more descriptive defaults
                        if 'owner_name' in unknown_fields:
                            owner_name = 'Property Owner (view details on CUZK)'
                        if 'owner_address' in unknown_fields:
                            owner_address = 'Property Address'
                        if 'property_type' in unknown_fields:
                            prop_type = 'Building'

                    # Get original owner and address if available
                    original_owner = property_data.get('original_owner', '')
                    if original_owner == 'Unknown':
                        logger.warning(f"Property {i+1} has 'Unknown' value for original_owner")
                        logger.warning(f"Property data: {property_data}")
                        original_owner = 'Property Owner (view details on CUZK)'

                    original_address = property_data.get('original_address', '')
                    if original_address == 'Unknown':
                        logger.warning(f"Property {i+1} has 'Unknown' value for original_address")
                        logger.warning(f"Property data: {property_data}")
                        original_address = property_data.get('address', 'Property Address')
                        if original_address == 'Unknown':
                            original_address = 'Property Address'

                    # Get SJTSK coordinates if available
                    x_sjtsk = property_data.get('x_sjtsk', '')
                    y_sjtsk = property_data.get('y_sjtsk', '')

                    # Add to results
                    result_text = f"Property {i+1}"

                    # Add icon for CUZK URL
                    if 'mapaidentifikace' in source:
                        result_text += " 🌐 [CUZK URL]"

                    result_text += ":\n"

                    # Show original owner if available
                    if original_owner and original_owner != 'Unknown':
                        result_text += f"Owner: {original_owner}\n"
                    elif owner_name and owner_name != 'Unknown' and 'click url' not in owner_name.lower():
                        result_text += f"Owner: {owner_name}\n"
                    else:
                        result_text += f"Owner: Property Owner (view details on CUZK)\n"

                    # Show original address if available
                    if original_address and original_address != 'Unknown':
                        result_text += f"Address: {original_address}\n"
                    elif 'address' in property_data and property_data['address'] and property_data['address'] != 'Unknown':
                        result_text += f"Address: {property_data['address']}\n"

                    # Show SJTSK coordinates if available
                    if x_sjtsk and y_sjtsk:
                        result_text += f"Coordinates (S-JTSK): x={x_sjtsk}, y={y_sjtsk}\n"

                    # Show property type
                    if prop_type and prop_type != 'Unknown':
                        result_text += f"Type: {prop_type}\n"
                    else:
                        result_text += f"Type: Building\n"

                    # If this is a CUZK MapaIdentifikace URL, make it clear it's a URL
                    if 'mapaidentifikace' in source and url:
                        # Show original address if available
                        if original_address:
                            result_text += f"Address: {original_address}\n"

                        # Add a clickable URL
                        result_text += "CUZK URL: "
                        self.app.batch_results_text.insert(tk.END, result_text)

                        # Add the clickable URL separately
                        url_start_pos = self.app.batch_results_text.index(tk.END)

                        # Insert the URL
                        self.app.batch_results_text.insert(tk.END, url)
                        url_end_pos = self.app.batch_results_text.index(tk.END)

                        # Make the URL clickable if the text widget supports tags
                        try:
                            self.app.batch_results_text.tag_add(f"url_{i}", url_start_pos, url_end_pos)
                            self.app.batch_results_text.tag_config(f"url_{i}", foreground="blue", underline=1)
                            self.app.batch_results_text.tag_bind(f"url_{i}", "<Button-1>",
                                                               lambda _, u=url: webbrowser.open(u))
                        except Exception as tag_error:
                            logger.error(f"Error making URL clickable: {tag_error}")

                        # Add newline after the URL
                        self.app.batch_results_text.insert(tk.END, "\n\n")
                    else:
                        # Regular property (not a URL)
                        result_text += f"Address: {owner_address}\n"
                        result_text += f"Type: {prop_type}\n\n"
                        self.app.batch_results_text.insert(tk.END, result_text)

                    # Store the property data for later use
                    self.app.property_list.append(property_data)

                # Configure the text widget to be read-only
                self.app.batch_results_text.config(state="disabled")

                # Configure the text widget to show a hand cursor over URLs
                self.app.batch_results_text.config(cursor="arrow")
                for i in range(len(properties)):
                    try:
                        self.app.batch_results_text.tag_bind(f"url_{i}", "<Enter>",
                                                           lambda _: self.app.batch_results_text.config(cursor="hand2"))
                        self.app.batch_results_text.tag_bind(f"url_{i}", "<Leave>",
                                                           lambda _: self.app.batch_results_text.config(cursor="arrow"))
                    except:
                        pass  # Ignore errors for non-URL properties

            # Show status
            self.app.show_status(f"Found {len(properties)} properties in {location_name}")

            # Enable the generate letters button if we have properties
            if hasattr(self.app, 'generate_letters_btn') and properties:
                self.app.generate_letters_btn.config(state="normal")
        except Exception as e:
            print(f"Error displaying batch results: {e}")
            MessageBoxes.show_error("Error", f"Error displaying batch results: {str(e)}")
            self.app.show_status("Ready")
