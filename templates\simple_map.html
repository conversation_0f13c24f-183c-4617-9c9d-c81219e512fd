<!DOCTYPE html>
<html>
<head>
    <title>Simple Google Maps Example</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }
    </style>
</head>
<body>
    <h1>Simple Google Maps Example</h1>
    <div id="map"></div>
    <script>
        function initMap() {
            // Create a map centered on Prague
            var map = new google.maps.Map(document.getElementById('map'), {
                center: {lat: {center_lat}, lng: {center_lng}},
                zoom: {zoom}
            });
            
            // Add a marker
            var marker = new google.maps.Marker({
                position: {lat: {marker_lat}, lng: {marker_lng}},
                map: map,
                title: '{marker_title}'
            });
        }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key={api_key}&callback=initMap" async defer></script>
</body>
</html>
