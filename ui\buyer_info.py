"""
Buyer information UI components for the Czech Property Registry application.

This module provides UI components for entering and managing buyer information.
"""

import tkinter as tk
from tkinter import ttk


class BuyerInfoUI:
    """
    UI components for buyer information.
    
    This class creates and manages the UI elements for entering
    and managing buyer information for letter generation.
    """
    
    def __init__(self, parent_frame, app):
        """
        Initialize the buyer information UI.
        
        Args:
            parent_frame: The parent frame to place the UI elements in
            app: The main application instance for callbacks
        """
        self.parent = parent_frame
        self.app = app
        
        # Create the UI elements
        self.create_ui()
    
    def create_ui(self):
        """Create the buyer information UI elements"""
        # Create a frame for the buyer information
        self.buyer_frame = ttk.LabelFrame(self.parent, text="Buyer Information")
        self.buyer_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create a grid for the buyer information
        buyer_grid = ttk.Frame(self.buyer_frame)
        buyer_grid.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Add buyer name label and entry
        ttk.Label(buyer_grid, text="Your Name:").grid(row=0, column=0, sticky="w", padx=2, pady=2)
        self.buyer_name = ttk.Entry(buyer_grid, width=25)
        self.buyer_name.grid(row=0, column=1, sticky="ew", padx=2, pady=2)
        
        # Add buyer address label and entry
        ttk.Label(buyer_grid, text="Your Address:").grid(row=1, column=0, sticky="w", padx=2, pady=2)
        self.buyer_address = ttk.Entry(buyer_grid, width=25)
        self.buyer_address.grid(row=1, column=1, sticky="ew", padx=2, pady=2)
        
        # Add buyer contact label and entry
        ttk.Label(buyer_grid, text="Your Contact:").grid(row=2, column=0, sticky="w", padx=2, pady=2)
        self.buyer_contact = ttk.Entry(buyer_grid, width=25)
        self.buyer_contact.grid(row=2, column=1, sticky="ew", padx=2, pady=2)
        
        # Add a separator
        ttk.Separator(buyer_grid, orient="horizontal").grid(row=3, column=0, columnspan=2, sticky="ew", padx=2, pady=5)
        
        # Add letter style label and combobox
        ttk.Label(buyer_grid, text="Letter Style:").grid(row=4, column=0, sticky="w", padx=2, pady=2)
        self.letter_style = ttk.Combobox(buyer_grid, width=15, state="readonly")
        self.letter_style['values'] = ("Formal", "Friendly", "Professional", "Direct")
        self.letter_style.current(0)  # Set default to Formal
        self.letter_style.grid(row=4, column=1, sticky="w", padx=2, pady=2)
        
        # Add letter tone label and combobox
        ttk.Label(buyer_grid, text="Letter Tone:").grid(row=5, column=0, sticky="w", padx=2, pady=2)
        self.letter_tone = ttk.Combobox(buyer_grid, width=15, state="readonly")
        self.letter_tone['values'] = ("Respectful", "Enthusiastic", "Neutral", "Persuasive")
        self.letter_tone.current(0)  # Set default to Respectful
        self.letter_tone.grid(row=5, column=1, sticky="w", padx=2, pady=2)
        
        # Add personalization checkbox
        self.letter_personalization = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            buyer_grid,
            text="Personalize Letter",
            variable=self.letter_personalization
        ).grid(row=6, column=0, columnspan=2, sticky="w", padx=2, pady=2)
        
        # Add use GPT checkbox
        self.use_gpt = tk.BooleanVar(value=True)
        self.gpt_checkbox = ttk.Checkbutton(
            buyer_grid,
            text="Use AI for Letter Generation",
            variable=self.use_gpt
        )
        self.gpt_checkbox.grid(row=7, column=0, columnspan=2, sticky="w", padx=2, pady=2)
        
        # Disable the GPT checkbox if GPT is not available
        if not hasattr(self.app, 'gpt') or self.app.gpt is None:
            self.gpt_checkbox.config(state="disabled")
            self.use_gpt.set(False)
        
        # Configure the grid to expand properly
        buyer_grid.columnconfigure(1, weight=1)
        
        # Store the variables in the app for access by other components
        self.app.buyer_name = self.buyer_name
        self.app.buyer_address = self.buyer_address
        self.app.buyer_contact = self.buyer_contact
        self.app.letter_style = self.letter_style
        self.app.letter_tone = self.letter_tone
        self.app.letter_personalization = self.letter_personalization
        self.app.use_gpt = self.use_gpt
