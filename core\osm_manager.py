"""
OSM manager for the Czech Property Scraper application.
Handles all OpenStreetMap-related functionality including:
- Fetching buildings with RUIAN references from OpenStreetMap
- Searching for properties by coordinates, address, or city
- Processing OSM building data
- Providing address suggestions for autocomplete
- Fetching owner information from CUZK using RUIAN IDs
"""

import threading

from ui.message_boxes import MessageBoxes
from utils.osm_helpers import (
    update_osm_component_fields,  # Updates UI fields from full address
    update_osm_full_address,      # Updates full address from UI fields
    get_osm_address_suggestions,  # Gets address suggestions for autocomplete
    get_osm_city_suggestions,     # Gets city suggestions for autocomplete
    get_osm_street_suggestions,   # Gets street suggestions for autocomplete
    get_osm_postal_suggestions    # Gets postal code suggestions for autocomplete
)


class OSMManager:
    """
    Manages OpenStreetMap functionality for the application.

    Provides methods for searching and retrieving property data from OpenStreetMap,
    with support for caching results to improve performance.
    """

    def __init__(self, app):
        """
        Initialize the OSM manager.

        Args:
            app: The main application instance with access to Google Maps and OSM APIs
        """
        self.app = app  # Main application with access to required services

    def fetch_properties_from_osm(self, lat, lng, radius=500):
        """
        Fetch properties with RUIAN references from OpenStreetMap.

        Uses caching to improve performance for repeated searches.
        Coordinates are rounded to 5 decimal places for better cache hits.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            radius (int): Search radius in meters (default: 500)

        Returns:
            list: List of buildings with RUIAN references and metadata
        """
        try:
            # Show status
            self.app.show_status(f"Fetching buildings from OpenStreetMap at coordinates {lat}, {lng}...")

            # Create a cache key based on coordinates and radius
            # Round coordinates to 5 decimal places for better cache hits
            lat_rounded = round(float(lat), 5)
            lng_rounded = round(float(lng), 5)
            cache_key = f"{lat_rounded}_{lng_rounded}_{radius}"

            # Check if we have cached results
            if hasattr(self.app, 'cache_manager'):
                cached_buildings = self.app.cache_manager.get('osm_buildings', cache_key)
                if cached_buildings:
                    print(f"Using cached OSM buildings for {cache_key}")
                    return cached_buildings

            # If not cached, fetch from OSM
            print(f"Fetching OSM buildings for {cache_key}")
            buildings = self.app.osm_integration.search_buildings_by_coordinates(lat, lng, radius)

            # Cache the results if we have a cache manager
            if hasattr(self.app, 'cache_manager') and buildings:
                self.app.cache_manager.set('osm_buildings', cache_key, buildings)

            return buildings
        except Exception as e:
            print(f"Error fetching properties from OSM: {e}")
            return []

    def search_buildings_in_osm_by_address(self, full_address=None, city=None, street=None, number=None, postal=None, radius=2000):
        """
        Search for buildings with RUIAN references in OpenStreetMap using address.

        Supports searching by either full address or address components.
        Uses Google Maps for geocoding the address to coordinates.

        Args:
            full_address (str, optional): Full address to search for (e.g., "Václavské náměstí 1, Praha")
            city (str, optional): City name (e.g., "Praha")
            street (str, optional): Street name (e.g., "Václavské náměstí")
            number (str, optional): House number (e.g., "1")
            postal (str, optional): Postal code (e.g., "11000")
            radius (int, optional): Search radius in meters (default: 2000)
        """
        try:
            # Show status
            if full_address:
                self.app.show_status(f"Searching for buildings at {full_address}...")
            elif city:
                self.app.show_status(f"Searching for buildings in {city}...")
            else:
                self.app.show_status("Searching for buildings...")

            # Get coordinates for the address
            if full_address:
                result = self.app.google_maps.geocode(full_address)
            elif city:
                # Construct address from components
                address = city
                if street:
                    address += f", {street}"
                if number:
                    address += f" {number}"
                if postal:
                    address += f", {postal}"

                result = self.app.google_maps.geocode(address)
            else:
                MessageBoxes.show_error("Error", "Please enter either a full address or at least a city name")
                return

            if not result:
                MessageBoxes.show_error("Error", f"Could not find coordinates for the address")
                return

            lat = result['lat']
            lng = result['lng']

            # Now search for buildings at these coordinates
            self.search_buildings_in_osm_by_coordinates(lat, lng, radius)
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def _search_buildings_thread(self, lat, lng, radius, location_name):
        """
        Common thread function for searching buildings in background.

        Fetches properties from OSM and updates UI when complete.
        Used by multiple search methods to avoid code duplication.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            radius (int): Search radius in meters
            location_name (str): Name of the location for display in UI
        """
        try:
            # Search for buildings with RUIAN references in OpenStreetMap
            buildings = self.fetch_properties_from_osm(lat, lng, radius)

            # Update the UI in the main thread
            self.app.root.after(0, lambda: self.process_osm_buildings(buildings, lat, lng, location_name))
        except Exception as e:
            print(f"Error searching for buildings in OpenStreetMap: {e}")
            self.app.root.after(0, lambda: MessageBoxes.show_error("Search Error", f"Error searching for buildings: {str(e)}"))
            self.app.show_status("Ready")

    def _run_in_thread(self, target_func, *args):
        """
        Run a function in a background thread.

        Uses thread pool if available, otherwise creates a new thread.
        Helper method to keep UI responsive during long-running operations.

        Args:
            target_func: Function to run in background
            *args: Arguments to pass to the function
        """
        if hasattr(self.app, 'thread_pool'):
            self.app.thread_pool.submit(target_func, *args)
        else:
            # Fallback to direct threading if thread pool is not available
            threading.Thread(target=lambda: target_func(*args), daemon=True).start()

    def search_buildings_in_osm_by_coordinates(self, lat, lng, radius=500):
        """
        Search for buildings with RUIAN references in OpenStreetMap using coordinates.

        Direct coordinate-based search method that runs in background thread.
        Most precise search method when exact coordinates are known.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            radius (int): Search radius in meters (default: 500)
        """
        try:
            # Show status
            self.app.show_status(f"Searching for buildings at coordinates {lat}, {lng}...")

            # Run the search in a thread
            location_name = f"coordinates {lat}, {lng}"
            self._run_in_thread(self._search_buildings_thread, lat, lng, radius, location_name)
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def search_buildings_in_osm_by_city(self, city, radius=2000):
        """
        Search for buildings with RUIAN references in OpenStreetMap using city name.

        Geocodes city name to coordinates using Google Maps API, then searches OSM.
        Useful for broad area searches when specific address isn't known.

        Args:
            city (str): City name (e.g., "Praha", "Brno")
            radius (int): Search radius in meters (default: 2000)
        """
        try:
            # Show status
            self.app.show_status(f"Searching for buildings in {city}...")

            # Get coordinates for the city
            result = self.app.google_maps.geocode(city)

            if not result:
                MessageBoxes.show_error("Error", f"Could not find coordinates for {city}")
                return

            lat = result['lat']
            lng = result['lng']

            # Run the search in a thread
            self._run_in_thread(self._search_buildings_thread, lat, lng, radius, city)
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def process_osm_buildings(self, buildings, lat, lng, location_name):
        """
        Process the buildings returned from OpenStreetMap.

        Delegates to OSM methods class for actual processing.
        This separation allows for different processing strategies.

        Args:
            buildings (list): List of building dictionaries with OSM metadata
            lat (float): Latitude coordinate of search center
            lng (float): Longitude coordinate of search center
            location_name (str): Name of the location for display in UI
        """
        # Delegate to the OSM methods class
        if hasattr(self.app, 'osm_methods'):
            self.app.osm_methods.process_osm_buildings(buildings, lat, lng, location_name)
        else:
            print("OSM methods not initialized")

    def fetch_owner_info_from_cuzk(self, ruian_id):
        """
        Fetch owner information from CUZK using RUIAN ID.

        Uses CUZK integration to retrieve property owner details.
        Key method for getting actual owner data after finding properties.

        Args:
            ruian_id (str): RUIAN ID (unique property identifier in Czech cadastre)

        Returns:
            dict: Owner information (name, address) or None if not found
        """
        try:
            # Show status
            self.app.show_status(f"Fetching owner information for RUIAN ID {ruian_id}...")

            # Use the CUZK integration
            return self.app.cuzk_integration.get_property_by_ruian(ruian_id)
        except Exception as e:
            print(f"Error fetching owner information from CUZK: {e}")
            return None

    # Helper methods for OSM search UI components
    def update_osm_component_fields(self, ui_component, event=None):
        """
        Update the component fields (city, street, etc.) based on the full address.

        Parses full address into components and updates UI fields.
        """
        update_osm_component_fields(ui_component, event)

    def update_osm_full_address(self, ui_component, event=None):
        """
        Update the full address field based on the component fields.

        Combines city, street, number, and postal code into full address.
        """
        update_osm_full_address(ui_component, event)

    def _get_suggestions(self, suggestion_type, text):
        """
        Get suggestions of a specific type for autocomplete.

        Helper method that maps suggestion types to appropriate functions.
        Used by the public suggestion methods to avoid code duplication.

        Args:
            suggestion_type (str): Type of suggestions to get ('address', 'city', 'street', 'postal')
            text (str): Text to get suggestions for (user input)

        Returns:
            list: List of matching suggestions for autocomplete
        """
        # Map suggestion types to helper functions
        suggestion_funcs = {
            'address': get_osm_address_suggestions,
            'city': get_osm_city_suggestions,
            'street': get_osm_street_suggestions,
            'postal': get_osm_postal_suggestions
        }

        # Get the appropriate function and call it
        if suggestion_type in suggestion_funcs:
            return suggestion_funcs[suggestion_type](self.app, text)
        return []

    def get_osm_address_suggestions(self, text):
        """
        Get full address suggestions for OSM search autocomplete.

        Returns matching addresses based on user input.
        """
        return self._get_suggestions('address', text)

    def get_osm_city_suggestions(self, text):
        """
        Get city suggestions for OSM search autocomplete.

        Returns matching city names based on user input.
        """
        return self._get_suggestions('city', text)

    def get_osm_street_suggestions(self, text):
        """
        Get street suggestions for OSM search autocomplete.

        Returns matching street names based on user input.
        """
        return self._get_suggestions('street', text)

    def get_osm_postal_suggestions(self, text):
        """
        Get postal code suggestions for OSM search autocomplete.

        Returns matching postal codes based on user input.
        """
        return self._get_suggestions('postal', text)
