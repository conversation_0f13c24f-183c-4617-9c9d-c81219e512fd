"""
Settings manager for the Czech Property Registry application.

This module provides functionality for managing application settings.
"""

import logging
from config.config import config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SettingsManager")


class SettingsManager:
    """
    Manages application settings.
    
    This class provides methods for loading, saving, and applying settings.
    """
    
    def __init__(self, app):
        """
        Initialize the settings manager.
        
        Args:
            app: The main application instance
        """
        self.app = app
        
        # Load settings
        self.load_settings()
        
    def load_settings(self):
        """Load settings from the configuration."""
        logger.info("Loading application settings")
        
        # Load batch settings
        self.max_batch_size = config.get_int("DEFAULT", "max_batch_size", 5)
        logger.info(f"Loaded max_batch_size: {self.max_batch_size}")
        
        # Load cache settings
        self.cache_expiry = config.get_int("CACHE", "expiry_hours", 24)
        logger.info(f"Loaded cache_expiry: {self.cache_expiry}")
        
        # Load UI settings
        self.font_scale = config.get_float("DEFAULT", "font_scale", 1.0)
        logger.info(f"Loaded font_scale: {self.font_scale}")
        
    def reload_settings(self):
        """Reload settings from the configuration and apply them."""
        logger.info("Reloading application settings")
        
        # Reload settings
        self.load_settings()
        
        # Apply settings that can be changed at runtime
        self._apply_runtime_settings()
        
    def _apply_runtime_settings(self):
        """Apply settings that can be changed at runtime."""
        # Apply cache settings
        if hasattr(self.app, 'cache_manager'):
            logger.info(f"Applying cache expiry: {self.cache_expiry} hours")
            self.app.cache_manager.default_expiry = self.cache_expiry * 3600  # Convert hours to seconds
            
        # Apply batch settings
        # These are read directly from the config when needed, so no action required
        
        # Apply other runtime settings as needed
        # ...
        
    def get_max_batch_size(self):
        """
        Get the maximum batch size.
        
        Returns:
            int: Maximum batch size
        """
        return self.max_batch_size
        
    def set_max_batch_size(self, size):
        """
        Set the maximum batch size.
        
        Args:
            size (int): Maximum batch size
        """
        if size < 1:
            size = 1
            
        self.max_batch_size = size
        config.set("DEFAULT", "max_batch_size", str(size))
        config.save_config()
        
    def get_cache_expiry(self):
        """
        Get the cache expiry time in hours.
        
        Returns:
            int: Cache expiry time in hours
        """
        return self.cache_expiry
        
    def set_cache_expiry(self, hours):
        """
        Set the cache expiry time in hours.
        
        Args:
            hours (int): Cache expiry time in hours
        """
        if hours < 1:
            hours = 1
            
        self.cache_expiry = hours
        config.set("CACHE", "expiry_hours", str(hours))
        config.save_config()
        
        # Apply the new setting
        if hasattr(self.app, 'cache_manager'):
            self.app.cache_manager.default_expiry = hours * 3600  # Convert hours to seconds
