"""
Data manager for the Czech Property Scraper application.
Handles data loading, storage, and caching.
"""

import json
import os
import logging
import threading
import time
from typing import Dict, Any, List, Optional

# Configure logging
logger = logging.getLogger(__name__)


class DataManager:
    """
    Manages data loading, storage, and caching for the application.

    This class implements a hybrid approach:
    1. Data is stored in JSON files for fast access
    2. Data is refreshed from APIs on a configurable schedule
    3. Data freshness is tracked and reported to the UI
    """

    def __init__(self, app):
        """
        Initialize the data manager.

        Args:
            app: The main application instance
        """
        self.app = app
        self.data_dir = "czech_data"
        self.data_cache = {}
        self.cache_lock = threading.Lock()
        self.always_prefer_api = False  # Changed to False to prioritize cache for static data
        self.max_cache_age = 30 * 24 * 60 * 60  # Maximum cache age in seconds (30 days for static data)

        # Define cache expiration times for different data types (in seconds)
        # Static data can have longer expiration times
        self.cache_expiration = {
            "regions": 90 * 24 * 60 * 60,      # 90 days for regions (very static)
            "cities": 60 * 24 * 60 * 60,       # 60 days for cities (very static)
            "districts": 60 * 24 * 60 * 60,    # 60 days for districts (very static)
            "cadastral": 30 * 24 * 60 * 60,    # 30 days for cadastral areas (somewhat static)
            "property_types": 30 * 24 * 60 * 60, # 30 days for property types (somewhat static)
            "streets": 14 * 24 * 60 * 60       # 14 days for streets (more likely to change)
        }

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # Initialize data storage
        self.initialize_data_storage()

        # Initialize data refresh service
        self._initialize_data_refresh_service()

    def _initialize_data_refresh_service(self):
        """Initialize the data refresh service."""
        try:
            from services.data_refresh_service import DataRefreshService
            self.refresh_service = DataRefreshService(self.app)

            # Start the refresh thread
            self.refresh_service.start_refresh_thread()
            logger.info("Data refresh service initialized and started")

            # Load only essential data initially
            self.refresh_service.load_essential_data()

            # Schedule loading of remaining data in the background
            if hasattr(self.app, 'root'):
                # Use Tkinter's after method to schedule background loading
                # This ensures the UI is responsive before loading more data
                self.app.root.after(5000, self.refresh_service.load_remaining_data_in_background)
            else:
                # If no Tkinter root is available, start background loading immediately
                self.refresh_service.load_remaining_data_in_background()

            logger.info("Progressive data loading initialized")
        except Exception as e:
            logger.error(f"Error initializing data refresh service: {e}")
            self.refresh_service = None

    def initialize_data_storage(self):
        """Initialize data storage for the application."""
        # Define data structure with default values
        data_structure = {
            # Property data storage
            'owner_name': "",
            'owner_address': "",
            'owner_gender': "",
            'batch_property_owners': [],  # List to store multiple property owners

            # Data storage for location search
            'regions_data': {},
            'districts_data': {},
            'cities_data': {},
            'cadastral_data': {},
            'property_list': [],

            # Czech data for names and addresses
            'czech_names': {},
            'czech_addresses': {},
            'czech_regions': {},
            'czech_cities': {},
            'czech_districts': {},
            'czech_cadastral': {}
        }

        # Initialize all attributes
        for attr_name, default_value in data_structure.items():
            setattr(self.app, attr_name, default_value)

    def load_czech_data(self, data_type: str, force_api: bool = None) -> Dict[str, Any]:
        """
        Load Czech data from JSON files with API refresh if needed.

        This method implements a progressive loading strategy:
        1. First check in-memory cache for fastest access
        2. Then check app attributes
        3. Then load from file
        4. Only refresh from API if necessary or explicitly requested

        Args:
            data_type (str): Type of data to load (names, addresses, regions, cities, districts, cadastral)
            force_api (bool, optional): Force using API data even if cache is available

        Returns:
            dict: Loaded data

        Raises:
            ValueError: If data cannot be loaded and no API refresh is available
        """
        # Map data types to file paths and attribute names
        data_map = {
            'names': {'file': 'czech_data/czech_names.json', 'attr': 'czech_names', 'refresh': 'names'},
            'addresses': {'file': 'czech_data/czech_addresses.json', 'attr': 'czech_addresses', 'refresh': 'addresses'},
            'regions': {'file': 'czech_data/czech_regions.json', 'attr': 'czech_regions', 'refresh': 'regions'},
            'cities': {'file': 'czech_data/czech_cities.json', 'attr': 'czech_cities', 'refresh': 'cities'},
            'districts': {'file': 'czech_data/czech_districts.json', 'attr': 'czech_districts', 'refresh': 'districts'},
            'cadastral': {'file': 'czech_data/czech_cadastral.json', 'attr': 'czech_cadastral', 'refresh': 'cadastral_areas'},
            'property_types': {'file': 'czech_data/property_types.json', 'attr': 'property_types', 'refresh': 'property_types'}
        }

        # Check if the data type is valid
        if data_type not in data_map:
            error_msg = f"Invalid data type: {data_type}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # Get the file path and attribute name
        file_path = data_map[data_type]['file']
        attribute_name = data_map[data_type]['attr']
        refresh_type = data_map[data_type]['refresh']

        # OPTIMIZATION: Check in-memory cache first (fastest)
        with self.cache_lock:
            if data_type in self.data_cache:
                logger.debug(f"Using in-memory cached data for {data_type}")
                # Show notification that cached data is being used
                self._show_cache_notification(data_type)

                # Schedule a background refresh if needed but don't wait for it
                if force_api or self.always_prefer_api:
                    self._schedule_background_refresh(data_type, refresh_type)

                return self.data_cache[data_type]

        # OPTIMIZATION: Check app attributes next (also fast)
        if hasattr(self.app, attribute_name) and getattr(self.app, attribute_name, None):
            data = getattr(self.app, attribute_name)
            if data:
                logger.debug(f"Using app attribute cached data for {data_type}")
                # Show notification that cached data is being used
                self._show_cache_notification(data_type)

                # Store in cache for faster future access
                with self.cache_lock:
                    self.data_cache[data_type] = data

                # Schedule a background refresh if needed but don't wait for it
                if force_api or self.always_prefer_api:
                    self._schedule_background_refresh(data_type, refresh_type)

                return data

        # OPTIMIZATION: Try to load from file (slower but still faster than API)
        if os.path.exists(file_path):
            try:
                # Load the data
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Cache the data
                setattr(self.app, attribute_name, data)

                # Store in cache
                with self.cache_lock:
                    self.data_cache[data_type] = data

                logger.info(f"Loaded {data_type} data from file {file_path}")

                # Show notification that cached data is being used
                self._show_cache_notification(data_type, "file")

                # Schedule a background refresh if needed but don't wait for it
                if force_api or self.always_prefer_api:
                    self._schedule_background_refresh(data_type, refresh_type)

                return data
            except Exception as e:
                logger.error(f"Error loading {data_type} data from file: {e}")
                # Continue to API refresh
        else:
            logger.warning(f"Data file not found: {file_path}")
            # Continue to API refresh

        # If we get here, we need to refresh the data from API
        # This is the slowest option but necessary if we have no cached data
        return self._refresh_data_from_api(data_type, refresh_type)

    def _schedule_background_refresh(self, data_type: str, refresh_type: str) -> None:
        """
        Schedule a background refresh of data without blocking the UI.

        Args:
            data_type: Type of data to refresh
            refresh_type: Type of refresh to perform
        """
        # Only schedule if we have a refresh service
        if hasattr(self, 'refresh_service') and self.refresh_service:
            # Check if refresh is already in progress
            freshness = self.refresh_service.get_data_freshness(refresh_type)
            if not freshness['refresh_in_progress']:
                logger.info(f"Scheduling background refresh of {data_type} data")

                # Use Tkinter's after method if available to avoid blocking the UI
                if hasattr(self.app, 'root'):
                    self.app.root.after(100, lambda: self.refresh_service.force_refresh(refresh_type, use_api=True))
                else:
                    # Otherwise start a thread
                    threading.Thread(
                        target=lambda: self.refresh_service.force_refresh(refresh_type, use_api=True),
                        daemon=True
                    ).start()

    def _check_data_freshness(self, refresh_type: str) -> None:
        """
        Check if data is stale and needs refreshing.

        For static data (regions, cities, etc.), we use longer expiration times
        and only refresh in the background if the data is stale or expired.

        Args:
            refresh_type: Type of data to check
        """
        if hasattr(self, 'refresh_service') and self.refresh_service:
            try:
                # Get data freshness
                freshness = self.refresh_service.get_data_freshness(refresh_type)

                # Map refresh_type to data_type for cache expiration lookup
                data_type_map = {
                    'regions': 'regions',
                    'cities': 'cities',
                    'districts': 'districts',
                    'cadastral_areas': 'cadastral',
                    'property_types': 'property_types',
                    'streets': 'streets'
                }

                data_type = data_type_map.get(refresh_type, refresh_type)

                # Get the appropriate cache expiration time for this data type
                # We'll use this in the future for more granular control

                # For static data types, we don't need to refresh as frequently
                is_static_data = data_type in ["regions", "cities", "districts", "cadastral"]

                # If always preferring API data, refresh regardless of status
                if self.always_prefer_api and not is_static_data:
                    if not freshness['refresh_in_progress']:
                        logger.info(f"Always preferring API data for {refresh_type}, triggering background refresh")
                        self.refresh_service.force_refresh(refresh_type, use_api=True)
                    return

                # For static data, only refresh if expired
                if is_static_data:
                    if freshness['status'] == 'expired' and not freshness['refresh_in_progress']:
                        logger.info(f"Static data for {refresh_type} is expired, triggering background refresh")
                        self.refresh_service.force_refresh(refresh_type, use_api=True)
                    return

                # For other data types, check if data is stale or expired
                if freshness['status'] in ('stale', 'expired') and not freshness['refresh_in_progress']:
                    logger.info(f"Data for {refresh_type} is {freshness['status']}, triggering background refresh")
                    self.refresh_service.force_refresh(refresh_type)
                elif freshness['status'] == 'unknown' and not freshness['refresh_in_progress']:
                    logger.info(f"Data freshness for {refresh_type} is unknown, triggering background refresh")
                    self.refresh_service.force_refresh(refresh_type)
            except Exception as e:
                logger.error(f"Error checking data freshness: {e}")

    def _refresh_data_from_api(self, data_type: str, refresh_type: str) -> Dict[str, Any]:
        """
        Refresh data from API.

        Args:
            data_type: Type of data to refresh
            refresh_type: Type of refresh to perform

        Returns:
            dict: Refreshed data

        Raises:
            ValueError: If data cannot be refreshed
        """
        # Use the CUZK Data API client if available
        if hasattr(self.app, 'cuzk_data_api'):
            try:
                if refresh_type == 'regions':
                    regions_data = self.app.cuzk_data_api.get_regions()
                    if regions_data:
                        # Transform to the expected format
                        data = {
                            "regions_czech": [region['name_czech'] for region in regions_data],
                            "regions_english": [region['name_english'] for region in regions_data],
                            "region_codes": [region['code'] for region in regions_data],
                            "region_mapping": {},
                            "region_coordinates": {
                                "Prague": {"lat": 50.0755, "lng": 14.4378},
                                "Czech Republic": {"lat": 49.8175, "lng": 15.4730}
                            },
                            "region_name_mapping": {}
                        }

                        # Save to file
                        file_path = 'czech_data/czech_regions.json'
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)

                        # Cache the data
                        setattr(self.app, 'czech_regions', data)

                        # Store in cache
                        with self.cache_lock:
                            self.data_cache[data_type] = data

                        logger.info(f"Refreshed {data_type} data from CUZK API")
                        return data

                elif refresh_type == 'cities':
                    # We need to get cities for each region
                    regions_data = self.app.cuzk_data_api.get_regions()
                    if regions_data:
                        cities_by_region = {}
                        cities_by_region_code = {}

                        for region in regions_data:
                            region_name = region['name_czech']
                            region_code = region['code']

                            cities_data = self.app.cuzk_data_api.get_cities_in_region(region_name)
                            if cities_data:
                                cities_by_region[region_name] = [city['name'] for city in cities_data]
                                cities_by_region_code[region_code] = [city['name'] for city in cities_data]

                        data = {
                            "cities_by_region": cities_by_region,
                            "cities_by_region_code": cities_by_region_code,
                            "city_name_mapping": {}
                        }

                        # Save to file
                        file_path = 'czech_data/czech_cities.json'
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)

                        # Cache the data
                        setattr(self.app, 'czech_cities', data)

                        # Store in cache
                        with self.cache_lock:
                            self.data_cache[data_type] = data

                        logger.info(f"Refreshed {data_type} data from CUZK API")
                        return data

                elif refresh_type == 'property_types':
                    property_types = self.app.cuzk_data_api.get_property_types()
                    if property_types:
                        data = {
                            "property_types": property_types
                        }

                        # Save to file
                        file_path = 'czech_data/property_types.json'
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)

                        # Cache the data
                        setattr(self.app, 'property_types', data)

                        # Store in cache
                        with self.cache_lock:
                            self.data_cache[data_type] = data

                        logger.info(f"Refreshed {data_type} data from CUZK API")
                        return data
            except Exception as e:
                logger.error(f"Error refreshing {data_type} data from CUZK API: {e}")
                # Fall back to refresh service

        # Fall back to refresh service if CUZK API client is not available or fails
        if hasattr(self, 'refresh_service') and self.refresh_service:
            try:
                # Force a refresh with API data
                logger.info(f"Forcing refresh of {refresh_type} data from API")
                success = self.refresh_service.force_refresh(refresh_type, use_api=True)

                if success:
                    # Wait for the refresh to complete (with timeout)
                    max_wait = 30  # seconds
                    for _ in range(max_wait):
                        time.sleep(1)
                        # Check if refresh is still in progress
                        freshness = self.refresh_service.get_data_freshness(refresh_type)
                        if not freshness['refresh_in_progress']:
                            break

                    # Get the file path and attribute name
                    data_map = {
                        'names': {'file': 'czech_data/czech_names.json', 'attr': 'czech_names'},
                        'addresses': {'file': 'czech_data/czech_addresses.json', 'attr': 'czech_addresses'},
                        'regions': {'file': 'czech_data/czech_regions.json', 'attr': 'czech_regions'},
                        'cities': {'file': 'czech_data/czech_cities.json', 'attr': 'czech_cities'},
                        'districts': {'file': 'czech_data/czech_districts.json', 'attr': 'czech_districts'},
                        'cadastral': {'file': 'czech_data/czech_cadastral.json', 'attr': 'czech_cadastral'},
                        'property_types': {'file': 'czech_data/property_types.json', 'attr': 'property_types'}
                    }

                    file_path = data_map[data_type]['file']
                    attribute_name = data_map[data_type]['attr']

                    # Load the freshly refreshed data directly from file
                    if os.path.exists(file_path):
                        try:
                            # Load the data
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)

                            # Cache the data
                            setattr(self.app, attribute_name, data)

                            # Store in cache
                            with self.cache_lock:
                                self.data_cache[data_type] = data

                            logger.info(f"Loaded freshly refreshed {data_type} data from API")
                            return data
                        except Exception as e:
                            logger.error(f"Error loading refreshed {data_type} data from file: {e}")
                            # Continue to error handling

                    error_msg = f"Failed to load refreshed {data_type} data from file"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                else:
                    error_msg = f"Failed to refresh {data_type} data from API"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            except Exception as e:
                error_msg = f"Error refreshing {data_type} data from API: {e}"
                logger.error(error_msg)
                raise ValueError(error_msg)
        else:
            error_msg = f"Data refresh service not available for {data_type}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    def get_data_freshness(self, data_type: str) -> Dict[str, Any]:
        """
        Get the freshness information for a specific data type.

        Args:
            data_type: Type of data to check

        Returns:
            dict: Dictionary with freshness information
        """
        # Map data types to refresh types
        refresh_map = {
            'names': 'names',
            'addresses': 'addresses',
            'regions': 'regions',
            'cities': 'cities',
            'districts': 'districts',
            'cadastral': 'cadastral_areas',
            'property_types': 'property_types'
        }

        # Check if the data type is valid
        if data_type not in refresh_map:
            return {
                "status": "unknown",
                "last_refresh": 0,
                "age_seconds": 0,
                "age_hours": 0,
                "age_days": 0,
                "refresh_in_progress": False
            }

        # Get the refresh type
        refresh_type = refresh_map[data_type]

        # Get freshness from the refresh service
        if hasattr(self, 'refresh_service') and self.refresh_service:
            return self.refresh_service.get_data_freshness(refresh_type)
        else:
            return {
                "status": "unknown",
                "last_refresh": 0,
                "age_seconds": 0,
                "age_hours": 0,
                "age_days": 0,
                "refresh_in_progress": False
            }

    def force_refresh(self, data_type: str, use_api: bool = True) -> bool:
        """
        Force a refresh of a specific data type.

        Args:
            data_type: Type of data to refresh
            use_api: Whether to force using API data (default: True)

        Returns:
            bool: True if refresh was started, False otherwise
        """
        # Map data types to refresh types
        refresh_map = {
            'names': 'names',
            'addresses': 'addresses',
            'regions': 'regions',
            'cities': 'cities',
            'districts': 'districts',
            'cadastral': 'cadastral_areas',
            'property_types': 'property_types'
        }

        # Check if the data type is valid
        if data_type not in refresh_map:
            logger.error(f"Invalid data type for refresh: {data_type}")
            return False

        # Get the refresh type
        refresh_type = refresh_map[data_type]

        # Force refresh using the refresh service
        if hasattr(self, 'refresh_service') and self.refresh_service:
            logger.info(f"Forcing refresh of {data_type} data (use_api={use_api})")
            return self.refresh_service.force_refresh(refresh_type, use_api=use_api)
        else:
            logger.error("Data refresh service not available")
            return False

    def _show_cache_notification(self, data_type: str, source: str = "memory") -> None:
        """
        Show a notification that cached data is being used.

        Args:
            data_type: Type of data being loaded from cache
            source: Source of the cached data (memory, file)
        """
        # Only show notification if we have a notification banner
        if hasattr(self.app, 'notification_banner'):
            # Get a user-friendly name for the data type
            data_type_names = {
                'regions': 'regions',
                'cities': 'cities',
                'districts': 'districts',
                'cadastral': 'cadastral areas',
                'property_types': 'property types',
                'streets': 'streets',
                'names': 'Czech names',
                'addresses': 'Czech addresses'
            }

            friendly_name = data_type_names.get(data_type, data_type)

            # Create the notification message
            if source == "memory":
                message = f"Using cached {friendly_name} data from memory. Real API data is preferred but not available."
            else:
                message = f"Using cached {friendly_name} data from file. Real API data is preferred but not available."

            # Show the notification
            self.app.notification_banner.show(
                message=message,
                notification_type="fallback",
                auto_hide=True,
                duration=5000,
                key=f"cache_{data_type}"
            )

            # Also update the status bar
            self.app.show_status(f"Using cached {friendly_name} data")

    def set_api_preference(self, prefer_api: bool = True, static_data_types: List[str] = None) -> None:
        """
        Set whether to always prefer API data over cached data.
        For static data types, we can keep the preference to use cache first.

        Args:
            prefer_api: Whether to always prefer API data (default: True)
            static_data_types: List of data types that should always prefer cache first
        """
        previous_value = self.always_prefer_api

        # For static data, we always prefer cache regardless of the setting
        if static_data_types is None:
            static_data_types = []

        # Set the preference for dynamic data
        self.always_prefer_api = prefer_api
        logger.info(f"API preference changed: {previous_value} -> {prefer_api} (except for static data types)")

        # Also update the refresh service if available
        if hasattr(self, 'refresh_service') and self.refresh_service:
            self.refresh_service.set_api_preference(prefer_api)

        # If we're now preferring API, trigger a refresh of only dynamic data types
        if prefer_api and not previous_value:
            logger.info("Now preferring API data, refreshing only dynamic data types")

            # Define which data types are dynamic vs static
            all_data_types = [
                'regions', 'cities', 'districts', 'cadastral', 'property_types', 'streets'
            ]

            # Filter out static data types
            dynamic_data_types = [dt for dt in all_data_types if dt not in static_data_types]

            logger.info(f"Static data types (will not be refreshed): {static_data_types}")
            logger.info(f"Dynamic data types (will be refreshed): {dynamic_data_types}")

            # Only refresh dynamic data types
            for data_type in dynamic_data_types:
                try:
                    self.force_refresh(data_type, use_api=True)
                except Exception as e:
                    logger.error(f"Error refreshing {data_type} data: {e}")

    def cache_data(self, cache_key: str, data: Any) -> bool:
        """
        Cache data in memory.

        Args:
            cache_key: Key to use for caching
            data: Data to cache

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with self.cache_lock:
                self.data_cache[cache_key] = data
            logger.debug(f"Cached data with key: {cache_key}")
            return True
        except Exception as e:
            logger.error(f"Error caching data: {e}")
            return False

    def get_cached_data(self, cache_key: str) -> Any:
        """
        Get data from cache.

        Args:
            cache_key: Key to use for retrieving cached data

        Returns:
            Any: Cached data or None if not found
        """
        try:
            with self.cache_lock:
                if cache_key in self.data_cache:
                    logger.debug(f"Retrieved cached data with key: {cache_key}")
                    return self.data_cache[cache_key]
            return None
        except Exception as e:
            logger.error(f"Error retrieving cached data: {e}")
            return None

    def clear_cache(self, cache_key: str = None) -> bool:
        """
        Clear cache.

        Args:
            cache_key: Key to clear from cache, or None to clear all

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with self.cache_lock:
                if cache_key:
                    if cache_key in self.data_cache:
                        del self.data_cache[cache_key]
                        logger.debug(f"Cleared cached data with key: {cache_key}")
                else:
                    self.data_cache.clear()
                    logger.debug("Cleared all cached data")
            return True
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False
