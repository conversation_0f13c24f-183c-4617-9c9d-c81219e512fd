# Development Guide

This guide provides information for developers who want to contribute to the Czech Property Registry application.

## Development Environment Setup

### Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- Git
- A code editor (VS Code, PyCharm, etc.)

### Setting Up the Development Environment

1. Clone the repository:

```bash
git clone https://github.com/yourusername/czech-property-registry.git
cd czech-property-registry
```

2. Create a virtual environment:

```bash
# On Windows
python -m venv venv
venv\Scripts\activate

# On macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies
```

4. Create a development configuration:

```bash
cp config.ini.sample config.ini
```

Edit the `config.ini` file to add your API keys and set `development_mode = true`.

## Project Structure

The project is organized into several packages:

```
Reality/
├── api/                  # API integrations
│   ├── cuzk/             # CUZK-specific API code
│   ├── google_maps/      # Google Maps API code
│   ├── osm/              # OpenStreetMap API code
│   └── property/         # Property search functionality
├── config/               # Configuration files
├── core/                 # Core application logic
├── data/                 # Data files
│   ├── cache/            # Cache files
│   ├── czech_data/       # Czech-specific data
│   ├── json/             # JSON data files
│   └── templates/        # HTML templates
├── docs/                 # Documentation
├── examples/             # Example data and methods
├── models/               # Data models
├── services/             # Service classes
├── tests/                # Test files
│   ├── integration/      # Integration tests
│   └── unit/             # Unit tests
├── ui/                   # User interface components
│   ├── components/       # Reusable UI components
│   ├── screens/          # Main application screens
│   └── dialogs/          # Dialog windows
└── utils/                # Utility functions and classes
```

## Coding Standards

### Python Style Guide

This project follows the [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide for Python code.

Key points:

- Use 4 spaces for indentation
- Maximum line length of 79 characters
- Use docstrings for all public modules, functions, classes, and methods
- Use snake_case for variable and function names
- Use CamelCase for class names
- Use UPPERCASE for constants

### Documentation

- All modules, classes, and functions should have docstrings
- Use [Google-style docstrings](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings)
- Keep docstrings up-to-date with code changes

### Testing

- Write unit tests for all new functionality
- Ensure all tests pass before submitting a pull request
- Aim for high test coverage

## Development Workflow

### Running the Application in Development Mode

```bash
python main.py
```

With `development_mode = true` in your `config.ini`, the application will:

- Use sample data when real data is not available
- Show more detailed error messages
- Enable additional debugging features

### Running Tests

```bash
# Run all tests
python -m unittest discover

# Run unit tests
python -m unittest discover tests/unit

# Run integration tests
python -m unittest discover tests/integration

# Run a specific test file
python -m unittest tests/unit/test_cuzk_integration.py
```

### Adding New Features

1. Create a new branch for your feature:

```bash
git checkout -b feature/your-feature-name
```

2. Implement your feature, following the coding standards
3. Write tests for your feature
4. Update documentation as needed
5. Submit a pull request

## Debugging

### Logging

The application uses Python's built-in logging module. To enable debug logging, set `log_level = DEBUG` in your `config.ini`.

You can also use the `utils/debug_app.py` module for additional debugging features:

```python
from utils.debug_app import debug_print, debug_trace

debug_print("Debug message")
debug_trace()  # Print a stack trace
```

### Debugging UI Issues

For UI issues, you can use the `debug_run.py` script:

```bash
python debug_run.py
```

This script runs the application with additional UI debugging features enabled.

## Building and Packaging

### Creating a Standalone Executable

```bash
# Install PyInstaller
pip install pyinstaller

# Create a standalone executable
pyinstaller --onefile --windowed main.py
```

The executable will be created in the `dist` directory.

### Creating a Distribution Package

```bash
# Install build tools
pip install build

# Build the package
python -m build
```

This will create a source distribution and a wheel in the `dist` directory.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests to ensure they pass
5. Submit a pull request

Please see [CONTRIBUTING.md](../CONTRIBUTING.md) for more details on the contribution process.
