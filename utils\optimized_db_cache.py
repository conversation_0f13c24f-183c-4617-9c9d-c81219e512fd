"""
Optimized database-backed cache for the Czech Property Registry application.
Provides persistent caching with better performance than file-based caching.
"""

import sqlite3
import pickle
import time
import os
import threading
import logging
import json
import hashlib
import functools
from typing import Dict, Any, Optional, List, Tuple, Set, Union

# Configure logging
logger = logging.getLogger("OptimizedDBCache")


class OptimizedDBCache:
    """
    An optimized database-backed cache using SQLite with connection pooling and prepared statements.
    Provides persistent caching with better performance than the original DBCache.
    """

    # Class-level connection pool
    _connection_pool = {}
    _pool_lock = threading.Lock()
    _max_pool_size = 5

    def __init__(self, db_path: str = "cache.db", auto_vacuum: bool = True,
                 pragma_optimizations: bool = True, memory_cache: bool = True,
                 memory_cache_size: int = 1000):
        """
        Initialize the optimized database cache.

        Args:
            db_path (str): Path to the SQLite database file
            auto_vacuum (bool): Whether to automatically vacuum the database
            pragma_optimizations (bool): Whether to apply SQLite pragma optimizations
            memory_cache (bool): Whether to use an in-memory cache for frequently accessed items
            memory_cache_size (int): Maximum number of items to keep in memory cache
        """
        self.db_path = db_path
        self.auto_vacuum = auto_vacuum
        self.pragma_optimizations = pragma_optimizations
        self.memory_cache = memory_cache
        self.memory_cache_size = memory_cache_size

        # In-memory cache for frequently accessed items
        self._mem_cache = {}
        self._mem_cache_lock = threading.Lock()

        # Prepared statements cache
        self._prepared_statements = {}
        self._statements_lock = threading.Lock()

        # Statistics
        self._stats = {
            'memory_hits': 0,
            'db_hits': 0,
            'misses': 0,
            'writes': 0,
            'deletes': 0
        }
        self._stats_lock = threading.Lock()

        # Initialize the database
        self._init_db()

        logger.info(f"OptimizedDBCache initialized with database at {db_path}")

    def _init_db(self) -> None:
        """Initialize the database schema with optimized indexes."""
        with self._get_connection() as conn:
            cursor = conn.cursor()

            # Apply performance optimizations
            if self.pragma_optimizations:
                cursor.execute("PRAGMA journal_mode = WAL")  # Write-Ahead Logging for better concurrency
                cursor.execute("PRAGMA synchronous = NORMAL")  # Reduce disk I/O
                cursor.execute("PRAGMA cache_size = 10000")  # Increase cache size (in pages)
                cursor.execute("PRAGMA temp_store = MEMORY")  # Store temp tables in memory
                cursor.execute("PRAGMA mmap_size = 30000000")  # Memory-mapped I/O

            # Create the cache table if it doesn't exist
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS cache (
                cache_name TEXT NOT NULL,
                key TEXT NOT NULL,
                value BLOB NOT NULL,
                metadata TEXT,
                created REAL NOT NULL,
                expires REAL,
                access_count INTEGER DEFAULT 0,
                last_access REAL,
                PRIMARY KEY (cache_name, key)
            )
            """)

            # Check if access_count column exists
            cursor.execute("PRAGMA table_info(cache)")
            columns = [column[1] for column in cursor.fetchall()]

            # Add access_count column if it doesn't exist
            if 'access_count' not in columns:
                cursor.execute("ALTER TABLE cache ADD COLUMN access_count INTEGER DEFAULT 0")
                logger.info("Added access_count column to cache table")

            # Add last_access column if it doesn't exist
            if 'last_access' not in columns:
                cursor.execute("ALTER TABLE cache ADD COLUMN last_access REAL")
                logger.info("Added last_access column to cache table")

            # Create optimized indexes
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_cache_expires ON cache (expires) WHERE expires IS NOT NULL")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_cache_access ON cache (access_count DESC)")

            # Create a table for cache metadata
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS cache_metadata (
                cache_name TEXT PRIMARY KEY,
                settings TEXT NOT NULL
            )
            """)

            conn.commit()

    def _get_connection(self) -> sqlite3.Connection:
        """
        Get a database connection from the pool or create a new one.

        Returns:
            sqlite3.Connection: Database connection
        """
        thread_id = threading.get_ident()

        with OptimizedDBCache._pool_lock:
            # Check if we already have a connection for this thread
            if thread_id in OptimizedDBCache._connection_pool:
                return OptimizedDBCache._connection_pool[thread_id]

            # Create a new connection if the pool isn't full
            if len(OptimizedDBCache._connection_pool) < OptimizedDBCache._max_pool_size:
                conn = sqlite3.connect(self.db_path, check_same_thread=False)
                conn.row_factory = sqlite3.Row
                OptimizedDBCache._connection_pool[thread_id] = conn
                return conn

            # If the pool is full, reuse the least recently used connection
            # This is a simplification - in a real implementation, you'd want to
            # track connection usage and potentially close idle connections
            conn = next(iter(OptimizedDBCache._connection_pool.values()))
            return conn

    @functools.lru_cache(maxsize=128)
    def _get_prepared_statement(self, conn: sqlite3.Connection, sql: str) -> sqlite3.Cursor:
        """
        Get a prepared statement for the given SQL.

        Args:
            conn: Database connection
            sql: SQL statement

        Returns:
            sqlite3.Cursor: Prepared statement
        """
        return conn.cursor()

    def create_cache(self, cache_name: str, expiry: Optional[int] = None, settings: Dict[str, Any] = None) -> None:
        """
        Create a new cache with the given name and settings.

        Args:
            cache_name (str): Name of the cache to create
            expiry (int, optional): Default expiry time in seconds
            settings (dict, optional): Additional settings for the cache
        """
        settings = settings or {}
        if expiry is not None:
            settings['default_expiry'] = expiry

        with self._get_connection() as conn:
            cursor = self._get_prepared_statement(conn, "INSERT OR REPLACE INTO cache_metadata (cache_name, settings) VALUES (?, ?)")
            cursor.execute("INSERT OR REPLACE INTO cache_metadata (cache_name, settings) VALUES (?, ?)",
                          (cache_name, json.dumps(settings)))
            conn.commit()

        logger.info(f"Created cache '{cache_name}' with settings {settings}")

    def get(self, cache_name: str, key: str) -> Optional[Any]:
        """
        Get a value from the cache with memory cache optimization.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key

        Returns:
            Any: The cached value or None if not found or expired
        """
        # Hash the key if it's too long
        if len(key) > 100:
            key = hashlib.md5(key.encode()).hexdigest()

        # Check memory cache first
        if self.memory_cache:
            mem_key = f"{cache_name}:{key}"
            with self._mem_cache_lock:
                if mem_key in self._mem_cache:
                    entry = self._mem_cache[mem_key]
                    # Check if expired
                    if entry['expires'] is None or time.time() < entry['expires']:
                        with self._stats_lock:
                            self._stats['memory_hits'] += 1
                        return entry['value']
                    else:
                        # Remove expired entry from memory cache
                        del self._mem_cache[mem_key]

        # Not in memory cache, check database
        with self._get_connection() as conn:
            cursor = self._get_prepared_statement(conn, "SELECT value, expires, access_count FROM cache WHERE cache_name = ? AND key = ?")
            cursor.execute("SELECT value, expires, access_count FROM cache WHERE cache_name = ? AND key = ?",
                          (cache_name, key))

            row = cursor.fetchone()

            if row is None:
                with self._stats_lock:
                    self._stats['misses'] += 1
                return None

            # Check if expired
            if row['expires'] is not None and time.time() >= row['expires']:
                # Delete expired entry
                delete_cursor = self._get_prepared_statement(conn, "DELETE FROM cache WHERE cache_name = ? AND key = ?")
                delete_cursor.execute("DELETE FROM cache WHERE cache_name = ? AND key = ?",
                                     (cache_name, key))
                conn.commit()

                with self._stats_lock:
                    self._stats['misses'] += 1
                    self._stats['deletes'] += 1

                return None

            # Update access count and last access time
            update_cursor = self._get_prepared_statement(conn,
                "UPDATE cache SET access_count = access_count + 1, last_access = ? WHERE cache_name = ? AND key = ?")
            update_cursor.execute(
                "UPDATE cache SET access_count = access_count + 1, last_access = ? WHERE cache_name = ? AND key = ?",
                (time.time(), cache_name, key)
            )
            conn.commit()

            # Deserialize the value
            try:
                value = pickle.loads(row['value'])

                # Add to memory cache if enabled
                if self.memory_cache:
                    with self._mem_cache_lock:
                        # Limit memory cache size
                        if len(self._mem_cache) >= self.memory_cache_size:
                            # Remove least recently used item
                            oldest_key = min(self._mem_cache.items(), key=lambda x: x[1].get('last_access', 0))[0]
                            del self._mem_cache[oldest_key]

                        # Add to memory cache
                        self._mem_cache[f"{cache_name}:{key}"] = {
                            'value': value,
                            'expires': row['expires'],
                            'last_access': time.time()
                        }

                with self._stats_lock:
                    self._stats['db_hits'] += 1

                return value
            except (pickle.PickleError, EOFError) as e:
                logger.warning(f"Error deserializing cache value for {cache_name}/{key}: {e}")

                # Delete corrupted entry
                delete_cursor = self._get_prepared_statement(conn, "DELETE FROM cache WHERE cache_name = ? AND key = ?")
                delete_cursor.execute("DELETE FROM cache WHERE cache_name = ? AND key = ?",
                                     (cache_name, key))
                conn.commit()

                with self._stats_lock:
                    self._stats['misses'] += 1
                    self._stats['deletes'] += 1

                return None

    def set(self, cache_name: str, key: str, value: Any, expiry: Optional[int] = None, metadata: Dict[str, Any] = None) -> None:
        """
        Set a value in the cache with memory cache optimization.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            value (Any): Value to cache
            expiry (int, optional): Expiry time in seconds. If None, uses cache-specific default.
            metadata (dict, optional): Additional metadata to store with the value
        """
        # Hash the key if it's too long
        if len(key) > 100:
            key = hashlib.md5(key.encode()).hexdigest()

        # Get cache settings
        settings = self._get_cache_settings(cache_name)

        # Calculate expiry timestamp
        expires = None
        if expiry is not None:
            expires = time.time() + expiry
        elif 'default_expiry' in settings:
            expires = time.time() + settings['default_expiry']

        # Serialize the value
        try:
            serialized_value = pickle.dumps(value, protocol=pickle.HIGHEST_PROTOCOL)
        except (pickle.PickleError, TypeError) as e:
            logger.warning(f"Error serializing cache value for {cache_name}/{key}: {e}")
            return

        # Serialize metadata
        serialized_metadata = None
        if metadata:
            serialized_metadata = json.dumps(metadata)

        # Update database
        with self._get_connection() as conn:
            cursor = self._get_prepared_statement(conn, """
                INSERT OR REPLACE INTO cache (cache_name, key, value, metadata, created, expires, access_count, last_access)
                VALUES (?, ?, ?, ?, ?, ?, 0, ?)
            """)

            current_time = time.time()
            cursor.execute(
                """
                INSERT OR REPLACE INTO cache (cache_name, key, value, metadata, created, expires, access_count, last_access)
                VALUES (?, ?, ?, ?, ?, ?, 0, ?)
                """,
                (cache_name, key, serialized_value, serialized_metadata, current_time, expires, current_time)
            )

            conn.commit()

        # Update memory cache if enabled
        if self.memory_cache:
            with self._mem_cache_lock:
                # Limit memory cache size
                if len(self._mem_cache) >= self.memory_cache_size:
                    # Remove least recently used item
                    oldest_key = min(self._mem_cache.items(), key=lambda x: x[1].get('last_access', 0))[0]
                    del self._mem_cache[oldest_key]

                # Add to memory cache
                self._mem_cache[f"{cache_name}:{key}"] = {
                    'value': value,
                    'expires': expires,
                    'last_access': current_time
                }

        with self._stats_lock:
            self._stats['writes'] += 1

    def delete(self, cache_name: str, key: str) -> bool:
        """
        Delete a value from the cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key

        Returns:
            bool: True if the value was deleted, False otherwise
        """
        # Hash the key if it's too long
        if len(key) > 100:
            key = hashlib.md5(key.encode()).hexdigest()

        # Remove from memory cache if present
        if self.memory_cache:
            mem_key = f"{cache_name}:{key}"
            with self._mem_cache_lock:
                if mem_key in self._mem_cache:
                    del self._mem_cache[mem_key]

        # Remove from database
        with self._get_connection() as conn:
            cursor = self._get_prepared_statement(conn, "DELETE FROM cache WHERE cache_name = ? AND key = ?")
            cursor.execute("DELETE FROM cache WHERE cache_name = ? AND key = ?", (cache_name, key))

            deleted = cursor.rowcount > 0
            conn.commit()

            if deleted:
                with self._stats_lock:
                    self._stats['deletes'] += 1

            return deleted

    def clear(self, cache_name: str) -> int:
        """
        Clear a specific cache.

        Args:
            cache_name (str): Name of the cache to clear

        Returns:
            int: Number of items cleared
        """
        # Clear memory cache entries for this cache
        if self.memory_cache:
            with self._mem_cache_lock:
                # Find keys to delete
                keys_to_delete = [k for k in self._mem_cache if k.startswith(f"{cache_name}:")]
                # Delete keys
                for k in keys_to_delete:
                    del self._mem_cache[k]

        # Clear database entries
        with self._get_connection() as conn:
            cursor = self._get_prepared_statement(conn, "DELETE FROM cache WHERE cache_name = ?")
            cursor.execute("DELETE FROM cache WHERE cache_name = ?", (cache_name,))

            deleted = cursor.rowcount
            conn.commit()

            with self._stats_lock:
                self._stats['deletes'] += deleted

            logger.info(f"Cleared {deleted} items from cache '{cache_name}'")

            return deleted

    def cleanup(self) -> Dict[str, int]:
        """
        Remove expired entries from all caches.

        Returns:
            Dict[str, int]: Dictionary mapping cache names to number of items removed
        """
        result = {}
        current_time = time.time()

        # Clear expired entries from memory cache
        if self.memory_cache:
            with self._mem_cache_lock:
                # Find expired keys
                expired_keys = [k for k, v in self._mem_cache.items()
                               if v['expires'] is not None and v['expires'] < current_time]
                # Delete expired keys
                for k in expired_keys:
                    del self._mem_cache[k]

        # Clear expired entries from database
        with self._get_connection() as conn:
            # Get all cache names with expired entries
            cursor = self._get_prepared_statement(conn,
                "SELECT DISTINCT cache_name FROM cache WHERE expires IS NOT NULL AND expires < ?")
            cursor.execute(
                "SELECT DISTINCT cache_name FROM cache WHERE expires IS NOT NULL AND expires < ?",
                (current_time,)
            )

            cache_names = [row['cache_name'] for row in cursor.fetchall()]

            # Delete expired entries for each cache
            for cache_name in cache_names:
                delete_cursor = self._get_prepared_statement(conn,
                    "DELETE FROM cache WHERE cache_name = ? AND expires IS NOT NULL AND expires < ?")
                delete_cursor.execute(
                    "DELETE FROM cache WHERE cache_name = ? AND expires IS NOT NULL AND expires < ?",
                    (cache_name, current_time)
                )

                deleted = delete_cursor.rowcount
                result[cache_name] = deleted

                with self._stats_lock:
                    self._stats['deletes'] += deleted

            conn.commit()

            # Vacuum the database if auto_vacuum is enabled and items were deleted
            if self.auto_vacuum and sum(result.values()) > 0:
                cursor.execute("VACUUM")
                conn.commit()

        return result

    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the cache.

        Returns:
            Dict[str, Any]: Dictionary with cache statistics
        """
        stats = {}

        with self._stats_lock:
            stats.update(self._stats)

        # Add memory cache stats
        if self.memory_cache:
            with self._mem_cache_lock:
                stats['memory_cache_size'] = len(self._mem_cache)
                stats['memory_cache_limit'] = self.memory_cache_size

        with self._get_connection() as conn:
            cursor = conn.cursor()

            # Get total number of items
            cursor.execute("SELECT COUNT(*) as count FROM cache")
            stats['total_items'] = cursor.fetchone()['count']

            # Get number of items per cache
            cursor.execute(
                """
                SELECT cache_name, COUNT(*) as count FROM cache
                GROUP BY cache_name
                """
            )

            stats['items_per_cache'] = {row['cache_name']: row['count'] for row in cursor.fetchall()}

            # Get number of expired items
            current_time = time.time()
            cursor.execute(
                """
                SELECT COUNT(*) as count FROM cache
                WHERE expires IS NOT NULL AND expires < ?
                """,
                (current_time,)
            )

            stats['expired_items'] = cursor.fetchone()['count']

            # Get database size
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()['page_count']

            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()['page_size']

            stats['db_size'] = page_count * page_size
            stats['db_size_mb'] = stats['db_size'] / (1024 * 1024)

            # Get frequently accessed items
            cursor.execute(
                """
                SELECT cache_name, key, access_count
                FROM cache
                ORDER BY access_count DESC
                LIMIT 10
                """
            )

            stats['top_accessed_items'] = [
                {'cache_name': row['cache_name'], 'key': row['key'], 'access_count': row['access_count']}
                for row in cursor.fetchall()
            ]

        return stats

    def _get_cache_settings(self, cache_name: str) -> Dict[str, Any]:
        """
        Get settings for a specific cache.

        Args:
            cache_name (str): Name of the cache

        Returns:
            Dict[str, Any]: Cache settings
        """
        with self._get_connection() as conn:
            cursor = self._get_prepared_statement(conn, "SELECT settings FROM cache_metadata WHERE cache_name = ?")
            cursor.execute("SELECT settings FROM cache_metadata WHERE cache_name = ?", (cache_name,))

            row = cursor.fetchone()

            if row is None:
                return {}

            return json.loads(row['settings'])

    def close(self) -> None:
        """Close all database connections in the pool."""
        with OptimizedDBCache._pool_lock:
            for conn in OptimizedDBCache._connection_pool.values():
                conn.close()
            OptimizedDBCache._connection_pool.clear()

        logger.info("OptimizedDBCache closed")

    def __del__(self) -> None:
        """Close the database connections when the object is deleted."""
        self.close()


# Create a global instance for convenience
optimized_db_cache = OptimizedDBCache()
