"""
Geocoding Cache Module

This module provides a specialized caching system for geocoding results
to ensure that only appropriate cache data is used for each address.
"""

import os
import json
import time
import hashlib
import logging
import unicodedata
import threading
from typing import Dict, Any, Optional, Tuple

# Configure logging
logger = logging.getLogger(__name__)

class GeocodingCache:
    """
    A specialized cache for geocoding results.
    
    This cache ensures that only appropriate cache data is used for each address
    by including location verification in the cache key and validating cached results.
    """
    
    def __init__(self, cache_dir="geocoding_cache", expiry_seconds=3600):
        """
        Initialize the geocoding cache.
        
        Args:
            cache_dir (str): Directory to store cache files
            expiry_seconds (int): Cache expiry time in seconds
        """
        self.cache_dir = cache_dir
        self.expiry_seconds = expiry_seconds
        self.memory_cache = {}
        self.cache_lock = threading.Lock()
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
    
    def normalize_address(self, address: str) -> str:
        """
        Normalize an address string for consistent cache keys.
        
        Args:
            address (str): The address to normalize
            
        Returns:
            str: The normalized address
        """
        # Convert to lowercase
        normalized = address.lower()
        
        # Remove diacritics
        normalized = unicodedata.normalize('NFKD', normalized).encode('ASCII', 'ignore').decode('ASCII')
        
        # Remove extra whitespace
        normalized = ' '.join(normalized.split())
        
        return normalized
    
    def generate_cache_key(self, address: str) -> str:
        """
        Generate a unique cache key for an address.
        
        Args:
            address (str): The address to generate a key for
            
        Returns:
            str: The cache key
        """
        # Normalize the address
        normalized_address = self.normalize_address(address)
        
        # Create a hash of the normalized address
        return hashlib.md5(normalized_address.encode('utf-8')).hexdigest()
    
    def get(self, address: str) -> Optional[Dict[str, Any]]:
        """
        Get geocoding results for an address from the cache.
        
        Args:
            address (str): The address to get results for
            
        Returns:
            dict or None: The cached geocoding results or None if not found or invalid
        """
        # Generate the cache key
        cache_key = self.generate_cache_key(address)
        
        # Check memory cache first
        with self.cache_lock:
            if cache_key in self.memory_cache:
                result, timestamp, cached_address = self.memory_cache[cache_key]
                
                # Check if the cache entry has expired
                if time.time() - timestamp < self.expiry_seconds:
                    # Validate that the cached address matches the requested address
                    if self._validate_address_match(address, cached_address):
                        logger.debug(f"Using memory cache for address: {address}")
                        return result
                else:
                    # Remove expired entry
                    del self.memory_cache[cache_key]
        
        # Check disk cache
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                result = cache_data.get('result')
                timestamp = cache_data.get('timestamp', 0)
                cached_address = cache_data.get('address', '')
                
                # Check if the cache entry has expired
                if time.time() - timestamp < self.expiry_seconds:
                    # Validate that the cached address matches the requested address
                    if self._validate_address_match(address, cached_address):
                        # Store in memory cache for faster access next time
                        with self.cache_lock:
                            self.memory_cache[cache_key] = (result, timestamp, cached_address)
                        
                        logger.debug(f"Using disk cache for address: {address}")
                        return result
            except Exception as e:
                logger.error(f"Error reading cache file {cache_file}: {e}")
        
        return None
    
    def set(self, address: str, result: Dict[str, Any]) -> None:
        """
        Store geocoding results for an address in the cache.
        
        Args:
            address (str): The address the results are for
            result (dict): The geocoding results to cache
        """
        # Generate the cache key
        cache_key = self.generate_cache_key(address)
        
        # Get current timestamp
        timestamp = time.time()
        
        # Store in memory cache
        with self.cache_lock:
            self.memory_cache[cache_key] = (result, timestamp, address)
        
        # Store in disk cache
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        try:
            cache_data = {
                'result': result,
                'timestamp': timestamp,
                'address': address,
                'normalized_address': self.normalize_address(address)
            }
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"Cached geocoding result for address: {address}")
        except Exception as e:
            logger.error(f"Error writing cache file {cache_file}: {e}")
    
    def clear(self) -> int:
        """
        Clear all cached geocoding results.
        
        Returns:
            int: Number of cache entries cleared
        """
        count = 0
        
        # Clear memory cache
        with self.cache_lock:
            count += len(self.memory_cache)
            self.memory_cache.clear()
        
        # Clear disk cache
        for filename in os.listdir(self.cache_dir):
            if filename.endswith('.json'):
                try:
                    os.remove(os.path.join(self.cache_dir, filename))
                    count += 1
                except Exception as e:
                    logger.error(f"Error removing cache file {filename}: {e}")
        
        logger.info(f"Cleared {count} geocoding cache entries")
        return count
    
    def _validate_address_match(self, request_address: str, cached_address: str) -> bool:
        """
        Validate that a cached address matches the requested address.
        
        Args:
            request_address (str): The address being requested
            cached_address (str): The address from the cache
            
        Returns:
            bool: True if the addresses match, False otherwise
        """
        # Normalize both addresses
        norm_request = self.normalize_address(request_address)
        norm_cached = self.normalize_address(cached_address)
        
        # Check for exact match
        if norm_request == norm_cached:
            return True
        
        # Check if one is a substring of the other (for partial matches)
        if norm_request in norm_cached or norm_cached in norm_request:
            # Additional validation could be added here
            # For example, checking that city names match
            return True
        
        return False
