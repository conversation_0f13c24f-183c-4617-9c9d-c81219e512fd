"""
Test script for the notification banner component.
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Add the parent directory to the path so we can import from the ui package
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.notification_banner import NotificationBanner

class NotificationBannerTest:
    """Test application for the notification banner"""
    
    def __init__(self, root):
        """Initialize the test application"""
        self.root = root
        self.root.title("Notification Banner Test")
        self.root.geometry("800x600")
        
        # Create the main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Initialize the notification banner
        self.notification_banner = NotificationBanner(self.main_frame)
        
        # Create a frame for the controls
        self.controls_frame = ttk.Frame(self.main_frame)
        self.controls_frame.pack(fill="x", padx=5, pady=5)
        
        # Create buttons for showing different types of notifications
        self.info_btn = ttk.Button(self.controls_frame, text="Show Info", 
                                  command=lambda: self.show_notification("info"))
        self.info_btn.grid(row=0, column=0, padx=5, pady=5)
        
        self.warning_btn = ttk.Button(self.controls_frame, text="Show Warning", 
                                     command=lambda: self.show_notification("warning"))
        self.warning_btn.grid(row=0, column=1, padx=5, pady=5)
        
        self.error_btn = ttk.Button(self.controls_frame, text="Show Error", 
                                   command=lambda: self.show_notification("error"))
        self.error_btn.grid(row=0, column=2, padx=5, pady=5)
        
        self.success_btn = ttk.Button(self.controls_frame, text="Show Success", 
                                     command=lambda: self.show_notification("success"))
        self.success_btn.grid(row=0, column=3, padx=5, pady=5)
        
        self.fallback_btn = ttk.Button(self.controls_frame, text="Show Fallback", 
                                      command=lambda: self.show_notification("fallback"))
        self.fallback_btn.grid(row=0, column=4, padx=5, pady=5)
        
        # Create a button to hide the notification
        self.hide_btn = ttk.Button(self.controls_frame, text="Hide Notification", 
                                  command=self.hide_notification)
        self.hide_btn.grid(row=1, column=0, columnspan=5, padx=5, pady=5)
        
        # Create a content frame to simulate the application content
        self.content_frame = ttk.Frame(self.main_frame, borderwidth=1, relief="solid")
        self.content_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Add some content to the content frame
        ttk.Label(self.content_frame, text="Application Content").pack(pady=50)
        
    def show_notification(self, notification_type):
        """Show a notification of the specified type"""
        messages = {
            "info": "This is an information message.",
            "warning": "This is a warning message.",
            "error": "This is an error message.",
            "success": "This is a success message.",
            "fallback": "Using fallback data for regions. This may not be up-to-date."
        }
        
        self.notification_banner.show(
            message=messages[notification_type],
            notification_type=notification_type,
            auto_hide=True,
            duration=5000,
            key=f"test_{notification_type}"
        )
        
    def hide_notification(self):
        """Hide the notification banner"""
        self.notification_banner.hide()

def main():
    """Main function to run the test"""
    root = tk.Tk()
    app = NotificationBannerTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
