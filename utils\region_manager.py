"""
Region Manager module for handling Czech regions data and UI interactions.
This module centralizes all region-related functionality for better organization.
"""

import os
import json
import threading
import time
import tkinter as tk
from ui.message_boxes import MessageBoxes
# No fallback data is used - removed DemoData import


class RegionManager:
    """
    Manages region data and UI interactions for the Czech Property Scraper application.
    Handles loading, caching, and updating region data in the UI.
    """

    def __init__(self, app):
        """
        Initialize the RegionManager with a reference to the main application.

        Args:
            app: The main PropertyScraperApp instance
        """
        self.app = app
        self.regions_data = {}
        self.districts_data = {}
        self.cities_data = {}
        self.cadastral_data = {}

        # Initialize empty regions data structure
        self.czech_regions = {}
        # Don't load regions data at initialization
        # self._load_czech_regions_data()

    def _load_czech_regions_data(self, data_type=None):
        """
        Load Czech regions data from API or JSON files

        Args:
            data_type (str, optional): Specific data type to load ('regions', 'cities', 'districts', 'cadastral')
                                      If None, loads all data types.
        """
        # Create directory if it doesn't exist
        if not os.path.exists('czech_data'):
            os.makedirs('czech_data')

        try:
            # Use the CUZK Data API client if available
            if hasattr(self.app, 'cuzk_data_api'):
                # Load regions data if requested or if loading all
                if data_type in (None, 'regions') and not self.czech_regions:
                    regions_data = self.app.cuzk_data_api.get_regions()
                    if regions_data:
                        # Transform to the expected format
                        self.czech_regions = {
                            "regions_czech": [region['name_czech'] for region in regions_data],
                            "regions_english": [region['name_english'] for region in regions_data],
                            "region_codes": [region['code'] for region in regions_data],
                            "region_mapping": {},
                            "region_coordinates": {
                                "Prague": {"lat": 50.0755, "lng": 14.4378},
                                "Czech Republic": {"lat": 49.8175, "lng": 15.4730}
                            },
                            "region_name_mapping": {}
                        }
                        print("Loaded Czech regions data from API")
                    else:
                        print("Failed to load regions data from API, falling back to file")
                        self._load_regions_from_file()

                # Load cities data if requested or if loading all
                if data_type in (None, 'cities') and not hasattr(self, 'czech_cities'):
                    self.czech_cities = {
                        "cities_by_region": {},
                        "cities_by_region_code": {},
                        "city_name_mapping": {}
                    }

                    # Load cities for each region
                    for i, region in enumerate(self.czech_regions.get("regions_czech", [])):
                        region_code = self.czech_regions.get("region_codes", [])[i] if i < len(self.czech_regions.get("region_codes", [])) else ""

                        cities_data = self.app.cuzk_data_api.get_cities_in_region(region)
                        if cities_data:
                            self.czech_cities["cities_by_region"][region] = [city['name'] for city in cities_data]
                            if region_code:
                                self.czech_cities["cities_by_region_code"][region_code] = [city['name'] for city in cities_data]

                    print("Loaded Czech cities data from API")

                # Load districts data if requested or if loading all
                if data_type in (None, 'districts') and not hasattr(self, 'czech_districts'):
                    self.czech_districts = {
                        "districts_by_city": {},
                        "districts_by_region_code": {}
                    }

                    # We'll load districts on demand when a city is selected
                    # This avoids making too many API calls at startup
                    print("Districts will be loaded from API on demand")

                # Load cadastral areas data if requested or if loading all
                if data_type in (None, 'cadastral') and not hasattr(self, 'czech_cadastral'):
                    self.czech_cadastral = {
                        "cadastral_areas_by_city": {},
                        "cadastral_areas_by_district": {},
                        "cadastral_template": [
                            "{name}",
                            "{name}-město",
                            "{name}-okolí",
                            "Katastr {name}"
                        ]
                    }

                    # We'll load cadastral areas on demand when a city is selected
                    # This avoids making too many API calls at startup
                    print("Cadastral areas will be loaded from API on demand")

                return

            # Fall back to loading from files if API client is not available
            self._load_data_from_files(data_type)

        except Exception as e:
            print(f"Error loading Czech data from API: {e}")
            # Fall back to loading from files
            self._load_data_from_files(data_type)

    def _load_data_from_files(self, data_type=None):
        """
        Load Czech data from files.

        Args:
            data_type (str, optional): Type of data to load ('regions', 'cities', 'districts', 'cadastral')
        """
        # Load regions data if requested or if loading all
        if data_type in (None, 'regions') and not self.czech_regions:
            self._load_regions_from_file()

        # Load cities data if requested or if loading all
        if data_type in (None, 'cities') and not hasattr(self, 'czech_cities'):
            self.czech_cities = {}
            if os.path.exists('czech_data/cities.json'):
                try:
                    with open('czech_data/cities.json', 'r', encoding='utf-8') as f:
                        self.czech_cities = json.load(f)
                    print("Loaded Czech cities data from file")
                except Exception as e:
                    print(f"Error loading Czech cities data: {e}")
                    # Use empty data structure without fallback
                    self.czech_cities = {
                        "cities_by_region": {},
                        "cities_by_region_code": {},
                        "city_name_mapping": {}
                    }
                    # Show error message
                    MessageBoxes.show_error("Error", "Failed to load cities data. Please check your internet connection and try again.")
            else:
                print("Czech cities file not found. Using default values.")
                # Use empty data structure without fallback
                self.czech_cities = {
                    "cities_by_region": {},
                    "cities_by_region_code": {},
                    "city_name_mapping": {}
                }
                # Show error message
                MessageBoxes.show_error("Error", "Cities data file not found. Please check your internet connection and try again.")

        # Load districts data if requested or if loading all
        if data_type in (None, 'districts') and not hasattr(self, 'czech_districts'):
            self.czech_districts = {}
            if os.path.exists('czech_data/districts.json'):
                try:
                    with open('czech_data/districts.json', 'r', encoding='utf-8') as f:
                        self.czech_districts = json.load(f)
                    print("Loaded Czech districts data from file")
                except Exception as e:
                    print(f"Error loading Czech districts data: {e}")
                    # Use empty data structure without fallback
                    self.czech_districts = {
                        "districts_by_city": {},
                        "districts_by_region_code": {}
                    }
                    # Show error message
                    MessageBoxes.show_error("Error", "Failed to load districts data. Please check your internet connection and try again.")
            else:
                print("Czech districts file not found. Using default values.")
                # Use empty data structure without fallback
                self.czech_districts = {
                    "districts_by_city": {},
                    "districts_by_region_code": {}
                }
                # Show error message
                MessageBoxes.show_error("Error", "Districts data file not found. Please check your internet connection and try again.")

        # Load cadastral areas data if requested or if loading all
        if data_type in (None, 'cadastral') and not hasattr(self, 'czech_cadastral'):
            self.czech_cadastral = {}
            if os.path.exists('czech_data/cadastral_areas.json'):
                try:
                    with open('czech_data/cadastral_areas.json', 'r', encoding='utf-8') as f:
                        self.czech_cadastral = json.load(f)
                    print("Loaded Czech cadastral areas data from file")
                except Exception as e:
                    print(f"Error loading Czech cadastral areas data: {e}")
                    # Use empty data structure without fallback
                    self.czech_cadastral = {
                        "cadastral_areas_by_city": {},
                        "cadastral_areas_by_district": {},
                        "cadastral_template": {}
                    }
                    # Show error message
                    MessageBoxes.show_error("Error", "Failed to load cadastral areas data. Please check your internet connection and try again.")
            else:
                print("Czech cadastral areas file not found. Using default values.")
                # Use empty data structure without fallback
                self.czech_cadastral = {
                    "cadastral_areas_by_city": {},
                    "cadastral_areas_by_district": {},
                    "cadastral_template": {}
                }
                # Show error message
                MessageBoxes.show_error("Error", "Cadastral areas data file not found. Please check your internet connection and try again.")

    def _load_regions_from_file(self):
        """Load regions data from file"""
        if os.path.exists('czech_data/regions.json'):
            try:
                with open('czech_data/regions.json', 'r', encoding='utf-8') as f:
                    self.czech_regions = json.load(f)
                print("Loaded Czech regions data from file")
            except Exception as e:
                print(f"Error loading Czech regions data: {e}")
                self._initialize_empty_regions_data()
        else:
            print("Czech regions file not found. Using default values.")
            self._initialize_empty_regions_data()

    def _initialize_empty_regions_data(self):
        """Initialize empty regions data structures without using fallback data"""
        # Use basic Czech regions data without fallback
        self.czech_regions = {
            "regions_czech": ["Praha", "Středočeský kraj", "Jihočeský kraj", "Plzeňský kraj",
                             "Karlovarský kraj", "Ústecký kraj", "Liberecký kraj",
                             "Královéhradecký kraj", "Pardubický kraj", "Kraj Vysočina",
                             "Jihomoravský kraj", "Olomoucký kraj", "Zlínský kraj",
                             "Moravskoslezský kraj"],
            "regions_english": ["Prague", "Central Bohemian Region", "South Bohemian Region",
                               "Plzeň Region", "Karlovy Vary Region", "Ústí nad Labem Region",
                               "Liberec Region", "Hradec Králové Region", "Pardubice Region",
                               "Vysočina Region", "South Moravian Region", "Olomouc Region",
                               "Zlín Region", "Moravian-Silesian Region"],
            "region_mapping": {},  # Will be populated as needed
            "region_codes": {},    # Will be populated as needed
            "region_coordinates": {
                "Prague": {"lat": 50.0755, "lng": 14.4378},
                "Czech Republic": {"lat": 49.8175, "lng": 15.4730}
            },
            "region_name_mapping": {}  # Will be populated as needed
        }

    def load_regions(self):
        """
        Load the regions into the combobox using lazy loading for better performance.
        Uses threading to avoid freezing the UI.
        """
        # Show a loading indicator
        self.app.region_combo.set("Loading...")
        self.app.region_combo['values'] = ["Loading..."]
        self.app.root.update_idletasks()

        # Use threading to avoid freezing the UI
        def load_regions_thread():
            try:
                # Check if we have cached regions
                if hasattr(self.app, 'cache_manager'):
                    cached_regions = self.app.cache_manager.get('city_suggestions', 'regions_english')
                    if cached_regions:
                        print("Using cached regions")
                        self.app.root.after(0, lambda: self._update_regions_ui(cached_regions, "cache"))
                        return

                # Get regions from the data source manager
                regions, source = self.app.data_source_manager.get_regions(language="english")

                # Update the UI in the main thread
                self.app.root.after(0, lambda: self._update_regions_ui(regions, source))

                # Cache the regions if we have a cache manager
                if hasattr(self.app, 'cache_manager') and regions:
                    self.app.cache_manager.set('city_suggestions', 'regions_english', regions)
            except Exception as e:
                print(f"Error loading regions: {e}")
                # Update the UI in the main thread
                self.app.root.after(0, lambda: self._update_regions_ui(None, None, str(e)))

        # Start the thread
        if hasattr(self.app, 'thread_pool'):
            self.app.thread_pool.submit(load_regions_thread)
        else:
            threading.Thread(target=load_regions_thread, daemon=True).start()

    def load_regions_simple(self):
        """
        Load the regions into the combobox - simpler version without threading.
        This is used when we just need to populate the combobox without UI updates.
        """
        regions = self.get_regions()
        self.app.region_combo['values'] = regions
        if regions:
            self.app.region_combo.set(regions[0])

    def _update_regions_ui(self, regions, source, error=None):
        """
        Update the UI with the loaded regions

        Args:
            regions: List of region names
            source: Source of the regions data
            error: Error message if any
        """
        if error:
            # Show error message
            self.app.show_status(f"Error loading regions: {error}")
            self.app.region_combo['values'] = []
            self.app.region_combo.set('')
            MessageBoxes.show_error("Error", f"Error loading regions: {error}")
            return

        if not regions:
            # If no regions were found, show an error
            self.app.region_combo.set("Error loading regions")
            self.app.region_combo['values'] = ["Error loading regions"]
            return

        # Store the regions data
        self.regions_data = {region: region for region in regions}

        # Update the region dropdown
        self.app.region_combo['values'] = regions

        # Set Prague as the default region if it exists in the list
        if regions:
            # Try to find Prague in the list
            prague_found = False
            for region in regions:
                if "Prague" in region:
                    self.app.region_combo.set(region)
                    prague_found = True
                    break

            # If Prague is not found, use the first region as default
            if not prague_found:
                self.app.region_combo.set(regions[0])

            # Trigger the region selection event
            self.on_region_selected(None)

        # Show data source in status bar
        self.app.show_status(f"Loaded regions from {source}")

    def on_region_selected(self, _):
        """
        Handle region selection event

        Args:
            _: The event that triggered this handler (unused)
        """
        selected_region = self.app.region_var.get()
        if not selected_region:
            return

        # Show a loading indicator
        self.app.district_combo.set("Loading...")
        self.app.district_combo['values'] = ["Loading..."]
        self.app.district_combo.config(state="disabled")
        self.app.city_combo.config(state="disabled")
        self.app.root.update_idletasks()

        # Use threading to avoid freezing the UI
        def load_districts_thread():
            self._load_districts_for_region(selected_region)

        # Start the thread
        if hasattr(self.app, 'thread_pool'):
            self.app.thread_pool.submit(load_districts_thread)
        else:
            threading.Thread(target=load_districts_thread, daemon=True).start()

    def _load_districts_for_region(self, selected_region):
        """
        Load districts for the selected region in a background thread

        Args:
            selected_region: The selected region name
        """
        try:
            # First, try to get districts directly from the region_districts in regions.json
            districts = self.czech_regions.get("region_districts", {}).get(selected_region, [])

            if districts:
                print(f"Using districts from regions.json for {selected_region}: {districts}")
                # Update the UI in the main thread
                self.app.root.after(0, lambda: self._update_districts_ui(selected_region, districts))
                return

            # If no districts found, try to get cities from the data source manager
            cities, source = self.app.data_source_manager.get_cities_in_region(selected_region)

            if cities:
                print(f"Using cities from {source} for {selected_region}: {cities}")
            else:
                print(f"No cities found for {selected_region} from any data source")

            # Update the UI in the main thread
            self.app.root.after(0, lambda: self._update_districts_ui(selected_region, cities))
        except Exception as e:
            print(f"Error loading districts: {e}")
            # Update the UI in the main thread
            self.app.root.after(0, lambda: self._update_districts_ui(selected_region, None, str(e)))

    def _update_districts_ui(self, selected_region, cities, error=None):
        """
        Update the UI with the loaded districts

        Args:
            selected_region: The selected region name
            cities: List of cities/districts in the region
            error: Error message if any
        """
        # Re-enable the combos
        self.app.region_combo.config(state="readonly")
        self.app.district_combo.config(state="readonly")

        if error:
            # Show error message
            self.app.show_status(f"Error: {error}")
            self.app.district_combo['values'] = []
            self.app.district_combo.set('')
            MessageBoxes.show_error("Error", f"Error loading districts: {error}")
            return

        if cities:
            # Store the cities data
            self.districts_data = {city: city for city in cities}

            # Update the district dropdown
            self.app.district_combo['values'] = cities
            self.app.district_combo.set('')

            # Update coordinates based on the selected region
            result = self.app.google_maps.geocode_address(f"{selected_region}, Czech Republic")
            if result:
                # Update coordinate fields
                self.app.coord_x.delete(0, tk.END)
                self.app.coord_x.insert(0, str(result['lat']))
                self.app.coord_y.delete(0, tk.END)
                self.app.coord_y.insert(0, str(result['lng']))

            # Show success message
            self.app.show_status(f"Loaded {len(cities)} districts for {selected_region}")
        else:
            self.app.district_combo['values'] = []
            self.app.district_combo.set('')
            self.app.show_status(f"No districts found for {selected_region}")
            MessageBoxes.show_warning("No Data", f"No districts found for {selected_region}")

    # Batch region functionality
    def load_batch_regions(self):
        """
        Load regions into the batch region combobox
        """
        try:
            # Get regions from the data source manager
            regions, _ = self.app.data_source_manager.get_regions(language="english")
            if regions:
                self.app.batch_region_combo['values'] = regions
                # Select Prague by default
                for i, region in enumerate(regions):
                    if "Prague" in region or "Praha" in region:
                        self.app.batch_region_combo.current(i)
                        # Trigger the region selection event
                        self.on_batch_region_selected()
                        break
        except Exception as e:
            print(f"Error loading batch regions: {e}")

    def on_batch_region_selected(self, *_):
        """
        Handle region selection in batch search

        Args:
            *_: Variable arguments (not used)
        """
        region = self.app.batch_region_var.get()
        if region:
            # Clear city selection
            self.app.batch_city_entry.delete(0, tk.END)

            # Show a status message
            self.app.show_status(f"Loading cities for {region}...")

            # Use threading to avoid freezing the UI
            def load_cities_thread():
                try:
                    # Load cities for the selected region using the data source manager
                    cities, source = self.app.data_source_manager.get_cities_in_region(region)

                    # Update the UI from the main thread
                    if cities:
                        self.app.root.after(10, lambda: self.app.show_status(f"Found {len(cities)} cities in {region} from {source}"))

                        # Select the first city
                        if cities:
                            self.app.root.after(10, lambda: self.app.batch_city_entry.insert(0, cities[0]))
                            self.app.root.after(100, self.app.show_batch_area_on_map)
                    else:
                        self.app.root.after(10, lambda: self.app.show_status(f"No cities found in {region}"))
                except Exception as e:
                    error_msg = f"Error loading cities: {e}"
                    self.app.root.after(10, lambda msg=error_msg: self.app.show_status(msg))

            # Start the thread
            threading.Thread(target=load_cities_thread, daemon=True).start()

    def get_batch_city_suggestions(self, text):
        """
        Get city suggestions for the batch city autocomplete with caching

        Args:
            text: The text to get suggestions for

        Returns:
            list: List of city suggestions
        """
        print(f"\n===== BATCH CITY SUGGESTIONS SEARCH =====")
        print(f"Searching for city suggestions with text: '{text}'")

        if not text or len(text) < 2:
            print(f"Text is too short (length: {len(text)}). Minimum length is 2. Returning empty list.")
            return []

        region = self.app.batch_region_var.get()
        print(f"Current region: '{region}'")

        # Create a cache key that includes the region for more specific caching
        cache_key = f"batch_city:{region}:{text}"
        print(f"Cache key: '{cache_key}'")

        with self.app._address_cache_lock:
            current_time = time.time()

            # Check if we have a cached result
            if cache_key in self.app._address_suggestions_cache:
                cache_entry = self.app._address_suggestions_cache[cache_key]
                if current_time < cache_entry['expiry']:
                    print(f"✓ CACHE HIT: Found valid cache entry for '{cache_key}'")
                    print(f"Cache entry contains {len(cache_entry['data'])} suggestions")
                    print(f"Cache entry expires in {cache_entry['expiry'] - current_time:.1f} seconds")

                    # Log the cached suggestions
                    if cache_entry['data']:
                        print(f"Cached suggestions:")
                        for i, suggestion in enumerate(cache_entry['data']):
                            if isinstance(suggestion, dict) and 'description' in suggestion:
                                print(f"  {i+1}. {suggestion['description']}")
                            else:
                                print(f"  {i+1}. {suggestion}")

                    print(f"===== END BATCH CITY SUGGESTIONS SEARCH =====\n")
                    return cache_entry['data']
                else:
                    # Remove expired entry
                    print(f"✗ CACHE EXPIRED: Cache entry for '{cache_key}' expired {current_time - cache_entry['expiry']:.1f} seconds ago")
                    del self.app._address_suggestions_cache[cache_key]
            else:
                print(f"✗ CACHE MISS: No cache entry found for '{cache_key}'")

        # DISABLED PREFIX MATCHING FOR CITY SEARCHES
        # This was causing incorrect matches (e.g., "Stod" returning "Kovanec")
        # Instead, we'll always use the exact cache key or make a new API call
        print(f"✗ PREFIX MATCHING DISABLED: Prefix matching is disabled for city searches to prevent incorrect matches")

        # For debugging purposes, let's still check what would have been returned
        # but we won't use the results
        debug_prefix_results = self._get_prefix_matches(text, f"batch_city:{region}:")
        if debug_prefix_results:
            print(f"DEBUG: Prefix matching would have returned {len(debug_prefix_results)} suggestions:")
            for i, suggestion in enumerate(debug_prefix_results):
                if isinstance(suggestion, dict) and 'description' in suggestion:
                    print(f"  {i+1}. {suggestion['description']}")
                else:
                    print(f"  {i+1}. {suggestion}")
        else:
            print(f"DEBUG: No prefix matches would have been found")

        try:
            # Use the Google Maps Places API for suggestions
            print(f"Making API call to Google Maps Places API for '{text}'")
            components = {'country': 'cz'}
            print(f"API parameters: types='(cities)', components={components}")

            suggestions = self.app.google_maps.get_place_predictions(
                text,
                types='(cities)',
                components=components
            )

            print(f"API returned {len(suggestions)} suggestions")

            # Log the API results
            if suggestions:
                print(f"API suggestions:")
                for i, suggestion in enumerate(suggestions):
                    if isinstance(suggestion, dict) and 'description' in suggestion:
                        print(f"  {i+1}. {suggestion['description']}")
                    else:
                        print(f"  {i+1}. {suggestion}")

            # Validate the suggestions to ensure they actually match what was searched for
            # This prevents incorrect matches like "Stod" returning "Kovanec"
            validated_suggestions = []
            if suggestions:
                print(f"Validating {len(suggestions)} suggestions for '{text}'...")
                for suggestion in suggestions:
                    suggestion_text = ""
                    if isinstance(suggestion, dict) and 'description' in suggestion:
                        suggestion_text = suggestion['description']
                    else:
                        suggestion_text = str(suggestion)

                    # Check if the suggestion contains the search text
                    # For city searches, we want to be more strict
                    if text.lower() in suggestion_text.lower():
                        print(f"  ✓ VALID SUGGESTION: '{suggestion_text}' contains '{text}'")
                        validated_suggestions.append(suggestion)
                    else:
                        # For short search terms (3 chars or less), we'll be more lenient
                        # and check if the first word of the suggestion starts with the search text
                        if len(text) <= 3:
                            suggestion_words = suggestion_text.lower().split()
                            if suggestion_words and suggestion_words[0].startswith(text.lower()):
                                print(f"  ✓ VALID SUGGESTION (first word match): '{suggestion_text}' starts with '{text}'")
                                validated_suggestions.append(suggestion)
                            else:
                                print(f"  ✗ INVALID SUGGESTION: '{suggestion_text}' does not match '{text}'")
                        else:
                            print(f"  ✗ INVALID SUGGESTION: '{suggestion_text}' does not match '{text}'")

                print(f"Validation complete: {len(validated_suggestions)} of {len(suggestions)} suggestions are valid")

            # Only cache and return validated suggestions
            if validated_suggestions:
                # Cache the results
                print(f"Caching {len(validated_suggestions)} validated results with key: '{cache_key}'")
                with self.app._address_cache_lock:
                    self.app._address_suggestions_cache[cache_key] = {
                        'data': validated_suggestions,
                        'expiry': time.time() + self.app._address_cache_expiry,
                        'search_text': text  # Store the original search text for future validation
                    }
                    print(f"Cache expiry set to {self.app._address_cache_expiry} seconds from now")

                print(f"===== END BATCH CITY SUGGESTIONS SEARCH =====\n")
                return validated_suggestions
            else:
                print(f"No valid suggestions found for '{text}'")
                print(f"===== END BATCH CITY SUGGESTIONS SEARCH =====\n")
                return []
        except Exception as e:
            print(f"ERROR: Failed to get batch city suggestions: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")

            # Return empty list on error
            print(f"===== END BATCH CITY SUGGESTIONS SEARCH =====\n")
            return []

    def _get_prefix_matches(self, text, prefix):
        """
        Get prefix matches from the cache to reduce API calls

        Args:
            text: The text to match
            prefix: The prefix to match

        Returns:
            list: List of matches
        """
        print(f"\n===== REGION MANAGER: PREFIX MATCH SEARCH =====")
        print(f"Searching for text: '{text}' with prefix: '{prefix}'")

        # Check for prefix matches in cache to reduce API calls
        with self.app._address_cache_lock:
            current_time = time.time()
            prefix_matches = []
            cache_entries_checked = 0
            cache_entries_valid = 0
            suggestions_checked = 0

            # Look for any cache entries that start with the prefix
            for key, entry in self.app._address_suggestions_cache.items():
                cache_entries_checked += 1

                # Check if the cache entry is valid and matches our prefix
                if key.startswith(prefix) and current_time < entry['expiry']:
                    cache_entries_valid += 1
                    print(f"Found valid cache entry with key: '{key}', expiry: {entry['expiry'] - current_time:.1f}s remaining")
                    print(f"Cache entry contains {len(entry['data'])} suggestions")

                    # Extract the search text from the cache key for more accurate matching
                    # Format is typically "batch_city:region:text"
                    cache_key_parts = key.split(':')
                    cache_search_text = cache_key_parts[-1] if len(cache_key_parts) > 2 else ""

                    # Skip this cache entry if the search text doesn't match our current search
                    # This prevents incorrect matches between unrelated searches
                    if cache_search_text and not (cache_search_text.lower().startswith(text.lower()) or
                                                text.lower().startswith(cache_search_text.lower())):
                        print(f"  ✗ SKIPPING CACHE ENTRY: Cache key text '{cache_search_text}' is not related to search '{text}'")
                        continue

                    # Check if the cached result starts with the text (not just contains it)
                    for suggestion in entry['data']:
                        suggestions_checked += 1

                        # For city suggestions, we need to check the description field
                        # or the main text depending on the data structure
                        suggestion_text = ""
                        suggestion_id = ""

                        if isinstance(suggestion, dict):
                            if 'description' in suggestion:
                                suggestion_text = suggestion['description']
                            if 'place_id' in suggestion:
                                suggestion_id = suggestion['place_id']
                            elif 'id' in suggestion:
                                suggestion_id = suggestion['id']
                        elif isinstance(suggestion, str):
                            suggestion_text = suggestion
                        else:
                            # Try to convert to string if it's another type
                            suggestion_text = str(suggestion)

                        # Log what we're checking to help diagnose issues
                        print(f"[{suggestions_checked}] Checking suggestion: '{suggestion_text}'")
                        print(f"  - Comparing if '{suggestion_text.lower()}' starts with '{text.lower()}'")

                        # More strict matching - ensure the suggestion starts with the exact search text
                        # For city names, we'll check if the first word matches to handle cases like "Mariánské Lázně"
                        suggestion_words = suggestion_text.lower().split()
                        text_words = text.lower().split()

                        # Check if the suggestion starts with the search text
                        if suggestion_text.lower().startswith(text.lower()):
                            prefix_matches.append(suggestion)
                            print(f"  ✓ MATCH FOUND: '{suggestion_text}' (ID: {suggestion_id}) for '{text}'")
                        # Check if the first word of the suggestion matches the first word of the search text
                        # This helps with multi-word city names like "Mariánské Lázně"
                        elif (len(suggestion_words) > 0 and len(text_words) > 0 and
                              suggestion_words[0].startswith(text_words[0]) and len(text_words[0]) >= 3):
                            prefix_matches.append(suggestion)
                            print(f"  ✓ PARTIAL MATCH FOUND: '{suggestion_text}' first word matches '{text_words[0]}'")
                        else:
                            print(f"  ✗ NO MATCH: '{suggestion_text}' does not start with '{text}'")
                            # Additional check to see if it contains the text anywhere (for debugging)
                            if text.lower() in suggestion_text.lower():
                                print(f"  ! NOTE: '{suggestion_text}' contains '{text}' but doesn't start with it")

            # Log summary
            print(f"\nSummary:")
            print(f"- Cache entries checked: {cache_entries_checked}")
            print(f"- Valid cache entries: {cache_entries_valid}")
            print(f"- Suggestions checked: {suggestions_checked}")
            print(f"- Matches found: {len(prefix_matches)}")

            if prefix_matches:
                print(f"- Matches: {[s['description'] if isinstance(s, dict) and 'description' in s else str(s) for s in prefix_matches]}")
            print(f"===== END PREFIX MATCH SEARCH =====\n")

            # Return unique matches
            return list(set(prefix_matches))

    def get_regions(self):
        """
        Get list of regions in Czech Republic

        Returns:
            list: List of region names
        """
        try:
            # Get regions from the data source manager
            regions, source = self.app.data_source_manager.get_regions(language="english")

            if regions:
                # Create a mapping of region names to IDs
                self.regions_data = {region: f"R{i+1}" for i, region in enumerate(regions)}
                print(f"Using regions from {source}: {regions}")
                self.app.show_status(f"Loaded regions from {source}")
                return regions

            # If no regions were found, return an empty list
            print("No regions found from any data source")
            return []

        except Exception as e:
            print(f"Error getting regions: {e}")
            return []

    def on_district_selected(self, _):
        """
        Handle district selection event

        Args:
            _: The event that triggered this handler (unused)
        """
        selected_district = self.app.district_var.get()
        if not selected_district:
            return

        # Show a loading indicator
        self.app.city_combo.set("Loading...")
        self.app.city_combo['values'] = ["Loading..."]
        self.app.city_combo.config(state="disabled")
        self.app.cadastral_combo.config(state="disabled")
        self.app.root.update_idletasks()

        # Use threading to avoid freezing the UI
        def load_cities_thread():
            try:
                # First, try to get districts directly from the city_districts in regions.json
                districts = self.czech_regions.get("city_districts", {}).get(selected_district, [])

                if districts:
                    print(f"Using cities from regions.json for {selected_district}: {districts}")
                    # Update the UI in the main thread
                    self.app.root.after(0, lambda: self._update_cities_ui(selected_district, districts))
                    return

                # If no districts found, try to get cities from the data source manager
                districts, _ = self.app.data_source_manager.get_districts_in_city(selected_district)

                # Check if we got any districts back
                if not districts or len(districts) == 0:
                    print(f"No districts returned for {selected_district}, using fallback data")

                    # Get districts from the JSON file
                    city_districts = self.czech_regions.get("districts_by_city", {})

                    # Try to find the city in our mapping
                    # First, check if we need to map the English name to Czech
                    city_name_mapping = self.czech_regions.get("city_name_mapping", {})
                    mapped_district = city_name_mapping.get(selected_district, selected_district)

                    # Check if the city name contains any of our keys (for partial matches)
                    fallback_districts = []
                    for key in city_districts.keys():
                        if key in mapped_district or mapped_district in key:
                            fallback_districts = city_districts[key]
                            break

                    if fallback_districts:
                        print(f"Using fallback districts for {selected_district}: {fallback_districts}")
                        districts = fallback_districts
                    else:
                        print(f"No fallback districts found for {selected_district}")
                        districts = []

                # Update the UI in the main thread
                self.app.root.after(0, lambda: self._update_cities_ui(selected_district, districts))
            except Exception as e:
                print(f"Error loading cities: {e}")
                # Update the UI in the main thread
                self.app.root.after(0, lambda: self._update_cities_ui(selected_district, None, str(e)))

        # Start the thread
        if hasattr(self.app, 'thread_pool'):
            self.app.thread_pool.submit(load_cities_thread)
        else:
            threading.Thread(target=load_cities_thread, daemon=True).start()

    def _update_cities_ui(self, selected_district, districts, error=None):
        """
        Update the UI with the loaded cities

        Args:
            selected_district: The selected district name
            districts: List of cities in the district
            error: Error message if any
        """
        # Re-enable the combos
        self.app.district_combo.config(state="readonly")
        self.app.city_combo.config(state="readonly")

        if error:
            # Show error message
            self.app.show_status(f"Error: {error}")
            self.app.city_combo['values'] = []
            self.app.city_combo.set('')
            MessageBoxes.show_error("Error", f"Error loading cities: {error}")
            return

        if districts:
            # Store the districts data
            self.cities_data = {district: district for district in districts}

            # Update the city dropdown
            self.app.city_combo['values'] = districts
            self.app.city_combo.set('')

            # Update coordinates based on the selected district
            result = self.app.google_maps.geocode_address(f"{selected_district}, Czech Republic")
            if result:
                # Update coordinate fields
                self.app.coord_x.delete(0, tk.END)
                self.app.coord_x.insert(0, str(result['lat']))
                self.app.coord_y.delete(0, tk.END)
                self.app.coord_y.insert(0, str(result['lng']))

            # Show success message
            self.app.show_status(f"Loaded {len(districts)} cities for {selected_district}")
        else:
            self.app.city_combo['values'] = []
            self.app.city_combo.set('')
            self.app.show_status(f"No cities found for {selected_district}")
            MessageBoxes.show_warning("No Data", f"No localities found for {selected_district}")

    def on_city_selected(self, _):
        """
        Handle city selection event

        Args:
            _: The event that triggered this handler (unused)
        """
        selected_city = self.app.city_var.get()
        if not selected_city:
            return

        # Show a loading indicator
        self.app.cadastral_combo.set("Loading...")
        self.app.cadastral_combo['values'] = ["Loading..."]
        self.app.root.update_idletasks()

        # Use threading to avoid freezing the UI
        def load_cadastral_thread():
            try:
                # Get the selected district
                selected_district = self.app.district_var.get()

                # First, try to get cadastral areas directly from the cadastral_areas in regions.json
                cadastral_areas = self.czech_regions.get("cadastral_areas", {}).get(selected_city, [])

                if cadastral_areas:
                    print(f"Using cadastral areas from regions.json for {selected_city}: {cadastral_areas}")
                    # Update the UI in the main thread
                    self.app.root.after(0, lambda: self._update_cadastral_ui(selected_city, selected_district, cadastral_areas))
                    return

                # If no cadastral areas found, try to get them from the data source manager
                cadastral_areas, _ = self.app.data_source_manager.get_cadastral_areas_in_city(selected_city)

                # If no cadastral areas returned from API, use fallback data
                if not cadastral_areas or len(cadastral_areas) == 0:
                    print(f"No cadastral areas returned for {selected_city} in {selected_district}, using fallback data")

                    # Try to get cadastral areas from the JSON file
                    # First check if we have data for this specific city
                    city_name_mapping = self.czech_regions.get("city_name_mapping", {})
                    mapped_city = city_name_mapping.get(selected_city, selected_city)

                    # Try to get cadastral areas for the city
                    cadastral_areas = self.czech_regions.get("cadastral_areas_by_city", {}).get(mapped_city, [])

                    # If no city-specific data, try district-specific data
                    if not cadastral_areas:
                        cadastral_areas = self.czech_regions.get("cadastral_areas_by_district", {}).get(selected_city, [])

                    # If still no data, generate using template
                    if not cadastral_areas:
                        template = self.czech_regions.get("cadastral_template", ["{name}", "{name}-město", "{name}-okolí", "Katastr {name}"])
                        cadastral_areas = [t.format(name=mapped_city) for t in template]

                    print(f"Using fallback cadastral areas: {cadastral_areas}")

                # Update the UI in the main thread
                self.app.root.after(0, lambda: self._update_cadastral_ui(selected_city, selected_district, cadastral_areas))
            except Exception as e:
                print(f"Error loading cadastral areas: {e}")
                # Update the UI in the main thread
                self.app.root.after(0, lambda: self._update_cadastral_ui(selected_city, selected_district, None, str(e)))

        # Start the thread
        if hasattr(self.app, 'thread_pool'):
            self.app.thread_pool.submit(load_cadastral_thread)
        else:
            threading.Thread(target=load_cadastral_thread, daemon=True).start()

    def _update_cadastral_ui(self, selected_city, selected_district, cadastral_areas, error=None):
        """
        Update the UI with the loaded cadastral areas

        Args:
            selected_city: The selected city name
            selected_district: The selected district name
            cadastral_areas: List of cadastral areas in the city
            error: Error message if any
        """
        try:
            # Re-enable the combos
            self.app.city_combo.config(state="readonly")
            self.app.cadastral_combo.config(state="readonly")

            if error:
                # Show error message
                self.app.show_status(f"Error: {error}")
                self.app.cadastral_combo['values'] = []
                self.app.cadastral_combo.set('')
                MessageBoxes.show_error("Error", f"Error loading cadastral areas: {error}")
                return

            if cadastral_areas:
                # Store the cadastral areas data
                self.cadastral_data = {area: area for area in cadastral_areas}

                # Update the cadastral dropdown
                self.app.cadastral_combo['values'] = cadastral_areas
                self.app.cadastral_combo.set('')

                # Also update the map to show the selected city/district
                result = self.app.google_maps.geocode_address(f"{selected_city}, {selected_district}, Czech Republic")
                if result:
                    # Update coordinate fields
                    self.app.coord_x.delete(0, tk.END)
                    self.app.coord_x.insert(0, str(result['lat']))
                    self.app.coord_y.delete(0, tk.END)
                    self.app.coord_y.insert(0, str(result['lng']))
            else:
                self.app.cadastral_combo['values'] = []
                self.app.cadastral_combo.set('')
                self.app.cadastral_combo.config(state="disabled")  # Disable the dropdown when no data
                MessageBoxes.show_warning("No Data", f"No cadastral areas found for {selected_city}")
        except Exception as e:
            print(f"Error in _update_cadastral_ui: {e}")
            self.app.cadastral_combo['values'] = []
            self.app.cadastral_combo.set('')
            self.app.cadastral_combo.config(state="disabled")  # Disable the dropdown on error

    def on_cadastral_selected(self, _):
        """
        Handle cadastral area selection event

        Args:
            _: The event that triggered this handler (unused)
        """
        selected_cadastral = self.app.cadastral_var.get()
        if not selected_cadastral:
            return

        # Update coordinates for the selected cadastral area
        try:
            # Get the selected district and city
            selected_district = self.app.district_var.get()
            selected_city = self.app.city_var.get()

            # Geocode the full address
            result = self.app.google_maps.geocode_address(
                f"{selected_cadastral}, {selected_city}, {selected_district}, Czech Republic"
            )

            if result:
                # Update coordinate fields
                self.app.coord_x.delete(0, tk.END)
                self.app.coord_x.insert(0, str(result['lat']))
                self.app.coord_y.delete(0, tk.END)
                self.app.coord_y.insert(0, str(result['lng']))

                # Update the Show Map button to indicate map is available
                self.app.show_map_btn.config(text="Show Map")
        except Exception as e:
            print(f"Error in on_cadastral_selected: {e}")

    def show_batch_area_on_map(self):
        """
        Show the selected area on the map
        """
        # Get city from the entry widget
        city = self.app.batch_city_entry.get() if hasattr(self.app, 'batch_city_entry') else ""
        region = self.app.batch_region_var.get()

        if not city and not region:
            MessageBoxes.show_warning("Selection Required", "Please select a region or city")
            return

        # Show status
        self.app.show_status(f"Finding coordinates for {city if city else region}...")

        # Use threading to avoid freezing the UI
        def geocode_thread():
            try:
                # Geocode the location
                location = city if city else region
                location += ", Czech Republic"

                # Get coordinates
                coords = self.app.google_maps.geocode_address(location)
                if coords:
                    lat = coords['lat']
                    lng = coords['lng']

                    # Update the UI from the main thread
                    self.app.root.after(10, lambda: self.app.show_status(f"Found coordinates: {lat:.6f}, {lng:.6f}"))
                    self.app.root.after(10, lambda: self.app.show_embedded_map_with_radius(lat, lng, int(self.app.radius_var.get())))

                    # Update the button text to indicate it can be used to refresh
                    self.app.root.after(10, lambda: self.app.update_batch_map_btn.config(text="Refresh Map"))
                else:
                    self.app.root.after(10, lambda: self.app.show_status("Could not find coordinates"))
                    self.app.root.after(10, lambda: MessageBoxes.show_error("Geocoding Error", "Could not find coordinates for the selected location"))
            except Exception as e:
                self.app.root.after(10, lambda: self.app.show_status(f"Error: {e}"))
                self.app.root.after(10, lambda: MessageBoxes.show_error("Error", f"An error occurred: {e}"))

        # Start the thread
        threading.Thread(target=geocode_thread, daemon=True).start()

    def clear_address_cache(self, prefix=None):
        """
        Clear the address suggestions cache

        Args:
            prefix (str, optional): If provided, only clear cache entries that start with this prefix
                                   If None, clear all cache entries

        Returns:
            int: Number of cache entries cleared
        """
        print(f"\n===== CLEARING ADDRESS CACHE =====")
        if prefix:
            print(f"Clearing cache entries with prefix: '{prefix}'")
        else:
            print(f"Clearing all cache entries")

        cleared_count = 0

        with self.app._address_cache_lock:
            # Get a list of keys to avoid modifying the dictionary during iteration
            keys = list(self.app._address_suggestions_cache.keys())

            for key in keys:
                if prefix is None or key.startswith(prefix):
                    del self.app._address_suggestions_cache[key]
                    cleared_count += 1
                    print(f"Cleared cache entry: '{key}'")

        print(f"Cleared {cleared_count} cache entries")
        print(f"===== END CLEARING ADDRESS CACHE =====\n")

        return cleared_count

    def clear_city_cache(self, city_name=None):
        """
        Clear the city suggestions cache, optionally for a specific city

        This is useful when incorrect city matches are found in the cache.

        Args:
            city_name (str, optional): If provided, only clear cache entries for this city
                                      If None, clear all city-related cache entries

        Returns:
            int: Number of cache entries cleared
        """
        print(f"\n===== CLEARING CITY CACHE =====")
        if city_name:
            print(f"Clearing cache entries for city: '{city_name}'")
        else:
            print(f"Clearing all city cache entries")

        cleared_count = 0

        # First try to use the cache manager if available
        if hasattr(self.app, 'cache_manager'):
            try:
                # Clear city suggestions cache
                if city_name:
                    # For a specific city, we need to find all keys that contain the city name
                    keys_to_clear = []
                    for cache_name in ['city_suggestions', 'address_suggestions']:
                        # Get all keys in this cache
                        all_keys = self.app.cache_manager.get_all_keys(cache_name)
                        # Filter keys that contain the city name
                        keys_to_clear.extend([
                            (cache_name, key) for key in all_keys
                            if city_name.lower() in key.lower()
                        ])

                    # Clear each matching key
                    for cache_name, key in keys_to_clear:
                        self.app.cache_manager.delete(cache_name, key)
                        cleared_count += 1
                        print(f"Cleared cache entry: '{cache_name}/{key}'")
                else:
                    # Clear all city-related caches
                    cleared_count += self.app.cache_manager.clear_cache('city_suggestions')
                    # Also clear city entries in address suggestions
                    address_keys = self.app.cache_manager.get_all_keys('address_suggestions')
                    for key in address_keys:
                        if 'city' in key.lower() or 'batch_city' in key.lower():
                            self.app.cache_manager.delete('address_suggestions', key)
                            cleared_count += 1
                            print(f"Cleared cache entry: 'address_suggestions/{key}'")
            except Exception as e:
                print(f"Error clearing cache with cache manager: {e}")

        # Also clear the old-style address cache
        with self.app._address_cache_lock:
            # Get a list of keys to avoid modifying the dictionary during iteration
            keys = list(self.app._address_suggestions_cache.keys())

            for key in keys:
                # Clear city-related entries
                if 'city' in key.lower() or 'batch_city' in key.lower():
                    # If a specific city was provided, only clear entries for that city
                    if city_name is None or city_name.lower() in key.lower():
                        del self.app._address_suggestions_cache[key]
                        cleared_count += 1
                        print(f"Cleared cache entry: '{key}'")

        print(f"Cleared {cleared_count} cache entries")
        print(f"===== END CLEARING CITY CACHE =====\n")

        # Show a message to the user
        if hasattr(self.app, 'show_status'):
            self.app.show_status(f"Cleared {cleared_count} city cache entries")

        # Show a message box to confirm the cache was cleared
        from ui.message_boxes import MessageBoxes
        if city_name:
            MessageBoxes.show_info("Cache Cleared", f"Cleared {cleared_count} cache entries for city '{city_name}'")
        else:
            MessageBoxes.show_info("Cache Cleared", f"Cleared {cleared_count} city cache entries")

        return cleared_count