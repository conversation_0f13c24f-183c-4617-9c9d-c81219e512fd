"""
Simple test for the Czech Property Registry application.

This script tests the basic functionality of the application.
"""

import os
import sys
import logging

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SimpleTest")

# Import the necessary modules
from api.cuzk.cuzk_integration import CUZKIntegration


def test_cuzk_integration():
    """Test the CUZK integration."""
    try:
        # Create the CUZK integration
        cuzk_integration = CUZKIntegration()
        
        # Test the get_property_by_ruian_id method
        ruian_id = "123456"  # Example RUIAN ID
        logger.info(f"Testing get_property_by_ruian_id with RUIAN ID: {ruian_id}")
        
        # Call the method
        property_data = cuzk_integration.get_property_by_ruian_id(ruian_id)
        
        # Log the result
        if property_data:
            logger.info(f"Successfully retrieved property data: {property_data}")
        else:
            logger.info("No property data found")
        
        # Test successful
        logger.info("CUZK integration test completed successfully")
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)


if __name__ == '__main__':
    test_cuzk_integration()
