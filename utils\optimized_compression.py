"""
Optimized data compression utilities for the Czech Property Registry application.
Provides efficient compression and decompression with multiple algorithms and caching.
"""

import zlib
import lzma
import bz2
import gzip
import time
import logging
import threading
import functools
from typing import Dict, Any, Optional, List, Tuple, Set, Union, Callable, Literal

# Configure logging
logger = logging.getLogger("OptimizedCompression")


class OptimizedCompressor:
    """
    An optimized data compressor with multiple compression algorithms,
    adaptive algorithm selection, and performance monitoring.
    """

    # Compression methods
    COMPRESSION_METHODS = {
        "zlib": {
            "compress": zlib.compress,
            "decompress": zlib.decompress
        },
        "lzma": {
            "compress": lzma.compress,
            "decompress": lzma.decompress
        },
        "bz2": {
            "compress": bz2.compress,
            "decompress": bz2.decompress
        },
        "gzip": {
            "compress": lambda data, level: gzip.compress(data, compresslevel=level),
            "decompress": gzip.decompress
        }
    }

    def __init__(self,
                 method: str = "zlib",
                 level: int = 6,
                 adaptive: bool = True,
                 cache_size: int = 1000,
                 min_size_to_compress: int = 1024):
        """
        Initialize the optimized data compressor.

        Args:
            method (str): Compression method to use (zlib, lzma, bz2, gzip)
            level (int): Compression level (1-9, higher is more compression)
            adaptive (bool): Whether to adaptively select the best compression method
            cache_size (int): Maximum number of items to keep in compression cache
            min_size_to_compress (int): Minimum data size to compress (smaller data won't be compressed)
        """
        if method not in self.COMPRESSION_METHODS:
            raise ValueError(f"Invalid compression method: {method}. "
                            f"Valid methods are: {', '.join(self.COMPRESSION_METHODS.keys())}")

        self.method = method
        self.level = level
        self.adaptive = adaptive
        self.cache_size = cache_size
        self.min_size_to_compress = min_size_to_compress

        # Compression cache
        self.compression_cache = {}
        self.decompression_cache = {}
        self.cache_lock = threading.Lock()

        # Statistics
        self.stats = {
            "compressed_bytes": 0,
            "uncompressed_bytes": 0,
            "compression_time": 0.0,
            "decompression_time": 0.0,
            "compression_count": 0,
            "decompression_count": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "method_stats": {
                method: {
                    "compression_ratio": 0.0,
                    "compression_time": 0.0,
                    "decompression_time": 0.0,
                    "count": 0
                } for method in self.COMPRESSION_METHODS
            }
        }
        self.stats_lock = threading.Lock()

        logger.info(f"OptimizedCompressor initialized with {method} method at level {level}")

    def compress(self, data: bytes, method: Optional[str] = None, level: Optional[int] = None) -> bytes:
        """
        Compress data using the specified method and level.

        Args:
            data (bytes): Data to compress
            method (str, optional): Compression method to use (overrides default)
            level (int, optional): Compression level to use (overrides default)

        Returns:
            bytes: Compressed data
        """
        # Don't compress small data
        if len(data) < self.min_size_to_compress:
            return data

        # Use default method and level if not specified
        method = method or self.method
        level = level or self.level

        # Check if we've already compressed this data
        data_hash = hash(data)
        cache_key = (data_hash, method, level)

        with self.cache_lock:
            if cache_key in self.compression_cache:
                with self.stats_lock:
                    self.stats["cache_hits"] += 1
                return self.compression_cache[cache_key]

        # Select the best method if adaptive compression is enabled
        if self.adaptive and method == self.method:
            method = self._select_best_method(data)

        # Compress the data
        start_time = time.time()
        try:
            compressed = self.COMPRESSION_METHODS[method]["compress"](data, level)
            compression_time = time.time() - start_time

            # Update statistics
            with self.stats_lock:
                self.stats["compressed_bytes"] += len(compressed)
                self.stats["uncompressed_bytes"] += len(data)
                self.stats["compression_time"] += compression_time
                self.stats["compression_count"] += 1
                self.stats["cache_misses"] += 1

                # Update method-specific statistics
                if method in self.stats["method_stats"]:
                    self.stats["method_stats"][method]["compression_ratio"] = (
                        (self.stats["method_stats"][method]["compression_ratio"] *
                         self.stats["method_stats"][method]["count"] +
                         len(data) / len(compressed)) /
                        (self.stats["method_stats"][method]["count"] + 1)
                    )
                    self.stats["method_stats"][method]["compression_time"] += compression_time
                    self.stats["method_stats"][method]["count"] += 1

            # Cache the result
            with self.cache_lock:
                # Limit cache size
                if len(self.compression_cache) >= self.cache_size:
                    # Remove a random item (simple approach)
                    self.compression_cache.pop(next(iter(self.compression_cache)))

                self.compression_cache[cache_key] = compressed

            return compressed
        except Exception as e:
            logger.warning(f"Compression failed with method {method}: {e}")
            # Fall back to the default method if the selected method fails
            if method != self.method:
                logger.info(f"Falling back to default method {self.method}")
                return self.compress(data, self.method, level)
            else:
                # If the default method fails, return the original data
                logger.warning(f"Default compression method {self.method} failed, returning uncompressed data")
                return data

    def decompress(self, data: bytes, method: Optional[str] = None) -> bytes:
        """
        Decompress data using the specified method.

        Args:
            data (bytes): Data to decompress
            method (str, optional): Compression method to use (overrides default)

        Returns:
            bytes: Decompressed data
        """
        # If data is too small, it's probably not compressed
        if len(data) < self.min_size_to_compress:
            return data

        # Use default method if not specified
        method = method or self.method

        # Check if we've already decompressed this data
        data_hash = hash(data)
        cache_key = (data_hash, method)

        with self.cache_lock:
            if cache_key in self.decompression_cache:
                with self.stats_lock:
                    self.stats["cache_hits"] += 1
                return self.decompression_cache[cache_key]

        # Try to decompress with the specified method
        start_time = time.time()
        try:
            decompressed = self.COMPRESSION_METHODS[method]["decompress"](data)
            decompression_time = time.time() - start_time

            # Update statistics
            with self.stats_lock:
                self.stats["decompression_time"] += decompression_time
                self.stats["decompression_count"] += 1
                self.stats["cache_misses"] += 1

                # Update method-specific statistics
                if method in self.stats["method_stats"]:
                    self.stats["method_stats"][method]["decompression_time"] += decompression_time

            # Cache the result
            with self.cache_lock:
                # Limit cache size
                if len(self.decompression_cache) >= self.cache_size:
                    # Remove a random item (simple approach)
                    self.decompression_cache.pop(next(iter(self.decompression_cache)))

                self.decompression_cache[cache_key] = decompressed

            return decompressed
        except Exception as e:
            logger.warning(f"Decompression failed with method {method}: {e}")

            # If the specified method fails, try all other methods
            if method == self.method:
                for alt_method in self.COMPRESSION_METHODS:
                    if alt_method != method:
                        try:
                            logger.info(f"Trying alternative method {alt_method}")
                            return self.decompress(data, alt_method)
                        except Exception:
                            pass

            # If all methods fail, return the original data
            logger.warning("All decompression methods failed, returning original data")
            return data

    def _select_best_method(self, data: bytes) -> str:
        """
        Select the best compression method for the given data.

        Args:
            data (bytes): Data to compress

        Returns:
            str: Best compression method
        """
        # If we don't have enough statistics, use the default method
        with self.stats_lock:
            if all(stats["count"] < 10 for _, stats in self.stats["method_stats"].items()):
                return self.method

            # Select the method with the best compression ratio
            best_method = self.method
            best_ratio = 0.0

            for method_name, stats in self.stats["method_stats"].items():
                if stats["count"] > 0 and stats["compression_ratio"] > best_ratio:
                    best_ratio = stats["compression_ratio"]
                    best_method = method_name

            return best_method

    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the compressor.

        Returns:
            Dict[str, Any]: Dictionary with compression statistics
        """
        with self.stats_lock:
            stats = self.stats.copy()

            # Calculate derived statistics
            if stats["compression_count"] > 0:
                stats["average_compression_time"] = stats["compression_time"] / stats["compression_count"]
            else:
                stats["average_compression_time"] = 0.0

            if stats["decompression_count"] > 0:
                stats["average_decompression_time"] = stats["decompression_time"] / stats["decompression_count"]
            else:
                stats["average_decompression_time"] = 0.0

            if stats["compressed_bytes"] > 0:
                stats["overall_compression_ratio"] = stats["uncompressed_bytes"] / stats["compressed_bytes"]
            else:
                stats["overall_compression_ratio"] = 1.0

            stats["space_saved"] = stats["uncompressed_bytes"] - stats["compressed_bytes"]
            stats["space_saved_mb"] = stats["space_saved"] / (1024 * 1024)

            return stats

    def clear_cache(self) -> None:
        """Clear the compression and decompression caches."""
        with self.cache_lock:
            self.compression_cache.clear()
            self.decompression_cache.clear()

        logger.info("Compression caches cleared")


# Create a global instance for convenience
optimized_compressor = OptimizedCompressor()
