"""
Test script for address geocoding and coordinate verification.

This script geocodes addresses using Google Maps API and verifies the coordinates
by converting them to S-JTSK and back to WGS84, then reverse geocoding them.
"""

import os
import sys
import logging
import argparse
import configparser
from typing import Dict, Any, List, Tuple, Optional

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("AddressCoordinateTest")

# Import necessary modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk, convert_sjtsk_to_wgs84


def load_api_key() -> Optional[str]:
    """Load Google Maps API key from config file."""
    config = configparser.ConfigParser()
    if os.path.exists('config.ini'):
        config.read('config.ini')
        if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
            return config['GOOGLE_MAPS']['api_key']
    return None


def test_address_coordinates(address: str, country: str = "Czech Republic") -> Dict[str, Any]:
    """
    Test address geocoding and coordinate verification.
    
    Args:
        address (str): Address to geocode
        country (str): Country to use for geocoding (default: "Czech Republic")
        
    Returns:
        dict: Dictionary with test results
    """
    # Initialize Google Maps integration
    api_key = load_api_key()
    if not api_key:
        logger.error("Google Maps API key not found in config.ini")
        return {'error': 'Google Maps API key not found'}
    
    google_maps = GoogleMapsIntegration()
    
    # Step 1: Geocode the address
    logger.info(f"Geocoding address: {address}, {country}")
    try:
        geocode_result = google_maps.geocode_address(address, country)
        if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
            logger.error(f"Failed to geocode address: {address}")
            return {'error': 'Geocoding failed'}
        
        lat = geocode_result['lat']
        lng = geocode_result['lng']
        formatted_address = geocode_result.get('formatted_address', address)
        logger.info(f"Geocoded to: {lat}, {lng}")
        logger.info(f"Formatted address: {formatted_address}")
        
        # Step 2: Convert to S-JTSK
        sjtsk_x, sjtsk_y = convert_wgs84_to_sjtsk(lat, lng)
        logger.info(f"Converted to S-JTSK: {sjtsk_x}, {sjtsk_y}")
        
        # Step 3: Convert back to WGS84
        reconverted_lat, reconverted_lng = convert_sjtsk_to_wgs84(sjtsk_x, sjtsk_y)
        logger.info(f"Reconverted to WGS84: {reconverted_lat}, {reconverted_lng}")
        
        # Calculate the difference
        lat_diff = abs(lat - reconverted_lat)
        lng_diff = abs(lng - reconverted_lng)
        logger.info(f"Coordinate difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")
        
        # Check if the difference is significant
        has_significant_error = lat_diff > 0.001 or lng_diff > 0.001
        if has_significant_error:
            logger.warning(f"Significant coordinate conversion error detected!")
        
        # Step 4: Reverse geocode the original coordinates
        reverse_result = google_maps.reverse_geocode(lat, lng)
        if reverse_result and 'formatted_address' in reverse_result:
            reverse_address = reverse_result['formatted_address']
            logger.info(f"Reverse geocoded address: {reverse_address}")
            
            # Compare addresses
            addresses_match = address.lower() in reverse_address.lower() or reverse_address.lower() in address.lower()
            if not addresses_match:
                logger.warning(f"Addresses don't match: '{address}' vs '{reverse_address}'")
        else:
            logger.warning(f"Reverse geocoding failed")
            reverse_address = None
            addresses_match = False
        
        # Step 5: Reverse geocode the reconverted coordinates
        reconverted_reverse_result = google_maps.reverse_geocode(reconverted_lat, reconverted_lng)
        if reconverted_reverse_result and 'formatted_address' in reconverted_reverse_result:
            reconverted_reverse_address = reconverted_reverse_result['formatted_address']
            logger.info(f"Reconverted reverse geocoded address: {reconverted_reverse_address}")
            
            # Compare addresses
            reconverted_addresses_match = address.lower() in reconverted_reverse_address.lower() or reconverted_reverse_address.lower() in address.lower()
            if not reconverted_addresses_match:
                logger.warning(f"Reconverted addresses don't match: '{address}' vs '{reconverted_reverse_address}'")
        else:
            logger.warning(f"Reconverted reverse geocoding failed")
            reconverted_reverse_address = None
            reconverted_addresses_match = False
        
        # Generate CUZK URL
        cuzk_url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={int(sjtsk_x)}&y={int(sjtsk_y)}"
        logger.info(f"CUZK URL: {cuzk_url}")
        
        # Return results
        return {
            'address': address,
            'formatted_address': formatted_address,
            'original_wgs84': {'lat': lat, 'lng': lng},
            'sjtsk': {'x': sjtsk_x, 'y': sjtsk_y},
            'reconverted_wgs84': {'lat': reconverted_lat, 'lng': reconverted_lng},
            'difference': {'lat_diff': lat_diff, 'lng_diff': lng_diff},
            'has_significant_error': has_significant_error,
            'reverse_address': reverse_address,
            'addresses_match': addresses_match,
            'reconverted_reverse_address': reconverted_reverse_address,
            'reconverted_addresses_match': reconverted_addresses_match,
            'cuzk_url': cuzk_url
        }
        
    except Exception as e:
        logger.error(f"Error testing address coordinates: {e}")
        return {'error': str(e)}


def main():
    """Main function to run the address coordinate test."""
    parser = argparse.ArgumentParser(description='Test address geocoding and coordinate verification.')
    parser.add_argument('address', type=str, help='Address to test')
    parser.add_argument('--country', type=str, default="Czech Republic", help='Country (default: Czech Republic)')
    
    args = parser.parse_args()
    
    results = test_address_coordinates(args.address, args.country)
    
    if 'error' in results:
        print(f"Error: {results['error']}")
        return
    
    # Print a summary
    print("\n=== Address Coordinate Test Results ===")
    print(f"Address: {results['address']}")
    print(f"Formatted address: {results['formatted_address']}")
    print(f"Original WGS84: {results['original_wgs84']['lat']}, {results['original_wgs84']['lng']}")
    print(f"Converted to S-JTSK: {results['sjtsk']['x']}, {results['sjtsk']['y']}")
    print(f"Reconverted to WGS84: {results['reconverted_wgs84']['lat']}, {results['reconverted_wgs84']['lng']}")
    print(f"Difference: lat_diff={results['difference']['lat_diff']:.6f}, lng_diff={results['difference']['lng_diff']:.6f}")
    print(f"Has significant error: {results['has_significant_error']}")
    print(f"Reverse geocoded address: {results['reverse_address']}")
    print(f"Addresses match: {results['addresses_match']}")
    print(f"Reconverted reverse geocoded address: {results['reconverted_reverse_address']}")
    print(f"Reconverted addresses match: {results['reconverted_addresses_match']}")
    print(f"CUZK URL: {results['cuzk_url']}")


if __name__ == "__main__":
    main()
