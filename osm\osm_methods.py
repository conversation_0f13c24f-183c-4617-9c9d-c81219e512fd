"""
OpenStreetMap integration methods for the Czech Property Registry application.
"""

import requests
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import threading
import tkinter as tk
from utils.message_boxes import MessageBoxes

class OSMMethods:
    """
    Class for handling OpenStreetMap integration methods.
    """
    
    def __init__(self, app):
        """
        Initialize the OSMMethods class.
        
        Args:
            app: The main application instance
        """
        self.app = app
        
    def fetch_properties_from_osm(self, lat, lng, radius=500):
        """
        Fetch buildings with RUIAN references from OpenStreetMap using Overpass API

        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius (int): Radius in meters

        Returns:
            list: List of buildings with RUIAN references
        """
        try:
            # Convert radius from meters to degrees (approximate)
            # 1 degree of latitude is approximately 111 km
            radius_deg = radius / 111000

            # Construct Overpass API query to find buildings with RUIAN references
            overpass_url = "https://overpass-api.de/api/interpreter"

            # Query for buildings with ref:ruian or ref:ruian:building tags
            overpass_query = f"""
            [out:xml][timeout:25];
            (
              node["ref:ruian"]({lat-radius_deg},{lng-radius_deg},{lat+radius_deg},{lng+radius_deg});
              node["ref:ruian:building"]({lat-radius_deg},{lng-radius_deg},{lat+radius_deg},{lng+radius_deg});
              way["ref:ruian"]({lat-radius_deg},{lng-radius_deg},{lat+radius_deg},{lng+radius_deg});
              way["ref:ruian:building"]({lat-radius_deg},{lng-radius_deg},{lat+radius_deg},{lng+radius_deg});
              relation["ref:ruian"]({lat-radius_deg},{lng-radius_deg},{lat+radius_deg},{lng+radius_deg});
              relation["ref:ruian:building"]({lat-radius_deg},{lng-radius_deg},{lat+radius_deg},{lng+radius_deg});
            );
            out body;
            >;
            out skel qt;
            """

            # Send request to Overpass API
            response = requests.post(overpass_url, data={"data": overpass_query})

            if response.status_code != 200:
                print(f"Error fetching data from Overpass API: {response.status_code}")
                return []

            # Parse XML response
            root = ET.fromstring(response.text)

            # Extract buildings with RUIAN references
            buildings = []

            for element in root.findall('./node') + root.findall('./way') + root.findall('./relation'):
                building = {
                    'type': element.tag,
                    'id': element.get('id'),
                    'lat': element.get('lat'),
                    'lon': element.get('lon'),
                    'tags': {}
                }

                # Extract all tags
                for tag in element.findall('./tag'):
                    k = tag.get('k')
                    v = tag.get('v')
                    building['tags'][k] = v

                # Get RUIAN reference
                ruian_ref = building['tags'].get('ref:ruian') or building['tags'].get('ref:ruian:building')

                if ruian_ref:
                    building['ruian_ref'] = ruian_ref
                    buildings.append(building)

            print(f"Found {len(buildings)} buildings with RUIAN references")
            return buildings

        except Exception as e:
            print(f"Error fetching buildings from OpenStreetMap: {e}")
            return []
            
    def fetch_properties_in_area(self, lat, lng, radius, max_results, property_type=None):
        """
        Fetch properties in a given area from the CUZK website

        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius (int): Radius in meters (used in API calls)
            max_results (int): Maximum number of results to return (used in API calls)
            property_type (str, optional): Type of property to filter by

        Returns:
            list: List of property data dictionaries
        """
        # Note: radius and max_results parameters are used in API calls to external services

        # First try to fetch buildings with RUIAN references from OpenStreetMap
        osm_buildings = self.fetch_properties_from_osm(lat, lng, radius)

        if osm_buildings:
            # Convert OSM buildings to property data format
            properties = []

            for building in osm_buildings:
                ruian_ref = building.get('ruian_ref')

                if ruian_ref:
                    # Create property data dictionary
                    property_data = {
                        'ruian_id': ruian_ref,
                        'source': 'openstreetmap',
                        'lat': building.get('lat'),
                        'lng': building.get('lon'),
                        'property_type': building['tags'].get('building', 'Unknown'),
                        'parcel_number': building['tags'].get('addr:conscriptionnumber', ''),
                        'cadastral_territory': building['tags'].get('addr:city', 'Unknown'),
                        'owner_name': 'Unknown (Click to fetch from CUZK)',
                        'owner_address': 'Unknown (Click to fetch from CUZK)',
                        'osm_id': f"{building['type']}/{building['id']}"
                    }

                    # Add address information if available
                    if 'addr:housenumber' in building['tags']:
                        property_data['address'] = f"{building['tags'].get('addr:street', '')} {building['tags'].get('addr:housenumber', '')}"
                        property_data['address'] += f", {building['tags'].get('addr:city', '')}"
                        property_data['address'] = property_data['address'].strip()

                    properties.append(property_data)

            # Limit to max_results
            return properties[:max_results] if max_results else properties
            
        return []
