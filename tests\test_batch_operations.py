"""
Test for batch operations with OSM and CUZK.

This module tests the batch operations functionality,
verifying that the application can handle multiple buildings
and perform batch operations on them.
"""

import os
import sys
import unittest
import logging
import tkinter as tk
from unittest.mock import MagicMock, patch

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BatchOperationsTest")

# Import the necessary modules
from api.osm.osm_methods import OSMMethods
from api.osm.osm_integration import OSMIntegration
from api.cuzk.cuzk_integration import CUZKIntegration
from utils.enhanced_cache_manager import EnhancedCacheManager
from core.optimized_network import create_optimized_session
from ui.progress_dialog import ProgressDialog


class TestBatchOperations(unittest.TestCase):
    """Test the batch operations functionality."""

    @classmethod
    def setUpClass(cls):
        """Set up the test environment."""
        # Create a root window for Tkinter
        cls.root = tk.Tk()
        cls.root.withdraw()  # Hide the window

        # Create a mock application
        cls.app = MagicMock()
        cls.app.root = cls.root

        # Create a session
        cls.app.session = create_optimized_session()

        # Create a cache manager
        cls.app.cache_manager = EnhancedCacheManager(
            default_expiry=300,  # 5 minutes
            disk_cache_dir="test_cache",
            max_memory_items=100
        )

        # Create the OSM integration
        cls.osm_integration = OSMIntegration(cls.app)

        # Create the CUZK integration
        cls.cuzk_integration = CUZKIntegration()

        # Set up the app with the integrations
        cls.app.osm_integration = cls.osm_integration
        cls.app.cuzk_integration = cls.cuzk_integration

        # Create the OSM methods
        cls.osm_methods = OSMMethods(cls.app)

        # Set up the app with the OSM methods
        cls.app.osm_methods = cls.osm_methods

        # Create a mock treeview
        cls.app.osm_treeview = MagicMock()

        # Coordinates for testing (Prague)
        cls.test_lat = 50.0755
        cls.test_lng = 14.4378
        cls.test_radius = 500  # meters

    @classmethod
    def tearDownClass(cls):
        """Clean up after the tests."""
        # Clean up the cache
        if hasattr(cls.app, 'cache_manager'):
            cls.app.cache_manager.clear_all()

        # Destroy the root window
        cls.root.destroy()

    def setUp(self):
        """Set up each test."""
        # Reset the mock treeview
        self.app.osm_treeview.reset_mock()

        # Create sample buildings with RUIAN IDs
        self.sample_buildings = [
            {
                'ruian_ref': '123456',
                'name': 'Building 1',
                'address': 'Address 1',
                'osm_id': '1',
                'osm_type': 'way',
                'lat': self.test_lat,
                'lng': self.test_lng,
                'tags': {'building': 'yes', 'name': 'Building 1'}
            },
            {
                'ruian_ref': '789012',
                'name': 'Building 2',
                'address': 'Address 2',
                'osm_id': '2',
                'osm_type': 'way',
                'lat': self.test_lat + 0.001,
                'lng': self.test_lng + 0.001,
                'tags': {'building': 'yes', 'name': 'Building 2'}
            },
            {
                'ruian_ref': '345678',
                'name': 'Building 3',
                'address': 'Address 3',
                'osm_id': '3',
                'osm_type': 'way',
                'lat': self.test_lat - 0.001,
                'lng': self.test_lng - 0.001,
                'tags': {'building': 'yes', 'name': 'Building 3'}
            }
        ]

        # Store the buildings in the app
        self.app.osm_buildings = self.sample_buildings

    def test_progress_dialog(self):
        """Test the progress dialog."""
        logger.info("Testing progress dialog")

        # Create a progress dialog
        progress = ProgressDialog(
            self.root,
            title="Test Progress",
            message="Testing progress dialog",
            max_value=10,
            determinate=True,
            cancelable=True
        )

        # Update the progress dialog
        for i in range(10):
            progress.update(i, item=f"Item {i+1}")
            self.root.update()  # Update the Tkinter event loop

        # Close the progress dialog
        progress.close()

        logger.info("Progress dialog test completed")

    @patch('webbrowser.open')
    @patch('api.osm.osm_methods.MessageBoxes')
    def test_open_selected_in_cuzk(self, mock_message_boxes, mock_open):
        """Test opening selected buildings in CUZK."""
        logger.info("Testing open_selected_in_cuzk")

        # Set up the mock treeview to return our sample buildings
        self.app.osm_treeview.selection.return_value = ['1', '2', '3']
        self.app.osm_treeview.item.side_effect = lambda item_id, column=None: {
            '1': {'values': ['Building 1', 'Address 1', '123456', 'View/Fetch']},
            '2': {'values': ['Building 2', 'Address 2', '789012', 'View/Fetch']},
            '3': {'values': ['Building 3', 'Address 3', '345678', 'View/Fetch']}
        }[item_id]

        # Configure the mock message boxes to return 'yes'
        mock_message_boxes.ask_yes_no.return_value = 'yes'

        # Call the method
        self.osm_methods.open_selected_in_cuzk()

        # Verify that webbrowser.open was called for each building
        self.assertEqual(mock_open.call_count, 3, "webbrowser.open was not called 3 times")

        # Verify that the correct URLs were used
        called_urls = [call_args[0][0] for call_args in mock_open.call_args_list]
        for url, building in zip(called_urls, self.sample_buildings):
            self.assertTrue(building['ruian_ref'] in url, f"RUIAN ID {building['ruian_ref']} not found in URL {url}")

        logger.info("open_selected_in_cuzk test completed")

    @patch('api.cuzk.cuzk_integration.CUZKIntegration.get_property_by_ruian_id')
    @patch('api.osm.osm_methods.MessageBoxes')
    def test_fetch_owner_info_for_selected(self, mock_message_boxes, mock_get_property):
        """Test fetching owner information for selected buildings."""
        logger.info("Testing fetch_owner_info_for_selected")

        # Set up the mock treeview to return our sample buildings
        self.app.osm_treeview.selection.return_value = ['1', '2', '3']
        self.app.osm_treeview.item.side_effect = lambda item_id, column=None: {
            '1': {'values': ['Building 1', 'Address 1', '123456', 'View/Fetch']},
            '2': {'values': ['Building 2', 'Address 2', '789012', 'View/Fetch']},
            '3': {'values': ['Building 3', 'Address 3', '345678', 'View/Fetch']}
        }[item_id]

        # Configure the mock message boxes to return 'yes'
        mock_message_boxes.ask_yes_no.return_value = 'yes'

        # Configure the mock get_property_by_ruian_id to return sample property data
        mock_get_property.side_effect = lambda ruian_id: {
            'ruian_id': ruian_id,
            'owner_name': f'Owner of {ruian_id}',
            'owner_address': f'Address of {ruian_id}',
            'property_type': 'Building',
            'parcel_number': f'Parcel {ruian_id}',
            'cadastral_territory': 'Prague',
            'ownership_share': '1/1',
            'lv_number': f'LV{ruian_id}'
        }

        # Set up the app with a property display
        self.app.property_display = MagicMock()
        self.app.property_list = []

        # Call the method
        self.osm_methods.fetch_owner_info_for_selected()

        # Verify that get_property_by_ruian_id was called for each building
        self.assertEqual(mock_get_property.call_count, 3, "get_property_by_ruian_id was not called 3 times")

        # Verify that the property list was updated
        self.assertEqual(len(self.app.property_list), 3, "Property list does not contain 3 items")

        # Verify that the property display was updated
        self.app.property_display.display_property_details.assert_called_once()

        logger.info("fetch_owner_info_for_selected test completed")


if __name__ == '__main__':
    unittest.main()
