"""
Run all tests for the Czech Property Registry application.

This script runs all the tests for the application,
including unit tests and integration tests.
"""

import os
import sys
import unittest
import logging

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TestRunner")


def run_all_tests():
    """Run all tests for the application."""
    logger.info("Running all tests...")
    
    # Discover and run all tests
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover(os.path.dirname(__file__), pattern="test_*.py")
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Log the results
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Failures: {len(result.failures)}")
    logger.info(f"Skipped: {len(result.skipped)}")
    
    # Return True if all tests passed
    return len(result.errors) == 0 and len(result.failures) == 0


def run_specific_test(test_module):
    """
    Run a specific test module.
    
    Args:
        test_module (str): Name of the test module to run
    """
    logger.info(f"Running test module: {test_module}")
    
    # Import the test module
    module_name = f"tests.{test_module}"
    __import__(module_name)
    
    # Get the module
    module = sys.modules[module_name]
    
    # Run the tests in the module
    test_loader = unittest.TestLoader()
    test_suite = test_loader.loadTestsFromModule(module)
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Log the results
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Failures: {len(result.failures)}")
    logger.info(f"Skipped: {len(result.skipped)}")
    
    # Return True if all tests passed
    return len(result.errors) == 0 and len(result.failures) == 0


if __name__ == '__main__':
    # Check if a specific test was requested
    if len(sys.argv) > 1:
        test_module = sys.argv[1]
        success = run_specific_test(test_module)
    else:
        success = run_all_tests()
    
    # Exit with the appropriate code
    sys.exit(0 if success else 1)
