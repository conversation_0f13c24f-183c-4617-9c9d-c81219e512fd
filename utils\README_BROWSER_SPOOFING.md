# Browser Spoofing for CUZK Website

This document explains the browser spoofing functionality implemented to avoid triggering CAPTCHA mechanisms on the CUZK website.

## Problem

When making automated requests to the CUZK website (nahlizenidokn.cuzk.cz), the website may detect bot-like behavior and present a more difficult CAPTCHA challenge. This makes it harder to retrieve property information programmatically.

## Solution

We've implemented a browser spoofing mechanism that makes our requests appear more like they're coming from a real human user. This helps avoid triggering the stricter CAPTCHA mechanisms.

## Implementation Details

The browser spoofing functionality is implemented in the `BrowserSpoofer` class in `utils/browser_spoofer.py`. This class provides methods for:

1. Creating requests sessions with realistic browser headers
2. Rotating user agents to appear as different browsers
3. Adding random delays between requests to mimic human behavior
4. Handling cookies and other browser characteristics

## How to Use

### Creating a Spoofed Session

```python
from utils.browser_spoofer import BrowserSpoofer

# Create a session with browser spoofing
session = BrowserSpoofer.create_spoofed_session(
    retry_total=3,
    backoff_factor=0.5,
    status_forcelist=[429, 500, 502, 503, 504],
    rotate_user_agent=True
)

# Use the session for making requests
response = session.get("https://nahlizenidokn.cuzk.cz/")
```

### Adding Human-like Delays

```python
from utils.browser_spoofer import BrowserSpoofer

# Add a random delay for navigation (2-5 seconds)
BrowserSpoofer.add_human_like_delay("navigation", verbose=True)

# Add a random delay for form filling (0.5-2 seconds)
BrowserSpoofer.add_human_like_delay("form_fill", verbose=True)

# Add a random delay for clicking (0.2-1 seconds)
BrowserSpoofer.add_human_like_delay("click", verbose=True)

# Add a random delay for reading content (3-8 seconds)
BrowserSpoofer.add_human_like_delay("read", verbose=True)
```

## Best Practices

To avoid triggering CAPTCHA mechanisms on the CUZK website, follow these best practices:

1. **Use the BrowserSpoofer**: Always use the `BrowserSpoofer` class when making requests to the CUZK website.

2. **Add Delays Between Requests**: Use the `add_human_like_delay` method to add realistic delays between requests.

3. **Limit Batch Requests**: When opening multiple CUZK URLs in the browser, limit the number of tabs opened at once and add delays between opening tabs.

4. **Rotate User Agents**: Use the `rotate_user_agent=True` parameter when creating a spoofed session to appear as different browsers.

5. **Handle Cookies**: The spoofed session automatically handles cookies, which helps maintain a session with the website.

## Example: Opening Multiple CUZK URLs

```python
def open_batch_cuzk_in_browser(center_lat, center_lng, radius, num_points=9, max_tabs=5):
    import random
    import time
    import webbrowser
    from utils.browser_spoofer import BrowserSpoofer
    
    # Generate the batch of URLs
    points = generate_batch_cuzk_urls(center_lat, center_lng, radius, num_points)
    
    # Limit the number of tabs to open
    points_to_open = points[:min(max_tabs, len(points))]
    
    # Open each URL in the browser with a random delay
    for i, point in enumerate(points_to_open):
        # Add a longer delay between opening tabs
        if i > 0:
            BrowserSpoofer.add_human_like_delay("navigation", verbose=True)
        
        print(f"Opening URL: {point['url']}")
        webbrowser.open(point['url'])
    
    return points
```

## Troubleshooting

If you still encounter CAPTCHA challenges despite using browser spoofing, try these solutions:

1. **Increase Delays**: Increase the delay between requests by modifying the delay ranges in `add_human_like_delay`.

2. **Reduce Batch Size**: Reduce the number of requests made in a short period of time.

3. **Add More User Agents**: Add more user agents to the `USER_AGENTS` list in the `BrowserSpoofer` class.

4. **Use a Proxy**: Consider using a proxy service to rotate IP addresses.

5. **Manual CAPTCHA Solving**: For critical operations, consider implementing a manual CAPTCHA solving step where the user is prompted to solve the CAPTCHA.
