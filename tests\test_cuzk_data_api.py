"""
Test the CUZK Data API client.
"""

import unittest
import os
import sys
import json
from unittest.mock import MagicMock, patch

# Add the parent directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the CUZK Data API client
from api.cuzk.cuzk_data_api import CUZKDataAPI


class TestCUZKDataAPI(unittest.TestCase):
    """Test the CUZK Data API client."""

    def setUp(self):
        """Set up the test."""
        # Create a mock app
        self.mock_app = MagicMock()
        self.mock_app.session = MagicMock()

        # Create the API client
        self.api = CUZKDataAPI(self.mock_app)

        # Create the cache directory if it doesn't exist
        os.makedirs("data/api_cache", exist_ok=True)

    def test_property_types(self):
        """Test the property types list."""
        # Get property types
        property_types = self.api.get_property_types()

        # Check that we have a list of property types
        self.assertIsInstance(property_types, list)
        self.assertGreater(len(property_types), 0)

        # Check that common property types are included
        self.assertIn('Bytový dům', property_types)
        self.assertIn('Rodinný dům', property_types)
        self.assertIn('Garáž', property_types)

    def test_translate_region_to_english(self):
        """Test the region translation function."""
        # Test some translations
        self.assertEqual(self.api._translate_region_to_english('Praha'), 'Prague')
        self.assertEqual(self.api._translate_region_to_english('Středočeský kraj'), 'Central Bohemian Region')
        self.assertEqual(self.api._translate_region_to_english('Unknown Region'), 'Unknown Region')

    def test_fallback_regions(self):
        """Test the fallback regions function."""
        # Create a temporary regions file
        os.makedirs('czech_data', exist_ok=True)
        with open('czech_data/regions.json', 'w', encoding='utf-8') as f:
            json.dump({
                "regions_czech": ["Praha", "Středočeský kraj"],
                "regions_english": ["Prague", "Central Bohemian Region"],
                "region_codes": ["1", "2"]
            }, f)

        # Call the fallback function
        regions = self.api._fallback_regions()

        # Check the results
        self.assertEqual(len(regions), 2)
        self.assertEqual(regions[0]['name_czech'], 'Praha')
        self.assertEqual(regions[0]['name_english'], 'Prague')
        self.assertEqual(regions[0]['code'], '1')

        # Clean up
        os.remove('czech_data/regions.json')


if __name__ == '__main__':
    unittest.main()
