"""
CAPTCHA handling utilities for the Czech Property Registry application.

This module provides utilities for handling CAPTCHA challenges on the CUZK website.
It integrates with the CaptchaSolver service to provide multiple solving methods.
"""

import tkinter as tk
from tkinter import ttk, simpledialog
import tempfile
import os
import logging
from PIL import Image, ImageTk
from ui.message_boxes import MessageBoxes

# Try to import the CaptchaSolver service
try:
    from services.captcha_solver import CaptchaSolver
    CAPTCHA_SOLVER_AVAILABLE = True
except ImportError:
    CAPTCHA_SOLVER_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)


class CaptchaHandler:
    """
    Handles CAPTCHA challenges from the CUZK website.
    """

    def __init__(self, app):
        """
        Initialize the CAPTCHA handler.

        Args:
            app: The main application instance
        """
        self.app = app
        self.captcha_solution = None

        # Initialize the CAPTCHA solver if available
        self.captcha_solver = None
        if CAPTCHA_SOLVER_AVAILABLE:
            try:
                self.captcha_solver = CaptchaSolver(app)
                logger.info("CAPTCHA solver initialized")
            except Exception as e:
                logger.error(f"Error initializing CAPTCHA solver: {e}")
        else:
            logger.warning("CAPTCHA solver not available")

    def is_captcha_page(self, soup):
        """
        Check if the current page contains a CAPTCHA

        Args:
            soup (BeautifulSoup): BeautifulSoup object of the page

        Returns:
            bool: True if the page contains a CAPTCHA, False otherwise
        """
        # Check for common CAPTCHA indicators
        captcha_indicators = [
            # Image CAPTCHA
            soup.find('img', {'id': lambda x: x and 'captcha' in x.lower()}),
            soup.find('img', {'src': lambda x: x and 'captcha' in x.lower()}),
            # Text mentioning CAPTCHA
            soup.find(string=lambda text: text and 'captcha' in text.lower()),
            # Input field for CAPTCHA
            soup.find('input', {'id': lambda x: x and 'captcha' in x.lower()}),
            # reCAPTCHA
            soup.find('div', {'class': 'g-recaptcha'})
        ]

        return any(captcha_indicators)

    def show_captcha_dialog(self, captcha_image_path):
        """
        Show a dialog with the CAPTCHA image and ask the user to solve it.

        Args:
            captcha_image_path (str): Path to the CAPTCHA image file

        Returns:
            str: The user's solution to the CAPTCHA
        """
        # Create a dialog window
        dialog = tk.Toplevel(self.app.root)
        dialog.title("CAPTCHA Verification")
        dialog.geometry("400x300")
        dialog.transient(self.app.root)
        dialog.grab_set()

        # Add a label with instructions
        ttk.Label(
            dialog,
            text="Please enter the characters shown in the image below:",
            wraplength=380,
            justify="center"
        ).pack(pady=10)

        # Load and display the CAPTCHA image
        try:
            img = Image.open(captcha_image_path)
            img = img.resize((200, 80), Image.LANCZOS)  # Resize for better visibility
            photo = ImageTk.PhotoImage(img)

            image_label = ttk.Label(dialog, image=photo)
            image_label.image = photo  # Keep a reference to prevent garbage collection
            image_label.pack(pady=10)
        except Exception as e:
            print(f"Error displaying CAPTCHA image: {e}")
            ttk.Label(
                dialog,
                text="Error displaying CAPTCHA image. Please enter the CAPTCHA from the browser.",
                wraplength=380,
                foreground="red"
            ).pack(pady=10)

        # Add an entry field for the CAPTCHA solution
        captcha_entry = ttk.Entry(dialog, width=20, font=("Arial", 12))
        captcha_entry.pack(pady=10)
        captcha_entry.focus_set()

        # Add buttons for submitting or canceling
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10, fill="x")

        # Function to handle the submit button
        def on_submit():
            self.captcha_solution = captcha_entry.get().strip()
            self.app.captcha_solution = self.captcha_solution  # Store in the app for compatibility
            dialog.destroy()

        # Function to handle the cancel button
        def on_cancel():
            self.captcha_solution = None
            self.app.captcha_solution = None  # Store in the app for compatibility
            dialog.destroy()

        # Function to handle the open in browser button
        def open_in_browser():
            import webbrowser
            if hasattr(self.app, 'original_captcha_url') and self.app.original_captcha_url:
                webbrowser.open(self.app.original_captcha_url)
            else:
                webbrowser.open("https://nahlizenidokn.cuzk.cz/")

        # Add the buttons
        submit_btn = ttk.Button(button_frame, text="Submit", command=on_submit)
        submit_btn.pack(side="left", padx=5, expand=True, fill="x")

        browser_btn = ttk.Button(button_frame, text="Open in Browser", command=open_in_browser)
        browser_btn.pack(side="left", padx=5, expand=True, fill="x")

        cancel_btn = ttk.Button(button_frame, text="Cancel", command=on_cancel)
        cancel_btn.pack(side="left", padx=5, expand=True, fill="x")

        # Bind the Enter key to submit
        captcha_entry.bind("<Return>", lambda _: on_submit())

        # Wait for the dialog to be closed
        dialog.wait_window()

        return self.captcha_solution

    def handle_captcha(self, soup, current_url=None):
        """
        Handle CAPTCHA by using the CaptchaSolver service or falling back to manual methods.

        Args:
            soup (BeautifulSoup): BeautifulSoup object of the page with CAPTCHA
            current_url (str, optional): The URL of the current page with the CAPTCHA

        Returns:
            bool: True if CAPTCHA was successfully handled, False otherwise
        """
        try:
            # Store the current URL for CAPTCHA submission
            if current_url:
                # Make sure we have the full URL
                if current_url.startswith('/'):
                    self.app.captcha_url = f"https://nahlizenidokn.cuzk.cz{current_url}"
                elif not current_url.startswith('http'):
                    self.app.captcha_url = f"https://nahlizenidokn.cuzk.cz/{current_url.lstrip('/')}"
                else:
                    self.app.captcha_url = current_url
            else:
                # Try to get the URL from the form action
                form = soup.find('form', {'id': 'Form1'})
                if form and 'action' in form.attrs:
                    action = form['action']
                    if action.startswith('/'):
                        self.app.captcha_url = f"https://nahlizenidokn.cuzk.cz{action}"
                    elif not action.startswith('http'):
                        self.app.captcha_url = f"https://nahlizenidokn.cuzk.cz/{action.lstrip('/')}"
                    else:
                        self.app.captcha_url = action
                else:
                    # If we can't find the form action, try to find the current page URL
                    # Look for a canonical link
                    canonical = soup.find('link', {'rel': 'canonical'})
                    if canonical and 'href' in canonical.attrs:
                        self.app.captcha_url = canonical['href']
                    else:
                        # Default to the main CUZK page if no URL is provided
                        self.app.captcha_url = "https://nahlizenidokn.cuzk.cz/"

            # Store the original URL for opening in browser
            self.app.original_captcha_url = self.app.captcha_url

            # Determine which solving methods to use based on settings
            solving_methods = []

            # Check if GPT is enabled for CAPTCHA solving
            if hasattr(self.app, 'use_gpt_captcha') and self.app.use_gpt_captcha.get():
                solving_methods.append('gpt')

            # Check if Tesseract is enabled for CAPTCHA solving
            if hasattr(self.app, 'use_tesseract_captcha') and self.app.use_tesseract_captcha.get():
                solving_methods.append('tesseract')

            # Always add manual as a fallback
            solving_methods.append('manual')

            logger.info(f"CAPTCHA solving methods: {solving_methods}")

            # Find the CAPTCHA image
            captcha_img = soup.find('img', {'id': lambda x: x and 'captcha' in x.lower()})
            if not captcha_img:
                captcha_img = soup.find('img', {'src': lambda x: x and 'captcha' in x.lower()})

            if captcha_img and 'src' in captcha_img.attrs:
                # Get the CAPTCHA image URL
                captcha_url = captcha_img['src']
                if not captcha_url.startswith('http'):
                    # Make relative URL absolute
                    captcha_url = f"https://nahlizenidokn.cuzk.cz/{captcha_url.lstrip('/')}"

                # Download the CAPTCHA image
                try:
                    captcha_response = self.app.session.get(captcha_url)
                    if captcha_response.status_code == 200:
                        # Use the CAPTCHA solver if available
                        if self.captcha_solver:
                            logger.info("Using CAPTCHA solver service")
                            captcha_solution = self.captcha_solver.solve(
                                captcha_response.content,
                                methods=solving_methods
                            )

                            if captcha_solution:
                                logger.info(f"CAPTCHA solved: {captcha_solution}")
                                # Show a message that the CAPTCHA was solved
                                self.app.show_status(f"CAPTCHA solved: {captcha_solution}")
                                # Store the CAPTCHA solution for use in the next request
                                self.captcha_solution = captcha_solution
                                self.app.captcha_solution = captcha_solution  # For compatibility
                                return True
                        else:
                            # Fall back to the old method if the solver is not available
                            logger.warning("CAPTCHA solver not available, falling back to legacy method")

                            # Save the CAPTCHA image to a temporary file
                            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                                temp_file.write(captcha_response.content)
                                captcha_image_path = temp_file.name

                            # Try to solve the CAPTCHA with GPT if enabled
                            captcha_solution = None
                            if 'gpt' in solving_methods and self.app.gpt:
                                try:
                                    logger.info("Attempting to solve CAPTCHA with GPT (legacy method)...")
                                    # Make sure we're passing the raw image content to GPT
                                    captcha_solution = self.app.gpt.solve_captcha(captcha_response.content)
                                    if captcha_solution:
                                        logger.info(f"GPT solved CAPTCHA: {captcha_solution}")
                                        # Show a message that GPT solved the CAPTCHA
                                        self.app.show_status(f"GPT solved CAPTCHA: {captcha_solution}")
                                        # Store the CAPTCHA solution for use in the next request
                                        self.captcha_solution = captcha_solution
                                        self.app.captcha_solution = captcha_solution  # For compatibility
                                        # Show a message box to inform the user
                                        MessageBoxes.show_info(
                                            "CAPTCHA Solved Automatically",
                                            f"GPT has automatically solved the CAPTCHA: {captcha_solution}\n\n"
                                            f"Continuing with the search..."
                                        )
                                        # Return True to indicate success
                                        return True
                                except Exception as e:
                                    logger.error(f"Error solving CAPTCHA with GPT: {e}")
                                    captcha_solution = None
                                    # Show a message about the error
                                    self.app.show_status(f"Error solving CAPTCHA with GPT: {e}")

                            # If GPT failed or is disabled, show the CAPTCHA to the user
                            if not captcha_solution:
                                # Show the CAPTCHA image to the user
                                captcha_solution = self.show_captcha_dialog(captcha_image_path)

                            # Clean up the temporary file
                            try:
                                os.unlink(captcha_image_path)
                            except:
                                pass

                            if captcha_solution:
                                # Store the CAPTCHA solution for use in the next request
                                self.captcha_solution = captcha_solution
                                self.app.captcha_solution = captcha_solution  # For compatibility
                                return True
                except Exception as e:
                    logger.error(f"Error downloading CAPTCHA image: {e}")

            # If we couldn't find or download the CAPTCHA image, ask the user to solve it manually
            MessageBoxes.show_info(
                "CAPTCHA Required",
                "A CAPTCHA is required to continue.\n\n"
                "The CUZK website will be opened in your browser.\n"
                "Please complete the CAPTCHA and then return to this application."
            )

            # Open the CUZK website in the browser
            import webbrowser
            webbrowser.open(self.app.original_captcha_url or "https://nahlizenidokn.cuzk.cz/")

            # Ask the user for the CAPTCHA solution
            captcha_solution = simpledialog.askstring(
                "CAPTCHA Solution",
                "Please enter the CAPTCHA solution from the browser:",
                parent=self.app.root
            )

            if captcha_solution:
                # Store the CAPTCHA solution for use in the next request
                self.captcha_solution = captcha_solution
                self.app.captcha_solution = captcha_solution  # For compatibility
                return True

            return False
        except Exception as e:
            logger.error(f"Error handling CAPTCHA: {e}")
            return False
