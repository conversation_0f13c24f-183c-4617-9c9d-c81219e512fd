"""
Optimized main entry point for the Czech Property Registry application.
Provides improved performance, memory usage, and resource management.
"""

import os
import sys
import time
import logging
import traceback
import argparse
from typing import Dict, Any, Optional, List, Tuple, Set, Union, Callable

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("OptimizedMain")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Czech Property Registry - Optimized")

    parser.add_argument(
        "--config",
        type=str,
        default="config.ini",
        help="Path to the configuration file"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )

    parser.add_argument(
        "--profile",
        action="store_true",
        help="Enable performance profiling"
    )

    parser.add_argument(
        "--memory-profile",
        action="store_true",
        help="Enable memory profiling"
    )

    return parser.parse_args()


def setup_logging(debug=False):
    """Set up logging with the specified level."""
    log_level = logging.DEBUG if debug else logging.INFO

    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    # Set log level for specific loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)

    logger.info(f"Logging configured with level: {logging.getLevelName(log_level)}")


def run_with_profiling(func, profile_output="profile_results.prof"):
    """Run a function with performance profiling."""
    import cProfile
    import pstats

    logger.info(f"Running with performance profiling. Output: {profile_output}")

    # Run with profiling
    profiler = cProfile.Profile()
    profiler.enable()

    try:
        func()
    finally:
        profiler.disable()

        # Save profiling results
        profiler.dump_stats(profile_output)

        # Print top 20 functions by cumulative time
        stats = pstats.Stats(profile_output)
        stats.strip_dirs().sort_stats('cumulative').print_stats(20)

        logger.info(f"Profiling results saved to {profile_output}")


def run_with_memory_profiling(func, memory_profile_output="memory_profile.dat"):
    """Run a function with memory profiling."""
    try:
        from memory_profiler import profile as memory_profile

        logger.info(f"Running with memory profiling. Output: {memory_profile_output}")

        # Create a wrapper function with memory profiling
        @memory_profile(precision=4, stream=open(memory_profile_output, 'w+'))
        def wrapped_func():
            return func()

        # Run the wrapped function
        return wrapped_func()
    except ImportError:
        logger.warning("memory_profiler not installed. Running without memory profiling.")
        return func()


def main():
    """Main entry point for the application."""
    # Parse command line arguments
    args = parse_arguments()

    # Set up logging
    setup_logging(args.debug)

    # Log startup information
    logger.info("Starting Czech Property Registry - Optimized")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current directory: {os.getcwd()}")

    try:
        # Import the app factory
        from core.app_factory import AppFactory

        # Define the run function
        def run_app():
            # Create and run the application
            app = AppFactory.create_app(app_type="optimized", config={"config_path": args.config})
            app.run()

        # Run with profiling if requested
        if args.profile:
            run_with_profiling(run_app)
        elif args.memory_profile:
            run_with_memory_profiling(run_app)
        else:
            # Run normally
            run_app()

        logger.info("Application exited normally")
        return 0
    except Exception as e:
        logger.error(f"Error starting application: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
