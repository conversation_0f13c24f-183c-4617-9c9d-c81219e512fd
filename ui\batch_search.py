"""
Batch search UI components for the Czech Property Registry application.

This module provides UI components for batch searching of properties
by type in a specific area, with support for smart batch processing.
Key features include:
- Smart batch processing with address search and radius selection
- Basic property search by type and location
- Property type filtering
- Results display and export
- Integration with Google Maps for address autocomplete
"""

import tkinter as tk
from tkinter import ttk
import json
import os
import logging
# Import necessary modules

from ui.message_boxes import MessageBoxes  # For displaying alerts and confirmations
from ui.property_type_filter import PropertyTypeFilter  # For filtering property types

# Set up logging
logger = logging.getLogger(__name__)


class BatchSearchUI:
    """
    UI components for batch property search functionality.

    This class creates and manages the UI elements for searching
    multiple properties by type in a specific area. It provides two main interfaces:

    1. Smart Batch Processing: Advanced search with address autocomplete and property filtering
    2. Basic Search: Simple search by property type and location

    The UI includes controls for selecting search parameters, displaying results,
    and performing actions on the found properties.
    """

    def __init__(self, parent_frame, app):
        """
        Initialize the batch search UI.

        Args:
            parent_frame: The parent frame to place the UI elements in
            app: The main application instance for callbacks and API access
        """
        self.parent = parent_frame  # Parent container for this UI
        self.app = app  # Main application with access to all services
        self.buildings_data = []  # Store building data for the smart processing tab

        # Create the UI elements (tabs, controls, etc.)
        self.create_ui()

        # Load property types from JSON file or use minimal set to prevent UI crashes
        self.property_types = self.load_property_types()

        # Populate the property type dropdown with available types
        self.populate_property_types()

        logger.info("BatchSearchUI initialized")

    def create_ui(self):
        """
        Create the batch search UI elements.

        Sets up a single frame for Smart Batch Processing with address autocomplete.
        """
        # Create the processing tab directly in the parent frame
        self.processing_tab = ttk.Frame(self.parent)
        self.processing_tab.pack(fill="both", expand=True, padx=5, pady=5)

        # Create the UI for the smart processing tab
        self.create_processing_tab()

    def create_search_tab(self):
        """
        Create the UI elements for the basic search tab.

        Sets up a form with fields for:
        - Property type selection
        - Location input
        - Search radius
        - Maximum results

        Also creates a results display area and action buttons.
        """
        # Create a frame for the batch search options
        self.options_frame = ttk.Frame(self.search_tab)
        self.options_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Add a description label
        ttk.Label(
            self.options_frame,
            text="Search for multiple properties of the same type in an area",
            wraplength=250,
            justify="center"
        ).pack(pady=5)

        # Create a grid for the input fields
        input_grid = ttk.Frame(self.options_frame)
        input_grid.pack(fill="x", padx=2, pady=2)

        # Property type selection
        ttk.Label(input_grid, text="Property Type:").grid(row=0, column=0, padx=2, pady=2, sticky="w")
        self.property_type_var = tk.StringVar()
        self.property_type_combo = ttk.Combobox(input_grid, textvariable=self.property_type_var, width=20)
        self.property_type_combo.grid(row=0, column=1, padx=2, pady=2, sticky="ew")

        # Location input
        ttk.Label(input_grid, text="Location:").grid(row=1, column=0, padx=2, pady=2, sticky="w")
        self.location_entry = ttk.Entry(input_grid, width=20)
        self.location_entry.grid(row=1, column=1, padx=2, pady=2, sticky="ew")

        # Radius input
        ttk.Label(input_grid, text="Radius (km):").grid(row=2, column=0, padx=2, pady=2, sticky="w")
        self.radius_var = tk.StringVar(value="2")
        radius_entry = ttk.Entry(input_grid, textvariable=self.radius_var, width=10)
        radius_entry.grid(row=2, column=1, padx=2, pady=2, sticky="w")

        # Maximum results input
        ttk.Label(input_grid, text="Max Results:").grid(row=3, column=0, padx=2, pady=2, sticky="w")
        self.max_results_var = tk.StringVar(value="50")
        max_results_entry = ttk.Entry(input_grid, textvariable=self.max_results_var, width=10)
        max_results_entry.grid(row=3, column=1, padx=2, pady=2, sticky="w")

        # Configure the grid to expand properly
        input_grid.columnconfigure(1, weight=1)

        # Add a separator
        ttk.Separator(self.options_frame, orient="horizontal").pack(fill="x", padx=5, pady=5)

        # Create a frame for the buttons
        button_frame = ttk.Frame(self.options_frame)
        button_frame.pack(fill="x", padx=2, pady=2)

        # Search button
        self.search_btn = ttk.Button(
            button_frame,
            text="Search Properties",
            command=self.on_search,
            style="Accent.TButton"
        )
        self.search_btn.pack(side="right", padx=2, pady=2)

        # Update Map button
        self.update_map_btn = ttk.Button(
            button_frame,
            text="Update Map",
            command=self.on_update_map
        )
        self.update_map_btn.pack(side="left", padx=2, pady=2)

        # Add a results section
        self.results_frame = ttk.LabelFrame(self.search_tab, text="Search Results")
        self.results_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Create a treeview for the results
        self.create_results_treeview()

        # Add action buttons for the results
        self.create_action_buttons()

    def create_processing_tab(self):
        """
        Create the UI elements for the smart processing tab.

        Sets up a paned window with:
        - Top section: Building selection with address search and autocomplete
        - Bottom section: Property type filter for selecting building types

        Also creates action buttons for processing selected buildings
        and a progress bar for tracking batch operations.
        """
        # Add a title and description at the top
        title_frame = ttk.Frame(self.processing_tab)
        title_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(
            title_frame,
            text="Czech Property Registry - Batch Search",
            font=("Arial", 14, "bold"),
            foreground="#4CAF50"
        ).pack(side="top", pady=5)

        ttk.Label(
            title_frame,
            text="Search for specific building types within a modifiable radius around a chosen address.",
            wraplength=600,
            justify="center"
        ).pack(side="top", pady=5)

        # Create a paned window to divide the tab into two sections
        self.processing_paned = ttk.PanedWindow(self.processing_tab, orient="vertical")
        self.processing_paned.pack(fill="both", expand=True, padx=5, pady=5)

        # Create the top frame for the building selection
        self.buildings_frame = ttk.LabelFrame(self.processing_paned, text="Step 1: Search for Buildings by Address")
        self.processing_paned.add(self.buildings_frame, weight=1)

        # Create the bottom frame for the property type filter
        self.filter_frame = ttk.LabelFrame(self.processing_paned, text="Step 2: Filter Buildings by Type")
        self.processing_paned.add(self.filter_frame, weight=1)

        # Create the building selection UI
        self.create_building_selection_ui()

        # Create the property type filter UI
        self.property_filter = PropertyTypeFilter(self.filter_frame, self.app, self.on_filter_changed)

        # Create a frame for the processing buttons
        self.processing_buttons_frame = ttk.Frame(self.processing_tab)
        self.processing_buttons_frame.pack(fill="x", padx=2, pady=2)

        # Process selected button
        self.process_btn = ttk.Button(
            self.processing_buttons_frame,
            text="Step 3: Process Selected Buildings",
            command=self.on_process_selected,
            state="disabled",
            style="Accent.TButton",
            width=30
        )
        self.process_btn.pack(side="right", padx=5, pady=5)

        # Select all button
        self.select_all_btn = ttk.Button(
            self.processing_buttons_frame,
            text="Select All",
            command=self.on_select_all_buildings,
            state="disabled"
        )
        self.select_all_btn.pack(side="left", padx=2, pady=2)

        # Deselect all button
        self.deselect_all_btn = ttk.Button(
            self.processing_buttons_frame,
            text="Deselect All",
            command=self.on_deselect_all_buildings,
            state="disabled"
        )
        self.deselect_all_btn.pack(side="left", padx=2, pady=2)

        # Create a progress bar for batch processing
        self.progress_frame = ttk.Frame(self.processing_tab)
        self.progress_frame.pack(fill="x", padx=2, pady=2)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            mode="determinate"
        )
        self.progress_bar.pack(fill="x", padx=2, pady=2)

        self.progress_label = ttk.Label(self.progress_frame, text="")
        self.progress_label.pack(side="left", padx=2, pady=2)

        # Add a "View Results" button
        self.view_results_btn = ttk.Button(
            self.progress_frame,
            text="View Results",
            command=self.on_view_results,
            state="disabled"
        )
        self.view_results_btn.pack(side="right", padx=2, pady=2)

    def create_building_selection_ui(self):
        """
        Create the UI elements for building selection.

        Sets up an address search form with Google Maps autocomplete
        and a treeview for displaying and selecting buildings.
        The address search includes radius selection for controlling
        the search area size around the specified address.
        """
        # Create a frame for the building selection controls
        controls_frame = ttk.Frame(self.buildings_frame)
        controls_frame.pack(fill="x", padx=2, pady=2)

        # Add a descriptive label at the top
        ttk.Label(
            self.buildings_frame,
            text="Search for buildings by address, filter by type, and select radius:",
            font=("", 10, "bold"),
            wraplength=400,
            justify="left"
        ).pack(fill="x", padx=5, pady=(5, 0), anchor="w")

        # Create a label for the building selection
        ttk.Label(
            controls_frame,
            text="Select buildings to process:",
            wraplength=250
        ).pack(side="left", padx=2, pady=2)

        # Create a refresh button
        self.refresh_btn = ttk.Button(
            controls_frame,
            text="Refresh Buildings",
            command=self.on_refresh_buildings
        )
        self.refresh_btn.pack(side="right", padx=2, pady=2)

        # Create an address search frame
        address_frame = ttk.LabelFrame(self.buildings_frame, text="Search by Address")
        address_frame.pack(fill="x", padx=2, pady=2, before=controls_frame)

        # Create a grid for the address input
        address_grid = ttk.Frame(address_frame)
        address_grid.pack(fill="x", padx=2, pady=2)

        # Address input with autocomplete
        ttk.Label(address_grid, text="Address:").grid(row=0, column=0, padx=2, pady=2, sticky="w")

        # Import the AutocompleteEntry class
        from utils.autocomplete import AutocompleteEntry

        # Create a function to get address suggestions
        def get_address_suggestions(text):
            # Use Mapy.cz for address suggestions if available
            if hasattr(self.app, 'mapycz_integration'):
                suggestions = self.app.mapycz_integration.get_place_predictions(text, components={"country": "cz"})
                if not suggestions and text and len(text) > 2:
                    # Show a status message when no suggestions are found
                    if hasattr(self.app, 'show_status'):
                        self.app.show_status(f"No address suggestions found for '{text}'")
                return suggestions
            # Fall back to Google Maps if Mapy.cz is not available
            elif hasattr(self.app, 'google_maps'):
                suggestions = self.app.google_maps.get_place_predictions(text, components={"country": "cz"})
                if not suggestions and text and len(text) > 2:
                    # Show a status message when no suggestions are found
                    if hasattr(self.app, 'show_status'):
                        self.app.show_status(f"No address suggestions found for '{text}'")
                return suggestions
            else:
                if hasattr(self.app, 'show_status'):
                    self.app.show_status("No geocoding API available")
                return []

        # Create the autocomplete entry
        self.address_entry = AutocompleteEntry(
            address_grid,
            autocomplete_function=get_address_suggestions,
            width=40
        )
        self.address_entry.grid(row=0, column=1, padx=2, pady=2, sticky="ew")

        # Add radius input with a more prominent UI
        radius_frame = ttk.LabelFrame(address_grid, text="Search Radius")
        radius_frame.grid(row=1, column=0, columnspan=2, padx=2, pady=5, sticky="ew")

        # Create a horizontal scale for radius selection
        self.address_radius_var = tk.StringVar(value="2")
        ttk.Label(radius_frame, text="Select radius around address (km):").pack(side="top", anchor="w", padx=5, pady=2)

        radius_control_frame = ttk.Frame(radius_frame)
        radius_control_frame.pack(fill="x", padx=5, pady=2)

        # Add a scale (slider) for radius selection
        radius_scale = ttk.Scale(
            radius_control_frame,
            from_=0.0,
            to=10.0,
            orient="horizontal",
            length=200,
            command=lambda v: self.address_radius_var.set(f"{float(v):.1f}")
        )
        radius_scale.set(2.0)  # Default value
        radius_scale.pack(side="left", fill="x", expand=True, padx=(0, 5))

        # Add an entry field for precise input
        radius_entry = ttk.Entry(radius_control_frame, textvariable=self.address_radius_var, width=5)
        radius_entry.pack(side="left", padx=2)
        ttk.Label(radius_control_frame, text="km").pack(side="left", padx=(0, 5))

        # Add common radius presets
        preset_frame = ttk.Frame(radius_frame)
        preset_frame.pack(fill="x", padx=5, pady=2)
        ttk.Label(preset_frame, text="Presets:").pack(side="left", padx=(0, 5))

        # Add a "City Only" button for radius 0
        ttk.Button(
            preset_frame,
            text="City Only",
            width=8,
            command=lambda: [self.address_radius_var.set("0.0"), radius_scale.set(0.0)]
        ).pack(side="left", padx=2)

        for preset in [0.5, 1, 2, 5, 10]:
            ttk.Button(
                preset_frame,
                text=f"{preset} km",
                width=5,
                command=lambda p=preset: [self.address_radius_var.set(str(p)), radius_scale.set(p)]
            ).pack(side="left", padx=2)

        # Create a search button with a more prominent style
        search_frame = ttk.Frame(address_grid)
        search_frame.grid(row=2, column=0, columnspan=2, padx=2, pady=5, sticky="ew")

        self.address_search_btn = ttk.Button(
            search_frame,
            text="Search for Buildings",
            command=self.on_address_search,
            style="Accent.TButton"  # Use an accent style if available
        )
        self.address_search_btn.pack(side="right", padx=5, pady=5)

        # Configure the grid to expand properly
        address_grid.columnconfigure(1, weight=1)

        # Create a treeview for the buildings
        self.create_buildings_treeview()

    def create_results_treeview(self):
        """
        Create the treeview for displaying search results.

        Sets up a treeview with columns for:
        - Address
        - Property type
        - Owner information

        Includes vertical and horizontal scrollbars and
        binds selection events to enable/disable action buttons.
        """
        # Create a frame for the treeview with scrollbars
        tree_frame = ttk.Frame(self.results_frame)
        tree_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Create the treeview
        columns = ("address", "type", "owner")
        self.results_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)

        # Define the column headings
        self.results_tree.heading("address", text="Address")
        self.results_tree.heading("type", text="Type")
        self.results_tree.heading("owner", text="Owner")

        # Define the column widths
        self.results_tree.column("address", width=150)
        self.results_tree.column("type", width=100)
        self.results_tree.column("owner", width=150)

        # Add vertical scrollbar
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=vsb.set)
        vsb.pack(side="right", fill="y")

        # Add horizontal scrollbar
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.results_tree.xview)
        self.results_tree.configure(xscrollcommand=hsb.set)
        hsb.pack(side="bottom", fill="x")

        # Pack the treeview
        self.results_tree.pack(side="left", fill="both", expand=True)

        # Bind selection event
        self.results_tree.bind("<<TreeviewSelect>>", self.on_result_selected)

    def create_action_buttons(self):
        """
        Create action buttons for the search results.

        Sets up buttons for:
        - Viewing property details
        - Generating letters to property owners
        - Exporting search results

        Buttons are initially disabled until results are selected.
        """
        # Create a frame for the action buttons
        action_frame = ttk.Frame(self.results_frame)
        action_frame.pack(fill="x", padx=2, pady=2)

        # View Details button
        self.view_btn = ttk.Button(
            action_frame,
            text="View Details",
            command=self.on_view_details,
            state="disabled"
        )
        self.view_btn.pack(side="left", padx=2, pady=2)

        # Generate Letters button
        self.generate_letters_btn = ttk.Button(
            action_frame,
            text="Generate Letters",
            command=self.on_generate_letters,
            state="disabled"
        )
        self.generate_letters_btn.pack(side="right", padx=2, pady=2)

        # Export Results button
        self.export_btn = ttk.Button(
            action_frame,
            text="Export Results",
            command=self.on_export_results,
            state="disabled"
        )
        self.export_btn.pack(side="right", padx=2, pady=2)

    def load_property_types(self):
        """
        Load property types from data manager or JSON file.

        Tries multiple sources in order:
        1. Data manager if available
        2. property_types_dict.json in czech_data directory
        3. property_types.json in czech_data directory
        4. Raise an error if no data is available

        Returns:
            dict: Dictionary of property types mapping IDs to display names
        """
        try:
            # Try to use the data manager if available
            if hasattr(self.app, 'data_manager'):
                try:
                    property_data = self.app.data_manager.load_czech_data('property_types')
                    if property_data and "property_types" in property_data:
                        property_types = property_data["property_types"]
                        # Convert list to dictionary
                        logger.info(f"Loaded {len(property_types)} property types from data manager")
                        return {f"type_{i}": prop_type for i, prop_type in enumerate(property_types)}
                except Exception as e:
                    logger.warning(f"Error loading property types from data manager: {e}")
                    # Continue to file-based loading

            # Try to load from the czech_data directory
            file_path = os.path.join("czech_data", "property_types_dict.json")
            if os.path.exists(file_path):
                with open(file_path, "r", encoding="utf-8") as f:
                    property_types = json.load(f)
                    logger.info(f"Loaded {len(property_types)} property types from {file_path}")
                    return property_types

            # Try the property_types.json file
            file_path = os.path.join("czech_data", "property_types.json")
            if os.path.exists(file_path):
                with open(file_path, "r", encoding="utf-8") as f:
                    property_data = json.load(f)

                    # Check if it's the new format with property_types key
                    if isinstance(property_data, dict) and "property_types" in property_data:
                        property_types = property_data["property_types"]
                        # Convert list to dictionary
                        logger.info(f"Loaded {len(property_types)} property types from {file_path}")
                        return {f"type_{i}": prop_type for i, prop_type in enumerate(property_types)}
                    # Check if it's the old format (just a list)
                    elif isinstance(property_data, list):
                        logger.info(f"Loaded {len(property_data)} property types from {file_path} (old format)")
                        return {f"type_{i}": prop_type for i, prop_type in enumerate(property_data)}
                    # It's already a dictionary
                    logger.info(f"Loaded {len(property_data)} property types from {file_path} (dictionary format)")
                    return property_data

            # If we get here, we couldn't load property types
            error_msg = "No property types data available"
            logger.error(error_msg)

            # Return a minimal set of property types to prevent UI crashes
            # This is not fallback data, but a last resort to prevent UI crashes
            logger.warning("Using minimal property types set to prevent UI crashes")
            return {
                "residential": "Residential Building",
                "commercial": "Commercial Building",
                "industrial": "Industrial Building",
                "apartment": "Apartment",
                "house": "House",
                "land": "Land",
                "other": "Other"
            }

        except Exception as e:
            logger.error(f"Error loading property types: {e}")

            # Return a minimal set of property types to prevent UI crashes
            # This is not fallback data, but a last resort to prevent UI crashes
            logger.warning("Using minimal property types set to prevent UI crashes")
            return {
                "residential": "Residential Building",
                "commercial": "Commercial Building",
                "industrial": "Industrial Building",
                "apartment": "Apartment",
                "house": "House",
                "land": "Land",
                "other": "Other"
            }

    def populate_property_types(self):
        """
        Populate property types for the property filter.

        Since we've removed the basic search tab with the property_type_combo,
        this method now only ensures the property types are loaded for the
        property filter component.
        """
        # We no longer need to populate a dropdown since we removed the basic search tab
        # The property filter component handles its own property type loading
        logger.info("Property types loaded for filter")

        # No need to do anything else since we're only using the property filter now

    def on_search(self):
        """
        Legacy method kept for compatibility.

        This method is no longer used since we've removed the basic search tab.
        It's kept for compatibility with any code that might call it.
        """
        # Show a message that this functionality has been removed
        MessageBoxes.show_info("Information", "Basic search has been removed. Please use the smart batch processing instead.")
        logger.info("Basic search method called but functionality has been removed")

    def on_update_map(self):
        """
        Legacy method kept for compatibility.

        This method is no longer used since we've removed the basic search tab.
        It's kept for compatibility with any code that might call it.
        """
        # Show a message that this functionality has been removed
        MessageBoxes.show_info("Information", "Basic search has been removed. Please use the smart batch processing instead.")
        logger.info("Update map method called but functionality has been removed")

    def on_result_selected(self, _):
        """
        Handle selection of a result in the treeview.

        Enables or disables action buttons based on whether
        any items are selected in the results treeview.

        Args:
            _ (Event): The event object (unused but required by tkinter)
        """
        # Enable the action buttons when a result is selected
        selected = self.results_tree.selection()
        if selected:
            self.view_btn.config(state="normal")
            self.generate_letters_btn.config(state="normal")
            self.export_btn.config(state="normal")
        else:
            self.view_btn.config(state="disabled")
            self.generate_letters_btn.config(state="disabled")
            self.export_btn.config(state="disabled")

    def on_view_details(self):
        """Handle the view details button click"""
        selected = self.results_tree.selection()
        if not selected:
            return

        # Get the selected item
        item_id = selected[0]
        item_data = self.results_tree.item(item_id, "values")

        # Call the application's view details method
        if hasattr(self.app, "view_property_details"):
            self.app.view_property_details(item_id, item_data)
        else:
            logger.warning("View details method not implemented in the application.")
            MessageBoxes.show_warning("Not Implemented", "View details functionality is not available.")

    def on_generate_letters(self):
        """Handle the generate letters button click"""
        selected = self.results_tree.selection()
        if not selected:
            return

        # Call the application's generate letters method
        if hasattr(self.app, "generate_batch_letters"):
            self.app.generate_batch_letters(selected)
        else:
            logger.warning("Generate letters method not implemented in the application.")
            MessageBoxes.show_warning("Not Implemented", "Generate letters functionality is not available.")

    def on_export_results(self):
        """Handle the export results button click"""
        # Call the application's export results method
        if hasattr(self.app, "export_batch_results"):
            self.app.export_batch_results()
        else:
            logger.warning("Export results method not implemented in the application.")
            MessageBoxes.show_warning("Not Implemented", "Export results functionality is not available.")

    def on_view_results(self):
        """Handle the view results button click"""
        # Show the results in a dialog or message box since we no longer have tabs
        if hasattr(self.app, 'property_list') and self.app.property_list:
            result_text = "Search Results:\n\n"
            for i, prop in enumerate(self.app.property_list):
                result_text += f"Property {i+1}:\n"
                result_text += f"Type: {prop.get('property_type', 'Building')}\n"
                result_text += f"Address: {prop.get('owner_address', 'Property Address')}\n"
                result_text += f"Owner: {prop.get('owner_name', 'Property Owner')}\n\n"

            MessageBoxes.show_info("Search Results", result_text)
        else:
            MessageBoxes.show_info("No Results", "No search results available.")

    def create_buildings_treeview(self):
        """Create the treeview for displaying buildings"""
        # Create a frame for the treeview with scrollbars
        tree_frame = ttk.Frame(self.buildings_frame)
        tree_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Create the treeview
        columns = ("name", "address", "ruian_id", "type")
        self.buildings_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8, selectmode="extended")

        # Define the column headings
        self.buildings_tree.heading("name", text="Building Name")
        self.buildings_tree.heading("address", text="Address")
        self.buildings_tree.heading("ruian_id", text="RUIAN ID")
        self.buildings_tree.heading("type", text="Building Type")

        # Define the column widths
        self.buildings_tree.column("name", width=150)
        self.buildings_tree.column("address", width=200)
        self.buildings_tree.column("ruian_id", width=100)
        self.buildings_tree.column("type", width=100)

        # Add vertical scrollbar
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.buildings_tree.yview)
        self.buildings_tree.configure(yscrollcommand=vsb.set)
        vsb.pack(side="right", fill="y")

        # Add horizontal scrollbar
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.buildings_tree.xview)
        self.buildings_tree.configure(xscrollcommand=hsb.set)
        hsb.pack(side="bottom", fill="x")

        # Pack the treeview
        self.buildings_tree.pack(side="left", fill="both", expand=True)

        # Bind selection event
        self.buildings_tree.bind("<<TreeviewSelect>>", self.on_building_selected)

    def update_results(self, results):
        """
        Update the results treeview with new data

        Args:
            results (list): List of property results to display
        """
        # Clear existing items
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Add new items
        for result in results:
            self.results_tree.insert("", "end", values=(
                result.get("address", ""),
                result.get("type", ""),
                result.get("owner", "")
            ))

        # Enable/disable the export button based on whether there are results
        if results:
            self.export_btn.config(state="normal")
        else:
            self.export_btn.config(state="disabled")

    def update_buildings_treeview(self, buildings):
        """
        Update the buildings treeview with new data.

        Clears existing items and populates the treeview with new buildings.
        Stores building data for later processing and formats display values.
        Called by batch search manager after search completes.

        Args:
            buildings (list): List of building dictionaries with OSM metadata
        """
        # Add debug logging
        logger.info(f"BatchSearchUI.update_buildings_treeview called with {len(buildings)} buildings")
        if buildings:
            logger.info(f"First building: {buildings[0]}")

        # Store the buildings
        self.buildings_data = buildings

        # Clear existing items
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)

        # Add new items
        for building in buildings:
            # Extract building details with better fallbacks
            name = building.get("name", "") or building.get("tags", {}).get("name", "Unnamed Building")
            address = building.get("address", "") or self._format_address_from_tags(building.get("tags", {}))

            # Get RUIAN ID with multiple fallbacks
            ruian_id = building.get("ruian_ref", "") or building.get("ruian_id", "") or building.get("tags", {}).get("ref:ruian", "")

            # Make sure we display the RUIAN ID even if it's nested in tags
            if not ruian_id and "tags" in building:
                for tag_key, tag_value in building["tags"].items():
                    if "ruian" in tag_key.lower() and tag_value:
                        ruian_id = tag_value
                        break

            building_type = building.get("building_type", "") or building.get("tags", {}).get("building", "unknown")

            # Insert the building into the treeview
            self.buildings_tree.insert("", "end", values=(
                name,
                address,
                ruian_id,  # This should now properly display the RUIAN ID
                building_type
            ), tags=(building_type,))

        # Enable the buttons if we have buildings
        if buildings:
            self.select_all_btn.config(state="normal")
            self.deselect_all_btn.config(state="normal")
        else:
            self.select_all_btn.config(state="disabled")
            self.deselect_all_btn.config(state="disabled")

        # Update the process button state
        self.update_process_button_state()

    def _format_address_from_tags(self, tags):
        """
        Format an address from OSM tags

        Args:
            tags (dict): Dictionary of OSM tags

        Returns:
            str: Formatted address
        """
        # Extract address components
        street = tags.get("addr:street", "")
        housenumber = tags.get("addr:housenumber", "")
        city = tags.get("addr:city", "")
        postcode = tags.get("addr:postcode", "")

        # Combine components
        address_parts = []
        if street and housenumber:
            address_parts.append(f"{street} {housenumber}")
        elif street:
            address_parts.append(street)

        if city:
            address_parts.append(city)

        if postcode:
            address_parts.append(postcode)

        # Return the formatted address
        return ", ".join(address_parts) if address_parts else "Unknown address"

    def on_building_selected(self, _):
        """
        Handle selection of a building in the treeview

        Args:
            _ (Event): The event object (unused but required by tkinter)
        """
        # Update the process button state
        self.update_process_button_state()

    def update_process_button_state(self):
        """Update the state of the process button based on selection"""
        # Enable the process button if at least one building is selected
        selected = self.buildings_tree.selection()
        if selected:
            self.process_btn.config(state="normal")
        else:
            self.process_btn.config(state="disabled")

    def on_select_all_buildings(self):
        """Select all buildings in the treeview"""
        # Get all items
        items = self.buildings_tree.get_children()

        # Select all items
        for item in items:
            self.buildings_tree.selection_add(item)

        # Update the process button state
        self.update_process_button_state()

    def on_deselect_all_buildings(self):
        """Deselect all buildings in the treeview"""
        # Remove all selections
        self.buildings_tree.selection_remove(self.buildings_tree.selection())

        # Update the process button state
        self.update_process_button_state()

    def on_filter_changed(self, selected_types):
        """
        Handle changes to the property type filter

        Args:
            selected_types (list): List of selected property type keys
        """
        # Get all items
        items = self.buildings_tree.get_children()

        # Get the selected types as display names
        selected_type_names = self.property_filter.get_selected_type_names()

        # Filter the items
        for item in items:
            # Get the building type
            building_type = self.buildings_tree.item(item, "values")[3]

            # Check if the building type is in the selected types
            if not selected_types or building_type.lower() in [t.lower() for t in selected_type_names]:
                # Show the item
                self.buildings_tree.item(item, tags=("visible",))
            else:
                # Hide the item and deselect it
                self.buildings_tree.item(item, tags=("hidden",))
                self.buildings_tree.selection_remove(item)

        # Update the process button state
        self.update_process_button_state()

    def on_address_search(self):
        """
        Handle the address search button click in the smart processing tab.

        Gets the address and radius from the entry fields, validates the radius,
        and calls the batch_search_by_address method to find properties.
        Shows a warning if the radius is not a valid number.

        This is the main entry point for the smart batch search functionality.
        """
        # Get the address from the address entry
        address = self.address_entry.get()

        if not address:
            MessageBoxes.show_warning("Missing Information", "Please enter an address to search for buildings.")
            return

        # Show a status message
        if hasattr(self.app, 'show_status'):
            self.app.show_status(f"Searching for buildings near {address}...")

        # Try to show the location on the map
        try:
            if hasattr(self.app, 'show_location_on_map'):
                self.app.show_location_on_map(address)
            elif hasattr(self.app, 'map_view'):
                if hasattr(self.app.map_view, 'show_location'):
                    self.app.map_view.show_location(address)
                elif hasattr(self.app.map_view, 'show_map_window') and hasattr(self.app.google_maps, 'geocode'):
                    # Try to geocode the address and show the map window
                    result = self.app.google_maps.geocode(address)
                    if result:
                        lat = result.get('lat')
                        lng = result.get('lng')
                        self.app.map_view.show_map_window(lat, lng, zoom=15)
                    else:
                        logger.warning(f"Could not geocode address: {address}")
                        MessageBoxes.show_warning("Geocoding Error", f"Could not find coordinates for address: {address}")
                else:
                    logger.warning("Map view does not have show_location method")
            else:
                logger.warning("Map view not initialized")
        except Exception as e:
            logger.error(f"Error showing location on map: {e}")
            # Continue with the search even if showing the map fails

        # Get the property type from the filter
        selected_types = self.property_filter.get_selected_types() if hasattr(self, 'property_filter') else []

        # Get the radius from the input field
        try:
            radius = float(self.address_radius_var.get())
        except ValueError:
            # Use a default radius of 2 km if the input is invalid
            radius = 2.0
            # Update the field with the default value
            self.address_radius_var.set("2")
            MessageBoxes.show_warning("Invalid Input", "Search radius must be a number. Using default value of 2 km.")

        # Use a default max results of 50
        max_results = 50

        # Call the application's batch search method with the address
        if hasattr(self.app, 'batch_search_by_address'):
            self.app.batch_search_by_address(address, selected_types, radius, max_results, self.update_buildings_treeview)
        elif hasattr(self.app, 'batch_search_manager') and hasattr(self.app.batch_search_manager, 'batch_search_by_address'):
            self.app.batch_search_manager.batch_search_by_address(address, selected_types, radius, max_results, self.update_buildings_treeview)
        elif hasattr(self.app, 'batch_search_properties'):
            # Fall back to the regular batch search method
            self.app.batch_search_properties("", address, radius, max_results)
        elif hasattr(self.app, 'batch_search_manager') and hasattr(self.app.batch_search_manager, 'batch_search_properties'):
            # Fall back to the regular batch search method
            self.app.batch_search_manager.batch_search_properties("", address, radius, max_results)
        else:
            logger.error("Batch search method not implemented in the application")
            MessageBoxes.show_error("Not Implemented", "Batch search functionality is not available.")

    def on_refresh_buildings(self):
        """Refresh the buildings list"""
        # Use the address from the address entry
        if hasattr(self, 'address_entry') and self.address_entry.get():
            self.on_address_search()
            return
        else:
            MessageBoxes.show_warning("Missing Information", "Please enter an address in the address field.")
            return

    def on_process_selected(self):
        """Process the selected buildings"""
        # Get the selected buildings
        selected_items = self.buildings_tree.selection()

        if not selected_items:
            MessageBoxes.show_warning("No Selection", "Please select at least one building to process.")
            return

        # Get the selected buildings data
        selected_buildings = []
        for item in selected_items:
            # Get the values from the treeview
            values = self.buildings_tree.item(item, "values")

            # Find the corresponding building data
            for building in self.buildings_data:
                if building.get("ruian_ref", "") == values[2]:  # RUIAN ID is in the third column
                    selected_buildings.append(building)
                    break

        # Check if we found all the buildings
        if len(selected_buildings) != len(selected_items):
            MessageBoxes.show_warning("Data Error", "Some selected buildings could not be found in the data.")
            return

        # Reset the progress bar
        self.progress_var.set(0)
        self.progress_label.config(text="Processing buildings...")

        # Define the progress callback
        def progress_callback(current, total):
            """Update the progress bar"""
            progress = (current / total) * 100
            self.progress_var.set(progress)
            self.progress_label.config(text=f"Processing building {current} of {total}...")

        # Define the completion callback
        def completion_callback(processed_properties):
            """Handle completion of batch processing"""
            self.progress_label.config(text=f"Processed {len(processed_properties)} buildings successfully.")

            # Show a success message
            MessageBoxes.show_info("Processing Complete", f"Successfully processed {len(processed_properties)} buildings.")

            # Update the results
            self.update_results([{
                "address": prop.get("building_address", ""),
                "type": prop.get("building_type", ""),
                "owner": prop.get("owner_name", "Unknown")
            } for prop in processed_properties])

            # Enable the View Results button
            self.view_results_btn.config(state="normal")

            # Keep the user on the Smart Processing tab
            # Results will be visible in the results treeview when they click "View Results"

        # Call the app's process_selected_buildings method
        if hasattr(self.app, "process_selected_buildings"):
            self.app.process_selected_buildings(
                selected_buildings,
                progress_callback,
                completion_callback
            )
        elif hasattr(self.app, "batch_processing_manager") and hasattr(self.app.batch_processing_manager, "process_selected_buildings"):
            self.app.batch_processing_manager.process_selected_buildings(
                selected_buildings,
                progress_callback,
                completion_callback
            )
        else:
            logger.error("Batch processing functionality not available")
            MessageBoxes.show_error("Error", "Batch processing functionality is not available.")
