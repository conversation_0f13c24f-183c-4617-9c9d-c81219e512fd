"""
Font scaling utilities for the Czech Property Registry application.

This module provides functionality for scaling fonts based on screen resolution
and user preferences.
"""

import tkinter as tk
from tkinter import ttk, font
import platform
import os
# No fallback data is used - removed DemoData import


class FontScaler:
    """
    Manages font scaling for the application.

    This class provides methods to:
    - Calculate appropriate font sizes based on screen resolution
    - Apply font scaling to ttk styles
    - Save and load font preferences
    """

    # Default font sizes for different UI elements without using fallback data
    DEFAULT_SIZES = {
        "default": 10,
        "small": 8,
        "medium": 11,
        "large": 12,
        "title": 14,
        "header": 16
    }

    # Font families to use (in order of preference)
    FONT_FAMILIES = {
        "windows": ["Segoe UI", "Arial", "Helvetica", "sans-serif"],
        "darwin": ["SF Pro Text", "Helvetica Neue", "Helvetica", "Arial", "sans-serif"],
        "linux": ["Ubuntu", "DejaVu Sans", "Liberation Sans", "Arial", "sans-serif"]
    }

    def __init__(self, root, settings=None):
        """
        Initialize the font scaler.

        Args:
            root: The root Tk window
            settings: The application settings manager (optional)
        """
        self.root = root
        self.settings = settings
        self.scale_factor = 1.0
        self.font_family = self._get_system_font()

        # Calculate initial scale factor based on screen resolution
        self._calculate_scale_factor()

        # Load user preferences if settings are available
        if settings:
            self._load_preferences()

    def _get_system_font(self):
        """Get the appropriate font family for the current platform."""
        system = platform.system().lower()

        if system in self.FONT_FAMILIES:
            # Return the first available font from the preference list
            for font_name in self.FONT_FAMILIES[system]:
                if font_name in font.families():
                    return font_name

        # Fallback to system default
        return font.nametofont("TkDefaultFont").actual()["family"]

    def _calculate_scale_factor(self):
        """Calculate the scale factor based on screen resolution."""
        try:
            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # Calculate DPI scaling
            # Higher resolution screens need larger fonts
            if screen_width >= 3840:  # 4K
                self.scale_factor = 1.5
            elif screen_width >= 2560:  # 2K/QHD
                self.scale_factor = 1.3
            elif screen_width >= 1920:  # Full HD
                self.scale_factor = 1.1
            else:  # HD or lower
                self.scale_factor = 1.0

            # Adjust for high DPI displays on Windows
            if platform.system() == "Windows":
                try:
                    import ctypes
                    user32 = ctypes.windll.user32
                    user32.SetProcessDPIAware()
                    dpi = user32.GetDpiForSystem()
                    if dpi > 96:  # Standard DPI is 96
                        self.scale_factor *= dpi / 96
                except:
                    pass
        except:
            # Fallback to default if calculation fails
            self.scale_factor = 1.0

    def _load_preferences(self):
        """Load font preferences from settings."""
        if self.settings:
            try:
                # Load scale factor from settings
                saved_scale = self.settings.get('UI', 'font_scale', str(self.scale_factor))
                self.scale_factor = float(saved_scale)

                # Load font family from settings
                saved_family = self.settings.get('UI', 'font_family', self.font_family)
                if saved_family in font.families():
                    self.font_family = saved_family
            except:
                # Ignore errors and use defaults
                pass

    def save_preferences(self):
        """Save font preferences to settings."""
        if self.settings:
            self.settings.set('UI', 'font_scale', str(self.scale_factor))
            self.settings.set('UI', 'font_family', self.font_family)
            self.settings.save()

    def get_font(self, size_key="default", is_bold=False, is_italic=False):
        """
        Get a font tuple with the appropriate scaling.

        Args:
            size_key (str): The size key from DEFAULT_SIZES
            is_bold (bool): Whether the font should be bold
            is_italic (bool): Whether the font should be italic

        Returns:
            tuple: A font tuple (family, size, style)
        """
        # Get the base size
        base_size = self.DEFAULT_SIZES.get(size_key, self.DEFAULT_SIZES["default"])

        # Apply scaling
        scaled_size = int(base_size * self.scale_factor)

        # Ensure minimum size
        scaled_size = max(scaled_size, 7)

        # Determine style
        style = ""
        if is_bold:
            style += "bold"
        if is_italic:
            style += " italic" if style else "italic"

        return (self.font_family, scaled_size, style)

    def apply_to_style(self, style):
        """
        Apply font scaling to a ttk Style object.

        Args:
            style: A ttk.Style object
        """
        # Default font
        default_font = self.get_font("default")
        style.configure(".", font=default_font)

        # TLabel
        style.configure("TLabel", font=default_font)

        # TButton
        style.configure("TButton", font=self.get_font("medium"))

        # TEntry
        style.configure("TEntry", font=default_font)

        # TCombobox
        style.configure("TCombobox", font=default_font)

        # TNotebook
        style.configure("TNotebook.Tab", font=default_font)

        # Accent button
        style.configure("Accent.TButton", font=self.get_font("medium", is_bold=True))

        # Small text
        style.configure("Small.TLabel", font=self.get_font("small"))

        # Heading
        style.configure("Heading.TLabel", font=self.get_font("large", is_bold=True))

        # Title
        style.configure("Title.TLabel", font=self.get_font("title", is_bold=True))

    def increase_scale(self, amount=0.1):
        """
        Increase the font scale factor.

        Args:
            amount (float): The amount to increase the scale by
        """
        self.scale_factor += amount
        self.scale_factor = min(self.scale_factor, 2.0)  # Cap at 2.0
        self.save_preferences()

    def decrease_scale(self, amount=0.1):
        """
        Decrease the font scale factor.

        Args:
            amount (float): The amount to decrease the scale by
        """
        self.scale_factor -= amount
        self.scale_factor = max(self.scale_factor, 0.7)  # Minimum 0.7
        self.save_preferences()

    def reset_scale(self):
        """Reset the font scale factor to the default."""
        self._calculate_scale_factor()
        self.save_preferences()
