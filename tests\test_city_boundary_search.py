"""
Test for searching an entire city area for properties.

This test selects a random city from a list of Czech cities, finds its boundaries using Google Maps API,
generates coordinates within those boundaries, converts them to S-JTSK, and creates CUZK URLs.

The test will fail if it cannot get the actual city boundaries from Google Maps API.
No fallback mechanism is used - the test requires real city boundaries.

Usage:
    python tests/run_city_boundary_search_test.py [city_name] [grid_size]

Arguments:
    city_name (optional): Name of the city to search (e.g., "Prague")
    grid_size (optional): Size of the grid to generate (e.g., 20)

Examples:
    # Use random city and default grid size
    python tests/run_city_boundary_search_test.py

    # Specify city
    python tests/run_city_boundary_search_test.py "Prague"

    # Specify city and grid size
    python tests/run_city_boundary_search_test.py "Prague" 30
"""

import os
import sys
import unittest
import logging
import random
import time
import json
import requests
from datetime import datetime
from typing import Dict, List, Any

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CityBoundarySearchTest")

# Import necessary modules
try:
    from api.google_maps.google_maps_integration import GoogleMapsIntegration
    from api.google_maps.city_boundaries import get_city_boundaries
    from api.cuzk.cuzk_integration import CUZKIntegration
    from api.osm.osm_integration import OSMIntegration
except ImportError as e:
    logger.error(f"Import error: {e}")
    logger.error("Make sure you're running this test from the project root directory")
    sys.exit(1)


class TestCityBoundarySearch(unittest.TestCase):
    """Tests for searching an entire city area for properties."""

    def setUp(self):
        """Set up test environment."""
        # List of Czech cities to choose from
        self.czech_cities = [
            "Prague",
            "Brno",
            "Ostrava",
            "Plzeň",
            "Liberec",
            "Olomouc",
            "České Budějovice",
            "Hradec Králové",
            "Ústí nad Labem",
            "Pardubice"
        ]

        # Create a mock app for API integrations
        class MockApp:
            def __init__(self):
                self.show_status = lambda msg: logger.info(msg)
                self.cache_manager = None
                self.session = requests.Session()
                self.google_maps = None  # Will be set after creation
                self.cuzk_integration = None  # Will be set after creation
                self.osm_integration = None  # Will be set after creation

        self.mock_app = MockApp()

        # Initialize API integrations
        self.google_maps = GoogleMapsIntegration()
        self.cuzk_integration = CUZKIntegration()

        # Set references in mock app
        self.mock_app.google_maps = self.google_maps
        self.mock_app.cuzk_integration = self.cuzk_integration

        # Initialize OSM integration with the mock app
        self.osm_integration = OSMIntegration(self.mock_app)

        # Cache for API responses to avoid repeated calls
        self.cache = {
            'city_boundaries': {},
            'coordinates': {},
            'ruian_data': {}
        }

        # Parse command line arguments if provided
        self.city_name = None
        self.density = 100  # Default density

        # Get command line arguments if running directly
        if len(sys.argv) > 1 and not sys.argv[1].startswith('-'):
            self.city_name = sys.argv[1]

        if len(sys.argv) > 2 and sys.argv[2].isdigit():
            self.density = int(sys.argv[2])

    def select_random_city(self) -> str:
        """
        Select a random city from the list of Czech cities.

        Returns:
            str: Name of the selected city
        """
        return random.choice(self.czech_cities)

    def get_city_boundaries(self, city_name: str) -> Dict[str, float]:
        """
        Get the boundaries of a city using the city_boundaries utility.

        Args:
            city_name (str): Name of the city

        Returns:
            dict: Dictionary with bounds information (north, south, east, west)

        Raises:
            AssertionError: If boundaries couldn't be determined
        """
        logger.info(f"Getting boundaries for city: {city_name}")

        # Check cache first
        cache_key = f"boundaries_{city_name.lower().replace(' ', '_')}"
        if cache_key in self.cache['city_boundaries']:
            logger.info(f"Using cached boundaries for {city_name}")
            return self.cache['city_boundaries'][cache_key]

        # Get boundaries using the city_boundaries utility
        from api.google_maps.city_boundaries import get_city_boundaries as get_boundaries
        boundaries = get_boundaries(city_name, "Czech Republic", self.google_maps)

        if boundaries:
            logger.info(f"Found boundaries for {city_name}: {boundaries}")
            # Cache the result
            self.cache['city_boundaries'][cache_key] = boundaries
            return boundaries
        else:
            # No fallback - fail the test if we can't get boundaries
            error_msg = f"Could not determine boundaries for {city_name}. Test requires actual city boundaries."
            logger.error(error_msg)
            raise AssertionError(error_msg)

    def is_point_in_polygon(self, point, polygon):
        """
        Check if a point is inside a polygon using the ray casting algorithm.

        Args:
            point (tuple): (lat, lng) coordinates of the point
            polygon (list): List of (lat, lng) tuples representing the polygon vertices

        Returns:
            bool: True if the point is inside the polygon, False otherwise
        """
        x, y = point
        n = len(polygon)
        inside = False

        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def generate_coordinates_in_city(self, boundaries: Dict[str, float], density: int = 100) -> List[Dict[str, float]]:
        """
        Generate coordinates within the city boundaries.

        This method generates a dense grid of points and then filters out points that are outside the city boundaries.

        Args:
            boundaries (dict): Dictionary with bounds information (north, south, east, west)
            density (int): Density of the grid (higher values mean more points)

        Returns:
            list: List of dictionaries with lat and lng keys
        """
        logger.info(f"Generating coordinates within city boundaries with density {density}")

        # Extract boundaries
        north = boundaries['north']
        south = boundaries['south']
        east = boundaries['east']
        west = boundaries['west']

        # Calculate area dimensions
        width = east - west
        height = north - south

        # Calculate step sizes based on density
        # We want approximately density^2 points in the area
        lat_step = height / density
        lng_step = width / density

        # Generate dense grid of coordinates
        all_coordinates = []
        for i in range(density + 1):
            for j in range(density + 1):
                lat = south + i * lat_step
                lng = west + j * lng_step

                all_coordinates.append({
                    'lat': lat,
                    'lng': lng
                })

        logger.info(f"Generated {len(all_coordinates)} coordinates within city boundaries")
        return all_coordinates

    def convert_coordinates_and_create_urls(self, coordinates: List[Dict[str, float]]) -> List[Dict[str, Any]]:
        """
        Convert WGS84 coordinates to S-JTSK and create CUZK URLs.

        Args:
            coordinates (list): List of dictionaries with lat and lng keys

        Returns:
            list: List of dictionaries with coordinate and URL information
        """
        logger.info(f"Converting {len(coordinates)} coordinates and creating CUZK URLs")

        results = []
        for coord in coordinates:
            lat = coord['lat']
            lng = coord['lng']

            # Convert WGS84 to S-JTSK
            x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)

            # Ensure coordinates are integers
            x_int = int(x)
            y_int = int(y)

            # Generate CUZK URL
            url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"

            # Create result dictionary
            result = {
                'lat': lat,
                'lng': lng,
                'x_sjtsk': x_int,
                'y_sjtsk': y_int,
                'url': url
            }

            results.append(result)

            # Add a small delay to avoid overwhelming the API
            time.sleep(0.01)

        logger.info(f"Created {len(results)} CUZK URLs")
        return results

    def check_ruian_api(self, lat: float, lng: float) -> Dict[str, Any]:
        """
        Check if coordinates are within a building using RUIAN API.

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84

        Returns:
            dict: RUIAN data if available, empty dict otherwise
        """
        try:
            # Convert WGS84 to S-JTSK
            x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)
            x_int, y_int = int(x), int(y)

            # Create a cache key
            cache_key = f"ruian_{x_int}_{y_int}"

            # Check cache first
            if cache_key in self.cache['ruian_data']:
                return self.cache['ruian_data'][cache_key]

            # Add a small delay to avoid overwhelming the API
            time.sleep(0.1)

            # Create basic RUIAN data
            ruian_data = {
                'property_type': 'Building',
                'ruian_id': f"coord_{x_int}_{y_int}",
                'x_sjtsk': x_int,
                'y_sjtsk': y_int,
                'url': f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}",
                'data_sources': []
            }

            # Skip OSM integration for now as it requires a proper cache manager
            # This would normally try to get additional data from OSM if available
            # try:
            #     buildings = self.osm_integration.fetch_buildings_by_coordinates(lat, lng, 50)  # Small radius
            #     if buildings:
            #         ruian_data['osm_data'] = buildings[0]  # Use the first building found
            #         ruian_data['data_sources'].append('osm')
            # except Exception as e:
            #     logger.debug(f"Error fetching OSM data: {e}")

            # Just add a placeholder for now
            ruian_data['data_sources'].append('test')

            # Cache the result
            self.cache['ruian_data'][cache_key] = ruian_data

            return ruian_data

        except Exception as e:
            logger.error(f"Error checking RUIAN API: {e}")
            return {}

    def test_city_boundary_search(self):
        """
        Test searching an entire city area for properties.

        This test:
        1. Selects a random city or uses the one provided via command line
        2. Gets the city boundaries
        3. Generates coordinates within those boundaries
        4. Converts coordinates to S-JTSK
        5. Creates CUZK URLs
        6. Outputs the results
        """
        # Use provided city name or select a random one
        city_name = self.city_name if self.city_name else self.select_random_city()
        logger.info(f"Selected city: {city_name}")

        # Get city boundaries - will raise AssertionError if boundaries can't be determined
        try:
            boundaries = self.get_city_boundaries(city_name)
        except AssertionError as e:
            self.fail(str(e))

        # Generate coordinates within the city using the specified density
        coordinates = self.generate_coordinates_in_city(boundaries, density=self.density)
        self.assertGreater(len(coordinates), 0, "No coordinates generated")

        logger.info(f"Generated {len(coordinates)} coordinates within {city_name}")

        # Convert coordinates and create CUZK URLs
        results = self.convert_coordinates_and_create_urls(coordinates)
        self.assertEqual(len(results), len(coordinates), "Number of results doesn't match number of coordinates")

        # Check RUIAN API for each coordinate (sample only first 10 to avoid overwhelming the API)
        logger.info("Checking RUIAN API for sample coordinates...")
        sample_size = min(10, len(results))
        for i in range(sample_size):
            result = results[i]
            ruian_data = self.check_ruian_api(result['lat'], result['lng'])
            if ruian_data:
                results[i].update(ruian_data)

        # Output some sample results
        logger.info(f"Sample results for {city_name}:")
        for i, result in enumerate(results[:5]):  # Show first 5 results
            logger.info(f"  {i+1}. WGS84({result['lat']}, {result['lng']}) -> S-JTSK({result['x_sjtsk']}, {result['y_sjtsk']})")
            logger.info(f"     URL: {result['url']}")

        # Create timestamp for the output file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save results to a file
        output_file = f"city_boundary_search_{city_name.lower().replace(' ', '_')}_{timestamp}.json"
        with open(output_file, 'w') as f:
            json.dump({
                'city': city_name,
                'boundaries': boundaries,
                'density': self.density,
                'total_coordinates': len(results),
                'timestamp': timestamp,
                'results': results
            }, f, indent=2)

        logger.info(f"Saved {len(results)} results to {output_file}")

        # Verify that we have a reasonable number of results
        expected_min_results = self.density * self.density / 4  # At least a quarter of the total possible points
        self.assertGreaterEqual(len(results), expected_min_results,
                               f"Expected at least {expected_min_results} results for {city_name}, got {len(results)}")


if __name__ == '__main__':
    unittest.main()
