"""
Debug script to run the application with additional logging.
"""

import sys
import os
import traceback
import logging
import tkinter as tk

# Configure more verbose logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# Add the current directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import the application factory
    from core.app_factory import AppFactory

    # Enable tkinter debugging
    tk.Tk.report_callback_exception = lambda self, exc, val, tb: print(f"Tkinter Error: {exc}, {val}", file=sys.stderr)

    def main():
        """Main entry point for the application with debug logging."""
        print("Starting application in debug mode...")

        # Create the root window
        print("Creating root window...")
        root = tk.Tk()

        # Add error handler for tkinter
        def report_callback_exception(exc, val, tb):
            print(f"Tkinter Error: {exc}, {val}")
            traceback.print_tb(tb)

        root.report_callback_exception = report_callback_exception

        # Create the application
        print("Creating application instance...")
        try:
            # Create the application using the factory
            app = AppFactory.create_app(root=root, app_type="standard")

            # Start the main loop
            print("Starting main loop...")
            app.run()

            print("Application exited normally.")
        except Exception as e:
            print(f"Error during application initialization: {e}")
            traceback.print_exc()
            if root:
                root.destroy()

    if __name__ == "__main__":
        main()
except Exception as e:
    print(f"Error: {e}")
    print("Traceback:")
    traceback.print_exc()
