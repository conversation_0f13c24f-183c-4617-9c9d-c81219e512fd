#!/usr/bin/env python3
"""
Batch Search Coordinates Test Script

This script tests the batch search functionality by:
1. Selecting a random location from Google Maps API
2. Choosing a random radius
3. Finding all coordinates within that radius
4. Fetching RUIAN data for each coordinate when available
5. Outputting the coordinates and RUIAN data to the console

The script implements a caching mechanism to avoid repeated API calls for the same data.
Cache entries expire after a configurable time period (default: 1 hour).

Usage:
    python scripts/batch_search_coordinates_test.py [location] [radius] [options]

Arguments:
    location (optional): Location to search (e.g., "Prague")
    radius (optional): Search radius in kilometers (e.g., 2.0)

Options:
    --refresh-cache: Force refresh of all cached data
    --cache-expiry SECONDS: Cache expiry time in seconds (default: 3600)

Examples:
    # Use random location and radius
    python scripts/batch_search_coordinates_test.py

    # Specify location and radius
    python scripts/batch_search_coordinates_test.py "Prague" 1.5

    # Force cache refresh
    python scripts/batch_search_coordinates_test.py --refresh-cache

    # Set custom cache expiry time (10 minutes)
    python scripts/batch_search_coordinates_test.py --cache-expiry 600

If no location and radius are provided, a random location and radius will be used.
"""

import os
import sys
import logging
import random
import time
import math
import argparse
import pickle
import threading
import traceback
from typing import Dict, Any, Optional
from unittest.mock import MagicMock
from datetime import datetime

# Add the project root directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging with colors for better readability
class ColoredFormatter(logging.Formatter):
    """Custom formatter to add colors to log messages based on level."""

    COLORS = {
        'DEBUG': '\033[94m',  # Blue
        'INFO': '\033[92m',   # Green
        'WARNING': '\033[93m', # Yellow
        'ERROR': '\033[91m',   # Red
        'CRITICAL': '\033[91m\033[1m', # Bold Red
        'RESET': '\033[0m'    # Reset
    }

    def format(self, record):
        log_message = super().format(record)
        level_name = record.levelname
        if level_name in self.COLORS:
            return f"{self.COLORS[level_name]}{log_message}{self.COLORS['RESET']}"
        return log_message

# Configure logging with colored output
logger = logging.getLogger("BatchSearchCoordinatesTest")
logger.setLevel(logging.INFO)

# Remove any existing handlers
for handler in logger.handlers[:]:
    logger.removeHandler(handler)

# Create console handler with colored formatter
console_handler = logging.StreamHandler()
console_handler.setFormatter(ColoredFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(console_handler)

# Also create a file handler for persistent logs
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'batch_search_test.log')
file_handler = logging.FileHandler(log_file)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

logger.info(f"Starting batch search test script - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
logger.info(f"Log file: {log_file}")

# Define custom exceptions for better error handling
class BatchSearchError(Exception):
    """Base exception for batch search errors."""
    pass

class GeocodingError(BatchSearchError):
    """Exception raised for errors in the geocoding process."""
    pass

class RUIANDataError(BatchSearchError):
    """Exception raised for errors in fetching RUIAN data."""
    pass

class CacheError(BatchSearchError):
    """Exception raised for errors in cache operations."""
    pass


class SimpleCache:
    """A simple cache implementation with expiration."""

    def __init__(self, name: str, expiry_seconds: int = 3600):
        """
        Initialize the cache.

        Args:
            name: Name of the cache
            expiry_seconds: Time in seconds before cache entries expire (default: 1 hour)
        """
        self.name = name
        self.expiry_seconds = expiry_seconds
        self.cache = {}  # {key: (value, timestamp)}
        self.lock = threading.Lock()
        self.cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cache')

        # Create cache directory if it doesn't exist
        if not os.path.exists(self.cache_dir):
            try:
                os.makedirs(self.cache_dir)
                logger.info(f"Created cache directory: {self.cache_dir}")
            except Exception as e:
                logger.warning(f"Failed to create cache directory {self.cache_dir}: {e}")
                # Fall back to current directory
                self.cache_dir = os.path.dirname(os.path.abspath(__file__))

        self.cache_file = os.path.join(self.cache_dir, f"cache_{name}.pkl")

        # Stats
        self.hits = 0
        self.misses = 0
        self.errors = 0
        self.last_error = None

        # Load existing cache
        self.load_cache()

        # Log cache initialization
        logger.info(f"Initialized {name} cache with {len(self.cache)} entries")
        logger.info(f"Cache file: {self.cache_file}")
        logger.info(f"Cache expiry: {expiry_seconds} seconds ({expiry_seconds/3600:.1f} hours)")

    def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache.

        Args:
            key: Cache key

        Returns:
            The cached value or None if not found or expired
        """
        try:
            with self.lock:
                if key in self.cache:
                    value, timestamp = self.cache[key]
                    age = time.time() - timestamp

                    # Check if expired
                    if age <= self.expiry_seconds:
                        self.hits += 1
                        logger.debug(f"Cache hit for {key} (age: {age:.1f}s)")
                        return value
                    else:
                        # Remove expired entry
                        logger.info(f"Cache entry for {key} has expired (age: {age:.1f}s)")
                        del self.cache[key]
                        self.save_cache()

                self.misses += 1
                logger.debug(f"Cache miss for {key}")
                return None
        except Exception as e:
            self.errors += 1
            self.last_error = str(e)
            logger.warning(f"Error retrieving from cache: {e}")
            return None

    def set(self, key: str, value: Any) -> None:
        """
        Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
        """
        try:
            with self.lock:
                self.cache[key] = (value, time.time())
                logger.debug(f"Added entry to {self.name} cache: {key}")
                self.save_cache()
        except Exception as e:
            self.errors += 1
            self.last_error = str(e)
            logger.warning(f"Error setting cache value: {e}")
            # Try to save what we can
            try:
                self.save_cache()
            except:
                pass

    def clear(self) -> None:
        """Clear all cache entries."""
        try:
            with self.lock:
                old_size = len(self.cache)
                self.cache = {}
                self.save_cache()
                logger.info(f"Cleared {self.name} cache ({old_size} entries removed)")
        except Exception as e:
            self.errors += 1
            self.last_error = str(e)
            logger.error(f"Error clearing cache: {e}")

    def cleanup(self) -> int:
        """
        Remove expired entries from the cache.

        Returns:
            Number of entries removed
        """
        try:
            with self.lock:
                current_time = time.time()
                expired_keys = [k for k, (_, timestamp) in self.cache.items()
                               if current_time - timestamp > self.expiry_seconds]

                if not expired_keys:
                    logger.info(f"No expired entries found in {self.name} cache")
                    return 0

                # Remove expired entries
                for key in expired_keys:
                    del self.cache[key]

                # Save updated cache
                self.save_cache()

                logger.info(f"Removed {len(expired_keys)} expired entries from {self.name} cache")
                logger.info(f"Cache size after cleanup: {len(self.cache)} entries")

                return len(expired_keys)
        except Exception as e:
            self.errors += 1
            self.last_error = str(e)
            logger.error(f"Error cleaning up cache: {e}")
            return 0

    def save_cache(self) -> None:
        """Save the cache to disk."""
        try:
            # Create a temporary file first to avoid corruption
            temp_file = f"{self.cache_file}.tmp"
            with open(temp_file, 'wb') as f:
                cache_data = {
                    'cache': self.cache,
                    'timestamp': time.time(),
                    'stats': {
                        'hits': self.hits,
                        'misses': self.misses,
                        'errors': self.errors,
                        'size': len(self.cache),
                        'last_error': self.last_error
                    }
                }
                pickle.dump(cache_data, f)

            # Rename the temporary file to the actual cache file
            if os.path.exists(self.cache_file):
                os.remove(self.cache_file)
            os.rename(temp_file, self.cache_file)

            logger.debug(f"Saved {self.name} cache to {self.cache_file} ({len(self.cache)} entries)")
        except Exception as e:
            self.errors += 1
            self.last_error = str(e)
            logger.error(f"Error saving cache to {self.cache_file}: {e}")
            # Use traceback for more detailed error information
            logger.debug(traceback.format_exc())

    def load_cache(self) -> None:
        """Load the cache from disk."""
        try:
            if os.path.exists(self.cache_file):
                logger.info(f"Loading cache from {self.cache_file}")

                # Check file size to avoid loading corrupted files
                file_size = os.path.getsize(self.cache_file)
                if file_size == 0:
                    logger.warning(f"Cache file {self.cache_file} is empty, skipping load")
                    self.cache = {}
                    return

                # Load the cache file
                with open(self.cache_file, 'rb') as f:
                    data = pickle.load(f)

                    # Extract cache data
                    self.cache = data.get('cache', {})

                    # Extract stats
                    stats = data.get('stats', {})
                    self.hits = stats.get('hits', 0)
                    self.misses = stats.get('misses', 0)
                    self.errors = stats.get('errors', 0)
                    self.last_error = stats.get('last_error')

                    # Get cache timestamp
                    timestamp = data.get('timestamp', 0)
                    age = time.time() - timestamp
                    logger.info(f"Cache file age: {age:.1f} seconds ({age/3600:.1f} hours)")

                    # Cleanup expired entries
                    removed = self.cleanup()

                    # Log cache load summary
                    logger.info(f"Loaded {len(self.cache)} entries from {self.name} cache")
                    logger.info(f"Cache stats - Hits: {self.hits}, Misses: {self.misses}, Errors: {self.errors}")
            else:
                logger.info(f"No existing cache file found at {self.cache_file}")
                self.cache = {}
        except Exception as e:
            self.errors += 1
            self.last_error = str(e)
            logger.error(f"Error loading cache from {self.cache_file}: {e}")
            logger.debug(traceback.format_exc())
            self.cache = {}

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        with self.lock:
            return {
                'name': self.name,
                'size': len(self.cache),
                'hits': self.hits,
                'misses': self.misses,
                'errors': self.errors,
                'hit_ratio': self.hits / (self.hits + self.misses) if (self.hits + self.misses) > 0 else 0,
                'expiry_seconds': self.expiry_seconds,
                'last_error': self.last_error
            }

# Import necessary modules
try:
    from api.google_maps.google_maps_integration import GoogleMapsIntegration
    from api.cuzk.cuzk_integration import CUZKIntegration
    from api.osm.osm_integration import OSMIntegration
except ImportError as e:
    logger.error(f"Import error: {e}")
    logger.error("Make sure you're running this script from the project root directory")
    sys.exit(1)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test batch search functionality")
    parser.add_argument("location", nargs="?", default=None, help="Location to search (e.g., 'Prague')")
    parser.add_argument("radius", nargs="?", type=float, default=None, help="Search radius in kilometers (e.g., 2.0)")
    parser.add_argument("--refresh-cache", action="store_true", help="Force refresh of all cached data")
    parser.add_argument("--cache-expiry", type=int, default=3600, help="Cache expiry time in seconds (default: 3600)")
    return parser.parse_args()


def get_random_location():
    """Get a random location in the Czech Republic."""
    czech_locations = [
        "Prague",
        "Brno",
        "Ostrava",
        "Plzeň",
        "Liberec",
        "Olomouc",
        "České Budějovice",
        "Hradec Králové",
        "Ústí nad Labem",
        "Pardubice"
    ]
    return random.choice(czech_locations)


def get_random_radius():
    """Get a random radius between 0.5 and 3.0 km."""
    return round(random.uniform(0.5, 3.0), 1)


def fetch_ruian_data(lat: float, lng: float, x_int: int, y_int: int, osm_integration=None, cuzk_integration=None, osm_cache=None, cuzk_cache=None) -> Dict[str, Any]:
    """
    Fetch RUIAN data for the given coordinates.

    Args:
        lat: Latitude in WGS84
        lng: Longitude in WGS84
        x_int: X coordinate in S-JTSK
        y_int: Y coordinate in S-JTSK
        osm_integration: OSM integration instance
        cuzk_integration: CUZK integration instance
        osm_cache: Cache for OSM data
        cuzk_cache: Cache for CUZK data

    Returns:
        Dict with RUIAN data
    """
    start_time = time.time()
    logger.debug(f"Fetching RUIAN data for coordinates: WGS84({lat}, {lng}), S-JTSK({x_int}, {y_int})")

    # Initialize with basic data
    ruian_data = {
        'property_type': 'Building',
        'ruian_id': f"coord_{x_int}_{y_int}",  # Use coordinates as a pseudo-RUIAN ID
        'x_sjtsk': x_int,
        'y_sjtsk': y_int,
        'url': f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}",
        'data_sources': []  # Track which sources provided data
    }

    # Try to get RUIAN data from OSM if available
    if osm_integration:
        osm_start_time = time.time()
        logger.debug(f"Attempting to fetch OSM data for coordinates: {lat}, {lng}")

        try:
            # Create a cache key based on coordinates and radius
            # Round coordinates to 5 decimal places for better cache hits
            radius = 100  # Small radius to find buildings at this exact point
            lat_rounded = round(float(lat), 5)
            lng_rounded = round(float(lng), 5)
            cache_key = f"{lat_rounded}_{lng_rounded}_{radius}"

            # Check cache first if available
            buildings = None
            cache_hit = False

            if osm_cache:
                try:
                    buildings = osm_cache.get(cache_key)
                    if buildings is not None:
                        cache_hit = True
                        logger.info(f"Using cached OSM buildings for {cache_key}")
                except CacheError as ce:
                    logger.warning(f"Cache error when retrieving OSM data: {ce}")

            # If not in cache or cache not available, fetch from OSM
            if buildings is None:
                logger.info(f"Fetching buildings from OpenStreetMap at coordinates {lat}, {lng} with radius {radius}m...")

                # Add a small delay to avoid overwhelming the OSM API
                time.sleep(0.1)

                buildings = osm_integration.fetch_buildings_by_coordinates(lat, lng, radius)

                # Cache the results if cache is available
                if osm_cache and buildings is not None:
                    try:
                        osm_cache.set(cache_key, buildings)
                        logger.debug(f"Cached OSM data for {cache_key}")
                    except CacheError as ce:
                        logger.warning(f"Cache error when storing OSM data: {ce}")

            # Process the buildings data
            if buildings and len(buildings) > 0:
                # Get the first building with a RUIAN reference
                building = buildings[0]

                # Extract RUIAN reference if available
                ruian_ref = building.get('ruian_ref', '')

                # Update the data with OSM information
                ruian_data.update({
                    'ruian_ref': ruian_ref,
                    'osm_id': building.get('osm_id', ''),
                    'osm_type': building.get('osm_type', ''),
                    'building_type': building.get('building_type', 'unknown'),
                    'name': building.get('name', ''),
                    'address': building.get('address', ''),
                    'source': 'osm'
                })

                # Add OSM to data sources
                ruian_data['data_sources'].append('osm')

                # Log success with details
                if ruian_ref:
                    logger.info(f"  Found RUIAN reference in OSM: {ruian_ref}")
                else:
                    logger.info(f"  Found building in OSM but no RUIAN reference")

                # Log additional building details at debug level
                logger.debug(f"  OSM Building details: {building}")
            else:
                logger.info(f"  No buildings found in OSM at coordinates {lat}, {lng}")

            # Log timing information
            osm_time = time.time() - osm_start_time
            logger.debug(f"OSM data fetch completed in {osm_time:.2f}s (cache hit: {cache_hit})")

        except Exception as e:
            logger.warning(f"Error fetching RUIAN data from OSM: {e}")
            logger.debug(traceback.format_exc())

            # Add error information to the data
            ruian_data['osm_error'] = str(e)

    # Try to get property data from CUZK if available
    if cuzk_integration:
        cuzk_start_time = time.time()
        logger.debug(f"Attempting to fetch CUZK data for coordinates: S-JTSK({x_int}, {y_int})")

        try:
            # Create a cache key for CUZK data
            cache_key = f"cuzk_{x_int}_{y_int}"

            # Check cache first if available
            property_data = None
            cache_hit = False

            if cuzk_cache:
                try:
                    property_data = cuzk_cache.get(cache_key)
                    if property_data is not None:
                        cache_hit = True
                        logger.info(f"Using cached CUZK data for {cache_key}")
                except CacheError as ce:
                    logger.warning(f"Cache error when retrieving CUZK data: {ce}")

            # If not in cache or cache not available, fetch from CUZK
            if property_data is None:
                logger.info(f"Fetching property data from CUZK at coordinates S-JTSK({x_int}, {y_int})...")

                # Add a small delay to avoid overwhelming the CUZK API
                time.sleep(0.1)

                # Get property data from CUZK
                property_data = cuzk_integration.get_property_by_coordinates(x_int, y_int)

                # Cache the results if cache is available
                if cuzk_cache and property_data is not None:
                    try:
                        cuzk_cache.set(cache_key, property_data)
                        logger.debug(f"Cached CUZK data for {cache_key}")
                    except CacheError as ce:
                        logger.warning(f"Cache error when storing CUZK data: {ce}")

            # Process the property data
            if property_data:
                # Update with CUZK data
                ruian_data.update(property_data)
                ruian_data['source'] = 'cuzk'

                # Add CUZK to data sources
                ruian_data['data_sources'].append('cuzk')

                # Log success
                logger.info(f"  Found property data in CUZK")

                # Log property details at debug level
                logger.debug(f"  CUZK Property details: {property_data}")
            else:
                logger.info(f"  No property data found in CUZK at coordinates S-JTSK({x_int}, {y_int})")

            # Log timing information
            cuzk_time = time.time() - cuzk_start_time
            logger.debug(f"CUZK data fetch completed in {cuzk_time:.2f}s (cache hit: {cache_hit})")

        except Exception as e:
            logger.warning(f"Error fetching property data from CUZK: {e}")
            logger.debug(traceback.format_exc())

            # Add error information to the data
            ruian_data['cuzk_error'] = str(e)

    # Log overall timing
    total_time = time.time() - start_time
    logger.debug(f"Total RUIAN data fetch completed in {total_time:.2f}s")

    # Add timestamp to the data
    ruian_data['timestamp'] = time.time()

    return ruian_data


def generate_points_in_area(lat, lng, radius_meters, location_name, radius_km, cuzk_integration, osm_integration=None, osm_cache=None, cuzk_cache=None):
    """
    Generate points in the area around the given coordinates and fetch RUIAN data for each point.

    Args:
        lat: Center latitude in WGS84
        lng: Center longitude in WGS84
        radius_meters: Search radius in meters
        location_name: Name of the location (for logging)
        radius_km: Search radius in kilometers (for logging)
        cuzk_integration: CUZK integration instance
        osm_integration: OSM integration instance (optional)
        osm_cache: Cache for OSM data (optional)
        cuzk_cache: Cache for CUZK data (optional)

    Returns:
        List of points with RUIAN data
    """
    start_time = time.time()
    logger.info(f"Generating points within {radius_km} km radius of {location_name} (WGS84: {lat}, {lng})")

    try:
        # Validate inputs
        if not isinstance(lat, (int, float)) or not isinstance(lng, (int, float)):
            raise ValueError(f"Invalid coordinates: lat={lat}, lng={lng}")

        if not isinstance(radius_meters, (int, float)) or radius_meters <= 0:
            raise ValueError(f"Invalid radius: {radius_meters} meters")

        # Convert radius from meters to degrees (approximate)
        # 1 degree of latitude is approximately 111,000 meters
        radius_lat = radius_meters / 111000
        # 1 degree of longitude varies with latitude
        radius_lng = radius_meters / (111000 * math.cos(math.radians(lat)))

        logger.debug(f"Radius in degrees: lat={radius_lat}, lng={radius_lng}")

        # Generate points in a grid pattern
        points = []

        # Add the center point first
        points.append({
            'lat': lat,
            'lng': lng,
            'distance': 0,  # Distance from center in meters
            'type': 'center'
        })

        # Number of points to generate in each direction
        grid_size = 3
        logger.debug(f"Using grid size: {grid_size}x{grid_size}")

        # Calculate step size
        lat_step = 2 * radius_lat / (grid_size - 1) if grid_size > 1 else 0
        lng_step = 2 * radius_lng / (grid_size - 1) if grid_size > 1 else 0

        # Generate grid of points
        for i in range(grid_size):
            for j in range(grid_size):
                # Calculate coordinates for this point
                point_lat = lat - radius_lat + i * lat_step
                point_lng = lng - radius_lng + j * lng_step

                # Skip the center point (already added)
                if point_lat == lat and point_lng == lng:
                    continue

                # Calculate approximate distance from center in meters
                # This is a simplified calculation and not exact
                dlat = point_lat - lat
                dlng = point_lng - lng
                distance = math.sqrt(dlat**2 + dlng**2) * 111000

                # Add to points list
                points.append({
                    'lat': point_lat,
                    'lng': point_lng,
                    'distance': distance,  # Distance from center in meters
                    'type': 'grid'
                })

        # Log the generated points
        logger.info(f"Generated {len(points)} coordinates within radius {radius_km} km of {location_name}")
        logger.debug(f"Grid pattern: {grid_size}x{grid_size} with center at ({lat}, {lng})")

        # Process each point and add RUIAN data
        successful_points = 0
        ruian_refs_found = 0

        for idx, point in enumerate(points):
            point_start_time = time.time()
            logger.info(f"Processing coordinate {idx+1}/{len(points)}...")

            try:
                # Convert WGS84 to S-JTSK
                try:
                    x, y = cuzk_integration.convert_wgs84_to_sjtsk(point['lat'], point['lng'])

                    # Ensure coordinates are integers
                    x_int = int(x)
                    y_int = int(y)

                    logger.debug(f"Converted WGS84({point['lat']}, {point['lng']}) to S-JTSK({x_int}, {y_int})")
                except Exception as e:
                    logger.error(f"Error converting coordinates: {e}")
                    logger.debug(traceback.format_exc())
                    continue

                # Generate MapaIdentifikace URL
                url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"

                # Fetch RUIAN data with caching
                try:
                    ruian_data = fetch_ruian_data(
                        point['lat'], point['lng'],
                        x_int, y_int,
                        osm_integration, cuzk_integration,
                        osm_cache, cuzk_cache
                    )
                except Exception as e:
                    logger.error(f"Error fetching RUIAN data: {e}")
                    logger.debug(traceback.format_exc())
                    ruian_data = {
                        'error': str(e),
                        'x_sjtsk': x_int,
                        'y_sjtsk': y_int,
                        'url': url
                    }

                # Add RUIAN data to the point
                point.update(ruian_data)

                # Output coordinate information with RUIAN data
                logger.info(f"Coordinate {idx+1} details:")
                logger.info(f"  WGS84: lat={point['lat']}, lng={point['lng']}")
                logger.info(f"  S-JTSK: x={x_int}, y={y_int}")
                logger.info(f"  URL: {url}")
                logger.info(f"  Distance from center: {point['distance']:.1f} meters")

                # Output RUIAN data if available
                if 'ruian_ref' in ruian_data and ruian_data['ruian_ref']:
                    logger.info(f"  RUIAN ID: {ruian_data['ruian_ref']}")
                    ruian_refs_found += 1

                if 'property_type' in ruian_data:
                    logger.info(f"  Property Type: {ruian_data['property_type']}")

                if 'building_type' in ruian_data:
                    logger.info(f"  Building Type: {ruian_data['building_type']}")

                if 'name' in ruian_data and ruian_data['name']:
                    logger.info(f"  Name: {ruian_data['name']}")

                if 'address' in ruian_data and ruian_data['address']:
                    logger.info(f"  Address: {ruian_data['address']}")

                if 'data_sources' in ruian_data and ruian_data['data_sources']:
                    logger.info(f"  Data Sources: {', '.join(ruian_data['data_sources'])}")
                elif 'source' in ruian_data:
                    logger.info(f"  Data Source: {ruian_data['source']}")

                # Log any errors
                if 'error' in ruian_data:
                    logger.warning(f"  Error: {ruian_data['error']}")
                if 'osm_error' in ruian_data:
                    logger.warning(f"  OSM Error: {ruian_data['osm_error']}")
                if 'cuzk_error' in ruian_data:
                    logger.warning(f"  CUZK Error: {ruian_data['cuzk_error']}")

                # Add a small delay to avoid overwhelming the console output
                time.sleep(0.1)

                # Count successful point processing
                successful_points += 1

                # Log timing for this point
                point_time = time.time() - point_start_time
                logger.debug(f"Processed coordinate {idx+1} in {point_time:.2f}s")

            except Exception as e:
                logger.error(f"Error processing coordinate {idx+1}: {e}")
                logger.debug(traceback.format_exc())

                # Add error information to the point
                point['error'] = str(e)

        # Log summary statistics
        total_time = time.time() - start_time
        logger.info(f"Point generation and processing completed in {total_time:.2f}s")
        logger.info(f"Successfully processed {successful_points}/{len(points)} coordinates")
        logger.info(f"Found {ruian_refs_found} RUIAN references")

        return points

    except Exception as e:
        logger.error(f"Error generating points: {e}")
        logger.debug(traceback.format_exc())
        return []


def main():
    """Main function."""
    # Parse command line arguments
    args = parse_arguments()

    # Initialize caches with configurable expiry times
    osm_cache = SimpleCache('osm_buildings', expiry_seconds=args.cache_expiry)
    cuzk_cache = SimpleCache('cuzk_properties', expiry_seconds=args.cache_expiry)

    # Clean up expired cache entries or force refresh if requested
    if args.refresh_cache:
        logger.info("Forcing cache refresh...")
        osm_cache.clear()
        cuzk_cache.clear()
    else:
        osm_cache.cleanup()
        cuzk_cache.cleanup()

    # Initialize Google Maps integration
    google_maps = GoogleMapsIntegration()

    # Initialize CUZK integration
    cuzk_integration = CUZKIntegration()

    # Initialize OSM integration with a mock app
    mock_app = MagicMock()
    mock_app.show_status = lambda msg: logger.info(msg)
    mock_app.cache_manager = MagicMock()
    mock_app.cache_manager.get = lambda cache_name, key: osm_cache.get(key) if cache_name == 'osm_buildings' else None
    mock_app.cache_manager.set = lambda cache_name, key, value: osm_cache.set(key, value) if cache_name == 'osm_buildings' else None
    osm_integration = OSMIntegration(mock_app)

    try:
        # Get location and radius
        location = args.location if args.location else get_random_location()
        radius_km = args.radius if args.radius is not None else get_random_radius()

        logger.info(f"Testing batch search with location: {location}, radius: {radius_km} km")

        # Convert radius from km to meters
        radius_meters = int(radius_km * 1000)

        # Get coordinates for the location
        logger.info(f"Geocoding location: {location}")
        geocode_result = google_maps.geocode(location)

        if not geocode_result:
            logger.error(f"Could not geocode location: {location}")
            # Try with a default location
            logger.info("Trying with default location: Prague")
            geocode_result = google_maps.geocode("Prague")
            if not geocode_result:
                logger.error("Could not geocode default location: Prague")
                return 1

        if 'lat' not in geocode_result or 'lng' not in geocode_result:
            logger.error(f"Invalid geocode result: {geocode_result}")
            return 1

        lat = geocode_result['lat']
        lng = geocode_result['lng']
        logger.info(f"Geocoded coordinates: lat={lat}, lng={lng}")

        # Generate points in the area and fetch RUIAN data with caching
        logger.info("Generating points and fetching RUIAN data...")
        points = generate_points_in_area(
            lat, lng, radius_meters, location, radius_km,
            cuzk_integration, osm_integration, osm_cache, cuzk_cache
        )

        # Check if points were found
        if not points:
            logger.error("No coordinates found")
            return 1

        # Output summary of RUIAN data found
        ruian_refs_found = sum(1 for p in points if 'ruian_ref' in p and p['ruian_ref'])
        logger.info(f"Successfully found and output {len(points)} coordinates")
        logger.info(f"RUIAN references found: {ruian_refs_found} out of {len(points)} points")

        # Output a summary of data sources
        sources = {}
        for point in points:
            source = point.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1

        logger.info("Data sources summary:")
        for source, count in sources.items():
            logger.info(f"  {source}: {count} points")

        # Output cache statistics
        logger.info("Cache statistics:")
        logger.info(f"  OSM cache: {len(osm_cache.cache)} entries, {osm_cache.hits} hits, {osm_cache.misses} misses")
        logger.info(f"  CUZK cache: {len(cuzk_cache.cache)} entries, {cuzk_cache.hits} hits, {cuzk_cache.misses} misses")

        return 0

    except Exception as e:
        logger.error(f"Error in script: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())
