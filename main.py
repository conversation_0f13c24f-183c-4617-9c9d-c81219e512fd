"""
Main entry point for the Czech Property Registry application.

This file serves as the entry point for the application. It creates the main
window and initializes the application.

Usage:
    python main.py [--optimized] [--config CONFIG_PATH]
"""

import tkinter as tk
import sys
import os
import logging
import argparse
import time
import threading
from ui.simple_loading_window import SimpleLoadingWindow

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)

# Create a logger for this module
logger = logging.getLogger(__name__)

# Add the current directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Czech Property Registry")

    parser.add_argument(
        "--optimized",
        action="store_true",
        help="Use the optimized application implementation"
    )

    parser.add_argument(
        "--config",
        type=str,
        default="config.ini",
        help="Path to the configuration file"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )

    return parser.parse_args()


def setup_logging(debug=False):
    """Set up logging with the appropriate level."""
    level = logging.DEBUG if debug else logging.INFO

    # Configure root logger
    logging.getLogger().setLevel(level)

    # Configure specific loggers
    for logger_name in ["core", "api", "ui", "utils"]:
        logging.getLogger(logger_name).setLevel(level)

    logger.info(f"Logging level set to: {logging.getLevelName(level)}")


def main():
    """Main entry point for the application."""
    # Parse command line arguments
    args = parse_arguments()

    # Set up logging
    setup_logging(args.debug)

    # Log startup information
    logger.info("Starting Czech Property Registry")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current directory: {os.getcwd()}")

    # Create a root window for the loading screen
    root = tk.Tk()
    root.withdraw()  # Hide the root window initially

    # Create the loading window
    loading = SimpleLoadingWindow(parent=root, app_title="Czech Property Registry")

    try:
        # Define initialization tasks
        def import_dependencies():
            global AppFactory
            from core.app_factory import AppFactory
            time.sleep(0.5)  # Small delay for visual feedback

        def create_application():
            global app
            # Determine the app type
            app_type = "optimized" if args.optimized else "standard"
            # Create the application
            app = AppFactory.create_app(app_type=app_type, config={"config_path": args.config})

        def initialize_apis():
            # Initialize APIs (this will be handled by the app, but we show it in the loading screen)
            # First check for Mapy.cz API key
            if hasattr(app, 'mapycz_integration') and hasattr(app.mapycz_integration, 'client'):
                if app.mapycz_integration.client.api_key:
                    logger.info("Mapy.cz API key found")
                else:
                    logger.warning("No Mapy.cz API key found, some features may be limited")

            # Then check Google Maps API key as fallback
            if hasattr(app, 'google_maps'):
                app.google_maps.validate_api_key()

            time.sleep(0.5)  # Small delay for visual feedback

        def prepare_ui():
            # Prepare the UI (this will be handled by the app, but we show it in the loading screen)
            time.sleep(0.5)  # Small delay for visual feedback

        # Define the tasks for the loading window
        tasks = [
            ("Loading dependencies", import_dependencies, "Importing required modules..."),
            ("Creating application", create_application, "Initializing application components..."),
            ("Connecting to APIs", initialize_apis, "Establishing connections to external services..."),
            ("Preparing user interface", prepare_ui, "Setting up the user interface...")
        ]

        # Run the tasks with the loading window
        def on_complete():
            # Close the loading window
            loading.close()

            # Show the main application window
            root.destroy()  # Destroy the temporary root

            # Run the application
            app.run()

        # Start the loading process
        try:
            # Process the first task directly
            task_name, task_func, status_msg = tasks[0]
            loading.set_task(task_name)
            loading.set_status(status_msg)
            task_func()
            loading.set_progress(25)

            # Process the second task directly
            task_name, task_func, status_msg = tasks[1]
            loading.set_task(task_name)
            loading.set_status(status_msg)
            task_func()
            loading.set_progress(50)

            # Process the remaining tasks
            for i, (task_name, task_func, status_msg) in enumerate(tasks[2:], 2):
                loading.set_task(task_name)
                loading.set_status(status_msg)
                task_func()
                loading.set_progress(50 + (i * 50 / len(tasks)))

            # Complete the loading process
            loading.finish()

            # Call the completion callback
            on_complete()
        except Exception as e:
            logger.error(f"Error during initialization: {e}", exc_info=True)
            loading.close()
            root.destroy()
            return 1

        logger.info("Application exited normally")
        return 0
    except Exception as e:
        # Close the loading window if there's an error
        loading.close()

        logger.error(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
