"""
Notification banner component for the Czech Property Registry application.
Provides a non-intrusive way to display notifications to the user.
"""

import tkinter as tk
from tkinter import ttk
import time
import threading


class NotificationBanner:
    """A banner for displaying notifications in the UI"""
    
    def __init__(self, parent, font_scaler=None):
        """
        Initialize the notification banner
        
        Args:
            parent: Parent widget (typically the main frame)
            font_scaler: Font scaler instance for consistent font sizing
        """
        self.parent = parent
        self.font_scaler = font_scaler
        self.banner_frame = None
        self.message_label = None
        self.close_button = None
        self.auto_hide_timer = None
        self.visible = False
        self.notifications = {}  # Store notifications by type to avoid duplicates
        self.notification_lock = threading.Lock()
        
        # Banner styles
        self.styles = {
            "info": {
                "background": "#e3f2fd",  # Light blue
                "foreground": "#0d47a1",  # Dark blue
                "border": "#bbdefb"       # Medium blue
            },
            "warning": {
                "background": "#fff9c4",  # Light yellow
                "foreground": "#f57f17",  # Dark orange
                "border": "#ffecb3"       # Medium yellow
            },
            "error": {
                "background": "#ffebee",  # Light red
                "foreground": "#c62828",  # Dark red
                "border": "#ffcdd2"       # Medium red
            },
            "success": {
                "background": "#e8f5e9",  # Light green
                "foreground": "#2e7d32",  # Dark green
                "border": "#c8e6c9"       # Medium green
            },
            "fallback": {
                "background": "#fff3e0",  # Light orange
                "foreground": "#e65100",  # Dark orange
                "border": "#ffe0b2"       # Medium orange
            }
        }
        
    def show(self, message, notification_type="info", auto_hide=True, duration=5000, key=None):
        """
        Show a notification banner
        
        Args:
            message (str): Message to display
            notification_type (str): Type of notification (info, warning, error, success, fallback)
            auto_hide (bool): Whether to automatically hide the banner after a duration
            duration (int): Duration in milliseconds before auto-hiding
            key (str): Optional key to identify this notification (to avoid duplicates)
        """
        # If a key is provided, check if we already have this notification
        if key:
            with self.notification_lock:
                if key in self.notifications:
                    # If the same notification is already visible, don't show it again
                    return
                # Store this notification
                self.notifications[key] = {
                    "message": message,
                    "type": notification_type,
                    "timestamp": time.time()
                }
        
        # Get style for this notification type
        style = self.styles.get(notification_type, self.styles["info"])
        
        # Create the banner frame if it doesn't exist
        if not self.banner_frame:
            self.banner_frame = ttk.Frame(self.parent, style="Banner.TFrame")
            
            # Create a custom style for the banner
            style_obj = ttk.Style()
            style_obj.configure("Banner.TFrame", background=style["background"])
            style_obj.configure("Banner.TLabel", background=style["background"], foreground=style["foreground"])
            style_obj.configure("Banner.TButton", background=style["background"])
            
            # Create the message label
            font = self.font_scaler.get_font("small") if self.font_scaler else ("Arial", 9)
            self.message_label = ttk.Label(
                self.banner_frame,
                text=message,
                style="Banner.TLabel",
                font=font,
                wraplength=600,
                justify="left",
                padding=(10, 5)
            )
            self.message_label.pack(side="left", fill="x", expand=True)
            
            # Create the close button
            self.close_button = ttk.Button(
                self.banner_frame,
                text="×",
                width=2,
                style="Banner.TButton",
                command=self.hide
            )
            self.close_button.pack(side="right", padx=5)
            
            # Add a border at the bottom
            border_frame = ttk.Frame(self.banner_frame, height=1, style="Border.TFrame")
            style_obj.configure("Border.TFrame", background=style["border"])
            border_frame.pack(side="bottom", fill="x")
            
            # Pack the banner at the top of the parent
            self.banner_frame.pack(side="top", fill="x", padx=0, pady=0)
        else:
            # Update existing banner
            style_obj = ttk.Style()
            style_obj.configure("Banner.TFrame", background=style["background"])
            style_obj.configure("Banner.TLabel", background=style["background"], foreground=style["foreground"])
            style_obj.configure("Banner.TButton", background=style["background"])
            
            # Update the border color
            style_obj.configure("Border.TFrame", background=style["border"])
            
            # Update the message
            self.message_label.config(text=message)
            
            # Make sure the banner is visible
            self.banner_frame.pack(side="top", fill="x", padx=0, pady=0)
        
        self.visible = True
        
        # Cancel any existing auto-hide timer
        if self.auto_hide_timer:
            self.parent.after_cancel(self.auto_hide_timer)
            self.auto_hide_timer = None
        
        # Set up auto-hide if requested
        if auto_hide:
            self.auto_hide_timer = self.parent.after(duration, self.hide)
    
    def hide(self):
        """Hide the notification banner"""
        if self.banner_frame and self.visible:
            self.banner_frame.pack_forget()
            self.visible = False
        
        # Cancel any auto-hide timer
        if self.auto_hide_timer:
            self.parent.after_cancel(self.auto_hide_timer)
            self.auto_hide_timer = None
    
    def clear_notification(self, key):
        """
        Clear a specific notification by key
        
        Args:
            key (str): The notification key to clear
        """
        with self.notification_lock:
            if key in self.notifications:
                del self.notifications[key]
    
    def clear_all_notifications(self):
        """Clear all stored notifications"""
        with self.notification_lock:
            self.notifications.clear()
