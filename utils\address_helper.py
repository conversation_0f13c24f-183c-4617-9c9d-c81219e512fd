"""
Address-related helper functions for the Czech Property Registry application.
"""

from typing import List, Dict, Any, Optional, Callable
import time
import threading


def get_prefix_matches(app, text: str, prefix_type: str, min_length: int = 3) -> List[Dict[str, Any]]:
    """
    Get suggestions from cache based on prefix matching.

    Args:
        app: The main application instance
        text (str): The text to match
        prefix_type (str): The type of cache to search (e.g., "address:", "city:")
        min_length (int): Minimum length of text to consider for matching

    Returns:
        list: List of matching suggestions or empty list if none found
    """
    if len(text) < min_length:
        return []

    # Check if we have a cache manager
    if hasattr(app, 'cache_manager'):
        # Extract the cache name from the prefix type (remove the colon)
        cache_name = prefix_type.rstrip(':')

        # Use the cache manager's prefix matching
        return app.cache_manager.get_prefix_matches(cache_name, text, min_length)

    # If we don't have a cache manager, check the old cache
    elif hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
        with app._address_cache_lock:
            current_time = time.time()

            for key, entry in app._address_suggestions_cache.items():
                # Skip expired entries
                if current_time > entry['expiry']:
                    continue

                # Check if this is the right type of cache entry
                if not key.startswith(prefix_type):
                    continue

                # Extract the cached query text
                cached_text = key[len(prefix_type):]

                # If the cached query starts with our current text and is longer
                # it might have relevant suggestions
                if cached_text.startswith(text) and len(cached_text) > len(text):
                    # Filter the suggestions to only include those relevant to the current text
                    filtered_suggestions = []
                    for suggestion in entry['data']:
                        # Check if the suggestion description contains the current text
                        if text.lower() in suggestion['description'].lower():
                            filtered_suggestions.append(suggestion)

                    # If we found relevant suggestions, return them
                    if filtered_suggestions:
                        print(f"Found prefix matches for '{text}' in cache: {[s['description'] for s in filtered_suggestions[:3]]}")
                        return filtered_suggestions

    return []


def get_address_suggestions(
    app,
    text: str,
    suggestion_type: str = "address",
    components: Optional[Dict[str, str]] = None,
    prefix: Optional[str] = None
) -> List[Dict[str, str]]:
    """
    Get address suggestions for the given text with caching.

    Args:
        app: The main application instance
        text (str): The text to get suggestions for
        suggestion_type (str): Type of suggestion to get (address, city, street, etc.)
        components (dict, optional): Additional components for the API request
        prefix (str, optional): Cache key prefix for specialized searches

    Returns:
        list: List of address suggestions
    """
    if not text or len(text) < 2:
        return []

    # Default components if not provided
    if components is None:
        components = {"country": "cz"}

    # Create cache key
    cache_key = f"{prefix + ':' if prefix else ''}{text}"

    # Check if we have a cache manager
    if hasattr(app, 'cache_manager'):
        # Use the cache manager
        cache_name = f"{suggestion_type}_suggestions"

        # Check cache first
        cached_result = app.cache_manager.get(cache_name, cache_key)
        if cached_result:
            return cached_result

        # Check for prefix matches to reduce API calls
        if len(text) >= 3:
            prefix_matches = app.cache_manager.get_prefix_matches(cache_name, cache_key)
            if prefix_matches and len(prefix_matches) > 0:
                return prefix_matches[0]  # Return the first match

    # If we don't have a cache manager, check the old cache
    elif hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
        with app._address_cache_lock:
            # Check if we have a cached result
            if cache_key in app._address_suggestions_cache:
                cache_entry = app._address_suggestions_cache[cache_key]
                if time.time() < cache_entry['expiry']:
                    return cache_entry['data']
                else:
                    # Remove expired entry
                    del app._address_suggestions_cache[cache_key]

    # Get suggestions from the API
    try:
        # Use the Google Maps API if available
        if hasattr(app, 'google_maps') and app.google_maps:
            # Determine the place type based on suggestion_type
            place_type = "address"
            if suggestion_type == "city":
                place_type = "(cities)"
            elif suggestion_type == "street":
                place_type = "address"

            # Use the input text directly
            search_text = text

            # Get suggestions from the API
            suggestions = app.google_maps.get_place_predictions(
                search_text,
                types=place_type,
                components=components
            )

            # If we got suggestions, cache and return them
            if suggestions:
                # Cache the results
                if hasattr(app, 'cache_manager'):
                    app.cache_manager.set(f"{suggestion_type}_suggestions", cache_key, suggestions)
                elif hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
                    with app._address_cache_lock:
                        app._address_suggestions_cache[cache_key] = {
                            'data': suggestions,
                            'expiry': time.time() + getattr(app, '_address_cache_expiry', 300)
                        }

                return suggestions
    except Exception as e:
        print(f"Error getting {suggestion_type} suggestions: {e}")
        # Log the error but don't use fallback data
        if hasattr(app, 'show_status'):
            app.show_status(f"Error getting address suggestions: {e}")

    # Return an empty list when API fails
    return []



