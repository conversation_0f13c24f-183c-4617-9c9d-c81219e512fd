"""
Robust Loading Window for the Czech Property Registry application.

This module provides a splash screen with loading progress information
that appears during application startup, with special care to avoid UI freezing.
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import logging
import queue
from typing import List, Tuple, Callable, Optional, Any

# Configure logging
logger = logging.getLogger(__name__)

class RobustLoadingWindow:
    """
    A splash screen window that shows loading progress during application startup.
    
    This window displays a progress bar and status messages to inform the user
    about what's happening during the initialization process, with special care
    to avoid UI freezing.
    """
    
    def __init__(self, parent=None, app_title="Czech Property Registry"):
        """
        Initialize the loading window.
        
        Args:
            parent: Parent window (if None, creates a new Toplevel window)
            app_title: Title of the application
        """
        self.parent = parent
        self.app_title = app_title
        
        # Create the window
        if parent is None:
            self.window = tk.Tk()
            self.window.withdraw()  # Hide until ready
        else:
            self.window = tk.Toplevel(parent)
            
        # Configure the window
        self.window.title(f"Loading {app_title}")
        self.window.geometry("500x300")
        self.window.resizable(False, False)
        self.window.configure(bg="#f0f0f0")
        
        # Center the window on screen
        self._center_window()
        
        # Set window attributes
        self.window.attributes("-topmost", True)
        
        # Remove window decorations if supported on platform
        try:
            self.window.overrideredirect(True)  # Remove title bar
        except:
            pass  # Not supported on all platforms
            
        # Create UI elements
        self._create_ui()
        
        # Initialize progress variables
        self.progress_value = 0
        self.progress_max = 100
        self.current_task = "Initializing..."
        self.tasks_completed = 0
        self.total_tasks = 0
        
        # Task queue for communication between threads
        self.task_queue = queue.Queue()
        
        # Flag to indicate if the window is closing
        self.closing = False
        
        # Show the window
        self.window.deiconify()
        self.window.update()
        
        # Start the UI update loop
        self._start_ui_update_loop()
        
    def _center_window(self):
        """Center the window on the screen."""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def _create_ui(self):
        """Create the UI elements for the loading window."""
        # Create main frame
        main_frame = ttk.Frame(self.window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Application title
        title_label = ttk.Label(
            main_frame, 
            text=self.app_title, 
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Loading image or logo (placeholder)
        logo_frame = ttk.Frame(main_frame, width=200, height=100)
        logo_frame.pack(pady=(0, 20))
        logo_frame.pack_propagate(False)
        
        logo_label = ttk.Label(
            logo_frame, 
            text="LOADING", 
            font=("Arial", 24, "bold")
        )
        logo_label.pack(fill=tk.BOTH, expand=True)
        
        # Current task label
        self.task_label = ttk.Label(
            main_frame, 
            text="Initializing application...",
            font=("Arial", 10)
        )
        self.task_label.pack(pady=(0, 10), anchor=tk.W)
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            orient=tk.HORIZONTAL, 
            length=460, 
            mode="determinate"
        )
        self.progress_bar.pack(pady=(0, 10), fill=tk.X)
        
        # Progress percentage
        self.percentage_label = ttk.Label(
            main_frame, 
            text="0%",
            font=("Arial", 9)
        )
        self.percentage_label.pack(anchor=tk.E)
        
        # Status message
        self.status_label = ttk.Label(
            main_frame, 
            text="Starting up...",
            font=("Arial", 9, "italic")
        )
        self.status_label.pack(pady=(10, 0), anchor=tk.W)
        
    def _start_ui_update_loop(self):
        """Start the UI update loop to process UI updates from the task queue."""
        def update_loop():
            if self.closing:
                return
                
            try:
                # Process all pending UI updates
                while not self.task_queue.empty():
                    update_func = self.task_queue.get_nowait()
                    update_func()
                    self.task_queue.task_done()
            except queue.Empty:
                pass
            except Exception as e:
                logger.error(f"Error in UI update loop: {e}")
                
            # Schedule the next update
            if not self.closing:
                self.window.after(50, update_loop)
                
        # Start the update loop
        self.window.after(50, update_loop)
        
    def set_task(self, task_name, status_message=""):
        """
        Set the current task name and status message.
        
        Args:
            task_name: Name of the current task
            status_message: Additional status information
        """
        def update_ui():
            self.current_task = task_name
            
            # Update the UI
            self.task_label.config(text=task_name)
            if status_message:
                self.status_label.config(text=status_message)
                
            # Increment tasks completed if this is a new task
            self.tasks_completed += 1
            if self.total_tasks > 0:
                progress = int((self.tasks_completed / self.total_tasks) * 100)
                self._set_progress_internal(progress)
                
        # Add the update to the queue
        self.task_queue.put(update_ui)
        
    def set_progress(self, value):
        """
        Set the progress bar value.
        
        Args:
            value: Progress value (0-100)
        """
        def update_ui():
            self._set_progress_internal(value)
            
        # Add the update to the queue
        self.task_queue.put(update_ui)
        
    def _set_progress_internal(self, value):
        """Internal method to update the progress bar."""
        self.progress_value = max(0, min(value, 100))
        
        # Update the UI
        self.progress_bar["value"] = self.progress_value
        self.percentage_label.config(text=f"{self.progress_value}%")
        
    def set_status(self, message):
        """
        Set the status message.
        
        Args:
            message: Status message to display
        """
        def update_ui():
            # Update the UI
            self.status_label.config(text=message)
            
        # Add the update to the queue
        self.task_queue.put(update_ui)
        
    def set_total_tasks(self, total):
        """
        Set the total number of tasks for progress calculation.
        
        Args:
            total: Total number of tasks
        """
        self.total_tasks = total
        self.tasks_completed = 0
        
    def close(self):
        """Close the loading window."""
        self.closing = True
        try:
            self.window.destroy()
        except:
            pass  # Window might already be closed
            
    def finish(self, delay=0.5):
        """
        Complete the loading process and close the window after a delay.
        
        Args:
            delay: Delay in seconds before closing
        """
        def update_ui():
            # Set progress to 100%
            self._set_progress_internal(100)
            self.task_label.config(text="Loading complete")
            self.status_label.config(text="Application is ready")
            
            # Wait for the specified delay
            if delay > 0:
                self.window.after(int(delay * 1000), self.close)
            else:
                self.close()
                
        # Add the update to the queue
        self.task_queue.put(update_ui)
        
    def run_with_loading(self, tasks, on_complete=None):
        """
        Run a list of tasks with loading indicators.
        
        Args:
            tasks: List of (task_name, function, status_message) tuples
            on_complete: Function to call when all tasks are complete
        """
        self.set_total_tasks(len(tasks))
        
        # Create a thread to run the tasks
        def run_tasks_thread():
            for i, (task_name, task_func, status_msg) in enumerate(tasks):
                if self.closing:
                    break
                    
                # Update the UI
                self.set_task(task_name, status_msg)
                
                try:
                    # Run the task in a separate thread to avoid blocking
                    task_result = [None]
                    task_error = [None]
                    task_done = threading.Event()
                    
                    def execute_task():
                        try:
                            task_result[0] = task_func()
                        except Exception as e:
                            logger.error(f"Error in task '{task_name}': {e}")
                            task_error[0] = e
                        finally:
                            task_done.set()
                            
                    # Start the task thread
                    task_thread = threading.Thread(target=execute_task, daemon=True)
                    task_thread.start()
                    
                    # Wait for the task to complete with a timeout
                    task_done.wait(timeout=30)  # 30 second timeout
                    
                    # Check if the task completed
                    if not task_done.is_set():
                        logger.warning(f"Task '{task_name}' is taking too long")
                        self.set_status(f"Task '{task_name}' is taking longer than expected...")
                        
                        # Wait for the task to complete (no timeout)
                        task_done.wait()
                        
                    # Check if there was an error
                    if task_error[0]:
                        self.set_status(f"Error: {str(task_error[0])}")
                        time.sleep(1)  # Show the error for a moment
                        
                except Exception as e:
                    logger.error(f"Error running task '{task_name}': {e}")
                    self.set_status(f"Error: {str(e)}")
                    time.sleep(1)  # Show the error for a moment
                    
                # Update progress
                progress = int(((i + 1) / len(tasks)) * 100)
                self.set_progress(progress)
                
                # Small delay for visual feedback
                time.sleep(0.1)
                
            # Complete the loading process
            self.finish()
            
            # Call the completion callback if provided
            if on_complete and not self.closing:
                on_complete()
                
        # Start the tasks thread
        threading.Thread(target=run_tasks_thread, daemon=True).start()
