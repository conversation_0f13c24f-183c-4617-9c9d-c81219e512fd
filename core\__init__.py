"""
Core module for the Czech Property Scraper application.
Contains the main application class and core functionality.
"""

from core.app import PropertyScraperApp
from core.optimized_app import OptimizedPropertyScraperApp
from core.app_base import AppBase
from core.app_factory import AppFactory
from core.batch_search import BatchSearchManagerBase, BatchSearchManagerImpl, BatchSearchManagerFactory

__all__ = [
    'PropertyScraperApp',
    'OptimizedPropertyScraperApp',
    'AppBase',
    'AppFactory',
    'BatchSearchManagerBase',
    'BatchSearchManagerImpl',
    'BatchSearchManagerFactory'
]
