"""
Smart Batch Search Manager for Czech Property Registry

This module provides a smart batch search functionality that finds all buildings
within a modifiable radius around an address, retrieves RUIAN data, and generates
URLs for owner information.
"""

import logging
import threading
import math
import time
import random
from typing import List, Dict, Any, Optional, Callable, Tuple
from models.ruian_id import <PERSON>uianID
from ui.message_boxes import MessageBoxes

# Configure logging
logger = logging.getLogger(__name__)

class SmartBatchSearchManager:
    """
    Manages smart batch search functionality for the application.

    Provides methods for searching properties by address with a customizable radius,
    retrieving RUIAN data, and generating MapaIdentifikace URLs for owner information.
    """

    def __init__(self, app):
        """
        Initialize the smart batch search manager.

        Args:
            app: The main application instance with access to Google Maps, OSM, and CUZK APIs
        """
        self.app = app  # Main application with access to all required services
        self.search_in_progress = False
        self.cancel_search = False

    def batch_search_by_address(self, address: str, property_types: List[str] = None,
                               radius: float = 2.0, max_results: int = 50,
                               callback: Optional[Callable] = None):
        """
        Perform a batch search for properties by address with a customizable radius.

        Args:
            address (str): Full address to search in (from Google Maps autocomplete)
            property_types (list, optional): List of property types to include in results
            radius (float): Search radius in kilometers (default: 2.0)
            max_results (int): Maximum number of results to return (default: 50)
            callback (callable, optional): Function to call with the results
        """
        try:
            if self.search_in_progress:
                logger.warning("Search already in progress, ignoring new request")
                return

            self.search_in_progress = True
            self.cancel_search = False

            logger.info(f"Smart batch searching by address: {address} with radius {radius}km")

            # Convert radius from km to meters
            radius_meters = int(float(radius) * 1000)

            # Show status
            self.app.show_status(f"Searching for properties near {address}...")

            # Start the search in a background thread
            threading.Thread(
                target=self._batch_search_thread,
                args=(address, property_types, radius_meters, max_results, callback),
                daemon=True
            ).start()

        except Exception as e:
            logger.error(f"Error in smart batch search: {e}", exc_info=True)
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.search_in_progress = False

    def _batch_search_thread(self, address: str, property_types: List[str],
                            radius: int, max_results: int, callback: Optional[Callable]):
        """
        Background thread for batch search to avoid freezing the UI.

        Args:
            address (str): Full address to search in
            property_types (list): List of property types to include
            radius (int): Search radius in meters
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
        """
        try:
            # Check if we have cached results for this search
            cache_key = f"{address}_{radius}_{max_results}_{'-'.join(property_types) if property_types else 'all'}"
            from_cache = False
            cache_timestamp = None

            # Check if we have a cache manager
            if hasattr(self.app, 'cache_manager') and hasattr(self.app.cache_manager, 'get'):
                # Try to get cached results
                cached_data = self.app.cache_manager.get('batch_search', cache_key)
                if cached_data:
                    logger.info(f"Using cached batch search results for {address}")
                    properties = cached_data.get('properties', [])
                    location_name = cached_data.get('location_name', address)
                    radius_km = float(radius) / 1000.0
                    cache_timestamp = cached_data.get('timestamp', time.time() - 3600)  # Default to 1 hour ago

                    # Add cache timestamp to each property
                    for prop in properties:
                        prop['cache_timestamp'] = cache_timestamp

                    # Call the callback with the cached results
                    if callback and properties:
                        logger.info(f"Calling callback with cached results: {len(properties)} properties")
                        self.app.root.after(0, lambda: callback(properties, location_name, radius_km, True))
                        self.app.root.after(0, lambda: self.app.show_status(
                            f"Found {len(properties)} properties near {location_name} (from cache)"))
                        self.search_in_progress = False
                        return

            # If we don't have cached results, proceed with the search
            # Get coordinates for the address using Google Maps
            geocode_result = self.app.google_maps.geocode(address)

            if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
                logger.error(f"Could not geocode address: {address}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "Geocoding Error", f"Could not find coordinates for address: {address}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            lat = geocode_result['lat']
            lng = geocode_result['lng']
            location_name = geocode_result.get('formatted_address', address)

            logger.info(f"Geocoded {address} to {lat}, {lng} ({location_name})")

            # Generate a grid of points within the radius
            grid_points = self._generate_grid_points(lat, lng, radius, max_results)
            logger.info(f"Generated {len(grid_points)} grid points within {radius/1000}km radius")

            # Search for properties at each grid point
            properties = []
            unique_ruian_ids = set()  # Track unique RUIAN IDs to avoid duplicates
            processed_points = 0

            for point in grid_points:
                if self.cancel_search:
                    logger.info("Search cancelled by user")
                    break

                # Update status every 5 points
                if processed_points % 5 == 0:
                    self.app.root.after(0, lambda p=processed_points, t=len(grid_points):
                                       self.app.show_status(f"Searching... ({p}/{t} points processed)"))

                # Search for properties at this point
                point_properties = self._search_properties_at_point(
                    point['lat'], point['lng'], property_types)

                # Add only unique properties based on RUIAN ID
                if point_properties:
                    for prop in point_properties:
                        ruian_id = prop.get('ruian_id')
                        if ruian_id and ruian_id not in unique_ruian_ids:
                            unique_ruian_ids.add(ruian_id)
                            properties.append(prop)
                        elif not ruian_id:
                            # If no RUIAN ID, add it anyway (might be from OSM)
                            properties.append(prop)

                    # Log the number of properties found so far
                    if len(properties) % 10 == 0:
                        logger.info(f"Found {len(properties)} unique properties so far")

                    # Check if we've reached the maximum number of results
                    if len(properties) >= max_results:
                        logger.info(f"Reached maximum number of results ({max_results})")
                        break

                processed_points += 1

                # Small delay to avoid overwhelming the server
                time.sleep(0.2)  # Increased delay to be more respectful to the API

            # Add radius to each property and prepare for URL generation when needed
            for prop in properties:
                # Add the radius to the property data
                prop['radius'] = radius / 1000  # Convert from meters to kilometers

                if 'lat' in prop and 'lng' in prop:
                    # Convert WGS84 coordinates to S-JTSK (returns integers)
                    x, y = self.app.cuzk_integration.convert_wgs84_to_sjtsk(prop['lat'], prop['lng'])
                    # Store S-JTSK coordinates in the property data
                    prop['x_sjtsk'] = x
                    prop['y_sjtsk'] = y
                    # URLs will be generated only when needed, not automatically

            # Add debug logging
            logger.info(f"Search completed: Found {len(properties)} properties near {location_name}")
            logger.info(f"First property: {properties[0] if properties else 'No properties'}")

            # Convert radius from meters to kilometers for display
            radius_km = float(radius) / 1000.0

            # Cache the results if we have a cache manager
            if hasattr(self.app, 'cache_manager') and hasattr(self.app.cache_manager, 'set'):
                cache_key = f"{address}_{radius}_{max_results}_{'-'.join(property_types) if property_types else 'all'}"
                cache_data = {
                    'properties': properties,
                    'location_name': location_name,
                    'timestamp': time.time()
                }
                # Cache for 1 hour (3600 seconds)
                self.app.cache_manager.set('batch_search', cache_key, cache_data, 3600)
                logger.info(f"Cached batch search results for {address}")

            # Call the callback with the results
            if callback:
                logger.info(f"Calling callback function: {callback.__name__ if hasattr(callback, '__name__') else 'unknown'}")
                logger.info(f"Passing radius: {radius_km} km")
                self.app.root.after(0, lambda: callback(properties, location_name, radius_km, False))
            else:
                # Use the default display method
                logger.info("No callback provided, using default display method")
                self.app.root.after(0, lambda: self._display_batch_results(properties, location_name, radius_km))

            # Update status
            self.app.root.after(0, lambda: self.app.show_status(
                f"Found {len(properties)} properties near {location_name}"))

        except Exception as e:
            logger.error(f"Error in batch search thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error(
                "Search Error", f"Error searching for properties: {str(e)}"))
            self.app.root.after(0, lambda: self.app.show_status("Ready"))

        finally:
            self.search_in_progress = False

    def _generate_grid_points(self, center_lat: float, center_lng: float,
                             radius: int, max_points: int) -> List[Dict[str, float]]:
        """
        Generate a grid of points within a radius around a center point.

        Args:
            center_lat (float): Center latitude in WGS84
            center_lng (float): Center longitude in WGS84
            radius (int): Radius in meters
            max_points (int): Maximum number of points to generate

        Returns:
            list: List of dictionaries with lat/lng coordinates
        """
        # Convert radius from meters to degrees (approximate)
        # 1 degree of latitude is approximately 111,000 meters
        radius_lat = radius / 111000
        # 1 degree of longitude varies with latitude
        radius_lng = radius / (111000 * math.cos(math.radians(center_lat)))

        points = []

        # Add the center point first
        points.append({'lat': center_lat, 'lng': center_lng})

        # Generate points in a grid pattern
        # For small radiuses, use a denser grid
        if radius <= 500:  # 500 meters or less
            # Use a dense grid with points every 50 meters
            step_size_lat = 50 / 111000  # 50 meters in degrees latitude
            step_size_lng = 50 / (111000 * math.cos(math.radians(center_lat)))  # 50 meters in degrees longitude
        elif radius <= 1000:  # 1 km or less
            # Use a medium grid with points every 100 meters
            step_size_lat = 100 / 111000  # 100 meters in degrees latitude
            step_size_lng = 100 / (111000 * math.cos(math.radians(center_lat)))  # 100 meters in degrees longitude
        else:
            # Use a sparse grid with points every 200 meters
            step_size_lat = 200 / 111000  # 200 meters in degrees latitude
            step_size_lng = 200 / (111000 * math.cos(math.radians(center_lat)))  # 200 meters in degrees longitude

        # Calculate the number of steps in each direction
        num_steps_lat = min(int(radius_lat / step_size_lat) + 1, 20)  # Limit to 20 steps
        num_steps_lng = min(int(radius_lng / step_size_lng) + 1, 20)  # Limit to 20 steps

        # Generate grid points
        for i in range(-num_steps_lat, num_steps_lat + 1):
            for j in range(-num_steps_lng, num_steps_lng + 1):
                # Calculate the coordinates of this point
                point_lat = center_lat + i * step_size_lat
                point_lng = center_lng + j * step_size_lng

                # Calculate the distance from the center
                dlat = point_lat - center_lat
                dlng = point_lng - center_lng
                distance = math.sqrt(dlat * dlat + dlng * dlng)

                # Only include points within the radius
                if distance <= radius_lat:
                    points.append({'lat': point_lat, 'lng': point_lng})

                    # Check if we've reached the maximum number of points
                    if len(points) >= max_points:
                        return points

        # If we don't have enough points from the grid, add some random points
        if len(points) < max_points:
            # Generate random points within the radius
            remaining_points = max_points - len(points)
            for _ in range(remaining_points):
                # Generate a random angle and distance
                angle = 2 * math.pi * random.random()
                distance = radius_lat * math.sqrt(random.random())

                # Calculate the coordinates of this point
                point_lat = center_lat + distance * math.sin(angle)
                point_lng = center_lng + distance * math.cos(angle)

                points.append({'lat': point_lat, 'lng': point_lng})

        return points

    def _search_properties_at_point(self, lat: float, lng: float,
                                   property_types: List[str] = None) -> List[Dict[str, Any]]:
        """
        Search for properties at a specific point using only RUIAN.

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84
            property_types (list, optional): List of property types to include

        Returns:
            list: List of property data dictionaries
        """
        try:
            # Convert WGS84 to S-JTSK
            x, y = self.app.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)

            # Add a small delay to avoid overwhelming the RUIAN API
            time.sleep(0.1)

            # Create a property with coordinates - don't generate URL yet
            x_int = int(x)
            y_int = int(y)

            # Create a RUIAN ID that includes the coordinates
            ruian_id = f"RUIAN_{x_int}_{y_int}"

            property_data = [{
                'property_type': 'Building',
                'ruian_id': ruian_id,
                'source': 'ruian',
                'lat': lat,
                'lng': lng,
                'x_sjtsk': x_int,
                'y_sjtsk': y_int,
                'owner_name': 'Property details will be available when needed',
                'owner_address': 'Address will be available when needed',
                'radius': 2.0  # Add radius information (in km)
            }]
            logger.info(f"Created property with RUIAN ID: {ruian_id}")

            # Filter by property type if specified
            if property_types and isinstance(property_types, list) and 'all' not in property_types:
                filtered_data = []
                for prop in property_data:
                    prop_type = prop.get('property_type', '').lower()
                    if any(ptype.lower() in prop_type for ptype in property_types):
                        filtered_data.append(prop)
                property_data = filtered_data

            return property_data

        except Exception as e:
            logger.error(f"Error searching properties at point {lat}, {lng}: {e}", exc_info=True)
            return []

    def _display_batch_results(self, properties: List[Dict[str, Any]], location_name: str, radius: float = None):
        """
        Display batch search results in the UI.

        Args:
            properties (list): List of property data dictionaries
            location_name (str): Name of the location searched
            radius (float, optional): Radius used for this search in kilometers
        """
        # Delegate to the property display manager if available
        if hasattr(self.app, 'property_display'):
            self.app.property_display.display_batch_results(properties, location_name)
        else:
            # Fallback to simple display
            logger.info(f"Found {len(properties)} properties near {location_name}")

            # If we have a batch results text widget, update it
            if hasattr(self.app, 'batch_results_text'):
                self.app.batch_results_text.config(state="normal")
                self.app.batch_results_text.delete(1.0, "end")

                self.app.batch_results_text.insert("end", f"Found {len(properties)} properties near {location_name}\n\n")

                for i, prop in enumerate(properties):
                    self.app.batch_results_text.insert("end", f"{i+1}. {prop.get('property_type', 'Building')}\n")
                    self.app.batch_results_text.insert("end", f"   RUIAN ID: {prop.get('ruian_id', 'Unknown')}\n")
                    self.app.batch_results_text.insert("end", f"   Address: {prop.get('owner_address', 'Unknown')}\n")

                    # Generate URL on demand if we have S-JTSK coordinates
                    if 'x_sjtsk' in prop and 'y_sjtsk' in prop:
                        url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={prop['x_sjtsk']}&y={prop['y_sjtsk']}"
                        self.app.batch_results_text.insert("end", f"   URL: {url}\n")
                    elif 'url' in prop:
                        self.app.batch_results_text.insert("end", f"   URL: {prop['url']}\n")
                    self.app.batch_results_text.insert("end", "\n")

                self.app.batch_results_text.config(state="disabled")

    def cancel_current_search(self):
        """Cancel the current search if one is in progress."""
        if self.search_in_progress:
            logger.info("Cancelling current search")
            self.cancel_search = True
            self.app.show_status("Cancelling search...")
        else:
            logger.info("No search in progress to cancel")
