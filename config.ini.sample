[DEFAULT]
# General application settings
development_mode = false
default_language = cs
log_level = INFO
cache_enabled = true
cache_expiry = 86400
default_region = Czech Republic
default_city = Prague
max_batch_size = 100
font_scale = 1.0

[OPENAI]
# Your OpenAI API key for gender determination and other AI features
# Get one at: https://platform.openai.com/api-keys
api_key = your_openai_api_key_here
model = gpt-3.5-turbo
temperature = 0.7
max_tokens = 150

[GOOGLE_MAPS]
# Your Google Maps API key for location services
# Get one at: https://developers.google.com/maps/documentation/javascript/get-api-key
api_key = your_google_maps_api_key_here
region = cz
language = cs

[CUZK]
# Your ČÚZK (Czech Office for Surveying, Mapping and Cadastre) credentials
# These are used to bypass CAPTCHA on the property registry website
username = your_cuzk_username
password = your_cuzk_password

[PATHS]
# Application file paths
data_dir = data
cache_dir = data/cache
templates_dir = data/templates
czech_data_dir = data/czech_data

[UI]
# User interface settings
window_width = 900
window_height = 700
theme = default
font_family = Arial
default_font_size = 9
small_font_size = 8
medium_font_size = 10
large_font_size = 11
title_font_size = 12

[CACHE]
# Cache settings
enabled = true
expiry_hours = 24
max_size_mb = 100
cleanup_on_start = true

[CAPTCHA]
# CAPTCHA solving settings
use_gpt = true
use_tesseract = true
tesseract_path = C:/Program Files/Tesseract-OCR/tesseract.exe
