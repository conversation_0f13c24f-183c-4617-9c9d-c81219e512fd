"""
Compare two locations to diagnose coordinate conversion issues.

This script geocodes two addresses and compares their coordinates in both WGS84 and S-JTSK.
It also generates URLs for both Google Maps and CUZK to visually compare the locations.
"""

import os
import sys
import logging
import argparse
from typing import Dict, Any, List, Tuple, Optional

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("LocationComparer")

# Import necessary modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk, convert_sjtsk_to_wgs84


def load_api_key() -> Optional[str]:
    """Load Google Maps API key from config file."""
    import configparser
    config = configparser.ConfigParser()
    if os.path.exists('config.ini'):
        config.read('config.ini')
        if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
            return config['GOOGLE_MAPS']['api_key']
    return None


def geocode_address(google_maps, address: str, country: str = "Czech Republic") -> Dict[str, Any]:
    """
    Geocode an address using Google Maps API.

    Args:
        google_maps: GoogleMapsIntegration instance
        address (str): Address to geocode
        country (str): Country to use for geocoding

    Returns:
        dict: Geocoding result
    """
    logger.info(f"Geocoding address: {address}, {country}")
    try:
        geocode_result = google_maps.geocode_address(address, country)
        if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
            logger.error(f"Failed to geocode address: {address}")
            return {'error': 'Geocoding failed'}

        lat = geocode_result['lat']
        lng = geocode_result['lng']
        formatted_address = geocode_result.get('formatted_address', address)
        logger.info(f"Geocoded to: {lat}, {lng}")
        logger.info(f"Formatted address: {formatted_address}")

        # Convert to S-JTSK
        sjtsk_x, sjtsk_y = convert_wgs84_to_sjtsk(lat, lng)
        logger.info(f"Converted to S-JTSK: {sjtsk_x}, {sjtsk_y}")

        # Generate URLs
        google_maps_url = f"https://www.google.com/maps/search/?api=1&query={lat},{lng}"
        cuzk_url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={int(sjtsk_x)}&y={int(sjtsk_y)}"

        return {
            'address': address,
            'formatted_address': formatted_address,
            'wgs84': {'lat': lat, 'lng': lng},
            'sjtsk': {'x': sjtsk_x, 'y': sjtsk_y},
            'google_maps_url': google_maps_url,
            'cuzk_url': cuzk_url
        }

    except Exception as e:
        logger.error(f"Error geocoding address: {e}")
        return {'error': str(e)}


def compare_locations(address1: str, address2: str, country: str = "Czech Republic") -> Dict[str, Any]:
    """
    Compare two locations by geocoding them and comparing their coordinates.

    Args:
        address1 (str): First address to compare
        address2 (str): Second address to compare
        country (str): Country to use for geocoding

    Returns:
        dict: Comparison results
    """
    # Initialize Google Maps integration
    api_key = load_api_key()
    if not api_key:
        logger.error("Google Maps API key not found in config.ini")
        return {'error': 'Google Maps API key not found'}

    google_maps = GoogleMapsIntegration()

    # Geocode both addresses
    location1 = geocode_address(google_maps, address1, country)
    location2 = geocode_address(google_maps, address2, country)

    if 'error' in location1 or 'error' in location2:
        return {'error': location1.get('error') or location2.get('error')}

    # Calculate distances
    from math import sqrt
    wgs84_distance = sqrt(
        (location1['wgs84']['lat'] - location2['wgs84']['lat'])**2 +
        (location1['wgs84']['lng'] - location2['wgs84']['lng'])**2
    )

    sjtsk_distance = sqrt(
        (location1['sjtsk']['x'] - location2['sjtsk']['x'])**2 +
        (location1['sjtsk']['y'] - location2['sjtsk']['y'])**2
    )

    # Calculate approximate distance in kilometers
    # 1 degree of latitude is approximately 111 kilometers
    wgs84_distance_km = wgs84_distance * 111

    # Return comparison results
    return {
        'location1': location1,
        'location2': location2,
        'wgs84_distance': wgs84_distance,
        'wgs84_distance_km': wgs84_distance_km,
        'sjtsk_distance': sjtsk_distance
    }


def main():
    """Main function to run the location comparison."""
    parser = argparse.ArgumentParser(description='Compare two locations to diagnose coordinate conversion issues.')
    parser.add_argument('address1', type=str, help='First address to compare')
    parser.add_argument('address2', type=str, help='Second address to compare')
    parser.add_argument('--country', type=str, default="Czech Republic", help='Country (default: Czech Republic)')

    args = parser.parse_args()

    results = compare_locations(args.address1, args.address2, args.country)

    if 'error' in results:
        print(f"Error: {results['error']}")
        return

    # Print a summary
    print("\n=== Location Comparison Results ===")
    print(f"Location 1: {results['location1']['address']}")
    print(f"Formatted address: {results['location1']['formatted_address']}")
    print(f"WGS84: {results['location1']['wgs84']['lat']}, {results['location1']['wgs84']['lng']}")
    print(f"S-JTSK: {results['location1']['sjtsk']['x']}, {results['location1']['sjtsk']['y']}")
    print(f"Google Maps URL: {results['location1']['google_maps_url']}")
    print(f"CUZK URL: {results['location1']['cuzk_url']}")

    print(f"\nLocation 2: {results['location2']['address']}")
    print(f"Formatted address: {results['location2']['formatted_address']}")
    print(f"WGS84: {results['location2']['wgs84']['lat']}, {results['location2']['wgs84']['lng']}")
    print(f"S-JTSK: {results['location2']['sjtsk']['x']}, {results['location2']['sjtsk']['y']}")
    print(f"Google Maps URL: {results['location2']['google_maps_url']}")
    print(f"CUZK URL: {results['location2']['cuzk_url']}")

    print("\nDistance:")
    print(f"WGS84 distance: {results['wgs84_distance']:.6f} degrees (approximately {results['wgs84_distance_km']:.2f} km)")
    print(f"S-JTSK distance: {results['sjtsk_distance']:.2f} meters")


if __name__ == "__main__":
    main()
