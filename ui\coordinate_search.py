"""
Coordinate search UI components for the Czech Property Registry application.

This module provides UI components for searching properties by coordinates.
"""

import tkinter as tk
from tkinter import ttk
from ui.message_boxes import MessageBoxes


class CoordinateSearchUI:
    """
    UI components for coordinate search functionality.

    This class creates and manages the UI elements for searching
    properties by coordinates (latitude and longitude).
    """

    def __init__(self, parent_frame, app):
        """
        Initialize the coordinate search UI.

        Args:
            parent_frame: The parent frame to place the UI elements in
            app: The main application instance for callbacks
        """
        self.parent = parent_frame
        self.app = app

        # Create the UI elements
        self.create_ui()

    def create_ui(self):
        """Create the coordinate search UI elements"""
        # Create a frame for the coordinate search
        coordinate_frame = ttk.LabelFrame(self.parent, text="Search by Coordinates")
        coordinate_frame.pack(fill="x", padx=5, pady=5)

        # Add a description
        ttk.Label(
            coordinate_frame,
            text="Enter coordinates to search for property information",
            wraplength=400,
            font=("Arial", 9),
            foreground="#333333"
        ).pack(pady=2)

        # Create a frame for the coordinate inputs
        coord_input_frame = ttk.Frame(coordinate_frame)
        coord_input_frame.pack(fill="x", padx=5, pady=5)

        # Add X coordinate (latitude) label and entry
        ttk.Label(coord_input_frame, text="Latitude (X):").grid(row=0, column=0, padx=2, pady=2, sticky="w")
        self.coord_x = ttk.Entry(coord_input_frame, width=15)
        self.coord_x.grid(row=0, column=1, padx=2, pady=2, sticky="w")

        # Add Y coordinate (longitude) label and entry
        ttk.Label(coord_input_frame, text="Longitude (Y):").grid(row=1, column=0, padx=2, pady=2, sticky="w")
        self.coord_y = ttk.Entry(coord_input_frame, width=15)
        self.coord_y.grid(row=1, column=1, padx=2, pady=2, sticky="w")

        # Add coordinate system information
        ttk.Label(
            coord_input_frame,
            text="Use WGS84 coordinate system (decimal degrees)",
            foreground="gray",
            font=("Arial", 8)
        ).grid(row=2, column=0, columnspan=2, padx=2, pady=2, sticky="w")

        # Add example coordinates
        ttk.Label(
            coord_input_frame,
            text="Example: 50.0755, 14.4378 (Prague)",
            foreground="gray",
            font=("Arial", 8)
        ).grid(row=3, column=0, columnspan=2, padx=2, pady=2, sticky="w")

        # Configure the grid to expand properly
        coord_input_frame.columnconfigure(1, weight=1)

        # Create a frame for the coordinate search buttons
        coord_button_frame = ttk.Frame(coordinate_frame)
        coord_button_frame.pack(fill="x", padx=5, pady=5)

        # Add search button for coordinates
        self.coord_search_btn = ttk.Button(
            coord_button_frame,
            text="Search",
            command=self.on_search_coordinates,
            style="Accent.TButton"
        )
        self.coord_search_btn.pack(side="right", padx=2)

        # Add show on map button for coordinates
        self.coord_show_map_btn = ttk.Button(
            coord_button_frame,
            text="Show on Map",
            command=self.on_show_coordinates_on_map
        )
        self.coord_show_map_btn.pack(side="left", padx=2)

        # Add a separator
        ttk.Separator(self.parent, orient="horizontal").pack(fill="x", padx=5, pady=5)

        # Create a frame for the parcel number search
        parcel_frame = ttk.LabelFrame(self.parent, text="Search by Parcel Number")
        parcel_frame.pack(fill="x", padx=5, pady=5)

        # Add a description
        ttk.Label(
            parcel_frame,
            text="Enter a parcel number to search for property information",
            wraplength=400,
            font=("Arial", 9),
            foreground="#333333"
        ).pack(pady=2)

        # Create a frame for the parcel number input
        parcel_input_frame = ttk.Frame(parcel_frame)
        parcel_input_frame.pack(fill="x", padx=5, pady=5)

        # Add parcel number label and entry
        ttk.Label(parcel_input_frame, text="Parcel Number:").pack(side="left", padx=2)
        self.parcel_number = ttk.Entry(parcel_input_frame, width=20)
        self.parcel_number.pack(side="left", padx=2, fill="x", expand=True)

        # Add example parcel number
        ttk.Label(
            parcel_input_frame,
            text="Example: 123/45",
            foreground="gray",
            font=("Arial", 8)
        ).pack(side="left", padx=2)

        # Create a frame for the parcel search button
        parcel_button_frame = ttk.Frame(parcel_frame)
        parcel_button_frame.pack(fill="x", padx=5, pady=5)

        # Add search button for parcel number
        self.parcel_search_btn = ttk.Button(
            parcel_button_frame,
            text="Search",
            command=self.on_search_parcel,
            style="Accent.TButton"
        )
        self.parcel_search_btn.pack(side="right", padx=2)

        # Add a separator
        ttk.Separator(self.parent, orient="horizontal").pack(fill="x", padx=5, pady=5)

        # Create a frame for the search results
        self.results_frame = ttk.LabelFrame(self.parent, text="Search Results")
        self.results_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Add a text widget for displaying results
        self.results_text = tk.Text(self.results_frame, height=10, width=50, wrap="word")
        scrollbar = ttk.Scrollbar(self.results_frame, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.pack(side="left", fill="both", expand=True, padx=2, pady=2)
        scrollbar.pack(side="right", fill="y", padx=(0, 2), pady=2)

        # Add initial text
        self.results_text.insert("1.0", "Search results will appear here. Enter coordinates or a parcel number and click 'Search'.")
        self.results_text.config(state="disabled")

    def on_search_coordinates(self):
        """Handle the search button click for coordinates"""
        # Get the coordinates
        try:
            lat = float(self.coord_x.get().strip())
            lng = float(self.coord_y.get().strip())
        except ValueError:
            MessageBoxes.show_warning("Invalid Input", "Please enter valid numeric coordinates.")
            return

        # Call the application's search method
        if hasattr(self.app, 'search_by_coordinates'):
            self.app.search_by_coordinates(lat, lng)
        else:
            print("Search by coordinates method not implemented in the application.")

    def on_show_coordinates_on_map(self):
        """Handle the show on map button click for coordinates"""
        # Get the coordinates
        try:
            # Validate coordinates but we don't need to use them here
            float(self.coord_x.get().strip())
            float(self.coord_y.get().strip())
        except ValueError:
            MessageBoxes.show_warning("Invalid Input", "Please enter valid numeric coordinates.")
            return

        # Call the application's map update method
        if hasattr(self.app, 'show_coordinates_on_map'):
            self.app.show_coordinates_on_map()
        else:
            print("Show coordinates on map method not implemented in the application.")

    def on_search_parcel(self):
        """Handle the search button click for parcel number"""
        # Get the parcel number
        parcel_number = self.parcel_number.get().strip()

        # Validate input
        if not parcel_number:
            MessageBoxes.show_warning("Missing Information", "Please enter a parcel number.")
            return

        # Call the application's search method
        if hasattr(self.app, 'search_by_parcel'):
            self.app.search_by_parcel(parcel_number)
        else:
            print("Search by parcel method not implemented in the application.")

    def update_results(self, text):
        """Update the results text widget with new text"""
        # Enable the text widget for updating
        self.results_text.config(state="normal")

        # Clear existing text
        self.results_text.delete("1.0", tk.END)

        # Insert new text
        self.results_text.insert("1.0", text)

        # Disable the text widget after updating
        self.results_text.config(state="disabled")

    def set_coordinates(self, lat, lng):
        """Set the coordinate fields"""
        self.coord_x.delete(0, tk.END)
        self.coord_x.insert(0, str(lat))

        self.coord_y.delete(0, tk.END)
        self.coord_y.insert(0, str(lng))
