"""
Canvas management utilities for the Czech Property Registry application.

This module provides functionality for managing canvas widgets,
including resizing and configuration.
"""

import tkinter as tk
from tkinter import ttk


class CanvasManager:
    """
    Manages canvas behavior for tkinter applications.

    Provides support for:
    - Canvas resizing
    - Content frame management
    - Scrollbar visibility control
    """

    def __init__(self, root, canvas, content_frame, vsb=None, hsb=None):
        """
        Initialize the CanvasManager.

        Args:
            root: The root tkinter window
            canvas: The canvas widget to manage
            content_frame: The frame inside the canvas containing the content
            vsb: Optional vertical scrollbar
            hsb: Optional horizontal scrollbar
        """
        self.root = root
        self.canvas = canvas
        self.content_frame = content_frame
        self.vsb = vsb
        self.hsb = hsb

        # Initialize processing flags
        self._processing_canvas_configure = False
        self._processing_content_configure = False

        # Bind events
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        self.content_frame.bind("<Configure>", self.on_content_configure)

    def on_canvas_configure(self, event):
        """Handle canvas resize event"""
        # Check if we're already processing a configure event to prevent stuttering
        if self._processing_canvas_configure:
            return

        self._processing_canvas_configure = True

        try:
            # Simply call force_resize to handle the resize
            self.force_resize()
        finally:
            # Clear the processing flag after a short delay
            self.root.after(50, self._clear_canvas_configure_flag)

    def _clear_canvas_configure_flag(self):
        """Clear the canvas configure processing flag"""
        self._processing_canvas_configure = False

    def on_content_configure(self, event=None):
        """Handle content frame resize event"""
        # Check if we're already processing a configure event to prevent stuttering
        if self._processing_content_configure:
            return

        self._processing_content_configure = True

        try:
            # Simply call force_resize to handle the resize
            self.force_resize()
        finally:
            # Clear the processing flag after a short delay
            self.root.after(50, self._clear_content_configure_flag)

    def _clear_content_configure_flag(self):
        """Clear the content configure processing flag"""
        self._processing_content_configure = False

    def _get_all_parents(self, widget):
        """
        Get all parent widgets of a given widget

        Args:
            widget: The widget to get parents for

        Returns:
            list: List of parent widgets
        """
        parents = []
        parent = widget.master

        while parent:
            parents.append(parent)
            try:
                parent = parent.master
            except AttributeError:
                break

        return parents

    def update_scrollbar_visibility(self):
        """Update the visibility of scrollbars based on content size"""
        # If no scrollbars are provided, there's nothing to update
        if not self.vsb and not self.hsb:
            return

        # Get the current scroll region
        scroll_region = self.canvas.cget("scrollregion")
        if not scroll_region:
            return

        # Parse the scroll region
        try:
            x1, y1, x2, y2 = map(int, scroll_region.split())
        except (ValueError, IndexError):
            return

        # Get the canvas size
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        # Determine if scrollbars are needed
        need_vsb = y2 - y1 > canvas_height
        need_hsb = x2 - x1 > canvas_width

        # Configure vertical scrollbar visibility
        if self.vsb:
            if need_vsb:
                self.vsb.pack(side="right", fill="y")
            else:
                # Don't hide vertical scrollbar - keep it visible for consistency
                pass

        # Configure horizontal scrollbar visibility
        if self.hsb:
            if need_hsb:
                self.hsb.pack(side="bottom", fill="x")
            else:
                # Don't hide horizontal scrollbar - keep it visible for consistency
                pass

    def reset_view(self):
        """Reset the canvas view to the top-left corner"""
        self.canvas.xview_moveto(0)
        self.canvas.yview_moveto(0)

    def scroll_to_widget(self, widget):
        """
        Scroll the canvas to make a specific widget visible

        Args:
            widget: The widget to scroll to
        """
        # Get the widget's position relative to the canvas
        x = widget.winfo_x()
        y = widget.winfo_y()

        # Get the widget's size
        width = widget.winfo_width()
        height = widget.winfo_height()

        # Get the canvas size
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        # Get the current scroll region
        scroll_region = self.canvas.cget("scrollregion")
        if not scroll_region:
            return

        # Parse the scroll region
        try:
            _, _, scroll_width, scroll_height = map(int, scroll_region.split())
        except (ValueError, IndexError):
            return

        # Calculate the target scroll position
        # Center the widget in the visible area if possible
        x_fraction = max(0, min(1, (x + width/2 - canvas_width/2) / scroll_width))
        y_fraction = max(0, min(1, (y + height/2 - canvas_height/2) / scroll_height))

        # Scroll to the target position
        self.canvas.xview_moveto(x_fraction)
        self.canvas.yview_moveto(y_fraction)

        # Update the canvas immediately
        self.canvas.update_idletasks()

    def force_resize(self):
        """
        Force a resize of the canvas and content frame

        This method is useful when the window size changes and we need to
        ensure that all elements resize properly.
        """
        # Update the canvas to ensure it has the correct size
        self.canvas.update_idletasks()

        # Get the content frame's full size
        content_width = self.content_frame.winfo_reqwidth()
        content_height = self.content_frame.winfo_reqheight()

        # Get the canvas size
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        # Use the larger of the content size or canvas size for the scroll region
        scroll_width = max(content_width, canvas_width)
        scroll_height = max(content_height, canvas_height)

        # Set the scroll region explicitly
        self.canvas.configure(scrollregion=(0, 0, scroll_width, scroll_height))

        # Make sure scrollbars appear/disappear as needed
        self.update_scrollbar_visibility()
