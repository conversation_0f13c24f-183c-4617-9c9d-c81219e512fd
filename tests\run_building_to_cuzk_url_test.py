"""
Run the test for converting building coordinates to CUZK MapaIdentifikace URLs.

This script runs the test for converting building coordinates to S-JTSK format
and generating MapaIdentifikace URLs for buildings found in batch search results.
"""

import unittest
import logging
import sys
import os

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from tests.test_building_to_cuzk_url import TestBuildingToCUZKURL

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def run_test() -> None:
    """Run the test for converting building coordinates to CUZK MapaIdentifikace URLs."""
    logger.info("Running test for converting building coordinates to CUZK MapaIdentifikace URLs")

    # Create a test suite with the TestBuildingToCUZKURL class
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBuildingToCUZKURL)

    # Run the test suite
    result = unittest.TextTestRunner(verbosity=2).run(suite)

    # Log the result
    if result.wasSuccessful():
        logger.info("All tests passed successfully")
    else:
        logger.error(f"Tests failed: {len(result.failures)} failures, {len(result.errors)} errors")


if __name__ == '__main__':
    run_test()
