"""
Simple test script for OSM and CUZK integration.

This script tests the integration between OpenStreetMap and CUZK,
verifying that the application can fetch buildings from OSM and
open the CUZK website with the retrieved RUIAN IDs.

Run this script directly to test the integration.
"""

import os
import sys
import logging
import time
import webbrowser
from typing import List, Dict, Any, Optional

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OSM_CUZK_Test")

# Import the necessary modules
from api.osm.osm_integration import OSMIntegration
from api.cuzk.cuzk_integration import CUZKIntegration
from utils.enhanced_cache_manager import EnhancedCacheManager
from core.optimized_network import create_optimized_session


class MockApp:
    """Mock application for testing."""

    def __init__(self):
        """Initialize the mock application."""
        # Create a session
        self.session = create_optimized_session()

        # Create a cache manager
        self.cache_manager = EnhancedCacheManager(
            default_expiry=300,  # 5 minutes
            disk_cache_dir="test_cache",
            max_memory_items=100
        )

        # Status tracking
        self.status = ""

    def show_status(self, message: str) -> None:
        """
        Show a status message.

        Args:
            message (str): Status message
        """
        self.status = message
        logger.info(f"Status: {message}")


def test_osm_cuzk_integration() -> None:
    """Test the integration between OSM and CUZK."""
    try:
        # Create a mock application
        app = MockApp()

        # Create the OSM integration
        osm_integration = OSMIntegration(app)

        # Create the CUZK integration
        cuzk_integration = CUZKIntegration()

        # Set up the app with the integrations
        app.osm_integration = osm_integration
        app.cuzk_integration = cuzk_integration

        # Coordinates for testing (Prague)
        test_lat = 50.0755
        test_lng = 14.4378
        test_radius = 500  # meters

        # Test fetching buildings from OSM
        logger.info(f"Testing OSM building fetch at coordinates {test_lat}, {test_lng}")
        buildings = osm_integration.search_buildings_by_coordinates(test_lat, test_lng, test_radius)

        # Verify that buildings were found
        if not buildings:
            logger.error("No buildings returned from OSM")
            return

        logger.info(f"Found {len(buildings)} buildings from OSM")

        # Check if any buildings have RUIAN references
        buildings_with_ruian = [b for b in buildings if 'ruian_ref' in b]
        logger.info(f"Found {len(buildings_with_ruian)} buildings with RUIAN references")

        # We should have at least one building with a RUIAN reference
        if not buildings_with_ruian:
            logger.error("No buildings with RUIAN references found")
            return

        # Get the first building with a RUIAN reference
        building = buildings_with_ruian[0]
        ruian_id = building['ruian_ref']

        # Test generating a CUZK URL from coordinates
        logger.info("Testing CUZK URL generation")
        url = cuzk_integration.generate_cuzk_url(test_lat, test_lng)

        # Verify that a URL was generated
        if not url:
            logger.error("No URL generated")
            return

        if not url.startswith("https://nahlizenidokn.cuzk.cz/"):
            logger.error("URL does not point to CUZK website")
            return

        logger.info(f"Generated CUZK URL: {url}")

        # Test opening the CUZK website with a RUIAN ID
        logger.info(f"Testing opening CUZK with RUIAN ID: {ruian_id}")

        # Ask the user if they want to open the CUZK website
        open_browser = input(f"Open CUZK website with RUIAN ID {ruian_id}? (y/n): ")

        if open_browser.lower() == 'y':
            # Open the CUZK website with the RUIAN ID
            url = f"https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id={ruian_id}"
            logger.info(f"Opening URL: {url}")
            webbrowser.open(url)

            # Wait for the user to confirm
            input("Press Enter after the CUZK website has opened...")

            logger.info(f"Successfully opened CUZK with RUIAN ID: {ruian_id}")
        else:
            logger.info("Skipping browser open test")

        # Test successful
        logger.info("All tests completed successfully")

    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)


if __name__ == '__main__':
    test_osm_cuzk_integration()
