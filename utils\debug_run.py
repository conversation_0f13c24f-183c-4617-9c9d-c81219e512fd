import traceback
import sys
import os

# Enable more verbose output
os.environ['PYTHONVERBOSE'] = '1'

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    try:
        print("Importing core module...")
        sys.stdout.flush()
        from core.app_factory import AppFactory

        print("Creating application instance...")
        sys.stdout.flush()
        app = AppFactory.create_app(app_type="standard")

        print("Starting application...")
        sys.stdout.flush()
        app.run()

        print("Application exited normally.")
    except Exception as e:
        print(f"Error: {e}")
        print("Traceback:")
        traceback.print_exc()
        sys.stdout.flush()

if __name__ == "__main__":
    main()
