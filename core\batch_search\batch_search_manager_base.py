"""
Base batch search manager for the Czech Property Registry application.

This module provides a base class for all batch search manager implementations,
with common functionality and standardized method signatures.
"""

import threading
import time
import logging
from typing import List, Dict, Any, Optional, Callable, Tuple, Union

# Set up logging
logger = logging.getLogger(__name__)


class BatchSearchManagerBase:
    """
    Base class for all batch search manager implementations.

    This class provides common functionality and standardized method signatures
    for batch search operations, including address-based, coordinate-based,
    and city-based searches.
    """

    def __init__(self, app):
        """
        Initialize the base batch search manager.

        Args:
            app: The main application instance
        """
        self.app = app
        self.search_in_progress = False
        self.cancel_search = False
        self.search_results = []
        self.search_thread = None
        logger.info(f"BatchSearchManagerBase initialized: {self.__class__.__name__}")

    def batch_search_by_address(self, address: str, radius: float = 2.0,
                               property_types: Optional[List[str]] = None,
                               max_results: int = 50,
                               callback: Optional[Callable[[List[Dict[str, Any]]], None]] = None) -> None:
        """
        Perform a batch search for properties by address with a customizable radius.

        Args:
            address (str): Full address to search in
            radius (float): Search radius in kilometers (default: 2.0)
            property_types (list, optional): List of property types to include in results
            max_results (int): Maximum number of results to return (default: 50)
            callback (callable, optional): Function to call with the results
        """
        try:
            if self.search_in_progress:
                logger.warning("Search already in progress, ignoring new request")
                return

            self.search_in_progress = True
            self.cancel_search = False

            logger.info(f"Batch searching by address: {address} with radius {radius}km")

            # Convert radius from km to meters
            radius_meters = int(float(radius) * 1000)

            # Show status
            self.app.show_status(f"Searching for properties near {address}...")

            # Start the search in a background thread
            self.search_thread = threading.Thread(
                target=self._batch_search_thread,
                args=(address, property_types, radius_meters, max_results, callback),
                daemon=True
            )
            self.search_thread.start()

        except Exception as e:
            logger.error(f"Error in batch search by address: {e}", exc_info=True)
            from ui.message_boxes import MessageBoxes
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.search_in_progress = False

    def batch_search_by_coordinates(self, lat: float, lng: float, radius: float = 2.0,
                                   property_types: Optional[List[str]] = None,
                                   max_results: int = 50,
                                   callback: Optional[Callable[[List[Dict[str, Any]]], None]] = None) -> None:
        """
        Perform a batch search for properties by coordinates with a customizable radius.

        Args:
            lat (float): Latitude
            lng (float): Longitude
            radius (float): Search radius in kilometers (default: 2.0)
            property_types (list, optional): List of property types to include in results
            max_results (int): Maximum number of results to return (default: 50)
            callback (callable, optional): Function to call with the results
        """
        try:
            if self.search_in_progress:
                logger.warning("Search already in progress, ignoring new request")
                return

            self.search_in_progress = True
            self.cancel_search = False

            logger.info(f"Batch searching by coordinates: {lat}, {lng} with radius {radius}km")

            # Convert radius from km to meters
            radius_meters = int(float(radius) * 1000)

            # Show status
            self.app.show_status(f"Searching for properties near coordinates {lat}, {lng}...")

            # Start the search in a background thread
            self.search_thread = threading.Thread(
                target=self._batch_search_by_coordinates_thread,
                args=(lat, lng, property_types, radius_meters, max_results, callback),
                daemon=True
            )
            self.search_thread.start()

        except Exception as e:
            logger.error(f"Error in batch search by coordinates: {e}", exc_info=True)
            from ui.message_boxes import MessageBoxes
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.search_in_progress = False

    def batch_search_by_city(self, city: str, property_types: Optional[List[str]] = None,
                            max_results: int = 100,
                            callback: Optional[Callable[[List[Dict[str, Any]]], None]] = None,
                            use_mapycz: bool = True) -> None:
        """
        Perform a batch search for all properties within a city.

        Args:
            city (str): City name
            property_types (list, optional): List of property types to include in results
            max_results (int): Maximum number of results to return (default: 100)
            callback (callable, optional): Function to call with the results
            use_mapycz (bool, optional): Whether to use Mapy.cz for city boundaries (default: True)
        """
        try:
            if self.search_in_progress:
                logger.warning("Search already in progress, ignoring new request")
                return

            self.search_in_progress = True
            self.cancel_search = False

            # Always use Mapy.cz by default
            use_mapycz = True

            logger.info(f"Batch searching by city: {city} using Mapy.cz")

            # Show status
            self.app.show_status(f"Searching for all properties in {city} using Mapy.cz...")

            # Start the search in a background thread
            self.search_thread = threading.Thread(
                target=self._batch_search_by_city_thread,
                args=(city, property_types, max_results, callback, use_mapycz),
                daemon=True
            )
            self.search_thread.start()

        except Exception as e:
            logger.error(f"Error in batch search by city: {e}", exc_info=True)
            from ui.message_boxes import MessageBoxes
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.search_in_progress = False

    def cancel_current_search(self) -> None:
        """Cancel the current search operation."""
        if self.search_in_progress:
            logger.info("Canceling current search")
            self.cancel_search = True
            self.app.show_status("Canceling search...")

    def _batch_search_thread(self, address: str, property_types: Optional[List[str]],
                            radius_meters: int, max_results: int,
                            callback: Optional[Callable[[List[Dict[str, Any]]], None]]) -> None:
        """
        Background thread for batch search by address.

        Args:
            address (str): Full address to search in
            property_types (list): List of property types to include
            radius_meters (int): Search radius in meters
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
        """
        # To be implemented by subclasses
        pass

    def _batch_search_by_coordinates_thread(self, lat: float, lng: float,
                                          property_types: Optional[List[str]],
                                          radius_meters: int, max_results: int,
                                          callback: Optional[Callable[[List[Dict[str, Any]]], None]]) -> None:
        """
        Background thread for batch search by coordinates.

        Args:
            lat (float): Latitude
            lng (float): Longitude
            property_types (list): List of property types to include
            radius_meters (int): Search radius in meters
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
        """
        # To be implemented by subclasses
        pass

    def _batch_search_by_city_thread(self, city: str, property_types: Optional[List[str]],
                                   max_results: int,
                                   callback: Optional[Callable[[List[Dict[str, Any]]], None]],
                                   use_mapycz: bool = True) -> None:
        """
        Background thread for batch search by city.

        Args:
            city (str): City name
            property_types (list): List of property types to include
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
            use_mapycz (bool): Whether to use Mapy.cz for city boundaries
        """
        # To be implemented by subclasses
        pass
