"""
Run the city boundary fallback test.

This script runs the test that verifies the application uses the user-specified radius
instead of defaulting to 5000 meters when city boundaries can't be fetched.
"""

import os
import sys
import logging
import unittest

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RunCityBoundaryFallbackTest")

# Import the test module
from tests.test_city_boundary_fallback import TestCityBoundaryFallback

if __name__ == '__main__':
    logger.info("Running city boundary fallback test...")
    
    # Create a test suite with the city boundary fallback test
    suite = unittest.TestSuite()
    suite.addTest(unittest.makeSuite(TestCityBoundaryFallback))
    
    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Log the results
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Failures: {len(result.failures)}")
    logger.info(f"Skipped: {len(result.skipped)}")
    
    # Exit with appropriate code
    sys.exit(len(result.errors) + len(result.failures))
