"""
Direct Batch Search UI for the Czech Property Registry application.

This module provides a simplified UI for batch searching of buildings
within a radius of a given address, with no limit on the number of results.
"""

import tkinter as tk
from tkinter import ttk
import webbrowser
import logging
import threading
from typing import List, Dict, Any, Optional
from ui.message_boxes import MessageBoxes

# Setup logging
logger = logging.getLogger("DirectBatchSearchUI")

class DirectBatchSearchUI:
    """
    A simplified UI for batch searching of buildings.
    
    This class creates and manages the UI elements for searching
    buildings within a radius of a given address, with no limit
    on the number of results.
    """
    
    def __init__(self, parent, app):
        """
        Initialize the batch search UI.
        
        Args:
            parent: The parent frame or window
            app: The main application instance
        """
        self.parent = parent
        self.app = app
        
        # Create the main frame
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Add a label to indicate this is the batch search UI
        ttk.Label(
            self.frame,
            text="Batch Search",
            font=("Arial", 16, "bold"),
            foreground="#4CAF50"
        ).pack(side="top", pady=10)
        
        # Create the search frame
        self.create_search_frame()
        
        # Create the results frame
        self.create_results_frame()
        
        # Initialize variables
        self.buildings = []
        self.search_in_progress = False
        
    def create_search_frame(self):
        """Create the search frame with address input and radius selection."""
        search_frame = ttk.LabelFrame(self.frame, text="Search Parameters")
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Address input
        ttk.Label(search_frame, text="Address:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.address_var = tk.StringVar()
        self.address_entry = ttk.Entry(search_frame, textvariable=self.address_var, width=40)
        self.address_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # Radius selection
        ttk.Label(search_frame, text="Radius (km):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.radius_var = tk.StringVar(value="1.0")
        radius_values = ["0.0", "0.2", "0.5", "1.0", "2.0", "5.0"]
        self.radius_combobox = ttk.Combobox(search_frame, textvariable=self.radius_var, values=radius_values, width=10)
        self.radius_combobox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Search button
        self.search_button = ttk.Button(search_frame, text="Search", command=self.on_search)
        self.search_button.grid(row=2, column=0, padx=5, pady=10)
        
        # Cancel button
        self.cancel_button = ttk.Button(search_frame, text="Cancel", command=self.on_cancel, state=tk.DISABLED)
        self.cancel_button.grid(row=2, column=1, padx=5, pady=10, sticky=tk.E)
        
        # Status label
        self.status_label = ttk.Label(search_frame, text="Ready")
        self.status_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Progress bar
        self.progress_frame = ttk.Frame(search_frame)
        self.progress_frame.grid(row=4, column=0, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        self.progress_bar = ttk.Progressbar(
            self.progress_frame, 
            orient=tk.HORIZONTAL, 
            length=400, 
            mode="indeterminate"
        )
        self.progress_bar.pack(fill=tk.X, expand=True)
        self.progress_bar.grid_remove()  # Hide initially
        
        # Configure grid
        search_frame.columnconfigure(1, weight=1)
        
    def create_results_frame(self):
        """Create the results frame with a treeview for displaying buildings."""
        results_frame = ttk.LabelFrame(self.frame, text="Search Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create a treeview for displaying buildings
        columns = ("id", "lat", "lng", "address", "url")
        self.buildings_tree = ttk.Treeview(results_frame, columns=columns, show="headings")
        
        # Define column headings
        self.buildings_tree.heading("id", text="#")
        self.buildings_tree.heading("lat", text="Latitude")
        self.buildings_tree.heading("lng", text="Longitude")
        self.buildings_tree.heading("address", text="Address")
        self.buildings_tree.heading("url", text="CUZK URL")
        
        # Define column widths
        self.buildings_tree.column("id", width=50, anchor=tk.CENTER)
        self.buildings_tree.column("lat", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("lng", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("address", width=200, anchor=tk.W)
        self.buildings_tree.column("url", width=250, anchor=tk.W)
        
        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.buildings_tree.yview)
        self.buildings_tree.configure(yscrollcommand=y_scrollbar.set)
        
        # Pack the treeview and scrollbars
        self.buildings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind double-click event to open URL
        self.buildings_tree.bind("<Double-1>", self.on_building_double_click)
        
        # Create buttons frame
        buttons_frame = ttk.Frame(results_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Open selected button
        self.open_selected_button = ttk.Button(buttons_frame, text="Open Selected", command=self.on_open_selected)
        self.open_selected_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # Results count label
        self.results_count_label = ttk.Label(buttons_frame, text="0 buildings found")
        self.results_count_label.pack(side=tk.RIGHT, padx=5, pady=5)
        
    def on_search(self):
        """Handle the search button click."""
        # Get the address and radius
        address = self.address_var.get()
        radius = self.radius_var.get()
        
        if not address:
            MessageBoxes.show_warning("Missing Information", "Please enter an address to search for buildings.")
            return
        
        try:
            radius_float = float(radius)
            if radius_float < 0:
                MessageBoxes.show_warning("Invalid Radius", "Radius must be 0 or greater.")
                return
        except ValueError:
            MessageBoxes.show_warning("Invalid Radius", "Please enter a valid number for the radius.")
            return
        
        # Update UI state
        self.search_button.config(state=tk.DISABLED)
        self.cancel_button.config(state=tk.NORMAL)
        self.status_label.config(text=f"Searching for buildings near {address}...")
        self.search_in_progress = True
        
        # Show progress bar
        self.progress_bar.grid()
        self.progress_bar.start()
        
        # Clear previous results
        self.clear_results()
        
        # Start the search in a separate thread
        def search_thread():
            try:
                # Call the batch search manager
                if hasattr(self.app, 'real_batch_search_manager'):
                    self.app.real_batch_search_manager.batch_search_by_address(
                        address, None, radius_float, None, self.display_search_results
                    )
                else:
                    # Update UI on the main thread
                    self.frame.after(0, lambda: self.handle_error("Real batch search manager not initialized"))
            except Exception as e:
                # Update UI on the main thread
                self.frame.after(0, lambda: self.handle_error(str(e)))
        
        # Start the thread
        threading.Thread(target=search_thread, daemon=True).start()
        
    def on_cancel(self):
        """Handle the cancel button click."""
        if hasattr(self.app, 'real_batch_search_manager'):
            self.app.real_batch_search_manager.cancel_current_search()
        
        # Update UI state
        self.search_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.status_label.config(text="Search cancelled")
        self.search_in_progress = False
        
        # Hide progress bar
        self.progress_bar.stop()
        self.progress_bar.grid_remove()
        
    def handle_error(self, error_msg):
        """Handle errors during search."""
        MessageBoxes.show_error("Error", error_msg)
        
        # Reset UI state
        self.search_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.status_label.config(text="Error in search")
        self.search_in_progress = False
        
        # Hide progress bar
        self.progress_bar.stop()
        self.progress_bar.grid_remove()
        
    def clear_results(self):
        """Clear the results treeview."""
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)
        
        self.buildings = []
        self.results_count_label.config(text="0 buildings found")
        
    def display_search_results(self, buildings):
        """
        Display the search results in the treeview.
        
        Args:
            buildings (list): List of building data dictionaries
        """
        # Update UI on the main thread
        def update_ui():
            # Stop progress bar
            self.progress_bar.stop()
            self.progress_bar.grid_remove()
            
            # Reset UI state
            self.search_button.config(state=tk.NORMAL)
            self.cancel_button.config(state=tk.DISABLED)
            self.search_in_progress = False
            
            if not buildings:
                self.status_label.config(text="No buildings found")
                MessageBoxes.show_info("No Results", "No buildings found in the specified area.")
                return
            
            # Store the buildings
            self.buildings = buildings
            
            # Add buildings to the treeview
            for building in buildings:
                # Get address if available
                address = building.get('name', '')
                if not address and 'street' in building:
                    address = f"{building['street']} {building.get('housenumber', '')}"
                    if 'city' in building:
                        address += f", {building['city']}"
                
                self.buildings_tree.insert("", tk.END, values=(
                    building['id'],
                    f"{building['lat']:.6f}",
                    f"{building['lng']:.6f}",
                    address,
                    building.get('url', '')
                ))
            
            # Update the results count label
            self.results_count_label.config(text=f"{len(buildings)} buildings found")
            self.status_label.config(text="Search completed")
        
        # Schedule UI update on the main thread
        self.frame.after(0, update_ui)
        
    def on_building_double_click(self, event):
        """Handle double-click on a building in the treeview."""
        # Get the selected item
        selected_item = self.buildings_tree.selection()
        if not selected_item:
            return
        
        # Get the URL from the selected item
        values = self.buildings_tree.item(selected_item[0], "values")
        url = values[4]  # URL is the 5th column (index 4)
        
        # Open the URL in the browser
        if url:
            webbrowser.open(url)
        
    def on_open_selected(self):
        """Open the selected buildings in the browser."""
        # Get the selected items
        selected_items = self.buildings_tree.selection()
        if not selected_items:
            MessageBoxes.show_info("No Selection", "Please select one or more buildings to open.")
            return
        
        # Open each selected building
        for item in selected_items:
            values = self.buildings_tree.item(item, "values")
            url = values[4]  # URL is the 5th column (index 4)
            
            if url:
                webbrowser.open(url)
