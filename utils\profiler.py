"""
Profiling utilities for the Czech Property Registry application.
Provides tools for measuring performance and identifying bottlenecks.
"""

import time
import functools
import logging
import threading
import cProfile
import pstats
import io
import os
import tracemalloc
from typing import Dict, List, Callable, Any, Optional, Tuple, Set, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("Profiler")


class Timer:
    """
    A simple timer for measuring execution time.
    Can be used as a context manager or decorator.
    """
    
    def __init__(self, name: str = None, logger_func: Callable = None):
        """
        Initialize the timer.
        
        Args:
            name (str, optional): Name for the timer
            logger_func (callable, optional): Function to log the result
        """
        self.name = name or "Timer"
        self.logger_func = logger_func or logger.info
        self.start_time = None
        self.end_time = None
    
    def __enter__(self) -> 'Timer':
        """Start the timer when entering a context."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Stop the timer when exiting a context."""
        self.stop()
        self.log()
    
    def __call__(self, func: Callable) -> Callable:
        """Use the timer as a decorator."""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with self:
                return func(*args, **kwargs)
        return wrapper
    
    def start(self) -> None:
        """Start the timer."""
        self.start_time = time.time()
    
    def stop(self) -> None:
        """Stop the timer."""
        self.end_time = time.time()
    
    def elapsed(self) -> float:
        """
        Get the elapsed time.
        
        Returns:
            float: Elapsed time in seconds
        """
        if self.start_time is None:
            return 0.0
        
        end_time = self.end_time or time.time()
        return end_time - self.start_time
    
    def log(self) -> None:
        """Log the elapsed time."""
        elapsed = self.elapsed()
        self.logger_func(f"{self.name}: {elapsed:.6f} seconds")


class FunctionProfiler:
    """
    Profiles function calls to identify performance bottlenecks.
    Tracks call count, total time, and average time.
    """
    
    def __init__(self):
        """Initialize the function profiler."""
        self._stats: Dict[str, Dict[str, Union[int, float]]] = {}
        self._lock = threading.Lock()
    
    def profile(self, func: Callable) -> Callable:
        """
        Decorator to profile a function.
        
        Args:
            func: Function to profile
            
        Returns:
            Decorated function
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with self._lock:
                key = f"{func.__module__}.{func.__qualname__}"
                if key not in self._stats:
                    self._stats[key] = {
                        'calls': 0,
                        'total_time': 0.0,
                        'min_time': float('inf'),
                        'max_time': 0.0
                    }
            
            # Measure execution time
            start_time = time.time()
            try:
                return func(*args, **kwargs)
            finally:
                elapsed = time.time() - start_time
                
                # Update statistics
                with self._lock:
                    self._stats[key]['calls'] += 1
                    self._stats[key]['total_time'] += elapsed
                    self._stats[key]['min_time'] = min(self._stats[key]['min_time'], elapsed)
                    self._stats[key]['max_time'] = max(self._stats[key]['max_time'], elapsed)
        
        return wrapper
    
    def get_stats(self) -> Dict[str, Dict[str, Union[int, float]]]:
        """
        Get profiling statistics.
        
        Returns:
            Dictionary of function statistics
        """
        with self._lock:
            # Create a copy with computed average
            result = {}
            for key, stats in self._stats.items():
                result[key] = stats.copy()
                if stats['calls'] > 0:
                    result[key]['avg_time'] = stats['total_time'] / stats['calls']
            return result
    
    def print_stats(self, sort_by: str = 'total_time', limit: int = 20) -> None:
        """
        Print profiling statistics.
        
        Args:
            sort_by (str): Field to sort by ('calls', 'total_time', 'avg_time', 'min_time', 'max_time')
            limit (int): Maximum number of functions to show
        """
        stats = self.get_stats()
        
        # Sort by the specified field
        sorted_stats = sorted(
            stats.items(),
            key=lambda x: x[1].get(sort_by, 0),
            reverse=True
        )
        
        # Print header
        print(f"\n{'Function':<50} {'Calls':>10} {'Total (s)':>12} {'Avg (s)':>12} {'Min (s)':>12} {'Max (s)':>12}")
        print("-" * 110)
        
        # Print statistics
        for i, (func, func_stats) in enumerate(sorted_stats):
            if i >= limit:
                break
                
            calls = func_stats['calls']
            total = func_stats['total_time']
            avg = total / calls if calls > 0 else 0
            min_time = func_stats['min_time'] if func_stats['min_time'] != float('inf') else 0
            max_time = func_stats['max_time']
            
            print(f"{func:<50} {calls:>10} {total:>12.6f} {avg:>12.6f} {min_time:>12.6f} {max_time:>12.6f}")
    
    def reset(self) -> None:
        """Reset all statistics."""
        with self._lock:
            self._stats.clear()


class DetailedProfiler:
    """
    A more detailed profiler using cProfile.
    Provides comprehensive profiling information.
    """
    
    def __init__(self, output_dir: str = "profiles"):
        """
        Initialize the detailed profiler.
        
        Args:
            output_dir (str): Directory to save profile files
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self._profiler = None
    
    def start(self) -> None:
        """Start profiling."""
        self._profiler = cProfile.Profile()
        self._profiler.enable()
        logger.info("Detailed profiling started")
    
    def stop(self) -> None:
        """Stop profiling."""
        if self._profiler:
            self._profiler.disable()
            logger.info("Detailed profiling stopped")
    
    def save(self, filename: str) -> None:
        """
        Save the profile to a file.
        
        Args:
            filename (str): Name of the file to save
        """
        if not self._profiler:
            logger.warning("No profile to save")
            return
            
        filepath = os.path.join(self.output_dir, filename)
        self._profiler.dump_stats(filepath)
        logger.info(f"Profile saved to {filepath}")
    
    def print_stats(self, sort_by: str = 'cumulative', limit: int = 20) -> None:
        """
        Print profiling statistics.
        
        Args:
            sort_by (str): Field to sort by ('calls', 'cumulative', 'filename', 'line', 'module', 'name', 'nfl', 'pcalls', 'stdname', 'time')
            limit (int): Maximum number of functions to show
        """
        if not self._profiler:
            logger.warning("No profile to print")
            return
            
        s = io.StringIO()
        ps = pstats.Stats(self._profiler, stream=s).sort_stats(sort_by)
        ps.print_stats(limit)
        print(s.getvalue())
    
    def __enter__(self) -> 'DetailedProfiler':
        """Start profiling when entering a context."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Stop profiling when exiting a context."""
        self.stop()
    
    def profile(self, func: Callable) -> Callable:
        """
        Decorator to profile a function.
        
        Args:
            func: Function to profile
            
        Returns:
            Decorated function
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            profiler = cProfile.Profile()
            profiler.enable()
            try:
                return func(*args, **kwargs)
            finally:
                profiler.disable()
                s = io.StringIO()
                ps = pstats.Stats(profiler, stream=s).sort_stats('cumulative')
                ps.print_stats(20)
                logger.info(f"Profile for {func.__name__}:\n{s.getvalue()}")
        
        return wrapper


class MemoryProfiler:
    """
    Profiles memory usage to identify memory leaks.
    Uses tracemalloc to track memory allocations.
    """
    
    def __init__(self):
        """Initialize the memory profiler."""
        self._snapshot = None
    
    def start(self) -> None:
        """Start tracking memory allocations."""
        tracemalloc.start()
        logger.info("Memory profiling started")
    
    def stop(self) -> None:
        """Stop tracking memory allocations."""
        tracemalloc.stop()
        logger.info("Memory profiling stopped")
    
    def take_snapshot(self) -> None:
        """Take a snapshot of current memory allocations."""
        self._snapshot = tracemalloc.take_snapshot()
        logger.info("Memory snapshot taken")
    
    def compare_to_snapshot(self, limit: int = 20) -> None:
        """
        Compare current memory usage to the previous snapshot.
        
        Args:
            limit (int): Maximum number of statistics to show
        """
        if not self._snapshot:
            logger.warning("No previous snapshot to compare to")
            return
            
        current_snapshot = tracemalloc.take_snapshot()
        top_stats = current_snapshot.compare_to(self._snapshot, 'lineno')
        
        print(f"\n{'Rank':>4} {'Size':>10} {'Count':>8} {'Trace'}")
        print("-" * 80)
        
        for i, stat in enumerate(top_stats[:limit], 1):
            frame = stat.traceback[0]
            print(f"{i:>4} {stat.size_diff:>10} {stat.count_diff:>8} {frame.filename}:{frame.lineno}")
    
    def print_stats(self, limit: int = 20) -> None:
        """
        Print memory usage statistics.
        
        Args:
            limit (int): Maximum number of statistics to show
        """
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')
        
        print(f"\n{'Rank':>4} {'Size':>10} {'Count':>8} {'Trace'}")
        print("-" * 80)
        
        for i, stat in enumerate(top_stats[:limit], 1):
            frame = stat.traceback[0]
            print(f"{i:>4} {stat.size:>10} {stat.count:>8} {frame.filename}:{frame.lineno}")
    
    def __enter__(self) -> 'MemoryProfiler':
        """Start profiling when entering a context."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Stop profiling when exiting a context."""
        self.stop()
    
    def profile(self, func: Callable) -> Callable:
        """
        Decorator to profile memory usage of a function.
        
        Args:
            func: Function to profile
            
        Returns:
            Decorated function
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            tracemalloc.start()
            start_snapshot = tracemalloc.take_snapshot()
            try:
                return func(*args, **kwargs)
            finally:
                end_snapshot = tracemalloc.take_snapshot()
                tracemalloc.stop()
                
                top_stats = end_snapshot.compare_to(start_snapshot, 'lineno')
                s = io.StringIO()
                print(f"Memory profile for {func.__name__}:", file=s)
                print(f"{'Rank':>4} {'Size':>10} {'Count':>8} {'Trace'}", file=s)
                print("-" * 80, file=s)
                
                for i, stat in enumerate(top_stats[:10], 1):
                    frame = stat.traceback[0]
                    print(f"{i:>4} {stat.size_diff:>10} {stat.count_diff:>8} {frame.filename}:{frame.lineno}", file=s)
                
                logger.info(s.getvalue())
        
        return wrapper


# Create global instances for convenience
function_profiler = FunctionProfiler()
memory_profiler = MemoryProfiler()
detailed_profiler = DetailedProfiler()
