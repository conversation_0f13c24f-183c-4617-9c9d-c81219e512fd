"""
Utility functions for the Czech Property Registry application.
"""

import time
import re
import math
import tkinter as tk


def show_status(app, message):
    """
    Show a status message in the status bar

    Args:
        app: The main application instance
        message (str): The message to display
    """
    if hasattr(app, 'status_bar'):
        app.status_bar.config(text=message)
        app.root.update_idletasks()


def convert_wgs84_to_sjtsk(lat, lng):
    """
    Convert WGS84 coordinates to S-JTSK (Krovak East North) coordinate system

    Args:
        lat (float): Latitude in WGS84
        lng (float): Longitude in WGS84

    Returns:
        tuple: (x, y) coordinates in S-JTSK or None if conversion failed
    """
    try:
        # This is a simplified conversion that may not be accurate for all locations
        # For a proper conversion, use a specialized library or service

        # Constants for the conversion
        a = 6378137.0  # WGS84 semi-major axis
        f = 1 / 298.257223563  # WGS84 flattening
        e2 = 2 * f - f * f  # WGS84 eccentricity squared

        # Convert latitude and longitude to radians
        lat_rad = math.radians(lat)
        lng_rad = math.radians(lng)

        # Calculate auxiliary values
        sin_lat = math.sin(lat_rad)
        cos_lat = math.cos(lat_rad)

        # Calculate N (radius of curvature in the prime vertical)
        N = a / math.sqrt(1 - e2 * sin_lat * sin_lat)

        # Calculate Cartesian coordinates in WGS84
        X = N * cos_lat * math.cos(lng_rad)
        Y = N * cos_lat * math.sin(lng_rad)
        Z = N * (1 - e2) * sin_lat

        # Transform from WGS84 to S-JTSK
        # These are approximate transformation parameters
        # For a more accurate transformation, use a specialized library

        # Simplified transformation (not accurate for all locations)
        x = -Y * 0.001 - 3700000  # Approximate X coordinate in S-JTSK
        y = -X * 0.001 - 1300000  # Approximate Y coordinate in S-JTSK

        return (x, y)

    except Exception as e:
        print(f"Error converting coordinates: {e}")
        return None


def schedule_cache_cleanup(app, interval=300):
    """
    Schedule periodic cache cleanup

    Args:
        app: The main application instance
        interval (int): Cleanup interval in seconds
    """
    def cleanup():
        clean_address_cache(app)
        app.root.after(interval * 1000, cleanup)

    app.root.after(interval * 1000, cleanup)


def clean_address_cache(app):
    """
    Clean expired entries from the address suggestions cache

    Args:
        app: The main application instance
    """
    try:
        if hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
            with app._address_cache_lock:
                current_time = time.time()
                expired_keys = []

                for key, (timestamp, _) in app._address_suggestions_cache.items():
                    if current_time - timestamp > app._address_cache_expiry:
                        expired_keys.append(key)

                for key in expired_keys:
                    del app._address_suggestions_cache[key]

                if expired_keys:
                    print(f"Cleaned {len(expired_keys)} expired entries from address cache")

    except Exception as e:
        print(f"Error cleaning address cache: {e}")


def get_address_suggestions(app, text, use_cache=True):
    """
    Get address suggestions for the given text

    Args:
        app: The main application instance
        text (str): The text to get suggestions for
        use_cache (bool): Whether to use the cache

    Returns:
        list: List of address suggestions
    """
    if not text or len(text) < 2:
        return []

    # Check if we have cached suggestions
    if use_cache and hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
        with app._address_cache_lock:
            if text in app._address_suggestions_cache:
                timestamp, suggestions = app._address_suggestions_cache[text]

                # Check if the cache entry is still valid
                if time.time() - timestamp <= app._address_cache_expiry:
                    return suggestions

    # If we don't have cached suggestions or they're expired, fetch new ones
    try:
        # Use the Google Maps API to get suggestions
        if hasattr(app, 'google_maps'):
            suggestions = app.google_maps.get_place_autocomplete(text)

            # Cache the suggestions
            if use_cache and hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
                with app._address_cache_lock:
                    app._address_suggestions_cache[text] = (time.time(), suggestions)

            return suggestions

    except Exception as e:
        print(f"Error getting address suggestions: {e}")

    # If we couldn't get suggestions, return an empty list
    return []


def get_osm_address_suggestions(app, text):
    """
    Get full address suggestions for the OSM address field with caching

    Args:
        app: The main application instance
        text (str): The text to get suggestions for

    Returns:
        list: List of address suggestions
    """
    if not text or len(text) < 2:
        return []

    # Try to get suggestions from the Google Maps API
    suggestions = get_address_suggestions(app, text)

    # If we couldn't get suggestions, return some dummy suggestions
    if not suggestions:
        # Create some dummy suggestions based on the input text
        if len(text) >= 3:
            dummy_suggestions = [
                f"{text} Street, Prague, Czech Republic",
                f"{text} Avenue, Brno, Czech Republic",
                f"{text} Square, Ostrava, Czech Republic"
            ]
            return dummy_suggestions

    return suggestions


def update_osm_component_fields(app, event=None):
    """
    Update the component fields based on the full address

    Args:
        app: The main application instance
        event: The event that triggered this function (optional)
    """
    try:
        # Get the full address
        full_address = app.osm_full_address.get().strip() if hasattr(app, 'osm_full_address') else ""

        if not full_address:
            return

        # Try to parse the address into components
        # This is a simple parser that may not work for all addresses

        # Try to extract the postal code (PSČ)
        postal_match = re.search(r'(\d{3}\s*\d{2})', full_address)
        postal_code = postal_match.group(1).replace(" ", "") if postal_match else ""

        # Try to extract the city
        city_match = re.search(r',\s*([^,]+)(?:,|\s+\d{3})', full_address)
        city = city_match.group(1).strip() if city_match else ""

        # Try to extract the street and number
        street_match = re.search(r'^([^,]+)', full_address)
        street_and_number = street_match.group(1).strip() if street_match else ""

        # Try to separate street and number
        number_match = re.search(r'(\d+[a-zA-Z]?(?:/\d+[a-zA-Z]?)?)\s*$', street_and_number)
        if number_match:
            number = number_match.group(1)
            street = street_and_number[:street_and_number.rfind(number)].strip()
        else:
            street = street_and_number
            number = ""

        # Update the component fields
        if hasattr(app, 'osm_city_entry'):
            app.osm_city_entry.delete(0, tk.END)
            app.osm_city_entry.insert(0, city)

        if hasattr(app, 'osm_street_entry'):
            app.osm_street_entry.delete(0, tk.END)
            app.osm_street_entry.insert(0, street)

        if hasattr(app, 'osm_number_entry'):
            app.osm_number_entry.delete(0, tk.END)
            app.osm_number_entry.insert(0, number)

        if hasattr(app, 'osm_postal_entry'):
            app.osm_postal_entry.delete(0, tk.END)
            app.osm_postal_entry.insert(0, postal_code)

    except Exception as e:
        print(f"Error updating component fields: {e}")


def update_osm_full_address(app, event=None):
    """
    Update the full address field based on the component fields

    Args:
        app: The main application instance
        event: The event that triggered this function (optional)
    """
    try:
        # Get the component values
        city = app.osm_city_entry.get().strip() if hasattr(app, 'osm_city_entry') else ""
        street = app.osm_street_entry.get().strip() if hasattr(app, 'osm_street_entry') else ""
        number = app.osm_number_entry.get().strip() if hasattr(app, 'osm_number_entry') else ""
        postal_code = app.osm_postal_entry.get().strip() if hasattr(app, 'osm_postal_entry') else ""

        # Construct the full address
        address_parts = []

        if street and number:
            address_parts.append(f"{street} {number}")
        elif street:
            address_parts.append(street)

        if city:
            address_parts.append(city)

        if postal_code:
            address_parts.append(postal_code)

        address_parts.append("Czech Republic")

        full_address = ", ".join(address_parts)

        # Update the full address field
        if hasattr(app, 'osm_full_address'):
            app.osm_full_address.delete(0, tk.END)
            app.osm_full_address.insert(0, full_address)

    except Exception as e:
        print(f"Error updating full address: {e}")
