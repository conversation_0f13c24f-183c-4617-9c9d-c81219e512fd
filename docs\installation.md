# Installation Guide

This guide will help you install and set up the Czech Property Registry application.

## Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- Git (optional, for cloning the repository)

## Installation Steps

### 1. Clone or Download the Repository

```bash
git clone https://github.com/yourusername/czech-property-registry.git
cd czech-property-registry
```

Or download and extract the ZIP file from the repository.

### 2. Create a Virtual Environment (Optional but Recommended)

```bash
# On Windows
python -m venv venv
venv\Scripts\activate

# On macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Configure the Application

Create a `config.ini` file based on the provided `config.ini.sample`:

```bash
cp config.ini.sample config.ini
```

Edit the `config.ini` file to add your API keys and other configuration settings:

```ini
[OPENAI]
api_key = your_openai_api_key_here

[GOOGLE_MAPS]
api_key = your_google_maps_api_key_here

[CUZK]
username = your_cuzk_username
password = your_cuzk_password
```

### 5. Run the Application

```bash
python main.py
```

## API Keys

### Google Maps API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Maps JavaScript API, Geocoding API, and Places API
4. Create an API key
5. Add the API key to your `config.ini` file

### OpenAI API Key

1. Go to the [OpenAI API Keys page](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add the API key to your `config.ini` file

## Troubleshooting

If you encounter any issues during installation, please check the [Troubleshooting](troubleshooting.md) guide or open an issue on the repository.
