"""
CAPTCHA solving service for the Czech Property Registry application.

This module provides a dedicated service for solving CAPTCHAs using multiple methods:
1. GPT-4 Vision API for automatic solving
2. Local OCR using Tesseract (if available)
3. Manual solving by the user as a fallback

The service is designed to be configurable and extensible, with support for
caching solutions and tracking success rates of different methods.
"""

import os
import base64
import logging
import tempfile
import threading
import time
from io import BytesIO
from typing import Optional, Dict, List, Tuple, Any, Union, Callable

# Import PIL for image processing
from PIL import Image

# Try to import Tesseract OCR
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)


class CaptchaSolver:
    """
    Service for solving CAPTCHAs using multiple methods.
    
    This class provides methods for solving CAPTCHAs using:
    - GPT-4 Vision API
    - Local OCR with Tesseract
    - Manual solving by the user
    
    It tracks success rates and can automatically select the best method.
    """
    
    def __init__(self, app=None):
        """
        Initialize the CAPTCHA solver.
        
        Args:
            app: The main application instance (optional)
        """
        self.app = app
        self.last_solution = None
        self.success_rates = {
            'gpt': {'success': 0, 'total': 0},
            'tesseract': {'success': 0, 'total': 0},
            'manual': {'success': 0, 'total': 0}
        }
        self.cache = {}  # Simple in-memory cache
        self.cache_lock = threading.Lock()
        
        # Configure Tesseract path if available
        if TESSERACT_AVAILABLE and hasattr(self.app, 'settings'):
            tesseract_path = self.app.settings.get('CAPTCHA', 'tesseract_path', None)
            if tesseract_path and os.path.exists(tesseract_path):
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
    
    def solve(self, image_data: Union[str, bytes], methods: List[str] = None) -> Optional[str]:
        """
        Solve a CAPTCHA using the specified methods in order.
        
        Args:
            image_data: Either a file path to the CAPTCHA image or the raw image data as bytes
            methods: List of methods to try, in order ('gpt', 'tesseract', 'manual')
                     If None, uses all available methods in the default order
        
        Returns:
            str: The solved CAPTCHA text or None if all methods failed
        """
        # Default methods order
        if methods is None:
            methods = ['gpt', 'tesseract', 'manual']
        
        # Ensure we have image data as bytes
        if isinstance(image_data, str):
            # It's a file path
            with open(image_data, 'rb') as f:
                image_data = f.read()
        
        # Try to get from cache first
        image_hash = self._hash_image(image_data)
        with self.cache_lock:
            if image_hash in self.cache:
                logger.info(f"CAPTCHA solution found in cache: {self.cache[image_hash]}")
                return self.cache[image_hash]
        
        # Try each method in order
        for method in methods:
            if method == 'gpt' and hasattr(self.app, 'gpt') and self.app.gpt:
                solution = self._solve_with_gpt(image_data)
                if solution:
                    self._update_success_rate('gpt', True)
                    self._cache_solution(image_hash, solution)
                    return solution
                self._update_success_rate('gpt', False)
            
            elif method == 'tesseract' and TESSERACT_AVAILABLE:
                solution = self._solve_with_tesseract(image_data)
                if solution:
                    self._update_success_rate('tesseract', True)
                    self._cache_solution(image_hash, solution)
                    return solution
                self._update_success_rate('tesseract', False)
            
            elif method == 'manual':
                # Manual solving requires a callback to the UI
                if hasattr(self.app, 'captcha_handler') and hasattr(self.app.captcha_handler, 'show_captcha_dialog'):
                    # Save the image to a temporary file
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                        temp_file.write(image_data)
                        temp_path = temp_file.name
                    
                    # Show the CAPTCHA dialog
                    solution = self.app.captcha_handler.show_captcha_dialog(temp_path)
                    
                    # Clean up the temporary file
                    try:
                        os.unlink(temp_path)
                    except:
                        pass
                    
                    if solution:
                        self._update_success_rate('manual', True)
                        self._cache_solution(image_hash, solution)
                        return solution
                    self._update_success_rate('manual', False)
        
        # All methods failed
        return None
    
    def _solve_with_gpt(self, image_data: bytes) -> Optional[str]:
        """
        Solve a CAPTCHA using GPT-4 Vision API.
        
        Args:
            image_data: The raw image data as bytes
        
        Returns:
            str: The solved CAPTCHA text or None if solving failed
        """
        if not hasattr(self.app, 'gpt') or not self.app.gpt:
            logger.warning("GPT integration not available")
            return None
        
        try:
            logger.info("Attempting to solve CAPTCHA with GPT...")
            solution = self.app.gpt.solve_captcha(image_data)
            if solution:
                logger.info(f"GPT solved CAPTCHA: {solution}")
                return solution
        except Exception as e:
            logger.error(f"Error solving CAPTCHA with GPT: {e}")
        
        return None
    
    def _solve_with_tesseract(self, image_data: bytes) -> Optional[str]:
        """
        Solve a CAPTCHA using Tesseract OCR.
        
        Args:
            image_data: The raw image data as bytes
        
        Returns:
            str: The solved CAPTCHA text or None if solving failed
        """
        if not TESSERACT_AVAILABLE:
            logger.warning("Tesseract OCR not available")
            return None
        
        try:
            # Load the image
            image = Image.open(BytesIO(image_data))
            
            # Preprocess the image for better OCR results
            image = self._preprocess_image(image)
            
            # Use Tesseract to extract text
            config = '--psm 8 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
            text = pytesseract.image_to_string(image, config=config).strip()
            
            # Clean up the text (remove non-alphanumeric characters)
            import re
            text = re.sub(r'[^a-zA-Z0-9]', '', text)
            
            if text:
                logger.info(f"Tesseract solved CAPTCHA: {text}")
                return text
        except Exception as e:
            logger.error(f"Error solving CAPTCHA with Tesseract: {e}")
        
        return None
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Preprocess the image for better OCR results.
        
        Args:
            image: The PIL Image object
        
        Returns:
            Image: The preprocessed image
        """
        # Convert to grayscale
        image = image.convert('L')
        
        # Increase contrast
        from PIL import ImageEnhance
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(2.0)
        
        # Resize if too small
        if image.width < 100 or image.height < 30:
            ratio = max(100 / image.width, 30 / image.height)
            new_width = int(image.width * ratio)
            new_height = int(image.height * ratio)
            image = image.resize((new_width, new_height), Image.LANCZOS)
        
        return image
    
    def _update_success_rate(self, method: str, success: bool):
        """
        Update the success rate for a method.
        
        Args:
            method: The method name ('gpt', 'tesseract', 'manual')
            success: Whether the method was successful
        """
        if method in self.success_rates:
            self.success_rates[method]['total'] += 1
            if success:
                self.success_rates[method]['success'] += 1
    
    def _cache_solution(self, image_hash: str, solution: str):
        """
        Cache a CAPTCHA solution.
        
        Args:
            image_hash: The hash of the image data
            solution: The CAPTCHA solution
        """
        with self.cache_lock:
            self.cache[image_hash] = solution
            self.last_solution = solution
    
    def _hash_image(self, image_data: bytes) -> str:
        """
        Create a hash of the image data for caching.
        
        Args:
            image_data: The raw image data as bytes
        
        Returns:
            str: A hash of the image data
        """
        import hashlib
        return hashlib.md5(image_data).hexdigest()
    
    def get_success_rates(self) -> Dict[str, float]:
        """
        Get the success rates for each method.
        
        Returns:
            dict: A dictionary of method names to success rates (0.0-1.0)
        """
        rates = {}
        for method, stats in self.success_rates.items():
            if stats['total'] > 0:
                rates[method] = stats['success'] / stats['total']
            else:
                rates[method] = 0.0
        return rates
    
    def report_success(self, success: bool):
        """
        Report whether the last CAPTCHA solution was successful.
        This is used to improve the success rate tracking.
        
        Args:
            success: Whether the last solution was successful
        """
        # This would be called after using the CAPTCHA solution
        # to let the solver know if it worked
        pass
