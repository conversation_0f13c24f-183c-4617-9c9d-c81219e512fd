"""
Run the city boundary search test.

This script runs the test that searches an entire city area for properties,
finds all coordinates within the city boundaries, converts them to S-JTSK,
and creates CUZK URLs.

The test will fail if it cannot get the actual city boundaries from Google Maps API.
No fallback mechanism is used - the test requires real city boundaries.

Usage:
    python tests/run_city_boundary_search_test.py [city_name] [density]

Arguments:
    city_name (optional): Name of the city to search (e.g., "Prague")
    density (optional): Density of the coordinate grid (e.g., 100)
                        Higher values mean more coordinates and more precise coverage

Examples:
    # Use random city and default density
    python tests/run_city_boundary_search_test.py

    # Specify city
    python tests/run_city_boundary_search_test.py "Prague"

    # Specify city and density
    python tests/run_city_boundary_search_test.py "Prague" 150
"""

import os
import sys
import logging
import unittest
import argparse

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RunCityBoundarySearchTest")

# Import the test module
from tests.test_city_boundary_search import TestCityBoundarySearch

if __name__ == '__main__':
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run the city boundary search test')
    parser.add_argument('city_name', nargs='?', help='Name of the city to search (e.g., "Prague")')
    parser.add_argument('density', nargs='?', type=int, help='Density of the coordinate grid (e.g., 100)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')

    args = parser.parse_args()

    # Set log level based on verbose flag
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create a test suite with just the city boundary search test
    suite = unittest.TestSuite()
    suite.addTest(TestCityBoundarySearch('test_city_boundary_search'))

    # Run the test
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Exit with appropriate status code
    sys.exit(not result.wasSuccessful())
