"""
City boundaries utility for the Czech Property Registry application.

This module provides functions for getting city boundaries using the Google Maps API.
No default data or fallbacks are used - all boundaries must come from the Google Maps API.
"""

import math
import logging
import requests
import time

logger = logging.getLogger(__name__)


def get_city_coordinates(city_name, country="Czech Republic", google_maps_api=None):
    """
    Get the coordinates of a city using Google Maps API.

    Args:
        city_name (str): Name of the city
        country (str, optional): Country name to narrow down the search
        google_maps_api (GoogleMapsIntegration, optional): Google Maps API instance

    Returns:
        tuple: (lat, lng) coordinates of the city or None if geocoding failed
    """
    # If Google Maps API instance is provided, use it
    if google_maps_api:
        # Add country to city name if not already included
        if country and country.lower() not in city_name.lower():
            full_city_name = f"{city_name}, {country}"
        else:
            full_city_name = city_name

        # Geocode the city
        geocode_result = google_maps_api.geocode(full_city_name)

        if geocode_result and 'lat' in geocode_result and 'lng' in geocode_result:
            return (geocode_result['lat'], geocode_result['lng'])

    # If no Google Maps API instance or geocoding failed, return None
    return None


def get_detailed_city_boundaries(city_name, country="Czech Republic", google_maps_api=None):
    """
    Get detailed city boundaries using the Google Maps Places API.

    This function attempts to get more precise city boundaries by using the Places API
    to get the city's viewport, which is more accurate than just using geocoding.

    Args:
        city_name (str): Name of the city
        country (str, optional): Country name to narrow down the search
        google_maps_api (GoogleMapsIntegration, optional): Google Maps API instance

    Returns:
        dict: Dictionary with bounds information (north, south, east, west)
              or None if boundaries couldn't be determined
    """
    if not google_maps_api or not google_maps_api.api_key:
        logger.error("Google Maps API instance with valid API key is required")
        return None

    try:
        # Add country to city name if not already included
        if country and country.lower() not in city_name.lower():
            full_city_name = f"{city_name}, {country}"
        else:
            full_city_name = city_name

        logger.info(f"Getting detailed boundaries for '{full_city_name}' using Places API")

        # Use the Places API to get detailed city information
        import urllib.parse
        encoded_input = urllib.parse.quote(full_city_name)

        # First try to get the place ID with more specific parameters for cities
        url = f"https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input={encoded_input}&inputtype=textquery&fields=place_id,geometry,name,types&key={google_maps_api.api_key}"

        # Make the request
        response = requests.get(url)
        data = response.json()

        logger.info(f"Place search response status: {data.get('status')}")

        if data.get('status') == 'OK' and data.get('candidates'):
            # Log the number of candidates found
            logger.info(f"Found {len(data.get('candidates'))} place candidates for {city_name}")

            # Get the first candidate
            candidate = data['candidates'][0]
            place_id = candidate.get('place_id')

            # Log the candidate details
            if 'name' in candidate:
                logger.info(f"Selected candidate: {candidate.get('name')}")
            if 'types' in candidate:
                logger.info(f"Place types: {candidate.get('types')}")

            if place_id:
                logger.info(f"Using place_id: {place_id}")

                # Now get detailed place information including viewport
                details_url = f"https://maps.googleapis.com/maps/api/place/details/json?place_id={place_id}&fields=geometry,name,address_components,types&key={google_maps_api.api_key}"
                details_response = requests.get(details_url)
                details_data = details_response.json()

                logger.info(f"Place details response status: {details_data.get('status')}")

                if details_data.get('status') == 'OK' and details_data.get('result'):
                    result = details_data['result']
                    geometry = result.get('geometry', {})

                    # Log place details
                    if 'name' in result:
                        logger.info(f"Place name: {result.get('name')}")
                    if 'types' in result:
                        logger.info(f"Place types: {result.get('types')}")

                    # Check if this is actually a city/locality
                    is_city = False
                    if 'types' in result:
                        city_types = ['locality', 'political', 'administrative_area_level_2', 'administrative_area_level_3']
                        for place_type in result.get('types', []):
                            if place_type in city_types:
                                is_city = True
                                logger.info(f"Confirmed {city_name} is a city/locality (type: {place_type})")
                                break

                    if not is_city:
                        logger.warning(f"{city_name} does not appear to be a city/locality based on place types")

                    # Try to get viewport (preferred) or location
                    if 'viewport' in geometry:
                        viewport = geometry['viewport']
                        boundaries = {
                            'north': viewport['northeast']['lat'],
                            'south': viewport['southwest']['lat'],
                            'east': viewport['northeast']['lng'],
                            'west': viewport['southwest']['lng']
                        }
                        logger.info(f"Found detailed boundaries for {city_name} using Places API: {boundaries}")
                        return boundaries
                    elif 'bounds' in geometry:
                        bounds = geometry['bounds']
                        boundaries = {
                            'north': bounds['northeast']['lat'],
                            'south': bounds['southwest']['lat'],
                            'east': bounds['northeast']['lng'],
                            'west': bounds['southwest']['lng']
                        }
                        logger.info(f"Found bounds for {city_name} using Places API: {boundaries}")
                        return boundaries
                    elif 'location' in geometry:
                        # If we only have a location point, create a small area around it
                        location = geometry['location']
                        # Create a small area around the point (approximately 1km in each direction)
                        lat_offset = 0.009  # About 1km in latitude
                        lng_offset = 0.015  # About 1km in longitude at Czech Republic latitude
                        boundaries = {
                            'north': location['lat'] + lat_offset,
                            'south': location['lat'] - lat_offset,
                            'east': location['lng'] + lng_offset,
                            'west': location['lng'] - lng_offset
                        }
                        logger.info(f"Created boundaries from location point for {city_name}: {boundaries}")
                        return boundaries

        # If we couldn't get detailed boundaries, fall back to geocoding
        logger.warning(f"Could not get detailed boundaries for {city_name} using Places API, falling back to geocoding")
        return None

    except Exception as e:
        logger.error(f"Error getting detailed city boundaries: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def get_city_boundaries(city_name, country="Czech Republic", google_maps_api=None):
    """
    Get the boundaries of a city using Google Maps API.

    This function tries multiple approaches to get the most accurate city boundaries:
    1. First, it tries to get detailed boundaries using the Places API
    2. If that fails, it tries to get boundaries from the geocoding API
    3. If that fails, it tries a more specific search with locality type
    4. As a last resort, it creates a small area around the city center point

    No default data or fallbacks are used - all boundaries must come from the Google Maps API.

    Args:
        city_name (str): Name of the city
        country (str, optional): Country name to narrow down the search
        google_maps_api (GoogleMapsIntegration, optional): Google Maps API instance

    Returns:
        dict: Dictionary with bounds information (north, south, east, west)
              or None if boundaries couldn't be determined
    """
    if not google_maps_api:
        logger.error("Google Maps API instance is required")
        return None

    logger.info(f"Getting boundaries for city: {city_name}")

    # First try to get detailed boundaries using Places API
    detailed_boundaries = get_detailed_city_boundaries(city_name, country, google_maps_api)
    if detailed_boundaries:
        return detailed_boundaries

    # If that fails, try to get boundaries from the geocoding API
    logger.info(f"Trying to get boundaries for {city_name} using geocoding API")

    # Add country to city name if not already included
    if country and country.lower() not in city_name.lower():
        full_city_name = f"{city_name}, {country}"
    else:
        full_city_name = city_name

    # Geocode the city
    geocode_result = google_maps_api.geocode(full_city_name)

    if geocode_result:
        # Try to get bounds or viewport from geocode result
        if 'geometry' in geocode_result:
            geometry = geocode_result['geometry']

            # Try bounds first
            if 'bounds' in geometry:
                bounds = geometry['bounds']
                boundaries = {
                    'north': bounds['northeast']['lat'],
                    'south': bounds['southwest']['lat'],
                    'east': bounds['northeast']['lng'],
                    'west': bounds['southwest']['lng']
                }
                logger.info(f"Found boundaries for {city_name} using geocoding API (bounds): {boundaries}")
                return boundaries

            # Fall back to viewport
            elif 'viewport' in geometry:
                viewport = geometry['viewport']
                boundaries = {
                    'north': viewport['northeast']['lat'],
                    'south': viewport['southwest']['lat'],
                    'east': viewport['northeast']['lng'],
                    'west': viewport['southwest']['lng']
                }
                logger.info(f"Found boundaries for {city_name} using geocoding API (viewport): {boundaries}")
                return boundaries

            # If we only have a location point, create a small area around it
            elif 'location' in geometry:
                location = geometry['location']
                # For smaller cities like Mariánské Lázně, create a larger area (approximately 2km in each direction)
                lat_offset = 0.018  # About 2km in latitude
                lng_offset = 0.030  # About 2km in longitude at Czech Republic latitude
                boundaries = {
                    'north': location['lat'] + lat_offset,
                    'south': location['lat'] - lat_offset,
                    'east': location['lng'] + lng_offset,
                    'west': location['lng'] - lng_offset
                }
                logger.info(f"Created boundaries from location point for {city_name}: {boundaries}")
                return boundaries
    else:
        logger.warning(f"Could not geocode {city_name}, trying more specific search")

    # Try a more specific search with locality type
    try:
        # Use the Places API with specific type for cities
        import urllib.parse
        encoded_input = urllib.parse.quote(f"{city_name} {country}")
        url = f"https://maps.googleapis.com/maps/api/place/textsearch/json?query={encoded_input}&type=locality&key={google_maps_api.api_key}"

        response = requests.get(url)
        data = response.json()

        if data.get('status') == 'OK' and data.get('results'):
            result = data['results'][0]
            geometry = result.get('geometry', {})

            # Try viewport first
            if 'viewport' in geometry:
                viewport = geometry['viewport']
                boundaries = {
                    'north': viewport['northeast']['lat'],
                    'south': viewport['southwest']['lat'],
                    'east': viewport['northeast']['lng'],
                    'west': viewport['southwest']['lng']
                }
                logger.info(f"Found boundaries for {city_name} using Places text search (viewport): {boundaries}")
                return boundaries

            # Fall back to location
            elif 'location' in geometry:
                location = geometry['location']
                # Create a small area around the point (approximately 2km in each direction)
                lat_offset = 0.018  # About 2km in latitude
                lng_offset = 0.030  # About 2km in longitude at Czech Republic latitude
                boundaries = {
                    'north': location['lat'] + lat_offset,
                    'south': location['lat'] - lat_offset,
                    'east': location['lng'] + lng_offset,
                    'west': location['lng'] - lng_offset
                }
                logger.info(f"Created boundaries from text search location for {city_name}: {boundaries}")
                return boundaries
    except Exception as e:
        logger.error(f"Error in text search for city boundaries: {e}")

    # If we still couldn't get boundaries, try one last approach with a direct geocode
    try:
        # Try a direct geocode with specific parameters
        import urllib.parse
        encoded_address = urllib.parse.quote(f"{city_name}, {country}")
        url = f"https://maps.googleapis.com/maps/api/geocode/json?address={encoded_address}&key={google_maps_api.api_key}"

        response = requests.get(url)
        data = response.json()

        if data.get('status') == 'OK' and data.get('results'):
            result = data['results'][0]
            geometry = result.get('geometry', {})

            # Check if this is a city/locality
            is_city = False
            for component in result.get('address_components', []):
                if 'locality' in component.get('types', []) or 'administrative_area_level_2' in component.get('types', []):
                    is_city = True
                    logger.info(f"Confirmed {city_name} is a city/locality from direct geocode")
                    break

            if not is_city:
                logger.warning(f"{city_name} does not appear to be a city/locality from direct geocode")

            # Try viewport first
            if 'viewport' in geometry:
                viewport = geometry['viewport']
                boundaries = {
                    'north': viewport['northeast']['lat'],
                    'south': viewport['southwest']['lat'],
                    'east': viewport['northeast']['lng'],
                    'west': viewport['southwest']['lng']
                }
                logger.info(f"Found boundaries for {city_name} using direct geocode (viewport): {boundaries}")
                return boundaries

            # Try bounds next
            elif 'bounds' in geometry:
                bounds = geometry['bounds']
                boundaries = {
                    'north': bounds['northeast']['lat'],
                    'south': bounds['southwest']['lat'],
                    'east': bounds['northeast']['lng'],
                    'west': bounds['southwest']['lng']
                }
                logger.info(f"Found boundaries for {city_name} using direct geocode (bounds): {boundaries}")
                return boundaries

            # Fall back to location
            elif 'location' in geometry:
                location = geometry['location']
                # Create a small area around the point (approximately 2km in each direction)
                lat_offset = 0.018  # About 2km in latitude
                lng_offset = 0.030  # About 2km in longitude at Czech Republic latitude
                boundaries = {
                    'north': location['lat'] + lat_offset,
                    'south': location['lat'] - lat_offset,
                    'east': location['lng'] + lng_offset,
                    'west': location['lng'] - lng_offset
                }
                logger.info(f"Created boundaries from direct geocode location for {city_name}: {boundaries}")
                return boundaries
    except Exception as e:
        logger.error(f"Error in direct geocode for city boundaries: {e}")

    # If we couldn't get boundaries after all attempts, return None
    logger.error(f"Could not determine boundaries for {city_name} after multiple attempts")
    return None
