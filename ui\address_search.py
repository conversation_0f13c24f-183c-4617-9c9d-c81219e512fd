"""
Address search UI components for the Czech Property Registry application.

This module provides UI components for searching properties by address.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from ui.message_boxes import MessageBoxes

from utils import AutocompleteEntry


class AddressSearchUI:
    """
    UI components for address search functionality.

    This class creates and manages the UI elements for searching
    properties by address, including full address and component-based search.
    """

    def __init__(self, parent_frame, app):
        """
        Initialize the address search UI.

        Args:
            parent_frame: The parent frame to place the UI elements in
            app: The main application instance for callbacks
        """
        self.parent = parent_frame
        self.app = app

        # Create the UI elements
        self.create_ui()

    def create_ui(self):
        """Create the address search UI elements"""
        # Create a frame for the full address search
        full_address_frame = ttk.LabelFrame(self.parent, text="Search by Full Address")
        full_address_frame.pack(fill="x", padx=5, pady=5)

        # Add a description
        ttk.Label(
            full_address_frame,
            text="Enter a complete address to search for property information",
            wraplength=400,
            font=("Arial", 9),
            foreground="#333333"
        ).pack(pady=2)

        # Create a frame for the full address input
        address_input_frame = ttk.Frame(full_address_frame)
        address_input_frame.pack(fill="x", padx=5, pady=5)

        # Add full address label and entry with autocomplete
        ttk.Label(address_input_frame, text="Full Address:").pack(side="left", padx=2)

        # Create the autocomplete entry for full address
        self.full_address = AutocompleteEntry(
            address_input_frame,
            width=40,
            autocomplete_function=self.get_address_suggestions
        )
        self.full_address.pack(side="left", padx=2, fill="x", expand=True)

        # Add a small help text
        ttk.Label(
            address_input_frame,
            text="Start typing to see suggestions",
            foreground="gray",
            font=("Arial", 7)
        ).pack(side="left", padx=2)

        # Create a frame for the search button
        button_frame = ttk.Frame(full_address_frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        # Add search button
        self.search_btn = ttk.Button(
            button_frame,
            text="Search",
            command=self.on_search_full_address,
            style="Accent.TButton"
        )
        self.search_btn.pack(side="right", padx=2)

        # Add show on map button
        self.show_map_btn = ttk.Button(
            button_frame,
            text="Show on Map",
            command=self.on_show_full_address_on_map
        )
        self.show_map_btn.pack(side="left", padx=2)

        # Add a separator
        ttk.Separator(self.parent, orient="horizontal").pack(fill="x", padx=5, pady=5)

        # Create a frame for the component-based address search
        component_frame = ttk.LabelFrame(self.parent, text="Search by Address Components")
        component_frame.pack(fill="x", padx=5, pady=5)

        # Add a description
        ttk.Label(
            component_frame,
            text="Enter address components individually",
            wraplength=400,
            font=("Arial", 9),
            foreground="#333333"
        ).pack(pady=2)

        # Create a grid for the component inputs
        component_grid = ttk.Frame(component_frame)
        component_grid.pack(fill="x", padx=5, pady=5)

        # Add city label and entry with autocomplete
        ttk.Label(component_grid, text="City:").grid(row=0, column=0, padx=2, pady=2, sticky="w")
        self.address_city = AutocompleteEntry(
            component_grid,
            width=25,
            autocomplete_function=self.get_city_suggestions
        )
        self.address_city.grid(row=0, column=1, padx=2, pady=2, sticky="ew")

        # Add street label and entry with autocomplete
        ttk.Label(component_grid, text="Street:").grid(row=1, column=0, padx=2, pady=2, sticky="w")
        self.address_street = AutocompleteEntry(
            component_grid,
            width=25,
            autocomplete_function=self.get_street_suggestions
        )
        self.address_street.grid(row=1, column=1, padx=2, pady=2, sticky="ew")

        # Add house number label and entry
        ttk.Label(component_grid, text="House Number:").grid(row=2, column=0, padx=2, pady=2, sticky="w")
        self.address_number = ttk.Entry(component_grid, width=10)
        self.address_number.grid(row=2, column=1, padx=2, pady=2, sticky="w")

        # Add postal code label and entry with autocomplete
        ttk.Label(component_grid, text="Postal Code (PSČ):").grid(row=3, column=0, padx=2, pady=2, sticky="w")
        self.address_postal = AutocompleteEntry(
            component_grid,
            width=10,
            autocomplete_function=self.get_postal_suggestions
        )
        self.address_postal.grid(row=3, column=1, padx=2, pady=2, sticky="w")

        # Configure the grid to expand properly
        component_grid.columnconfigure(1, weight=1)

        # Create a frame for the component search buttons
        component_button_frame = ttk.Frame(component_frame)
        component_button_frame.pack(fill="x", padx=5, pady=5)

        # Add search button for components
        self.component_search_btn = ttk.Button(
            component_button_frame,
            text="Search",
            command=self.on_search_component_address,
            style="Accent.TButton"
        )
        self.component_search_btn.pack(side="right", padx=2)

        # Add show on map button for components
        self.component_show_map_btn = ttk.Button(
            component_button_frame,
            text="Show on Map",
            command=self.on_show_component_address_on_map
        )
        self.component_show_map_btn.pack(side="left", padx=2)

        # Add a separator
        ttk.Separator(self.parent, orient="horizontal").pack(fill="x", padx=5, pady=5)

        # Create a frame for the search results
        self.results_frame = ttk.LabelFrame(self.parent, text="Search Results")
        self.results_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Add a text widget for displaying results
        self.results_text = tk.Text(self.results_frame, height=10, width=50, wrap="word")
        scrollbar = ttk.Scrollbar(self.results_frame, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.pack(side="left", fill="both", expand=True, padx=2, pady=2)
        scrollbar.pack(side="right", fill="y", padx=(0, 2), pady=2)

        # Add initial text
        self.results_text.insert("1.0", "Search results will appear here. Enter an address and click 'Search'.")
        self.results_text.config(state="disabled")

    def get_address_suggestions(self, text):
        """Get address suggestions from the app"""
        if hasattr(self.app, 'get_address_suggestions'):
            return self.app.get_address_suggestions(text)
        return []

    def get_city_suggestions(self, text):
        """Get city suggestions from the app"""
        if hasattr(self.app, 'get_city_suggestions'):
            return self.app.get_city_suggestions(text)
        return []

    def get_street_suggestions(self, text):
        """Get street suggestions from the app"""
        if hasattr(self.app, 'get_street_suggestions'):
            return self.app.get_street_suggestions(text)
        return []

    def get_postal_suggestions(self, text):
        """Get postal code suggestions from the app"""
        if hasattr(self.app, 'get_postal_suggestions'):
            return self.app.get_postal_suggestions(text)
        return []

    def on_search_full_address(self):
        """Handle the search button click for full address"""
        # Get the full address
        full_address = self.full_address.get().strip()

        # Validate input
        if not full_address:
            MessageBoxes.show_missing_information("Please enter a full address.")
            return

        # Call the application's search method
        if hasattr(self.app, 'search_by_full_address'):
            self.app.search_by_full_address(full_address)
        else:
            print("Search by full address method not implemented in the application.")

    def on_show_full_address_on_map(self):
        """Handle the show on map button click for full address"""
        # Get the full address
        full_address = self.full_address.get().strip()

        # Validate input
        if not full_address:
            MessageBoxes.show_missing_information("Please enter a full address to show on the map.")
            return

        # Call the application's map update method
        if hasattr(self.app, 'show_full_address_on_map'):
            self.app.show_full_address_on_map()
        else:
            print("Show full address on map method not implemented in the application.")

    def on_search_component_address(self):
        """Handle the search button click for component address"""
        # Get the component values
        city = self.address_city.get().strip()
        street = self.address_street.get().strip()
        number = self.address_number.get().strip()
        postal_code = self.address_postal.get().strip()

        # Validate input
        if not city:
            MessageBoxes.show_missing_information("Please enter at least a city name.")
            return

        # Call the application's search method
        if hasattr(self.app, 'search_by_component_address'):
            self.app.search_by_component_address(city, street, number, postal_code)
        else:
            print("Search by component address method not implemented in the application.")

    def on_show_component_address_on_map(self):
        """Handle the show on map button click for component address"""
        # Get the component values
        city = self.address_city.get().strip()
        street = self.address_street.get().strip()
        number = self.address_number.get().strip()
        postal_code = self.address_postal.get().strip()

        # Validate input
        if not city:
            MessageBoxes.show_missing_information("Please enter at least a city name to show on the map.")
            return

        # Call the application's map update method
        if hasattr(self.app, 'show_component_address_on_map'):
            self.app.show_component_address_on_map(city, street, number, postal_code)
        else:
            print("Show component address on map method not implemented in the application.")

    def update_results(self, text):
        """Update the results text widget with new text"""
        # Enable the text widget for updating
        self.results_text.config(state="normal")

        # Clear existing text
        self.results_text.delete("1.0", tk.END)

        # Insert new text
        self.results_text.insert("1.0", text)

        # Disable the text widget after updating
        self.results_text.config(state="disabled")
