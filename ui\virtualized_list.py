"""
Virtualized list widget for efficient rendering of large datasets.
Only renders the visible items, significantly improving performance for large lists.
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import List, Callable, Any, Optional, Dict, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("VirtualizedList")


class VirtualizedList(ttk.Frame):
    """
    A virtualized list widget that only renders visible items.
    Significantly improves performance for large lists.
    """

    def __init__(self, master, 
                 item_height: int = 30, 
                 buffer_items: int = 5,
                 width: int = 400,
                 height: int = 300,
                 bg: str = "#ffffff",
                 **kwargs):
        """
        Initialize the virtualized list.

        Args:
            master: Parent widget
            item_height (int): Height of each item in pixels
            buffer_items (int): Number of items to render above and below the visible area
            width (int): Width of the list
            height (int): Height of the list
            bg (str): Background color
            **kwargs: Additional arguments for the frame
        """
        super().__init__(master, **kwargs)
        
        self.item_height = item_height
        self.buffer_items = buffer_items
        self.width = width
        self.height = height
        self.bg_color = bg
        
        # Data
        self._items: List[Any] = []
        self._visible_items: Dict[int, tk.Widget] = {}
        self._render_item_func: Optional[Callable[[Any, tk.Widget], None]] = None
        self._create_item_widget_func: Optional[Callable[[], tk.Widget]] = None
        
        # UI components
        self._canvas = tk.Canvas(
            self, 
            width=width, 
            height=height, 
            bg=bg,
            highlightthickness=0
        )
        self._scrollbar = ttk.Scrollbar(
            self, 
            orient="vertical", 
            command=self._canvas.yview
        )
        self._canvas.configure(yscrollcommand=self._scrollbar.set)
        
        # Layout
        self._canvas.grid(row=0, column=0, sticky="nsew")
        self._scrollbar.grid(row=0, column=1, sticky="ns")
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
        # Content frame
        self._content_frame = tk.Frame(self._canvas, bg=bg)
        self._content_window = self._canvas.create_window(
            (0, 0), 
            window=self._content_frame, 
            anchor="nw",
            width=width
        )
        
        # Bind events
        self._canvas.bind("<Configure>", self._on_canvas_configure)
        self._canvas.bind("<MouseWheel>", self._on_mousewheel)
        self._canvas.bind("<Button-4>", self._on_mousewheel)
        self._canvas.bind("<Button-5>", self._on_mousewheel)
        self._canvas.bind_all("<MouseWheel>", self._on_mousewheel)
        self._canvas.bind_all("<Button-4>", self._on_mousewheel)
        self._canvas.bind_all("<Button-5>", self._on_mousewheel)
        
        # Track scroll position
        self._last_scroll_position = 0
        self._canvas.bind("<Motion>", self._check_scroll_position)
        
        logger.info("VirtualizedList initialized")

    def set_item_renderer(self, 
                         create_widget_func: Callable[[], tk.Widget],
                         render_func: Callable[[Any, tk.Widget], None]) -> None:
        """
        Set the functions to create and render item widgets.

        Args:
            create_widget_func: Function that creates a new widget for an item
            render_func: Function that renders an item into a widget
        """
        self._create_item_widget_func = create_widget_func
        self._render_item_func = render_func

    def set_items(self, items: List[Any]) -> None:
        """
        Set the items to display in the list.

        Args:
            items: List of items to display
        """
        self._items = items
        self._update_content_height()
        self._render_visible_items()
        logger.info(f"Set {len(items)} items in virtualized list")

    def get_items(self) -> List[Any]:
        """
        Get the current items in the list.

        Returns:
            List of items
        """
        return self._items

    def add_item(self, item: Any) -> None:
        """
        Add an item to the list.

        Args:
            item: Item to add
        """
        self._items.append(item)
        self._update_content_height()
        self._render_visible_items()

    def remove_item(self, item: Any) -> None:
        """
        Remove an item from the list.

        Args:
            item: Item to remove
        """
        if item in self._items:
            self._items.remove(item)
            self._update_content_height()
            self._render_visible_items()

    def clear(self) -> None:
        """Clear all items from the list."""
        self._items = []
        self._update_content_height()
        self._render_visible_items()
        logger.info("Cleared virtualized list")

    def _update_content_height(self) -> None:
        """Update the height of the content frame based on the number of items."""
        total_height = len(self._items) * self.item_height
        self._canvas.configure(scrollregion=(0, 0, self.width, total_height))
        
        # If the content is smaller than the canvas, disable scrolling
        if total_height <= self.height:
            self._scrollbar.grid_remove()
        else:
            self._scrollbar.grid()

    def _get_visible_range(self) -> Tuple[int, int]:
        """
        Get the range of visible items.

        Returns:
            Tuple of (start_index, end_index)
        """
        # Get the current scroll position
        scroll_top = self._canvas.yview()[0] * len(self._items) * self.item_height
        
        # Calculate the visible range
        start_idx = max(0, int(scroll_top / self.item_height) - self.buffer_items)
        visible_items = int(self.height / self.item_height) + 2 * self.buffer_items
        end_idx = min(len(self._items), start_idx + visible_items)
        
        return start_idx, end_idx

    def _render_visible_items(self) -> None:
        """Render only the visible items."""
        if not self._create_item_widget_func or not self._render_item_func:
            logger.warning("Item renderer not set")
            return
            
        # Get the visible range
        start_idx, end_idx = self._get_visible_range()
        
        # Remove widgets that are no longer visible
        for idx in list(self._visible_items.keys()):
            if idx < start_idx or idx >= end_idx:
                self._visible_items[idx].destroy()
                del self._visible_items[idx]
        
        # Create widgets for newly visible items
        for idx in range(start_idx, end_idx):
            if idx >= len(self._items):
                break
                
            if idx not in self._visible_items:
                # Create a new widget
                widget = self._create_item_widget_func()
                widget.place(x=0, y=idx * self.item_height, width=self.width, height=self.item_height)
                
                # Render the item
                self._render_item_func(self._items[idx], widget)
                
                # Store the widget
                self._visible_items[idx] = widget

    def _on_canvas_configure(self, event) -> None:
        """Handle canvas resize events."""
        # Update the width of the content window
        self.width = event.width
        self._canvas.itemconfig(self._content_window, width=event.width)
        
        # Re-render visible items
        self._render_visible_items()

    def _on_mousewheel(self, event) -> None:
        """Handle mousewheel events for scrolling."""
        # Different platforms have different mousewheel events
        if event.num == 4 or event.delta > 0:
            self._canvas.yview_scroll(-1, "units")
        elif event.num == 5 or event.delta < 0:
            self._canvas.yview_scroll(1, "units")
            
        # Check if we need to re-render
        self._check_scroll_position(event)

    def _check_scroll_position(self, event) -> None:
        """Check if the scroll position has changed enough to re-render."""
        current_pos = self._canvas.yview()[0]
        if abs(current_pos - self._last_scroll_position) > 0.01:
            self._last_scroll_position = current_pos
            self._render_visible_items()

    def update_item(self, idx: int) -> None:
        """
        Update a specific item.

        Args:
            idx (int): Index of the item to update
        """
        if idx in self._visible_items and idx < len(self._items):
            self._render_item_func(self._items[idx], self._visible_items[idx])

    def update_all_visible(self) -> None:
        """Update all visible items."""
        for idx in self._visible_items:
            if idx < len(self._items):
                self._render_item_func(self._items[idx], self._visible_items[idx])

    def scroll_to_item(self, idx: int) -> None:
        """
        Scroll to make a specific item visible.

        Args:
            idx (int): Index of the item to scroll to
        """
        if 0 <= idx < len(self._items):
            # Calculate the position to scroll to
            pos = idx * self.item_height / (len(self._items) * self.item_height)
            self._canvas.yview_moveto(pos)
            self._render_visible_items()

    def get_visible_items(self) -> List[Any]:
        """
        Get the currently visible items.

        Returns:
            List of visible items
        """
        start_idx, end_idx = self._get_visible_range()
        return self._items[start_idx:end_idx]
