#!/usr/bin/env python3
"""
Test script for specific geocoding functionality that's failing in the application.
"""

import requests
import configparser
import urllib.parse
import json

def load_api_key():
    """Load Google Maps API key from config file"""
    config = configparser.ConfigParser()
    try:
        config.read('config.ini')
        if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
            api_key = config['GOOGLE_MAPS']['api_key']
            print(f"API key found: {api_key[:5]}..." if api_key else "API key is empty")
            return api_key
        else:
            print("API key not found in config.ini")
            return None
    except Exception as e:
        print(f"Error loading API key: {e}")
        return None

def test_geocode_address(address, country="Czech Republic", api_key=None):
    """Test geocoding an address"""
    print(f"\n=== Testing Geocoding for '{address}' ===")
    
    # Add country to address if not already included
    if country and country.lower() not in address.lower():
        full_address = f"{address}, {country}"
    else:
        full_address = address
    
    print(f"Full address: {full_address}")
    
    # Properly encode the address for URL
    encoded_address = urllib.parse.quote(full_address)
    url = f"https://maps.googleapis.com/maps/api/geocode/json?address={encoded_address}&key={api_key}"
    
    print(f"Making request to: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response status: {data.get('status', 'Unknown')}")
            
            if data.get('status') == 'OK':
                result = data.get('results', [])[0]
                location = result.get('geometry', {}).get('location', {})
                formatted_address = result.get('formatted_address', '')
                
                print("Geocoding successful!")
                print(f"Formatted address: {formatted_address}")
                print(f"Latitude: {location.get('lat')}")
                print(f"Longitude: {location.get('lng')}")
                
                return {
                    'lat': location.get('lat'),
                    'lng': location.get('lng'),
                    'formatted_address': formatted_address
                }
            else:
                print(f"Error: {data.get('error_message', 'Unknown error')}")
                print(f"Full response: {json.dumps(data, indent=2)}")
                return None
        else:
            print(f"HTTP error: {response.status_code}")
            print(f"Response content: {response.text}")
            return None
    except Exception as e:
        print(f"Exception: {e}")
        return None

def main():
    """Main function"""
    print("Specific Geocoding Test")
    print("======================")
    
    api_key = load_api_key()
    if not api_key:
        print("No API key found. Please check your config.ini file.")
        return
    
    # Test addresses that might be failing in the application
    test_addresses = [
        "Prague",
        "Prague, Czech Republic",
        "Karlovy Vary",
        "Brno",
        "Plzeňská, Prague",
        "Václavské náměstí, Prague",  # Test with diacritics
        "Náměstí Republiky 1, Prague 1"  # Test with more complex address
    ]
    
    results = {}
    for address in test_addresses:
        result = test_geocode_address(address, api_key=api_key)
        results[address] = result
    
    # Summary
    print("\n=== Summary ===")
    for address, result in results.items():
        status = "OK" if result else "FAILED"
        print(f"{address}: {status}")
    
    # Check if any failed
    failed = [address for address, result in results.items() if not result]
    if failed:
        print("\nSome addresses failed to geocode:")
        for address in failed:
            print(f"- {address}")
        print("\nPossible issues:")
        print("1. Network connectivity problems")
        print("2. Rate limiting (too many requests)")
        print("3. Issues with special characters in addresses")
        print("4. API key restrictions")
    else:
        print("\nAll addresses geocoded successfully!")
        print("If the application is still having issues, the problem might be in how it's processing the API responses.")

if __name__ == "__main__":
    main()
