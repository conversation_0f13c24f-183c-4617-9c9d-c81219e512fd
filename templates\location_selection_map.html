<!DOCTYPE html>
<html>
<head>
    <title>Select Location</title>
    <meta name="viewport" content="initial-scale=1.0">
    <meta charset="utf-8">
    <style>
        #map {
            height: 100%;
        }
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            z-index: 1;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <div id="info">
        <h3>Selected Location</h3>
        <p>Click on the map to select a location</p>
        <p id="coordinates">Lat: {lat}, Lng: {lng}</p>
        <p id="address">Address: Loading...</p>
        <button id="selectButton" onclick="selectLocation()">Select This Location</button>
    </div>

    <script>
        var map;
        var marker;
        var selectedLat = {lat};
        var selectedLng = {lng};
        var selectedAddress = "";

        function initMap() {
            // Simple map implementation - similar to the working HTML file
            map = new google.maps.Map(document.getElementById('map'), {
                center: {lat: {lat}, lng: {lng}},
                zoom: {zoom}
            });

            // Add a marker at the initial position
            marker = new google.maps.Marker({
                position: {lat: {lat}, lng: {lng}},
                map: map,
                draggable: true,
                title: 'Drag me!'
            });

            // Add click event to the map
            map.addListener('click', function(event) {
                marker.setPosition(event.latLng);
                selectedLat = event.latLng.lat();
                selectedLng = event.latLng.lng();
                document.getElementById('coordinates').textContent = 'Lat: ' + selectedLat.toFixed(6) + ', Lng: ' + selectedLng.toFixed(6);
                document.getElementById('address').textContent = 'Address: Updating...';

                // Try to get the address if Geocoder is available
                try {
                    if (google.maps.Geocoder) {
                        var geocoder = new google.maps.Geocoder();
                        var latlng = {lat: selectedLat, lng: selectedLng};

                        geocoder.geocode({'location': latlng}, function(results, status) {
                            if (status === 'OK') {
                                if (results[0]) {
                                    selectedAddress = results[0].formatted_address;
                                    document.getElementById('address').textContent = 'Address: ' + selectedAddress;
                                } else {
                                    document.getElementById('address').textContent = 'Address: No results found';
                                }
                            } else {
                                document.getElementById('address').textContent = 'Address: Geocoder failed due to: ' + status;
                            }
                        });
                    }
                } catch(e) {
                    console.error('Geocoding error:', e);
                    document.getElementById('address').textContent = 'Address: Geocoding not available';
                }
            });

            // Add drag end event to the marker with similar geocoding logic
            marker.addListener('dragend', function() {
                var position = marker.getPosition();
                selectedLat = position.lat();
                selectedLng = position.lng();
                document.getElementById('coordinates').textContent = 'Lat: ' + selectedLat.toFixed(6) + ', Lng: ' + selectedLng.toFixed(6);
                document.getElementById('address').textContent = 'Address: Updating...';

                try {
                    if (google.maps.Geocoder) {
                        var geocoder = new google.maps.Geocoder();
                        var latlng = {lat: selectedLat, lng: selectedLng};

                        geocoder.geocode({'location': latlng}, function(results, status) {
                            if (status === 'OK') {
                                if (results[0]) {
                                    selectedAddress = results[0].formatted_address;
                                    document.getElementById('address').textContent = 'Address: ' + selectedAddress;
                                } else {
                                    document.getElementById('address').textContent = 'Address: No results found';
                                }
                            } else {
                                document.getElementById('address').textContent = 'Address: Geocoder failed due to: ' + status;
                            }
                        });
                    }
                } catch(e) {
                    console.error('Geocoding error:', e);
                    document.getElementById('address').textContent = 'Address: Geocoding not available';
                }
            });
        }

        function selectLocation() {
            // Save the selected location to localStorage
            localStorage.setItem('selectedLat', selectedLat);
            localStorage.setItem('selectedLng', selectedLng);
            localStorage.setItem('selectedAddress', selectedAddress);

            // Alert the user
            alert('Location selected: ' + selectedAddress);
        }
    </script>
    <!-- Using only the Maps JavaScript API without additional libraries -->
    <script src="https://maps.googleapis.com/maps/api/js?key={api_key}&callback=initMap" async defer></script>
</body>
</html>
