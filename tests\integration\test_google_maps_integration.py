#!/usr/bin/env python3
"""
Test script for the GoogleMapsIntegration class.
This script tests the geocoding and reverse geocoding functionality.
"""

import sys
import os
from api.google_maps.google_maps_integration import GoogleMapsIntegration

def test_geocoding():
    """Test geocoding functionality"""
    print("\n=== Testing Geocoding ===")
    
    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()
    
    # Test addresses
    test_addresses = [
        "Prague",
        "Prague, Czech Republic",
        "Karlovy Vary",
        "Brno",
        "Plzeňská, Prague",
        "Václavské náměstí, Prague",  # Test with diacritics
        "Náměstí Republiky 1, Prague 1"  # Test with more complex address
    ]
    
    results = {}
    for address in test_addresses:
        print(f"\nGeocoding '{address}'...")
        result = google_maps.geocode_address(address)
        results[address] = result
        
        if result:
            print(f"Success: {result['formatted_address']}")
            print(f"Coordinates: {result['lat']}, {result['lng']}")
        else:
            print(f"Failed to geocode '{address}'")
    
    # Summary
    print("\n=== Geocoding Summary ===")
    for address, result in results.items():
        status = "OK" if result else "FAILED"
        print(f"{address}: {status}")
    
    # Check if any failed
    failed = [address for address, result in results.items() if not result]
    if failed:
        print("\nSome addresses failed to geocode:")
        for address in failed:
            print(f"- {address}")
    else:
        print("\nAll addresses geocoded successfully!")

def test_reverse_geocoding():
    """Test reverse geocoding functionality"""
    print("\n=== Testing Reverse Geocoding ===")
    
    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()
    
    # Test coordinates (Prague, Brno, Karlovy Vary, Ostrava)
    test_coordinates = [
        (50.0755, 14.4378),  # Prague
        (49.1951, 16.6068),  # Brno
        (50.2329, 12.8713),  # Karlovy Vary
        (49.8209, 18.2625)   # Ostrava
    ]
    
    results = {}
    for lat, lng in test_coordinates:
        print(f"\nReverse geocoding coordinates {lat}, {lng}...")
        result = google_maps.reverse_geocode(lat, lng)
        results[(lat, lng)] = result
        
        if result:
            print(f"Success: {result['formatted_address']}")
            if 'address_components' in result:
                components = result['address_components']
                if 'locality' in components:
                    print(f"City: {components['locality']}")
                if 'administrative_area_level_1' in components:
                    print(f"Region: {components['administrative_area_level_1']}")
                if 'country' in components:
                    print(f"Country: {components['country']}")
        else:
            print(f"Failed to reverse geocode coordinates {lat}, {lng}")
    
    # Summary
    print("\n=== Reverse Geocoding Summary ===")
    for coords, result in results.items():
        status = "OK" if result else "FAILED"
        print(f"{coords}: {status}")
    
    # Check if any failed
    failed = [coords for coords, result in results.items() if not result]
    if failed:
        print("\nSome coordinates failed to reverse geocode:")
        for coords in failed:
            print(f"- {coords}")
    else:
        print("\nAll coordinates reverse geocoded successfully!")

def test_place_predictions():
    """Test place predictions functionality"""
    print("\n=== Testing Place Predictions ===")
    
    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()
    
    # Test queries
    test_queries = [
        "Prague",
        "Brno",
        "Karlovy",
        "Václavské",
        "Náměstí"
    ]
    
    results = {}
    for query in test_queries:
        print(f"\nGetting place predictions for '{query}'...")
        predictions = google_maps.get_place_predictions(query)
        results[query] = predictions
        
        if predictions:
            print(f"Found {len(predictions)} predictions:")
            for i, prediction in enumerate(predictions[:3]):  # Show only first 3
                print(f"  {i+1}. {prediction.get('description')}")
        else:
            print(f"No predictions found for '{query}'")
    
    # Summary
    print("\n=== Place Predictions Summary ===")
    for query, predictions in results.items():
        status = f"OK ({len(predictions)} results)" if predictions else "FAILED"
        print(f"{query}: {status}")

def main():
    """Main function"""
    print("Google Maps Integration Test")
    print("===========================")
    
    # Test geocoding
    test_geocoding()
    
    # Test reverse geocoding
    test_reverse_geocoding()
    
    # Test place predictions
    test_place_predictions()

if __name__ == "__main__":
    main()
