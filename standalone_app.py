"""
Standalone Czech Property Registry application.

This is a standalone version of the Czech Property Registry application
that searches a city, determines boundaries, calculates density, gets all coordinates
in the area, converts them, fetches data from RUIAN, and creates CUZK URLs.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser
import logging
import threading
import sys
import time
import math
import requests
import argparse
import subprocess
import os
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Import API modules
from api.simple_google_maps import SimpleGoogleMaps
from api.cuzk.cuzk_integration import CUZKIntegration
from api.google_maps.city_boundaries import get_city_boundaries

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("standalone.log"),
        logging.StreamHandler()
    ]
)

# Create a logger for this module
logger = logging.getLogger(__name__)

class StandaloneApp:
    """
    Standalone version of the Czech Property Registry application.

    This app searches a city, determines boundaries, calculates density, gets all coordinates
    in the area, converts them, fetches data from RUIAN, and creates CUZK URLs.
    """

    def __init__(self):
        """Initialize the application."""
        # Create the main window
        self.root = tk.Tk()
        self.root.title("Czech Property Registry - Standalone")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)

        # Initialize API components
        logger.info("Initializing API components...")
        self.google_maps = SimpleGoogleMaps()
        self.cuzk_integration = CUZKIntegration()

        # Create a session for API requests
        self.session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # We'll use our own implementation for OSM integration
        # instead of using the OSMIntegration class
        self.osm_integration = self

        # Initialize search state variables
        self.search_in_progress = False
        self.cancel_search = False
        self.current_boundaries = None
        self.current_coordinates = []
        self.current_results = []

        # Initialize caches
        self.osm_cache = {}
        self.ruian_cache = {}

        # Create the main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create the UI
        self.create_ui()

        # For compatibility with other components
        self.show_status = self.update_status

    def create_ui(self):
        """Create the user interface."""
        # Create a notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create the batch search tab
        self.batch_search_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.batch_search_frame, text="Batch Search")

        # Create the batch search UI
        self.create_batch_search_ui()

    def create_batch_search_ui(self):
        """Create the batch search UI."""
        # Add a label to indicate this is the batch search UI
        ttk.Label(
            self.batch_search_frame,
            text="City Batch Search",
            font=("Arial", 16, "bold"),
            foreground="#4CAF50"
        ).pack(side="top", pady=10)

        # Create the search frame
        search_frame = ttk.LabelFrame(self.batch_search_frame, text="Search Parameters")
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        # City input
        ttk.Label(search_frame, text="City:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        # Import the SimpleAutocompleteEntry
        from utils.simple_autocomplete import SimpleAutocompleteEntry

        # Create the autocomplete entry
        self.city_entry = SimpleAutocompleteEntry(
            search_frame,
            autocomplete_function=self.get_autocomplete_suggestions,
            width=40
        )
        self.city_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)

        # Store a reference to the entry's variable
        self.city_var = self.city_entry.var

        # Radius selection for city boundary search
        ttk.Label(search_frame, text="Radius (km):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.radius_var = tk.StringVar(value="0.0")
        radius_values = ["0.0", "0.2", "0.5", "1.0", "2.0", "5.0"]
        self.radius_combobox = ttk.Combobox(search_frame, textvariable=self.radius_var, values=radius_values, width=10)
        self.radius_combobox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(search_frame, text="(0.0 = search entire city)").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)

        # Coordinate density
        ttk.Label(search_frame, text="Coordinate Density:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.density_var = tk.StringVar(value="auto")
        density_values = ["auto", "low", "medium", "high", "very high"]
        self.density_combobox = ttk.Combobox(search_frame, textvariable=self.density_var, values=density_values, width=10)
        self.density_combobox.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # Create a frame for density explanation
        density_info_frame = ttk.Frame(search_frame)
        density_info_frame.grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)

        # Add density explanation
        ttk.Label(density_info_frame, text="auto = calculate based on city size").pack(anchor=tk.W)
        ttk.Label(density_info_frame, text="low ≈ 100m, medium ≈ 50m, high ≈ 20m, very high ≈ 10m").pack(anchor=tk.W)

        # Search button
        self.search_button = ttk.Button(search_frame, text="Search", command=self.on_search)
        self.search_button.grid(row=3, column=0, padx=5, pady=10)

        # Cancel button
        self.cancel_button = ttk.Button(search_frame, text="Cancel", command=self.on_cancel_search)
        self.cancel_button.grid(row=3, column=1, padx=5, pady=10, sticky=tk.W)
        self.cancel_button.config(state=tk.DISABLED)

        # Status label
        self.status_label = ttk.Label(search_frame, text="Ready")
        self.status_label.grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Switch to full app button
        self.switch_to_full_app_btn = ttk.Button(
            search_frame,
            text="Switch to Full App",
            command=self.on_switch_to_full_app
        )
        self.switch_to_full_app_btn.grid(row=4, column=2, sticky=tk.E, padx=5, pady=5)

        # Progress frame
        progress_frame = ttk.Frame(search_frame)
        progress_frame.grid(row=5, column=0, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)

        # Progress bar
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            orient=tk.HORIZONTAL,
            length=400,
            mode="determinate"
        )
        self.progress_bar.pack(fill=tk.X, expand=True)

        # Progress label
        self.progress_label = ttk.Label(progress_frame, text="")
        self.progress_label.pack(fill=tk.X, expand=True, pady=2)

        # Configure grid
        search_frame.columnconfigure(1, weight=1)

        # Create the results frame
        results_frame = ttk.LabelFrame(self.batch_search_frame, text="Search Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a treeview for displaying buildings
        columns = ("id", "lat", "lng", "x_sjtsk", "y_sjtsk", "url")
        self.buildings_tree = ttk.Treeview(results_frame, columns=columns, show="headings")

        # Define column headings
        self.buildings_tree.heading("id", text="#")
        self.buildings_tree.heading("lat", text="Latitude")
        self.buildings_tree.heading("lng", text="Longitude")
        self.buildings_tree.heading("x_sjtsk", text="X (S-JTSK)")
        self.buildings_tree.heading("y_sjtsk", text="Y (S-JTSK)")
        self.buildings_tree.heading("url", text="CUZK URL")

        # Define column widths
        self.buildings_tree.column("id", width=50, anchor=tk.CENTER)
        self.buildings_tree.column("lat", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("lng", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("x_sjtsk", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("y_sjtsk", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("url", width=400, anchor=tk.W)

        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.buildings_tree.yview)
        self.buildings_tree.configure(yscrollcommand=y_scrollbar.set)

        # Pack the treeview and scrollbars
        self.buildings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind double-click event to open URL
        self.buildings_tree.bind("<Double-1>", self.on_building_double_click)

        # Create buttons frame
        buttons_frame = ttk.Frame(results_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # Open selected button
        self.open_selected_button = ttk.Button(buttons_frame, text="Open Selected", command=self.on_open_selected)
        self.open_selected_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Results count label
        self.results_count_label = ttk.Label(buttons_frame, text="0 buildings found")
        self.results_count_label.pack(side=tk.RIGHT, padx=5, pady=5)

    def on_search(self):
        """Handle the search button click."""
        # Get the city and radius
        city = self.city_var.get()
        radius = self.radius_var.get()
        density = self.density_var.get()

        if not city:
            self.show_message("Missing Information", "Please enter a city name to search.")
            return

        try:
            radius_float = float(radius)
            if radius_float < 0:
                self.show_message("Invalid Radius", "Radius must be 0 or greater.")
                return
        except ValueError:
            self.show_message("Invalid Radius", "Please enter a valid number for the radius.")
            return

        # Update UI state
        self.search_button.config(state=tk.DISABLED)
        self.cancel_button.config(state=tk.NORMAL)
        self.search_in_progress = True
        self.cancel_search = False

        # Clear previous results
        self.clear_results()

        # Reset progress bar
        self.progress_bar["value"] = 0
        self.progress_label.config(text="")

        # Start the search in a background thread
        threading.Thread(target=self._search_thread, args=(city, radius_float, density), daemon=True).start()

    def _search_thread(self, city, radius, density):
        """
        Background thread for searching buildings in a city.

        Args:
            city (str): City name to search
            radius (float): Radius in kilometers (0 = entire city)
            density (str): Coordinate density (low, medium, high)
        """
        try:
            # Define progress stages and their weights
            stages = {
                "geocoding": 5,           # 0-5%
                "boundaries": 10,         # 5-15%
                "density_calc": 5,        # 15-20%
                "coord_generation": 25,   # 20-45%
                "data_fetching": 50,      # 45-95%
                "display": 5              # 95-100%
            }
            current_progress = 0

            # Update progress function that tracks overall progress
            def update_stage_progress(stage, stage_progress, message=None):
                nonlocal current_progress
                stage_weight = stages[stage]
                stage_start = sum(weight for s, weight in stages.items() if s < stage)

                # Calculate overall progress
                overall_progress = stage_start + (stage_weight * stage_progress / 100)
                current_progress = overall_progress

                # Update UI with progress
                if message:
                    self.update_progress(overall_progress, message)
                else:
                    self.update_progress(overall_progress)

            # Step 1: Geocode the city
            update_stage_progress("geocoding", 0, "Geocoding city...")

            # Add country to city name if not already included
            if "czech republic" not in city.lower() and "česk" not in city.lower():
                full_city_name = f"{city}, Czech Republic"
            else:
                full_city_name = city

            self.update_status(f"Geocoding city: {full_city_name}")

            # Try with specific components to ensure we get Czech cities
            try:
                # First try with a more specific search for exact city name
                import urllib.parse

                # Clean up the city name for comparison
                clean_city = city.lower().strip()

                # Try using the Places API for more precise city search
                encoded_input = urllib.parse.quote(f"{city} Czech Republic")
                url = f"https://maps.googleapis.com/maps/api/place/textsearch/json?query={encoded_input}&type=locality&key={self.google_maps.api_key}"

                logger.info(f"Searching for exact city match: {city}")
                response = requests.get(url, timeout=10)

                if response.status_code == 200:
                    data = response.json()

                    if data.get('status') == 'OK' and data.get('results'):
                        # Log the number of results
                        logger.info(f"Found {len(data.get('results'))} place results for {city}")

                        # Look for an exact match first
                        exact_match = None
                        for result in data.get('results', []):
                            result_name = result.get('name', '').lower().strip()

                            # Check if this is an exact match
                            if result_name == clean_city:
                                logger.info(f"Found exact match for {city}: {result.get('name')}")
                                exact_match = result
                                break

                            # Check if it's a partial match (city name is contained in result name)
                            if clean_city in result_name or result_name in clean_city:
                                logger.info(f"Found partial match for {city}: {result.get('name')}")
                                if not exact_match:  # Only use partial match if no exact match found
                                    exact_match = result

                        # If we found an exact match, use it
                        if exact_match:
                            # Check if this is actually in Czech Republic
                            formatted_address = exact_match.get('formatted_address', '')
                            if 'Czech Republic' in formatted_address or 'Czechia' in formatted_address or 'CZ' in formatted_address:
                                logger.info(f"Confirmed {city} is in Czech Republic")

                                # Get the geometry
                                geometry = exact_match.get('geometry', {})

                                # Get the location
                                if 'location' in geometry:
                                    location = geometry['location']
                                    geocode_result = {
                                        'lat': location['lat'],
                                        'lng': location['lng'],
                                        'formatted_address': exact_match.get('formatted_address', ''),
                                        'name': exact_match.get('name', '')
                                    }

                                    logger.info(f"Using exact match for {city}: {geocode_result.get('name')} at {geocode_result.get('lat')}, {geocode_result.get('lng')}")

                # If we couldn't find an exact match, try standard geocoding with country restriction
                if not geocode_result:
                    logger.info(f"No exact match found, trying standard geocoding with country restriction: {full_city_name}")

                    encoded_address = urllib.parse.quote(full_city_name)
                    url = f"https://maps.googleapis.com/maps/api/geocode/json?address={encoded_address}&components=country:cz&key={self.google_maps.api_key}"

                    response = requests.get(url, timeout=10)

                    if response.status_code == 200:
                        data = response.json()

                        if data['status'] == 'OK' and data['results']:
                            # Extract the location
                            location = data['results'][0]['geometry']['location']

                            # Get the formatted address and extract the city name
                            formatted_address = data['results'][0]['formatted_address']
                            address_components = data['results'][0]['address_components']

                            # Try to extract the city name from address components
                            result_city = None
                            for component in address_components:
                                if 'locality' in component.get('types', []):
                                    result_city = component.get('long_name', '')
                                    break

                            # If we couldn't find a locality, try administrative_area_level_2
                            if not result_city:
                                for component in address_components:
                                    if 'administrative_area_level_2' in component.get('types', []):
                                        result_city = component.get('long_name', '')
                                        break

                            # If we still couldn't find a city name, use the first part of the formatted address
                            if not result_city and formatted_address:
                                result_city = formatted_address.split(',')[0].strip()

                            # Check if the result city matches our search
                            if result_city:
                                result_city_clean = result_city.lower().strip()

                                # Check for exact or partial match
                                if result_city_clean == clean_city or clean_city in result_city_clean or result_city_clean in clean_city:
                                    logger.info(f"Found matching city: {result_city}")

                                    geocode_result = {
                                        'lat': location['lat'],
                                        'lng': location['lng'],
                                        'formatted_address': formatted_address,
                                        'address_components': address_components,
                                        'name': result_city
                                    }
                                else:
                                    logger.warning(f"Geocoding returned a different city: {result_city} instead of {city}")
                                    # We'll still use this result if it's in Czech Republic

                                    # Verify this is actually in Czech Republic
                                    is_czech = False
                                    for component in address_components:
                                        if 'country' in component.get('types', []) and component.get('short_name') == 'CZ':
                                            is_czech = True
                                            break

                                    if is_czech:
                                        logger.info(f"Using non-matching city in Czech Republic: {result_city}")
                                        geocode_result = {
                                            'lat': location['lat'],
                                            'lng': location['lng'],
                                            'formatted_address': formatted_address,
                                            'address_components': address_components,
                                            'name': result_city
                                        }
                                    else:
                                        logger.warning(f"Geocoding result is not in Czech Republic")
                                        geocode_result = None
                        else:
                            logger.warning(f"Geocoding error: {data['status']}")
                            geocode_result = None
                    else:
                        logger.warning(f"Geocoding HTTP error: {response.status_code}")
                        geocode_result = None

                # If all direct methods failed, fall back to the API
                if not geocode_result:
                    logger.info(f"Falling back to standard geocoding for: {full_city_name}")
                    geocode_result = self.google_maps.geocode(full_city_name)

                    # If we got a result, check if it's the right city
                    if geocode_result:
                        # Try to extract the city name from address components
                        result_city = None
                        for component in geocode_result.get('address_components', []):
                            if 'locality' in component.get('types', []):
                                result_city = component.get('long_name', '')
                                break

                        if result_city:
                            result_city_clean = result_city.lower().strip()

                            # Check for exact or partial match
                            if result_city_clean != clean_city and clean_city not in result_city_clean and result_city_clean not in clean_city:
                                logger.warning(f"Standard geocoding returned a different city: {result_city} instead of {city}")
                                # We'll still use this result, but log the warning
            except Exception as e:
                logger.error(f"Error in direct geocoding: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # Fall back to the API
                geocode_result = self.google_maps.geocode(full_city_name)

            update_stage_progress("geocoding", 100)

            if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
                self.handle_error(f"Could not geocode city: {city}")
                return

            # Check if we got a different city than requested
            if 'name' in geocode_result:
                result_city = geocode_result['name']
                if result_city.lower().strip() != city.lower().strip():
                    # Show a warning dialog
                    warning_message = f"Warning: The search returned '{result_city}' instead of '{city}'.\n\nDo you want to continue with '{result_city}'?"
                    if not messagebox.askyesno("City Mismatch", warning_message):
                        self.handle_cancellation()
                        return

                    # Update the city name in the UI
                    self.city_entry.set(result_city)

            city_lat = geocode_result['lat']
            city_lng = geocode_result['lng']

            self.update_status(f"Found coordinates for {geocode_result.get('name', city)}: {city_lat:.6f}, {city_lng:.6f}")

            # Step 2: Get city boundaries
            update_stage_progress("boundaries", 0, "Determining city boundaries...")

            if radius == 0:
                # Search the entire city
                self.update_status(f"Fetching boundaries for city: {city}")

                # Try to get boundaries with direct API call first
                try:
                    # Use the Places API with specific type for cities
                    import urllib.parse
                    encoded_input = urllib.parse.quote(f"{city} Czech Republic")
                    url = f"https://maps.googleapis.com/maps/api/place/textsearch/json?query={encoded_input}&type=locality&key={self.google_maps.api_key}"

                    logger.info(f"Searching for city boundaries with direct API call: {city}")
                    response = requests.get(url, timeout=10)

                    if response.status_code == 200:
                        data = response.json()

                        if data.get('status') == 'OK' and data.get('results'):
                            # Log the number of results
                            logger.info(f"Found {len(data.get('results'))} place results for {city}")

                            # Get the first result
                            result = data['results'][0]

                            # Log the result name
                            logger.info(f"Selected place: {result.get('name', 'Unknown')}")

                            # Check if this is actually in Czech Republic
                            formatted_address = result.get('formatted_address', '')
                            if 'Czech Republic' in formatted_address or 'Czechia' in formatted_address or 'CZ' in formatted_address:
                                logger.info(f"Confirmed {city} is in Czech Republic")

                                # Get the geometry
                                geometry = result.get('geometry', {})

                                # Try viewport first
                                if 'viewport' in geometry:
                                    viewport = geometry['viewport']
                                    boundaries = {
                                        'north': viewport['northeast']['lat'],
                                        'south': viewport['southwest']['lat'],
                                        'east': viewport['northeast']['lng'],
                                        'west': viewport['southwest']['lng']
                                    }
                                    logger.info(f"Found boundaries for {city} using Places API: {boundaries}")
                                    self.current_boundaries = boundaries
                                    update_stage_progress("boundaries", 100)

                    # If we get here, the direct API call failed
                    logger.warning(f"Direct API call for city boundaries failed, falling back to get_city_boundaries")
                except Exception as e:
                    logger.error(f"Error in direct API call for city boundaries: {e}")

                # Fall back to the standard function
                boundaries = get_city_boundaries(city, "Czech Republic", self.google_maps)
                update_stage_progress("boundaries", 100)

                if not boundaries:
                    self.handle_error(f"Could not determine boundaries for city: {city}")
                    return

                self.current_boundaries = boundaries

                north = boundaries['north']
                south = boundaries['south']
                east = boundaries['east']
                west = boundaries['west']

                self.update_status(f"Found city boundaries: N={north:.6f}, S={south:.6f}, E={east:.6f}, W={west:.6f}")
            else:
                # Search within a radius of the city center
                # Convert radius from km to degrees (approximate)
                # 1 degree of latitude is approximately 111 km
                lat_offset = radius / 111.0
                # 1 degree of longitude varies with latitude
                lng_offset = radius / (111.0 * math.cos(math.radians(city_lat)))

                boundaries = {
                    'north': city_lat + lat_offset,
                    'south': city_lat - lat_offset,
                    'east': city_lng + lng_offset,
                    'west': city_lng - lng_offset
                }

                self.current_boundaries = boundaries

                north = boundaries['north']
                south = boundaries['south']
                east = boundaries['east']
                west = boundaries['west']

                self.update_status(f"Created search area with radius {radius}km around {city}")
                update_stage_progress("boundaries", 100)

            update_stage_progress("density_calc", 0, "Calculating coordinate density...")

            # Step 3: Calculate coordinate density based on area size
            # Calculate area dimensions in km
            width_km = (east - west) * 111.0 * math.cos(math.radians(city_lat))
            height_km = (north - south) * 111.0
            area_km2 = width_km * height_km

            update_stage_progress("density_calc", 50, f"Calculating density for area: {area_km2:.2f} km²")

            # Determine grid step based on density and area size
            if density == "auto":
                # Automatically determine density based on city size
                if area_km2 < 1:  # Very small area (< 1 km²)
                    grid_step = 0.00005  # Approximately 5m between points (ultra high density)
                elif area_km2 < 5:  # Small area (1-5 km²)
                    grid_step = 0.0001  # Approximately 10m between points (super high density)
                elif area_km2 < 20:  # Medium-small area (5-20 km²)
                    grid_step = 0.0002  # Approximately 20m between points (very high density)
                elif area_km2 < 50:  # Medium area (20-50 km²)
                    grid_step = 0.0003  # Approximately 30m between points (high density)
                elif area_km2 < 100:  # Medium-large area (50-100 km²)
                    grid_step = 0.0004  # Approximately 40m between points (medium-high density)
                elif area_km2 < 200:  # Large area (100-200 km²)
                    grid_step = 0.0005  # Approximately 50m between points (medium density)
                else:  # Very large area (> 200 km²)
                    grid_step = 0.0008  # Approximately 80m between points (adjusted density)

                self.update_status(f"Auto-selected grid density: {grid_step * 100000:.0f}m between points")
            elif density == "low":
                grid_step = 0.001  # Approximately 100m between points
            elif density == "medium":
                grid_step = 0.0005  # Approximately 50m between points
            elif density == "high":
                grid_step = 0.0002  # Approximately 20m between points
            else:  # very high
                grid_step = 0.0001  # Approximately 10m between points

            # Calculate number of points
            lat_steps = int((north - south) / grid_step) + 1
            lng_steps = int((east - west) / grid_step) + 1
            total_points = lat_steps * lng_steps

            update_stage_progress("density_calc", 100, f"Density calculated: {grid_step * 100000:.0f}m between points")

            # Check if the number of points is very large
            if total_points > 500000:
                warning_message = f"Warning: This search will generate {total_points:,} coordinates, which may take a long time and use significant resources."
                self.update_status(warning_message)

                # Ask for confirmation
                if not messagebox.askyesno("Large Search Area",
                                          f"{warning_message}\n\nDo you want to continue with this search?\n\nYou can reduce the number of coordinates by:\n- Using a smaller radius\n- Selecting a lower density\n- Searching a more specific area"):
                    self.handle_cancellation()
                    return

            self.update_status(f"Area: {area_km2:.2f} km² with {total_points:,} coordinates at {density} density")

            # Step 4: Generate coordinates within the boundaries
            update_stage_progress("coord_generation", 0, f"Generating {total_points:,} coordinates...")

            coordinates = []
            points_processed = 0

            for i in range(lat_steps):
                if self.cancel_search:
                    self.handle_cancellation()
                    return

                lat = south + i * grid_step

                for j in range(lng_steps):
                    lng = west + j * grid_step

                    coordinates.append({
                        'lat': lat,
                        'lng': lng
                    })

                    points_processed += 1

                    # Update progress every 100 points or at least every 1%
                    update_interval = max(100, int(total_points / 100))
                    if points_processed % update_interval == 0 or points_processed == total_points:
                        progress_percent = (points_processed / total_points) * 100
                        update_stage_progress("coord_generation", progress_percent,
                                             f"Generated {points_processed:,}/{total_points:,} coordinates ({progress_percent:.1f}%)")

            self.current_coordinates = coordinates
            update_stage_progress("coord_generation", 100, f"Generated {len(coordinates):,} coordinates")

            # Step 5: Convert coordinates and fetch RUIAN data
            update_stage_progress("data_fetching", 0, "Converting coordinates and fetching RUIAN data...")

            # Initialize the results display
            self.root.after(0, lambda: self._initialize_results_display())

            results = []
            processed_count = 0
            total_count = len(coordinates)
            batch_size = 10  # Process this many coordinates before updating UI
            current_batch = []

            for coord in coordinates:
                if self.cancel_search:
                    self.handle_cancellation()
                    return

                lat = coord['lat']
                lng = coord['lng']

                # Convert WGS84 to S-JTSK
                x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)

                # Ensure coordinates are integers
                x_int = int(x)
                y_int = int(y)

                # Generate CUZK URL
                url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"

                # Fetch RUIAN data
                ruian_data = self._fetch_ruian_data(lat, lng, x_int, y_int)

                # Create result dictionary
                result = {
                    'id': processed_count + 1,
                    'lat': lat,
                    'lng': lng,
                    'x_sjtsk': x_int,
                    'y_sjtsk': y_int,
                    'url': url,
                    'ruian_id': ruian_data.get('ruian_id', f"coord_{x_int}_{y_int}"),
                    'property_type': ruian_data.get('property_type', 'Building')
                }

                # Add additional RUIAN data if available
                if 'building_type' in ruian_data:
                    result['building_type'] = ruian_data['building_type']
                if 'street' in ruian_data:
                    result['street'] = ruian_data['street']
                if 'housenumber' in ruian_data:
                    result['housenumber'] = ruian_data['housenumber']
                if 'city' in ruian_data:
                    result['city'] = ruian_data['city']
                if 'postcode' in ruian_data:
                    result['postcode'] = ruian_data['postcode']

                results.append(result)
                current_batch.append(result)
                processed_count += 1

                # Update UI with current batch of results
                if len(current_batch) >= batch_size or processed_count == total_count:
                    # Add the current batch to the treeview
                    batch_to_display = current_batch.copy()
                    self.root.after(0, lambda b=batch_to_display: self._add_batch_to_treeview(b, processed_count, total_count))
                    current_batch = []

                # Update progress at regular intervals
                update_interval = max(10, int(total_count / 100))
                if processed_count % update_interval == 0 or processed_count == total_count:
                    progress_percent = (processed_count / total_count) * 100
                    update_stage_progress("data_fetching", progress_percent,
                                         f"Processed {processed_count:,}/{total_count:,} coordinates ({progress_percent:.1f}%)")

                    # Add a small delay to avoid overwhelming the system
                    time.sleep(0.01)

            self.current_results = results
            update_stage_progress("data_fetching", 100, f"Completed processing {len(results):,} coordinates")

            # Step 6: Finalize results
            update_stage_progress("display", 90, "Finalizing results...")
            self.root.after(0, lambda: self._finalize_results_display(results, update_stage_progress))

        except Exception as e:
            self.handle_error(f"Error in search: {str(e)}")
            import traceback
            logger.error(f"Search error: {traceback.format_exc()}")

    def on_cancel_search(self):
        """Cancel the current search operation."""
        if self.search_in_progress:
            self.cancel_search = True
            self.update_status("Cancelling search...")
            self.cancel_button.config(state=tk.DISABLED)

    def handle_cancellation(self):
        """Handle search cancellation."""
        self.root.after(0, lambda: self.update_status("Search cancelled"))
        self.root.after(0, lambda: self.search_button.config(state=tk.NORMAL))
        self.root.after(0, lambda: self.cancel_button.config(state=tk.DISABLED))
        self.search_in_progress = False

    def handle_error(self, error_message):
        """Handle errors during search."""
        logger.error(error_message)
        self.root.after(0, lambda: self.update_status(f"Error: {error_message}"))
        self.root.after(0, lambda: self.search_button.config(state=tk.NORMAL))
        self.root.after(0, lambda: self.cancel_button.config(state=tk.DISABLED))
        self.root.after(0, lambda: self.show_message("Error", error_message))
        self.search_in_progress = False

    def update_progress(self, percentage, message=""):
        """Update the progress bar and message."""
        self.root.after(0, lambda: self._update_progress_ui(percentage, message))

    def _update_progress_ui(self, percentage, message):
        """Update the progress UI elements."""
        self.progress_bar["value"] = percentage
        self.progress_label.config(text=message)
        self.update_status(message)

    def update_status(self, message):
        """Update the status label."""
        self.status_label.config(text=message)

    def _fetch_ruian_data(self, lat, lng, x_int, y_int):
        """
        Fetch RUIAN data for the given coordinates.

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84
            x_int (int): X coordinate in S-JTSK
            y_int (int): Y coordinate in S-JTSK

        Returns:
            dict: RUIAN data
        """
        try:
            # Create a cache key
            cache_key = f"{lat:.6f}_{lng:.6f}"

            # Check if we have cached data
            if cache_key in self.ruian_cache:
                return self.ruian_cache[cache_key]

            # Initialize with basic data
            ruian_data = {
                'property_type': 'Building',
                'ruian_id': f"coord_{x_int}_{y_int}",
                'x_sjtsk': x_int,
                'y_sjtsk': y_int,
                'url': f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}",
                'data_sources': []
            }

            # Try to get building data from OSM
            try:
                # Search for buildings near the coordinates
                buildings = self.find_buildings_by_coordinates(lat, lng, 50)  # 50m radius

                if buildings:
                    # Use the first building found
                    building = buildings[0]

                    # Extract RUIAN ID if available
                    if building.get('ruian_ref'):
                        ruian_data['ruian_id'] = building['ruian_ref']

                    # Extract tags
                    tags = building.get('tags', {})

                    # Extract building type
                    if 'building' in tags:
                        ruian_data['building_type'] = tags['building']

                    # Extract address components
                    if 'addr:street' in tags:
                        ruian_data['street'] = tags['addr:street']
                    if 'addr:housenumber' in tags:
                        ruian_data['housenumber'] = tags['addr:housenumber']
                    if 'addr:city' in tags:
                        ruian_data['city'] = tags['addr:city']
                    if 'addr:postcode' in tags:
                        ruian_data['postcode'] = tags['addr:postcode']

                    # Add OSM as a data source
                    ruian_data['data_sources'].append('osm')

                    # Log success
                    logger.info(f"Found building data from OSM for coordinates {lat:.6f}, {lng:.6f}")
            except Exception as e:
                # Log the error but continue
                logger.error(f"Error fetching OSM data: {e}")
                # Don't re-raise the exception - we'll continue without OSM data

            # Try to get property data from CUZK
            try:
                # Check if the CUZK integration has the method
                if hasattr(self.cuzk_integration, 'get_property_by_coordinates'):
                    property_data = self.cuzk_integration.get_property_by_coordinates(x_int, y_int)

                    if property_data:
                        # Update RUIAN data with property data
                        for key, value in property_data.items():
                            ruian_data[key] = value

                        # Add CUZK as a data source
                        ruian_data['data_sources'].append('cuzk')

                        # Log success
                        logger.info(f"Found property data from CUZK for coordinates {x_int}, {y_int}")
                else:
                    # Log that the method is not available
                    logger.info("CUZK integration does not have get_property_by_coordinates method")

                    # Still generate the URL
                    ruian_data['url'] = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"
                    logger.info(f"Generated CUZK URL: {ruian_data['url']}")
            except Exception as e:
                # Log the error but continue
                logger.error(f"Error fetching CUZK data: {e}")
                # Don't re-raise the exception - we'll continue without CUZK data

                # Still generate the URL
                ruian_data['url'] = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"
                logger.info(f"Generated CUZK URL: {ruian_data['url']}")

            # Cache the result
            self.ruian_cache[cache_key] = ruian_data

            return ruian_data

        except Exception as e:
            logger.error(f"Error fetching RUIAN data: {e}")
            return {
                'property_type': 'Building',
                'ruian_id': f"coord_{x_int}_{y_int}",
                'x_sjtsk': x_int,
                'y_sjtsk': y_int,
                'url': f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}",
                'error': str(e)
            }

    def clear_results(self):
        """Clear the results treeview."""
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)

        self.results_count_label.config(text="0 buildings found")

    def _initialize_results_display(self):
        """Initialize the results display before processing coordinates."""
        # Clear previous results
        self.clear_results()

        # Update the status
        self.status_label.config(text="Processing coordinates... Results will appear as they're processed")
        self.results_count_label.config(text="Processing coordinates...")

    def _add_batch_to_treeview(self, batch, processed_count, total_count):
        """
        Add a batch of results to the treeview as they're processed.

        Args:
            batch (list): List of building data dictionaries to add
            processed_count (int): Number of coordinates processed so far
            total_count (int): Total number of coordinates to process
        """
        # Add each building in the batch to the treeview
        for building in batch:
            # Format the display values
            building_id = building['id']
            lat = f"{building['lat']:.6f}"
            lng = f"{building['lng']:.6f}"
            x_sjtsk = building['x_sjtsk']
            y_sjtsk = building['y_sjtsk']
            url = building['url']

            # Insert into treeview
            self.buildings_tree.insert("", tk.END, values=(
                building_id,
                lat,
                lng,
                x_sjtsk,
                y_sjtsk,
                url
            ))

        # Update the results count label with progress
        self.results_count_label.config(text=f"{processed_count:,}/{total_count:,} coordinates processed")

        # Update the status
        self.status_label.config(text=f"Processing coordinates: {processed_count:,}/{total_count:,} ({processed_count/total_count*100:.1f}%)")

        # Force the UI to update
        self.root.update_idletasks()

    def _finalize_results_display(self, buildings, update_stage_progress=None):
        """
        Finalize the results display after all coordinates have been processed.

        Args:
            buildings (list): Complete list of building data dictionaries
            update_stage_progress (function, optional): Function to update progress stages
        """
        # Update progress if function is provided
        if update_stage_progress:
            update_stage_progress("display", 95, "Finalizing results...")

        # Reset UI state
        self.search_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.search_in_progress = False

        if not buildings:
            self.status_label.config(text="No buildings found")
            self.show_message("No Results", "No buildings found in the specified area.")

            # Complete progress bar even if no results
            if update_stage_progress:
                update_stage_progress("display", 100, "Search completed with no results")

            return

        # Update the results count label
        self.results_count_label.config(text=f"{len(buildings):,} coordinates found")
        self.status_label.config(text="Search completed")

        # Log the results
        logger.info(f"Found {len(buildings):,} coordinates")

        # Show statistics message
        self._show_results_statistics(buildings)

        # Complete the progress bar
        if update_stage_progress:
            update_stage_progress("display", 100, "Search completed successfully")

    def _show_results_statistics(self, buildings):
        """
        Show statistics about the search results.

        Args:
            buildings (list): List of building data dictionaries
        """
        # Calculate statistics
        ruian_ids = sum(1 for b in buildings if 'ruian_id' in b and not b['ruian_id'].startswith('coord_'))
        osm_buildings = sum(1 for b in buildings if 'data_sources' in b and 'osm' in b.get('data_sources', []))
        cuzk_buildings = sum(1 for b in buildings if 'data_sources' in b and 'cuzk' in b.get('data_sources', []))

        # Get area information if available
        area_info = ""
        if hasattr(self, 'current_boundaries') and self.current_boundaries:
            north = self.current_boundaries['north']
            south = self.current_boundaries['south']
            east = self.current_boundaries['east']
            west = self.current_boundaries['west']

            # Calculate area dimensions in km
            width_km = (east - west) * 111.0 * math.cos(math.radians((north + south) / 2))
            height_km = (north - south) * 111.0
            area_km2 = width_km * height_km

            area_info = f"Search area: {area_km2:.2f} km²\n"
            density_info = f"Coordinate density: {len(buildings) / area_km2:.1f} points/km²\n" if area_km2 > 0 else ""
        else:
            density_info = ""

        stats_message = (
            f"Search completed successfully!\n\n"
            f"{area_info}"
            f"Total coordinates: {len(buildings):,}\n"
            f"{density_info}"
            f"With RUIAN IDs: {ruian_ids}\n"
            f"With OSM data: {osm_buildings}\n"
            f"With CUZK data: {cuzk_buildings}\n\n"
            f"Double-click on a row or use 'Open Selected' to view property details.\n"
            f"You can continue to interact with results while more are being loaded."
        )

        self.show_message("Search Results", stats_message)

    def display_search_results(self, buildings, update_stage_progress=None):
        """
        Display the search results in the treeview.
        This method is kept for backward compatibility.

        Args:
            buildings (list): List of building data dictionaries
            update_stage_progress (function, optional): Function to update progress stages
        """
        # Initialize the display
        self._initialize_results_display()

        # Add all buildings at once
        self._add_batch_to_treeview(buildings, len(buildings), len(buildings))

        # Finalize the display
        self._finalize_results_display(buildings, update_stage_progress)

    def show_message(self, title, message):
        """Show a message box."""
        messagebox.showinfo(title, message)

    def find_buildings_by_coordinates(self, lat, lng, radius=50):
        """
        Find buildings near the given coordinates using OpenStreetMap.
        This method is required by OSMIntegration.

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84
            radius (int): Search radius in meters

        Returns:
            list: List of buildings
        """
        try:
            # Create a cache key
            cache_key = f"{lat:.6f}_{lng:.6f}_{radius}"

            # Check if we have cached data
            if cache_key in self.osm_cache:
                return self.osm_cache[cache_key]

            # Construct the Overpass API query
            overpass_url = "https://overpass-api.de/api/interpreter"

            # Use a simpler query to avoid potential errors
            overpass_query = f"""
            [out:json];
            (
              node["building"](around:{radius},{lat},{lng});
              way["building"](around:{radius},{lat},{lng});
              relation["building"](around:{radius},{lat},{lng});
            );
            out center;
            """

            # Make the request
            response = requests.post(overpass_url, data={"data": overpass_query}, timeout=30)

            # Check if the request was successful
            if response.status_code != 200:
                logger.error(f"Error fetching buildings from OpenStreetMap: {response.status_code}")
                return []

            # Parse the response
            data = response.json()

            # Extract buildings
            buildings = []

            for element in data.get('elements', []):
                # Get the tags
                tags = element.get('tags', {})

                # Extract coordinates based on element type
                if element.get('type') == 'node':
                    building = {
                        'type': element.get('type'),
                        'id': element.get('id'),
                        'lat': element.get('lat'),
                        'lng': element.get('lon'),
                        'tags': tags,
                        'ruian_ref': tags.get('ref:ruian', '')
                    }
                    buildings.append(building)
                elif element.get('type') in ['way', 'relation'] and 'center' in element:
                    building = {
                        'type': element.get('type'),
                        'id': element.get('id'),
                        'lat': element['center']['lat'],
                        'lng': element['center']['lon'],
                        'tags': tags,
                        'ruian_ref': tags.get('ref:ruian', '')
                    }
                    buildings.append(building)

            # Cache the results
            self.osm_cache[cache_key] = buildings

            return buildings

        except Exception as e:
            logger.error(f"Error finding buildings by coordinates: {e}")
            return []

    def on_building_double_click(self, _):
        """
        Handle double-click on a building in the treeview.

        Args:
            _: The event that triggered this callback (unused)
        """
        # Get the selected item
        selected_item = self.buildings_tree.selection()
        if not selected_item:
            return

        # Get the URL from the selected item
        values = self.buildings_tree.item(selected_item[0], "values")
        url = values[5]  # URL is the 6th column (index 5)

        # Open the URL in the browser
        if url:
            webbrowser.open(url)

    def on_open_selected(self):
        """Open the selected buildings in the browser."""
        # Get the selected items
        selected_items = self.buildings_tree.selection()
        if not selected_items:
            self.show_message("No Selection", "Please select one or more coordinates to open.")
            return

        # Open each selected building
        for item in selected_items:
            values = self.buildings_tree.item(item, "values")
            url = values[5]  # URL is the 6th column (index 5)

            if url:
                webbrowser.open(url)
                # Add a small delay to avoid overwhelming the browser
                time.sleep(0.5)

    def get_autocomplete_suggestions(self, text):
        """
        Get autocomplete suggestions for the given text.

        This method is used by the SimpleAutocompleteEntry widget.

        Args:
            text: Text to get suggestions for

        Returns:
            list: List of suggestion strings
        """
        print(f"DEBUG: get_autocomplete_suggestions called with text='{text}'")

        # If the text is too short, return an empty list
        if len(text) < 3:
            print(f"DEBUG: Text is too short for autocomplete")
            return []

        # Check if we have the Google Maps API available
        if hasattr(self, 'google_maps'):
            print(f"DEBUG: Google Maps API is available")

            # Try to use get_autocomplete_suggestions if it exists
            if hasattr(self.google_maps, 'get_autocomplete_suggestions'):
                print(f"DEBUG: Using get_autocomplete_suggestions method")
                # Get autocomplete suggestions
                suggestions = self.google_maps.get_autocomplete_suggestions(text)
            # Fall back to get_place_predictions if get_autocomplete_suggestions doesn't exist
            else:
                print(f"DEBUG: get_autocomplete_suggestions not found, using get_place_predictions instead")
                # Get place predictions and convert them to suggestions
                raw_suggestions = self.google_maps.get_place_predictions(text)
                suggestions = []
                if raw_suggestions:
                    for suggestion in raw_suggestions:
                        if isinstance(suggestion, dict) and 'description' in suggestion:
                            suggestions.append(suggestion['description'])
                        else:
                            suggestions.append(str(suggestion))

            print(f"DEBUG: Got {len(suggestions) if suggestions else 0} suggestions")
            return suggestions
        else:
            print(f"DEBUG: Google Maps API is not available")
            return []

    def on_switch_to_full_app(self):
        """Switch to the full application."""
        try:
            # Ask for confirmation
            if not messagebox.askyesno(
                "Switch to Full App",
                "Are you sure you want to switch to the full application? This will close the standalone app."
            ):
                return

            # Get the current city
            city = self.city_var.get()

            # Update status
            self.status_label.config(text="Launching full application...")

            # Launch the full application using the launcher
            launcher_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "launcher.py")

            # Check if the file exists
            if not os.path.exists(launcher_path):
                messagebox.showerror("Error", f"Launcher not found at: {launcher_path}")
                return

            # Launch the full application
            subprocess.Popen([sys.executable, launcher_path, "--full-app"])

            # Close the standalone app after a short delay
            self.root.after(1000, self.root.destroy)

        except Exception as e:
            logger.error(f"Error switching to full app: {e}")
            messagebox.showerror("Error", f"Error switching to full app: {e}")

    def run(self):
        """Run the application."""
        # Validate Google Maps API key
        if not self.google_maps.validate_api_key():
            self.show_message(
                "API Key Missing",
                "Google Maps API key is missing or invalid. Some features may not work correctly."
            )

        # Start the main loop
        self.root.mainloop()


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Standalone Czech Property Registry")

    parser.add_argument(
        "--city",
        type=str,
        help="City to search for"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )

    return parser.parse_args()

def setup_logging(debug=False):
    """Set up logging with the appropriate level."""
    level = logging.DEBUG if debug else logging.INFO

    # Configure root logger
    logging.getLogger().setLevel(level)

    # Configure specific loggers
    for logger_name in ["api", "utils"]:
        logging.getLogger(logger_name).setLevel(level)

    logger.info(f"Logging level set to: {logging.getLevelName(level)}")

def main():
    """Main entry point for the application."""
    try:
        # Parse command line arguments
        args = parse_arguments()

        # Set up logging
        setup_logging(args.debug)

        # Log startup information
        logger.info("Starting Standalone Czech Property Registry")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Current directory: {os.getcwd()}")

        # Create the application
        app = StandaloneApp()

        # Set the city if provided
        if args.city:
            logger.info(f"Setting city to: {args.city}")
            app.city_var.set(args.city)

        # Run the application
        app.run()
        return 0
    except Exception as e:
        logger.error(f"Error starting application: {e}")
        # Print the stack trace
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
