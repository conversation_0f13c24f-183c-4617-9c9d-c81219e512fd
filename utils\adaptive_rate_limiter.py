"""
Adaptive rate limiting utilities for the Czech Property Registry application.
Provides mechanisms for dynamically adjusting rate limits based on server responses.
"""

import time
import threading
import logging
import math
import statistics
from typing import Dict, List, Optional, Tuple, Any, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("AdaptiveRateLimiter")


class AdaptiveRateLimiter:
    """
    A rate limiter that dynamically adjusts rate limits based on server responses.
    Increases rate limits when requests succeed and decreases them when requests fail.
    """
    
    def __init__(self, 
                 initial_rate: float = 1.0, 
                 min_rate: float = 0.1, 
                 max_rate: float = 10.0,
                 increase_factor: float = 1.1,
                 decrease_factor: float = 0.5,
                 window_size: int = 10,
                 success_threshold: float = 0.9,
                 response_time_threshold: float = 1.0):
        """
        Initialize the adaptive rate limiter.
        
        Args:
            initial_rate (float): Initial requests per second
            min_rate (float): Minimum requests per second
            max_rate (float): Maximum requests per second
            increase_factor (float): Factor to increase rate by on success
            decrease_factor (float): Factor to decrease rate by on failure
            window_size (int): Number of requests to consider for adaptation
            success_threshold (float): Minimum success rate to increase rate
            response_time_threshold (float): Maximum response time to consider a request fast
        """
        self.initial_rate = initial_rate
        self.min_rate = min_rate
        self.max_rate = max_rate
        self.increase_factor = increase_factor
        self.decrease_factor = decrease_factor
        self.window_size = window_size
        self.success_threshold = success_threshold
        self.response_time_threshold = response_time_threshold
        
        self._rate = initial_rate
        self._last_request_time = 0.0
        self._lock = threading.Lock()
        
        # Request history
        self._request_history: List[Tuple[bool, float]] = []  # (success, response_time)
        self._history_lock = threading.Lock()
        
        # Domain-specific rates
        self._domain_rates: Dict[str, float] = {}
        self._domain_histories: Dict[str, List[Tuple[bool, float]]] = {}
        self._domain_lock = threading.Lock()
        
        # Statistics
        self._stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'rate_increases': 0,
            'rate_decreases': 0,
            'average_response_time': 0.0,
            'average_wait_time': 0.0,
            'total_wait_time': 0.0
        }
        self._stats_lock = threading.Lock()
        
        logger.info(f"AdaptiveRateLimiter initialized with rate {initial_rate} req/s")
    
    def wait(self) -> float:
        """
        Wait if necessary to comply with the rate limit.
        
        Returns:
            float: Time waited in seconds
        """
        with self._lock:
            current_time = time.time()
            time_since_last_request = current_time - self._last_request_time
            min_interval = 1.0 / self._rate
            
            wait_time = 0.0
            if time_since_last_request < min_interval:
                wait_time = min_interval - time_since_last_request
                time.sleep(wait_time)
            
            self._last_request_time = time.time()
            
            with self._stats_lock:
                self._stats['total_wait_time'] += wait_time
                self._stats['average_wait_time'] = (
                    self._stats['total_wait_time'] / self._stats['total_requests'] 
                    if self._stats['total_requests'] > 0 else 0.0
                )
            
            return wait_time
    
    def wait_for_domain(self, domain: str) -> float:
        """
        Wait if necessary to comply with the domain-specific rate limit.
        
        Args:
            domain (str): Domain name
            
        Returns:
            float: Time waited in seconds
        """
        with self._domain_lock:
            # Get or create domain-specific rate
            if domain not in self._domain_rates:
                self._domain_rates[domain] = self.initial_rate
                self._domain_histories[domain] = []
            
            rate = self._domain_rates[domain]
        
        # Calculate wait time
        current_time = time.time()
        with self._lock:
            time_since_last_request = current_time - self._last_request_time
            min_interval = 1.0 / rate
            
            wait_time = 0.0
            if time_since_last_request < min_interval:
                wait_time = min_interval - time_since_last_request
                time.sleep(wait_time)
            
            self._last_request_time = time.time()
            
            with self._stats_lock:
                self._stats['total_wait_time'] += wait_time
                self._stats['average_wait_time'] = (
                    self._stats['total_wait_time'] / self._stats['total_requests'] 
                    if self._stats['total_requests'] > 0 else 0.0
                )
            
            return wait_time
    
    def update(self, success: bool, response_time: float) -> None:
        """
        Update the rate limiter based on a request result.
        
        Args:
            success (bool): Whether the request was successful
            response_time (float): Response time in seconds
        """
        with self._history_lock:
            # Add to history
            self._request_history.append((success, response_time))
            
            # Trim history if needed
            if len(self._request_history) > self.window_size:
                self._request_history = self._request_history[-self.window_size:]
            
            # Only adapt if we have enough history
            if len(self._request_history) >= self.window_size:
                self._adapt_rate()
        
        # Update statistics
        with self._stats_lock:
            self._stats['total_requests'] += 1
            if success:
                self._stats['successful_requests'] += 1
            else:
                self._stats['failed_requests'] += 1
            
            # Update average response time
            self._stats['average_response_time'] = (
                (self._stats['average_response_time'] * (self._stats['total_requests'] - 1) + response_time) / 
                self._stats['total_requests']
            )
    
    def update_for_domain(self, domain: str, success: bool, response_time: float) -> None:
        """
        Update the domain-specific rate limiter based on a request result.
        
        Args:
            domain (str): Domain name
            success (bool): Whether the request was successful
            response_time (float): Response time in seconds
        """
        with self._domain_lock:
            # Get or create domain-specific rate and history
            if domain not in self._domain_rates:
                self._domain_rates[domain] = self.initial_rate
                self._domain_histories[domain] = []
            
            # Add to history
            self._domain_histories[domain].append((success, response_time))
            
            # Trim history if needed
            if len(self._domain_histories[domain]) > self.window_size:
                self._domain_histories[domain] = self._domain_histories[domain][-self.window_size:]
            
            # Only adapt if we have enough history
            if len(self._domain_histories[domain]) >= self.window_size:
                self._adapt_domain_rate(domain)
        
        # Also update global statistics
        self.update(success, response_time)
    
    def _adapt_rate(self) -> None:
        """Adapt the global rate based on request history."""
        # Calculate success rate
        successes = sum(1 for success, _ in self._request_history if success)
        success_rate = successes / len(self._request_history)
        
        # Calculate average response time
        response_times = [rt for _, rt in self._request_history]
        avg_response_time = sum(response_times) / len(response_times)
        
        # Determine if we should increase or decrease the rate
        with self._lock:
            old_rate = self._rate
            
            if success_rate >= self.success_threshold and avg_response_time <= self.response_time_threshold:
                # Increase rate, but don't exceed max_rate
                self._rate = min(self._rate * self.increase_factor, self.max_rate)
                
                if self._rate > old_rate:
                    with self._stats_lock:
                        self._stats['rate_increases'] += 1
                    
                    logger.debug(f"Increased rate to {self._rate:.2f} req/s (success rate: {success_rate:.2f}, avg response time: {avg_response_time:.4f}s)")
            elif success_rate < self.success_threshold or avg_response_time > self.response_time_threshold:
                # Decrease rate, but don't go below min_rate
                self._rate = max(self._rate * self.decrease_factor, self.min_rate)
                
                if self._rate < old_rate:
                    with self._stats_lock:
                        self._stats['rate_decreases'] += 1
                    
                    logger.debug(f"Decreased rate to {self._rate:.2f} req/s (success rate: {success_rate:.2f}, avg response time: {avg_response_time:.4f}s)")
    
    def _adapt_domain_rate(self, domain: str) -> None:
        """
        Adapt the domain-specific rate based on request history.
        
        Args:
            domain (str): Domain name
        """
        history = self._domain_histories[domain]
        
        # Calculate success rate
        successes = sum(1 for success, _ in history if success)
        success_rate = successes / len(history)
        
        # Calculate average response time
        response_times = [rt for _, rt in history]
        avg_response_time = sum(response_times) / len(response_times)
        
        # Determine if we should increase or decrease the rate
        old_rate = self._domain_rates[domain]
        
        if success_rate >= self.success_threshold and avg_response_time <= self.response_time_threshold:
            # Increase rate, but don't exceed max_rate
            self._domain_rates[domain] = min(old_rate * self.increase_factor, self.max_rate)
            
            if self._domain_rates[domain] > old_rate:
                logger.debug(f"Increased rate for {domain} to {self._domain_rates[domain]:.2f} req/s")
        elif success_rate < self.success_threshold or avg_response_time > self.response_time_threshold:
            # Decrease rate, but don't go below min_rate
            self._domain_rates[domain] = max(old_rate * self.decrease_factor, self.min_rate)
            
            if self._domain_rates[domain] < old_rate:
                logger.debug(f"Decreased rate for {domain} to {self._domain_rates[domain]:.2f} req/s")
    
    def get_rate(self) -> float:
        """
        Get the current global rate limit.
        
        Returns:
            float: Current rate in requests per second
        """
        with self._lock:
            return self._rate
    
    def get_domain_rate(self, domain: str) -> float:
        """
        Get the current domain-specific rate limit.
        
        Args:
            domain (str): Domain name
            
        Returns:
            float: Current rate in requests per second
        """
        with self._domain_lock:
            return self._domain_rates.get(domain, self.initial_rate)
    
    def set_rate(self, rate: float) -> None:
        """
        Set the global rate limit.
        
        Args:
            rate (float): New rate in requests per second
        """
        with self._lock:
            self._rate = max(min(rate, self.max_rate), self.min_rate)
            logger.info(f"Manually set rate to {self._rate:.2f} req/s")
    
    def set_domain_rate(self, domain: str, rate: float) -> None:
        """
        Set a domain-specific rate limit.
        
        Args:
            domain (str): Domain name
            rate (float): New rate in requests per second
        """
        with self._domain_lock:
            self._domain_rates[domain] = max(min(rate, self.max_rate), self.min_rate)
            logger.info(f"Manually set rate for {domain} to {self._domain_rates[domain]:.2f} req/s")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the rate limiter.
        
        Returns:
            Dictionary with statistics
        """
        with self._stats_lock:
            stats = self._stats.copy()
        
        with self._lock:
            stats['current_rate'] = self._rate
        
        with self._domain_lock:
            stats['domain_rates'] = self._domain_rates.copy()
        
        return stats


# Create a global instance for convenience
adaptive_rate_limiter = AdaptiveRateLimiter()
