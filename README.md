# Czech Property Registry Application

[![CI](https://github.com/petkol122/Reality/actions/workflows/ci.yml/badge.svg)](https://github.com/petkol122/Reality/actions/workflows/ci.yml)
[![codecov](https://codecov.io/gh/petkol122/Reality/branch/main/graph/badge.svg)](https://codecov.io/gh/petkol122/Reality)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

This application integrates with Czech property registry systems to retrieve owner information based on location data.

## Features

- Search for property information by address, coordinates, or location
- Batch selection of buildings to retrieve property owner information
- Integration with CUZK website (nahlizenidokn.cuzk.gov.cz)
- Support for multiple data sources (OpenStreetMap, Google Maps, Wikipedia)
- Gender determination for letter personalization
- Letter generation for property owners

## Directory Structure

```
Reality/
├── api/                  # API integrations
│   ├── cuzk/             # CUZK-specific API code
│   ├── google_maps/      # Google Maps API code
│   ├── osm/              # OpenStreetMap API code
│   └── property/         # Property search functionality
├── config/               # Configuration files
├── core/                 # Core application logic
├── data/                 # Data files
│   ├── cache/            # Cache files
│   ├── czech_data/       # Czech-specific data
│   ├── json/             # JSON data files
│   └── templates/        # HTML templates
├── docs/                 # Documentation
├── examples/             # Example data and methods
├── models/               # Data models
├── services/             # Service classes
├── tests/                # Test files
│   ├── integration/      # Integration tests
│   └── unit/             # Unit tests
├── ui/                   # User interface components
│   ├── components/       # Reusable UI components
│   ├── screens/          # Main application screens
│   └── dialogs/          # Dialog windows
├── utils/                # Utility functions and classes
│   └── simple_autocomplete.py  # Simplified autocomplete widget
├── autocomplete_app.py   # Standalone application with autocomplete
└── main.py               # Main application entry point
```

## Installation

1. Clone the repository
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
   For development, also install development dependencies:
   ```
   pip install -r requirements-dev.txt
   ```
3. Configure your API keys in `config.ini`

## Usage

### Main Application

Run the full application:

```
python main.py
```

### Standalone Application with Autocomplete

For a simplified version with just the autocomplete functionality:

```
python autocomplete_app.py
```

The standalone application provides:
- Address autocomplete using Google Maps API
- Batch search for buildings within a radius of an address
- Display of search results in a table
- Proper threading for background tasks
- Error handling and user feedback

#### API Key Setup

The standalone application requires a Google Maps API key for address autocomplete and geocoding. See [API_KEY_SETUP.md](API_KEY_SETUP.md) for instructions on how to set up your API key.

## Data Sources

The application uses the following data sources in priority order:
1. OpenStreetMap (primary)
2. Google Maps (secondary)
3. Wikipedia (tertiary)

The application implements a hybrid caching strategy:
1. Data is fetched from real APIs and stored in JSON files
2. Data is cached in memory for fast access
3. Data is refreshed on a configurable schedule
4. No fallback or demo data is used - only real data from APIs

## Configuration

The application can be configured using the `config.ini` file:

```ini
[OPENAI]
api_key = your_api_key_here

[GOOGLE_MAPS]
api_key = your_api_key_here

[SETTINGS]
default_language = cs
development_mode = false

[CACHE]
enabled = true
expiry_hours = 24
max_size_mb = 100
cleanup_on_start = true
```

## Development

### Running Tests

To run the tests:

```bash
# Run all unit tests
python tests/run_tests.py --type unit

# Run a specific test
python tests/run_tests.py --type unit --test test_region_manager

# Run with verbose output
python tests/run_tests.py --type unit --verbose
```

Alternatively, you can use pytest:

```bash
# Run all tests
pytest

# Run unit tests only
pytest tests/unit

# Run a specific test file
pytest tests/unit/test_region_manager.py
```

### Continuous Integration

This project uses GitHub Actions for continuous integration. The CI pipeline includes:

1. **Testing**: Runs the test suite on multiple Python versions and operating systems
2. **Code Coverage**: Measures test coverage and reports to Codecov
3. **Linting**: Checks code quality with flake8, black, and mypy
4. **Building**: Creates executable files for distribution

The CI workflow runs automatically on pushes to main/master branches and on pull requests.

### Code Coverage

We use pytest-cov to measure code coverage and Codecov to track coverage metrics over time. You can view the current coverage report on [Codecov](https://codecov.io/gh/petkol122/Reality).

To run tests with coverage locally:

```bash
pytest --cov=. --cov-report=html
```

This will generate an HTML coverage report in the `htmlcov` directory.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
