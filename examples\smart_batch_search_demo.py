"""
Smart Batch Search Demo

This script demonstrates the smart batch search functionality that finds all buildings
within a modifiable radius around an address, retrieves RUIAN data, and generates
URLs for owner information.

Usage:
    python examples/smart_batch_search_demo.py [address] [radius_km]

Example:
    python examples/smart_batch_search_demo.py "Prague, Czech Republic" 1.0
"""

import os
import sys
import logging
import argparse
import webbrowser
from typing import List, Dict, Any

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SmartBatchSearchDemo")

# Import required modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from api.cuzk.cuzk_integration import CUZKIntegration
from api.osm.osm_integration import OSMIntegration


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Smart Batch Search Demo')
    parser.add_argument('address', nargs='?', default="Prague, Czech Republic",
                        help='Address to search around (default: Prague, Czech Republic)')
    parser.add_argument('radius', nargs='?', type=float, default=1.0,
                        help='Search radius in kilometers (default: 1.0)')
    parser.add_argument('--max-results', type=int, default=10,
                        help='Maximum number of results to return (default: 10)')
    parser.add_argument('--property-type', default='all',
                        help='Property type to search for (default: all)')
    parser.add_argument('--open-urls', action='store_true',
                        help='Open the generated URLs in a web browser')
    return parser.parse_args()


def generate_grid_points(center_lat, center_lng, radius_meters, max_points):
    """Generate a grid of points within a radius around a center point."""
    import math

    # Convert radius from meters to degrees (approximate)
    # 1 degree of latitude is approximately 111,000 meters
    radius_lat = radius_meters / 111000
    # 1 degree of longitude varies with latitude
    radius_lng = radius_meters / (111000 * math.cos(math.radians(center_lat)))

    points = []

    # Add the center point first
    points.append({'lat': center_lat, 'lng': center_lng})

    # Generate concentric circles of points
    num_circles = min(10, max_points // 2)  # Maximum 10 circles

    for circle in range(1, num_circles + 1):
        # Calculate the radius of this circle
        circle_radius_lat = (radius_lat * circle) / num_circles
        circle_radius_lng = (radius_lng * circle) / num_circles

        # Calculate the number of points on this circle
        # More points on outer circles
        num_points_on_circle = min(max_points // num_circles, 8 * circle)

        for i in range(num_points_on_circle):
            angle = 2 * math.pi * i / num_points_on_circle

            point_lat = center_lat + circle_radius_lat * math.sin(angle)
            point_lng = center_lng + circle_radius_lng * math.cos(angle)

            points.append({'lat': point_lat, 'lng': point_lng})

            # Check if we've reached the maximum number of points
            if len(points) >= max_points:
                return points

    return points


def search_properties_at_point(lat, lng, cuzk_integration, osm_integration, property_type='all'):
    """Search for properties at a specific point."""
    try:
        # Convert WGS84 to S-JTSK
        x, y = cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)

        # Try to get property data from CUZK
        property_data = cuzk_integration.get_property_by_coordinates(x, y)

        if not property_data:
            # If no property found, try OSM
            osm_buildings = osm_integration.find_buildings_by_coordinates(lat, lng, 50)

            if osm_buildings:
                # Convert OSM buildings to property data format
                property_data = []

                for building in osm_buildings:
                    ruian_ref = building.get('ruian_ref')

                    if ruian_ref:
                        # Create property data dictionary
                        prop = {
                            'ruian_id': ruian_ref,
                            'source': 'openstreetmap',
                            'lat': building.get('lat', lat),
                            'lng': building.get('lon', lng),
                            'property_type': building['tags'].get('building', 'Building'),
                            'parcel_number': building['tags'].get('addr:conscriptionnumber', ''),
                            'cadastral_territory': building['tags'].get('addr:city', 'Location'),
                            'owner_name': 'Property Owner (view details on CUZK)',
                            'owner_address': building['tags'].get('addr:street', '') + ' ' +
                                            building['tags'].get('addr:housenumber', '') or 'Property Address',
                            'osm_id': f"{building.get('type', 'osm')}/{building.get('id', '')}"
                        }

                        # Filter by property type if specified
                        if property_type == 'all':
                            property_data.append(prop)
                        else:
                            prop_type = prop.get('property_type', '').lower()
                            if property_type.lower() in prop_type:
                                property_data.append(prop)

            return property_data

        # If we found a single property, convert it to a list
        if isinstance(property_data, dict):
            property_data = [property_data]

        # Filter by property type if specified
        if property_type != 'all':
            filtered_data = []
            for prop in property_data:
                prop_type = prop.get('property_type', '').lower()
                if property_type.lower() in prop_type:
                    filtered_data.append(prop)
            property_data = filtered_data

        return property_data

    except Exception as e:
        logger.error(f"Error searching properties at point {lat}, {lng}: {e}", exc_info=True)
        return []


def main():
    """Run the smart batch search demo."""
    args = parse_arguments()

    logger.info(f"Smart Batch Search Demo")
    logger.info(f"Address: {args.address}")
    logger.info(f"Radius: {args.radius} km")

    # Initialize components
    from unittest.mock import MagicMock

    # Initialize Google Maps integration (real)
    google_maps = GoogleMapsIntegration()

    # Initialize CUZK integration (real for coordinate conversion, mock for property data)
    cuzk_integration = CUZKIntegration()
    # Mock the get_property_by_coordinates method
    cuzk_integration.get_property_by_coordinates = MagicMock(return_value={
        'property_type': 'Building',
        'ruian_id': '12345678',
        'owner_address': 'Test Address 123, Prague',
        'owner_name': 'Test Owner',
        'lat': 50.0755,
        'lng': 14.4378,
        'cadastral_territory': 'Prague',
        'parcel_number': '123/456'
    })

    # Create a mock OSM integration
    osm_integration = MagicMock()
    osm_integration.find_buildings_by_coordinates = MagicMock(return_value=[
        {
            'type': 'way',
            'id': '123456',
            'lat': 50.0755,
            'lon': 14.4378,
            'tags': {
                'building': 'residential',
                'ref:ruian': '87654321',
                'addr:street': 'Test Street',
                'addr:housenumber': '123',
                'addr:city': 'Prague'
            },
            'ruian_ref': '87654321'
        }
    ])

    # Get coordinates for the address
    geocode_result = google_maps.geocode(args.address)

    if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
        logger.error(f"Could not geocode address: {args.address}")
        return

    lat = geocode_result['lat']
    lng = geocode_result['lng']
    location_name = geocode_result.get('formatted_address', args.address)

    logger.info(f"Geocoded {args.address} to {lat}, {lng} ({location_name})")

    # Generate a grid of points within the radius
    radius_meters = int(float(args.radius) * 1000)
    grid_points = generate_grid_points(lat, lng, radius_meters, args.max_results)
    logger.info(f"Generated {len(grid_points)} grid points within {args.radius}km radius")

    # Search for properties at each grid point
    properties = []
    processed_points = 0

    for point in grid_points:
        # Update status every 10 points
        if processed_points % 10 == 0:
            logger.info(f"Searching... ({processed_points}/{len(grid_points)} points processed)")

        # Search for properties at this point
        point_properties = search_properties_at_point(
            point['lat'], point['lng'], cuzk_integration, osm_integration, args.property_type)

        # Add any found properties to the results
        if point_properties:
            properties.extend(point_properties)

            # Check if we've reached the maximum number of results
            if len(properties) >= args.max_results:
                logger.info(f"Reached maximum number of results ({args.max_results})")
                break

        processed_points += 1

    # Generate MapaIdentifikace URLs for each property
    for prop in properties:
        if 'lat' in prop and 'lng' in prop:
            # Convert WGS84 coordinates to S-JTSK
            x, y = cuzk_integration.convert_wgs84_to_sjtsk(prop['lat'], prop['lng'])
            # Generate the MapaIdentifikace URL
            prop['url'] = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
            # Add S-JTSK coordinates to the property data
            prop['x_sjtsk'] = x
            prop['y_sjtsk'] = y

    # Display the results
    logger.info(f"Found {len(properties)} properties near {location_name}")

    for i, prop in enumerate(properties):
        logger.info(f"\n{i+1}. {prop.get('property_type', 'Building')}")
        logger.info(f"   RUIAN ID: {prop.get('ruian_id', 'Unknown')}")
        logger.info(f"   Address: {prop.get('owner_address', 'Unknown')}")
        if 'url' in prop:
            logger.info(f"   URL: {prop['url']}")

            # Open the URL in a web browser if requested
            if args.open_urls:
                logger.info(f"   Opening URL in web browser...")
                webbrowser.open(prop['url'])

    # Export the results to CSV
    if properties:
        import csv

        csv_file = f"smart_batch_search_results_{args.address.replace(' ', '_')}.csv"
        logger.info(f"\nExporting results to {csv_file}")

        with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
            # Create a CSV writer
            writer = csv.writer(csvfile)

            # Write the header row
            writer.writerow([
                "Property Type", "RUIAN ID", "Address", "URL",
                "Latitude", "Longitude", "X (S-JTSK)", "Y (S-JTSK)"
            ])

            # Write the data rows
            for prop in properties:
                writer.writerow([
                    prop.get('property_type', 'Building'),
                    prop.get('ruian_id', 'Unknown'),
                    prop.get('owner_address', 'Unknown'),
                    prop.get('url', ''),
                    prop.get('lat', ''),
                    prop.get('lng', ''),
                    prop.get('x_sjtsk', ''),
                    prop.get('y_sjtsk', '')
                ])

        logger.info(f"Results exported to {csv_file}")


if __name__ == '__main__':
    main()
