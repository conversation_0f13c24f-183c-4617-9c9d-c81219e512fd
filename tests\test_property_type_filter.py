"""
Test for property type filtering in the Czech Property Registry application.

This script tests the property type filtering functionality.
"""

import os
import sys
import logging
import tkinter as tk

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("PropertyTypeFilterTest")

# Import the necessary modules
from ui.property_type_filter_dialog import PropertyTypeFilterDialog


def test_property_type_filter_dialog():
    """Test the property type filter dialog."""
    try:
        # Create a root window
        root = tk.Tk()
        root.title("Property Type Filter Test")
        root.geometry("300x200")
        
        # Create a button to open the dialog
        def open_dialog():
            # Sample property types
            property_types = [
                "apartments", "house", "residential", "yes", "detached", 
                "commercial", "industrial", "retail", "office", "school"
            ]
            
            # Create the dialog
            filter_dialog = PropertyTypeFilterDialog(root, property_types)
            selected_types = filter_dialog.show()
            
            # Log the selected types
            logger.info(f"Selected types: {selected_types}")
            
            # Update the label
            if selected_types:
                result_label.config(text=f"Selected {len(selected_types)} types")
            else:
                result_label.config(text="No types selected")
        
        # Create a button to open the dialog
        open_button = tk.Button(root, text="Open Filter Dialog", command=open_dialog)
        open_button.pack(pady=20)
        
        # Create a label to show the result
        result_label = tk.Label(root, text="No types selected yet")
        result_label.pack(pady=10)
        
        # Start the main loop
        logger.info("Starting the test application")
        root.mainloop()
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)


if __name__ == '__main__':
    logger.info("Starting property type filter test")
    test_property_type_filter_dialog()
    logger.info("Test completed")
