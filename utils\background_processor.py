"""
Background processing utilities for the Czech Property Registry application.
Provides mechanisms for running operations in the background.
"""

import threading
import queue
import time
import logging
import uuid
import traceback
from typing import Dict, Any, Optional, List, Callable, Tuple, Set, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BackgroundProcessor")


class TaskStatus:
    """Enumeration of task statuses."""
    PENDING = 'pending'
    RUNNING = 'running'
    COMPLETED = 'completed'
    FAILED = 'failed'
    CANCELLED = 'cancelled'


class Task:
    """Represents a background task."""
    
    def __init__(self, 
                 task_id: str, 
                 func: Callable, 
                 args: Tuple = (), 
                 kwargs: Dict[str, Any] = None,
                 priority: int = 0,
                 timeout: Optional[float] = None,
                 on_complete: Optional[Callable] = None,
                 on_error: Optional[Callable] = None,
                 on_progress: Optional[Callable] = None):
        """
        Initialize a task.
        
        Args:
            task_id (str): Unique identifier for the task
            func (callable): Function to execute
            args (tuple): Positional arguments for the function
            kwargs (dict): Keyword arguments for the function
            priority (int): Task priority (lower number = higher priority)
            timeout (float, optional): Maximum execution time in seconds
            on_complete (callable, optional): Callback for task completion
            on_error (callable, optional): Callback for task error
            on_progress (callable, optional): Callback for task progress
        """
        self.task_id = task_id
        self.func = func
        self.args = args
        self.kwargs = kwargs or {}
        self.priority = priority
        self.timeout = timeout
        self.on_complete = on_complete
        self.on_error = on_error
        self.on_progress = on_progress
        
        self.status = TaskStatus.PENDING
        self.result = None
        self.error = None
        self.progress = 0.0
        self.start_time = None
        self.end_time = None
        self.thread = None
        self.cancelled = False
    
    def __lt__(self, other):
        """Compare tasks by priority for the priority queue."""
        if not isinstance(other, Task):
            return NotImplemented
        return self.priority < other.priority


class BackgroundProcessor:
    """
    Processes tasks in the background.
    Manages a pool of worker threads and a task queue.
    """
    
    def __init__(self, num_workers: int = 5, max_queue_size: int = 100):
        """
        Initialize the background processor.
        
        Args:
            num_workers (int): Number of worker threads
            max_queue_size (int): Maximum number of tasks in the queue
        """
        self.num_workers = num_workers
        self.max_queue_size = max_queue_size
        
        self._task_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self._tasks: Dict[str, Task] = {}
        self._tasks_lock = threading.Lock()
        self._workers: List[threading.Thread] = []
        self._running = False
        self._stop_event = threading.Event()
        
        # Progress reporting
        self._progress_callbacks: Dict[str, List[Callable[[str, float], None]]] = {}
        self._progress_lock = threading.Lock()
        
        # Statistics
        self._stats = {
            'tasks_submitted': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'tasks_cancelled': 0,
            'total_execution_time': 0.0,
            'average_execution_time': 0.0,
            'max_execution_time': 0.0,
            'min_execution_time': float('inf')
        }
        self._stats_lock = threading.Lock()
        
        logger.info(f"BackgroundProcessor initialized with {num_workers} workers")
    
    def start(self) -> None:
        """Start the background processor."""
        if self._running:
            return
        
        self._running = True
        self._stop_event.clear()
        
        # Start worker threads
        for i in range(self.num_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"BackgroundWorker-{i}",
                daemon=True
            )
            worker.start()
            self._workers.append(worker)
        
        logger.info(f"Started {self.num_workers} worker threads")
    
    def stop(self, wait: bool = True) -> None:
        """
        Stop the background processor.
        
        Args:
            wait (bool): Whether to wait for tasks to complete
        """
        if not self._running:
            return
        
        self._running = False
        self._stop_event.set()
        
        if wait:
            # Wait for all workers to finish
            for worker in self._workers:
                worker.join()
            
            logger.info("All worker threads have stopped")
        else:
            logger.info("Stopping worker threads (not waiting)")
        
        self._workers = []
    
    def submit(self, 
              func: Callable, 
              *args, 
              task_id: Optional[str] = None, 
              priority: int = 0, 
              timeout: Optional[float] = None, 
              on_complete: Optional[Callable] = None, 
              on_error: Optional[Callable] = None, 
              on_progress: Optional[Callable] = None, 
              **kwargs) -> str:
        """
        Submit a task for background execution.
        
        Args:
            func (callable): Function to execute
            *args: Positional arguments for the function
            task_id (str, optional): Unique identifier for the task
            priority (int): Task priority (lower number = higher priority)
            timeout (float, optional): Maximum execution time in seconds
            on_complete (callable, optional): Callback for task completion
            on_error (callable, optional): Callback for task error
            on_progress (callable, optional): Callback for task progress
            **kwargs: Keyword arguments for the function
            
        Returns:
            str: Task ID
        """
        # Generate a task ID if not provided
        if task_id is None:
            task_id = str(uuid.uuid4())
        
        # Create the task
        task = Task(
            task_id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout,
            on_complete=on_complete,
            on_error=on_error,
            on_progress=on_progress
        )
        
        # Store the task
        with self._tasks_lock:
            self._tasks[task_id] = task
        
        # Add to the queue
        self._task_queue.put((priority, task))
        
        # Update statistics
        with self._stats_lock:
            self._stats['tasks_submitted'] += 1
        
        logger.debug(f"Submitted task {task_id} with priority {priority}")
        
        # Start the processor if it's not running
        if not self._running:
            self.start()
        
        return task_id
    
    def _worker_loop(self) -> None:
        """Main loop for worker threads."""
        while self._running:
            try:
                # Get a task from the queue
                try:
                    _, task = self._task_queue.get(timeout=0.1)
                except queue.Empty:
                    continue
                
                # Check if the task has been cancelled
                if task.cancelled:
                    self._task_queue.task_done()
                    continue
                
                # Process the task
                self._process_task(task)
                
                # Mark the task as done in the queue
                self._task_queue.task_done()
            
            except Exception as e:
                logger.error(f"Error in worker thread: {e}")
                traceback.print_exc()
    
    def _process_task(self, task: Task) -> None:
        """
        Process a task.
        
        Args:
            task (Task): Task to process
        """
        # Update task status
        with self._tasks_lock:
            task.status = TaskStatus.RUNNING
            task.start_time = time.time()
            task.thread = threading.current_thread()
        
        logger.debug(f"Processing task {task.task_id}")
        
        try:
            # Set up progress reporting
            if task.on_progress:
                def progress_callback(progress: float) -> None:
                    self._update_progress(task.task_id, progress)
                
                # Add progress callback to kwargs if the function accepts it
                task.kwargs['progress_callback'] = progress_callback
            
            # Execute the task with timeout if specified
            if task.timeout:
                result = self._execute_with_timeout(task)
            else:
                result = task.func(*task.args, **task.kwargs)
            
            # Update task status
            with self._tasks_lock:
                task.status = TaskStatus.COMPLETED
                task.result = result
                task.end_time = time.time()
                
                # Update statistics
                execution_time = task.end_time - task.start_time
                with self._stats_lock:
                    self._stats['tasks_completed'] += 1
                    self._stats['total_execution_time'] += execution_time
                    self._stats['average_execution_time'] = (
                        self._stats['total_execution_time'] / self._stats['tasks_completed']
                    )
                    self._stats['max_execution_time'] = max(
                        self._stats['max_execution_time'], execution_time
                    )
                    self._stats['min_execution_time'] = min(
                        self._stats['min_execution_time'], execution_time
                    )
            
            logger.debug(f"Task {task.task_id} completed in {execution_time:.4f} seconds")
            
            # Call completion callback
            if task.on_complete:
                try:
                    task.on_complete(result)
                except Exception as e:
                    logger.error(f"Error in completion callback for task {task.task_id}: {e}")
        
        except Exception as e:
            # Update task status
            with self._tasks_lock:
                task.status = TaskStatus.FAILED
                task.error = e
                task.end_time = time.time()
                
                # Update statistics
                with self._stats_lock:
                    self._stats['tasks_failed'] += 1
            
            logger.error(f"Task {task.task_id} failed: {e}")
            traceback.print_exc()
            
            # Call error callback
            if task.on_error:
                try:
                    task.on_error(e)
                except Exception as e:
                    logger.error(f"Error in error callback for task {task.task_id}: {e}")
    
    def _execute_with_timeout(self, task: Task) -> Any:
        """
        Execute a task with a timeout.
        
        Args:
            task (Task): Task to execute
            
        Returns:
            The result of the task
            
        Raises:
            TimeoutError: If the task times out
        """
        result_queue = queue.Queue()
        exception_queue = queue.Queue()
        
        def target():
            try:
                result = task.func(*task.args, **task.kwargs)
                result_queue.put(result)
            except Exception as e:
                exception_queue.put(e)
        
        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(timeout=task.timeout)
        
        if thread.is_alive():
            raise TimeoutError(f"Task {task.task_id} timed out after {task.timeout} seconds")
        
        if not exception_queue.empty():
            raise exception_queue.get()
        
        return result_queue.get()
    
    def _update_progress(self, task_id: str, progress: float) -> None:
        """
        Update the progress of a task.
        
        Args:
            task_id (str): Task ID
            progress (float): Progress value (0.0 to 1.0)
        """
        # Update task progress
        with self._tasks_lock:
            if task_id in self._tasks:
                self._tasks[task_id].progress = progress
        
        # Call progress callbacks
        with self._progress_lock:
            for callback in self._progress_callbacks.get('task_progress', []):
                try:
                    callback(task_id, progress)
                except Exception as e:
                    logger.error(f"Error in progress callback: {e}")
        
        # Call task-specific progress callback
        with self._tasks_lock:
            if task_id in self._tasks and self._tasks[task_id].on_progress:
                try:
                    self._tasks[task_id].on_progress(progress)
                except Exception as e:
                    logger.error(f"Error in task progress callback: {e}")
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a task.
        
        Args:
            task_id (str): Task ID
            
        Returns:
            Dictionary with task information or None if not found
        """
        with self._tasks_lock:
            if task_id not in self._tasks:
                return None
            
            task = self._tasks[task_id]
            
            return {
                'task_id': task.task_id,
                'status': task.status,
                'progress': task.progress,
                'result': task.result,
                'error': str(task.error) if task.error else None,
                'start_time': task.start_time,
                'end_time': task.end_time,
                'execution_time': task.end_time - task.start_time if task.end_time else None,
                'priority': task.priority,
                'timeout': task.timeout,
                'cancelled': task.cancelled
            }
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all tasks.
        
        Returns:
            Dictionary mapping task IDs to task information
        """
        with self._tasks_lock:
            return {
                task_id: self.get_task(task_id)
                for task_id in self._tasks
            }
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a task.
        
        Args:
            task_id (str): Task ID
            
        Returns:
            bool: True if the task was cancelled, False otherwise
        """
        with self._tasks_lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            
            # If the task is already completed or failed, it can't be cancelled
            if task.status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
                return False
            
            # Mark the task as cancelled
            task.cancelled = True
            task.status = TaskStatus.CANCELLED
            
            # Update statistics
            with self._stats_lock:
                self._stats['tasks_cancelled'] += 1
            
            logger.info(f"Cancelled task {task_id}")
            
            return True
    
    def register_progress_callback(self, callback: Callable[[str, float], None]) -> None:
        """
        Register a callback for task progress updates.
        
        Args:
            callback: Function to call with (task_id, progress)
        """
        with self._progress_lock:
            if 'task_progress' not in self._progress_callbacks:
                self._progress_callbacks['task_progress'] = []
            self._progress_callbacks['task_progress'].append(callback)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the background processor.
        
        Returns:
            Dictionary with statistics
        """
        with self._stats_lock:
            stats = self._stats.copy()
        
        with self._tasks_lock:
            stats['pending_tasks'] = sum(1 for task in self._tasks.values() if task.status == TaskStatus.PENDING)
            stats['running_tasks'] = sum(1 for task in self._tasks.values() if task.status == TaskStatus.RUNNING)
            stats['completed_tasks'] = sum(1 for task in self._tasks.values() if task.status == TaskStatus.COMPLETED)
            stats['failed_tasks'] = sum(1 for task in self._tasks.values() if task.status == TaskStatus.FAILED)
            stats['cancelled_tasks'] = sum(1 for task in self._tasks.values() if task.status == TaskStatus.CANCELLED)
            stats['total_tasks'] = len(self._tasks)
        
        stats['queue_size'] = self._task_queue.qsize()
        stats['num_workers'] = len(self._workers)
        stats['running'] = self._running
        
        return stats


# Create a global instance for convenience
background_processor = BackgroundProcessor()
background_processor.start()
