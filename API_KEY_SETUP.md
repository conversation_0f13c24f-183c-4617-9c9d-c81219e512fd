# Google Maps API Key Setup

This application uses the Google Maps API for address autocomplete and geocoding. You need to set up a Google Maps API key to use these features.

## Getting a Google Maps API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Geocoding API
   - Places API
4. Create an API key and restrict it to the APIs you enabled

## Setting Up the API Key in the Application

There are three ways to provide your Google Maps API key to the application:

### Option 1: Edit the API Key File (Recommended)

1. Open the `google_maps_api_key.txt` file in the root directory of the application
2. Replace `YOUR_GOOGLE_MAPS_API_KEY_HERE` with your actual API key
3. Save the file

Example:
```
AIzaSyBhG7xkDWvDRdLXYSGMHt-JEcUQmIzPSQs
```

### Option 2: Set an Environment Variable

Set an environment variable named `GOOGLE_MAPS_API_KEY` with your API key:

On Windows:
```
set GOOGLE_MAPS_API_KEY=YOUR_API_KEY_HERE
```

On macOS/Linux:
```
export GOOGLE_MAPS_API_KEY=YOUR_API_KEY_HERE
```

### Option 3: Pass the API Key Directly in the Code

You can also modify the code to pass the API key directly to the `SimpleGoogleMaps` constructor:

```python
self.google_maps = SimpleGoogleMaps(api_key="YOUR_API_KEY_HERE")
```

## API Key Restrictions

For security reasons, it's recommended to restrict your API key:

1. In the Google Cloud Console, go to the "Credentials" page
2. Find your API key and click "Edit"
3. Under "Application restrictions", select "HTTP referrers" and add your domain
4. Under "API restrictions", select "Restrict key" and select only the APIs you need

## Troubleshooting

If you're experiencing issues with the API key:

1. Make sure the API key is correct and has no extra spaces or characters
2. Verify that the Geocoding API and Places API are enabled for your project
3. Check if your API key has any restrictions that might be preventing it from working
4. Look for error messages in the application logs

## API Usage and Billing

The Google Maps API is a paid service with a free tier. You get a certain number of free requests per month, after which you'll be charged. Make sure to set up billing in the Google Cloud Console to avoid service interruptions.

For more information, see the [Google Maps Platform Billing](https://developers.google.com/maps/billing/gmp-billing) documentation.
