"""
Test script to verify the optimizations.
This script runs a series of tests to measure the performance improvements.
"""

import time
import logging
import tkinter as tk
import threading
import os
import sys
import gc
import psutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OptimizationTest")

# Import the optimized components
from utils.enhanced_cache_manager import EnhancedCacheManager, CacheStorageType
from utils.thread_manager import ThreadManager
from utils.db_cache import DBCache
from utils.memory_manager import memory_monitor, weak_cache
from utils.lazy_loader import LazyDataLoader, lazy_property
from utils.profiler import function_profiler, Timer, MemoryProfiler
from ui.virtualized_list import VirtualizedList
from ui.virtualized_tree import VirtualizedTree


class OptimizationTest:
    """Class to test the performance optimizations."""
    
    def __init__(self):
        """Initialize the optimization test."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window
        self.root.title("Optimization Test")
        self.root.geometry("800x600")
        
        # Create a frame for the tests
        self.frame = tk.Frame(self.root)
        self.frame.pack(fill="both", expand=True)
        
        # Create a label for the results
        self.results_label = tk.Label(self.frame, text="Running tests...", justify="left", anchor="nw")
        self.results_label.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Initialize components
        self._init_components()
        
        logger.info("Optimization test initialized")
    
    def _init_components(self):
        """Initialize the components to test."""
        # Initialize the enhanced cache manager
        self.enhanced_cache = EnhancedCacheManager(
            default_expiry=300,
            disk_cache_dir="test_cache",
            max_memory_items=1000,
            cleanup_interval=300
        )
        
        # Initialize the database cache
        self.db_cache = DBCache(db_path="test_db_cache.db")
        
        # Initialize the thread manager
        self.thread_manager = ThreadManager(max_workers=5)
        
        # Initialize the lazy data loader
        self.lazy_loader = LazyDataLoader()
        
        # Initialize the memory profiler
        self.memory_profiler = MemoryProfiler()
        
        # Initialize the virtualized list
        self.virtualized_list = VirtualizedList(
            self.frame,
            item_height=30,
            buffer_items=5,
            width=400,
            height=300
        )
        
        # Initialize the virtualized tree
        self.virtualized_tree = VirtualizedTree(
            self.frame,
            item_height=30,
            buffer_items=5,
            width=400,
            height=300,
            indent_width=20
        )
        
        # Start memory monitoring
        memory_monitor.start()
    
    def run_tests(self):
        """Run all optimization tests."""
        results = {}
        
        # Run the tests
        with Timer("All Tests"):
            # Test caching
            results["cache"] = self.test_caching()
            
            # Test threading
            results["threading"] = self.test_threading()
            
            # Test memory management
            results["memory"] = self.test_memory_management()
            
            # Test UI virtualization
            results["ui"] = self.test_ui_virtualization()
        
        # Display the results
        self._display_results(results)
        
        return results
    
    def test_caching(self):
        """Test the caching optimizations."""
        logger.info("Testing caching optimizations...")
        results = {}
        
        # Test the enhanced cache manager
        with Timer("Enhanced Cache") as t1:
            # Create a test cache
            self.enhanced_cache.create_cache(
                'test_cache',
                expiry=60,
                storage_type=CacheStorageType.BOTH
            )
            
            # Write to the cache
            for i in range(1000):
                self.enhanced_cache.set('test_cache', f"key_{i}", f"value_{i}")
            
            # Read from the cache
            for i in range(1000):
                value = self.enhanced_cache.get('test_cache', f"key_{i}")
                assert value == f"value_{i}", f"Expected 'value_{i}', got '{value}'"
        
        results["enhanced_cache"] = t1.elapsed()
        
        # Test the database cache
        with Timer("Database Cache") as t2:
            # Create a test cache
            self.db_cache.create_cache('test_cache', expiry=60)
            
            # Write to the cache
            for i in range(1000):
                self.db_cache.set('test_cache', f"key_{i}", f"value_{i}")
            
            # Read from the cache
            for i in range(1000):
                value = self.db_cache.get('test_cache', f"key_{i}")
                assert value == f"value_{i}", f"Expected 'value_{i}', got '{value}'"
        
        results["db_cache"] = t2.elapsed()
        
        logger.info(f"Caching test results: Enhanced Cache: {results['enhanced_cache']:.4f}s, DB Cache: {results['db_cache']:.4f}s")
        return results
    
    def test_threading(self):
        """Test the threading optimizations."""
        logger.info("Testing threading optimizations...")
        results = {}
        
        # Define a simple task
        def task(task_id, sleep_time):
            time.sleep(sleep_time)
            return f"Task {task_id} completed after {sleep_time} seconds"
        
        # Test submitting tasks
        with Timer("Thread Submission") as t1:
            futures = []
            for i in range(100):
                future = self.thread_manager.submit(
                    f"task_{i}",
                    task,
                    f"task_{i}",
                    0.01  # 10ms sleep
                )
                futures.append((i, future))
        
        results["thread_submission"] = t1.elapsed()
        
        # Test waiting for tasks
        with Timer("Thread Waiting") as t2:
            for i, future in futures:
                result = future.result(timeout=1.0)
        
        results["thread_waiting"] = t2.elapsed()
        
        logger.info(f"Threading test results: Submission: {results['thread_submission']:.4f}s, Waiting: {results['thread_waiting']:.4f}s")
        return results
    
    def test_memory_management(self):
        """Test the memory management optimizations."""
        logger.info("Testing memory management optimizations...")
        results = {}
        
        # Test weak cache
        with Timer("Weak Cache") as t1:
            # Create some objects
            objects = [object() for _ in range(1000)]
            
            # Store them in the weak cache
            for i, obj in enumerate(objects):
                weak_cache.set(f"obj_{i}", obj)
            
            # Access them from the weak cache
            for i in range(1000):
                obj = weak_cache.get(f"obj_{i}")
                assert obj is objects[i], f"Expected {objects[i]}, got {obj}"
            
            # Clear references to some objects
            for i in range(0, 1000, 2):
                objects[i] = None
            
            # Force garbage collection
            gc.collect()
            
            # Check which objects are still in the cache
            count = 0
            for i in range(1000):
                obj = weak_cache.get(f"obj_{i}")
                if obj is not None:
                    count += 1
            
            # We expect about half of the objects to be gone
            assert count < 1000, f"Expected fewer than 1000 objects, got {count}"
        
        results["weak_cache"] = t1.elapsed()
        
        # Test memory monitoring
        with Timer("Memory Monitoring") as t2:
            # Get memory info
            memory_info = memory_monitor.get_memory_info()
            
            # Force garbage collection
            gc_stats = memory_monitor.force_garbage_collection()
        
        results["memory_monitoring"] = t2.elapsed()
        
        logger.info(f"Memory management test results: Weak Cache: {results['weak_cache']:.4f}s, Memory Monitoring: {results['memory_monitoring']:.4f}s")
        return results
    
    def test_ui_virtualization(self):
        """Test the UI virtualization optimizations."""
        logger.info("Testing UI virtualization optimizations...")
        results = {}
        
        # Test virtualized list
        with Timer("Virtualized List") as t1:
            # Set up the list renderer
            self.virtualized_list.set_item_renderer(
                create_widget_func=lambda: tk.Frame(self.virtualized_list._content_frame),
                render_func=lambda item, widget: tk.Label(widget, text=str(item)).pack()
            )
            
            # Add items to the list
            items = [f"Item {i}" for i in range(10000)]
            self.virtualized_list.set_items(items)
            
            # Scroll through the list
            for i in range(0, 10000, 100):
                self.virtualized_list.scroll_to_item(i)
        
        results["virtualized_list"] = t1.elapsed()
        
        # Test virtualized tree
        with Timer("Virtualized Tree") as t2:
            # Set up the tree renderer
            self.virtualized_tree.set_node_renderer(
                create_widget_func=lambda: tk.Frame(self.virtualized_tree._content_frame),
                render_func=lambda node, widget, level: tk.Label(widget, text=str(node.data)).pack()
            )
            
            # Add nodes to the tree
            for i in range(100):
                self.virtualized_tree.add_node(f"node_{i}", f"Node {i}")
                
                # Add child nodes
                for j in range(10):
                    child_id = f"node_{i}_{j}"
                    self.virtualized_tree.add_node(child_id, f"Child {j} of Node {i}", f"node_{i}")
                    
                    # Add grandchild nodes
                    for k in range(5):
                        grandchild_id = f"node_{i}_{j}_{k}"
                        self.virtualized_tree.add_node(grandchild_id, f"Grandchild {k} of Child {j}", child_id)
            
            # Expand all nodes
            self.virtualized_tree.expand_all()
            
            # Scroll through the tree
            for i in range(0, 100, 10):
                self.virtualized_tree.scroll_to_node(f"node_{i}")
        
        results["virtualized_tree"] = t2.elapsed()
        
        logger.info(f"UI virtualization test results: List: {results['virtualized_list']:.4f}s, Tree: {results['virtualized_tree']:.4f}s")
        return results
    
    def _display_results(self, results):
        """Display the test results."""
        # Format the results
        text = "Optimization Test Results:\n\n"
        
        # Caching results
        text += "Caching Optimizations:\n"
        text += f"  Enhanced Cache: {results['cache']['enhanced_cache']:.4f}s\n"
        text += f"  Database Cache: {results['cache']['db_cache']:.4f}s\n\n"
        
        # Threading results
        text += "Threading Optimizations:\n"
        text += f"  Thread Submission: {results['threading']['thread_submission']:.4f}s\n"
        text += f"  Thread Waiting: {results['threading']['thread_waiting']:.4f}s\n\n"
        
        # Memory management results
        text += "Memory Management Optimizations:\n"
        text += f"  Weak Cache: {results['memory']['weak_cache']:.4f}s\n"
        text += f"  Memory Monitoring: {results['memory']['memory_monitoring']:.4f}s\n\n"
        
        # UI virtualization results
        text += "UI Virtualization Optimizations:\n"
        text += f"  Virtualized List: {results['ui']['virtualized_list']:.4f}s\n"
        text += f"  Virtualized Tree: {results['ui']['virtualized_tree']:.4f}s\n"
        
        # Update the label
        self.results_label.config(text=text)
    
    def cleanup(self):
        """Clean up resources."""
        # Stop memory monitoring
        memory_monitor.stop()
        
        # Close the database cache
        self.db_cache.close()
        
        # Shut down the thread manager
        self.thread_manager.shutdown()
        
        # Destroy the window
        self.root.destroy()


if __name__ == "__main__":
    test = OptimizationTest()
    try:
        test.root.after(100, test.run_tests)
        test.root.deiconify()  # Show the window
        test.root.mainloop()
    finally:
        test.cleanup()
