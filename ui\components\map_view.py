import tkinter as tk
from tkinter import ttk, messagebox
import os
import webbrowser
import tempfile
from utils.template_loader import TemplateLoader

class MapView:
    """Class for displaying and interacting with a map"""

    def __init__(self, parent, google_maps_integration):
        """Initialize the map view"""
        self.parent = parent
        self.google_maps = google_maps_integration
        self.template_loader = TemplateLoader()

    def show_map_window(self, lat=50.0755, lng=14.4378, zoom=7):
        """Show a window with a map centered at the specified coordinates"""
        # Create a new top-level window
        self.map_window = tk.Toplevel(self.parent)
        self.map_window.title("Select Location on Map")
        self.map_window.geometry("800x600")

        # If we have a Google Maps API key, use the JavaScript API
        if self.google_maps.api_key:
            self.show_google_map(lat, lng, zoom)
        else:
            # Otherwise, show a message and a button to open in browser
            self.show_map_placeholder(lat, lng)

    def show_google_map(self, lat, lng, zoom):
        """Show a Google Map using the JavaScript API"""
        # Create a frame for the map
        map_frame = ttk.Frame(self.map_window)
        map_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create HTML file with Google Maps
        html = self.create_map_html(lat, lng, zoom)

        # Create a temporary HTML file
        fd, path = tempfile.mkstemp(suffix=".html")
        with os.fdopen(fd, 'w') as f:
            f.write(html)

        # Open the HTML file in the default browser
        webbrowser.open('file://' + path)

        # Add a message to the window
        ttk.Label(map_frame, text="Map opened in your browser. Click on the map to select a location.").pack(pady=20)

        # Add a button to close the window
        ttk.Button(map_frame, text="Close", command=self.map_window.destroy).pack(pady=10)

    def show_map_placeholder(self, lat, lng):
        """Show a placeholder with instructions to set up Google Maps API"""
        # Create a frame for the placeholder
        placeholder_frame = ttk.Frame(self.map_window)
        placeholder_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Add a message
        ttk.Label(placeholder_frame, text="Google Maps API key is not working or not provided.").pack(pady=10)
        ttk.Label(placeholder_frame, text="We'll open Google Maps in your browser instead.").pack(pady=5)

        # Add coordinates input fields
        coords_frame = ttk.Frame(placeholder_frame)
        coords_frame.pack(pady=10)

        ttk.Label(coords_frame, text="Latitude:").grid(row=0, column=0, padx=5, pady=5)
        lat_entry = ttk.Entry(coords_frame, width=15)
        lat_entry.grid(row=0, column=1, padx=5, pady=5)
        lat_entry.insert(0, str(lat))

        ttk.Label(coords_frame, text="Longitude:").grid(row=1, column=0, padx=5, pady=5)
        lng_entry = ttk.Entry(coords_frame, width=15)
        lng_entry.grid(row=1, column=1, padx=5, pady=5)
        lng_entry.insert(0, str(lng))

        # Add a button to open Google Maps in browser with the entered coordinates
        ttk.Button(
            placeholder_frame,
            text="Open Google Maps with These Coordinates",
            command=lambda: self.google_maps.open_map_in_browser(
                float(lat_entry.get()),
                float(lng_entry.get())
            )
        ).pack(pady=10)

        # Add a button to open Google Maps for Czech Republic
        ttk.Button(
            placeholder_frame,
            text="Open Google Maps for Czech Republic",
            command=lambda: self.google_maps.open_map_in_browser()
        ).pack(pady=5)

        # Add a button to close the window
        ttk.Button(placeholder_frame, text="Close", command=self.map_window.destroy).pack(pady=10)

        # Add a link to get a Google Maps API key
        ttk.Label(placeholder_frame, text="Get a Google Maps API key:").pack(pady=10)
        link_label = ttk.Label(placeholder_frame, text="https://developers.google.com/maps/documentation/javascript/get-api-key", foreground="blue", cursor="hand2")
        link_label.pack(pady=2)
        link_label.bind("<Button-1>", lambda e: webbrowser.open("https://developers.google.com/maps/documentation/javascript/get-api-key"))

        # Automatically open Google Maps in browser
        self.google_maps.open_map_in_browser(lat, lng)

    def show_location(self, location):
        """Show a location on the map

        Args:
            location (str): The location to show on the map
        """
        # Use the Google Maps integration to geocode the location
        if self.google_maps:
            # Geocode the location to get coordinates
            result = self.google_maps.geocode(location)
            if result:
                lat = result.get('lat')
                lng = result.get('lng')
                # Show the map window with the coordinates
                self.show_map_window(lat, lng, zoom=15)
            else:
                # If geocoding failed, show an error message
                messagebox.showerror("Error", f"Could not find coordinates for {location}")
        else:
            # If Google Maps integration is not available, show an error message
            messagebox.showerror("Error", "Google Maps integration is not available")

    def create_map_html(self, lat, lng, zoom):
        """Create HTML with Google Maps"""
        try:
            # Use the template to create the HTML
            html = self.template_loader.format_template(
                'location_selection_map',
                lat=lat,
                lng=lng,
                zoom=zoom,
                api_key=self.google_maps.api_key
            )
        except Exception as e:
            print(f"Error loading location_selection_map template: {e}")
            # Fallback to inline HTML
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Select Location</title>
                <meta name="viewport" content="initial-scale=1.0">
                <meta charset="utf-8">
                <style>
                    #map {{
                        height: 100%;
                    }}
                    html, body {{
                        height: 100%;
                        margin: 0;
                        padding: 0;
                    }}
                    #info {{
                        position: absolute;
                        bottom: 20px;
                        left: 20px;
                        background-color: white;
                        padding: 10px;
                        border-radius: 5px;
                        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                        z-index: 1;
                    }}
                </style>
            </head>
            <body>
                <div id="map"></div>
                <div id="info">
                    <h3>Selected Location</h3>
                    <p>Click on the map to select a location</p>
                    <p id="coordinates">Lat: {lat}, Lng: {lng}</p>
                    <p id="address">Address: Loading...</p>
                    <button id="selectButton" onclick="selectLocation()">Select This Location</button>
                </div>

                <script>
                    var map;
                    var marker;
                    var selectedLat = {lat};
                    var selectedLng = {lng};
                    var selectedAddress = "";

                    function initMap() {{
                        // Simple map implementation - similar to the working HTML file
                        map = new google.maps.Map(document.getElementById('map'), {{
                            center: {{lat: {lat}, lng: {lng}}},
                            zoom: {zoom}
                        }});

                        // Add a marker at the initial position
                        marker = new google.maps.Marker({{
                            position: {{lat: {lat}, lng: {lng}}},
                            map: map,
                            draggable: true,
                            title: 'Drag me!'
                        }});

                        // Add click event to the map
                        map.addListener('click', function(event) {{
                            marker.setPosition(event.latLng);
                            selectedLat = event.latLng.lat();
                            selectedLng = event.latLng.lng();
                            document.getElementById('coordinates').textContent = 'Lat: ' + selectedLat.toFixed(6) + ', Lng: ' + selectedLng.toFixed(6);
                            document.getElementById('address').textContent = 'Address: Updating...';

                            // Try to get the address if Geocoder is available
                            try {{
                                if (google.maps.Geocoder) {{
                                    var geocoder = new google.maps.Geocoder();
                                    var latlng = {{lat: selectedLat, lng: selectedLng}};

                                    geocoder.geocode({{'location': latlng}}, function(results, status) {{
                                        if (status === 'OK') {{
                                            if (results[0]) {{
                                                selectedAddress = results[0].formatted_address;
                                                document.getElementById('address').textContent = 'Address: ' + selectedAddress;
                                            }} else {{
                                                document.getElementById('address').textContent = 'Address: No results found';
                                            }}
                                        }} else {{
                                            document.getElementById('address').textContent = 'Address: Geocoder failed due to: ' + status;
                                        }}
                                    }});
                                }}
                            }} catch(e) {{
                                console.error('Geocoding error:', e);
                                document.getElementById('address').textContent = 'Address: Geocoding not available';
                            }}
                        }});

                        // Add drag end event to the marker with similar geocoding logic
                        marker.addListener('dragend', function() {{
                            var position = marker.getPosition();
                            selectedLat = position.lat();
                            selectedLng = position.lng();
                            document.getElementById('coordinates').textContent = 'Lat: ' + selectedLat.toFixed(6) + ', Lng: ' + selectedLng.toFixed(6);
                            document.getElementById('address').textContent = 'Address: Updating...';

                            try {{
                                if (google.maps.Geocoder) {{
                                    var geocoder = new google.maps.Geocoder();
                                    var latlng = {{lat: selectedLat, lng: selectedLng}};

                                    geocoder.geocode({{'location': latlng}}, function(results, status) {{
                                        if (status === 'OK') {{
                                            if (results[0]) {{
                                                selectedAddress = results[0].formatted_address;
                                                document.getElementById('address').textContent = 'Address: ' + selectedAddress;
                                            }} else {{
                                                document.getElementById('address').textContent = 'Address: No results found';
                                            }}
                                        }} else {{
                                            document.getElementById('address').textContent = 'Address: Geocoder failed due to: ' + status;
                                        }}
                                    }});
                                }}
                            }} catch(e) {{
                                console.error('Geocoding error:', e);
                                document.getElementById('address').textContent = 'Address: Geocoding not available';
                            }}
                        }});
                    }}

                    function selectLocation() {{
                        // Save the selected location to localStorage
                        localStorage.setItem('selectedLat', selectedLat);
                        localStorage.setItem('selectedLng', selectedLng);
                        localStorage.setItem('selectedAddress', selectedAddress);

                        // Alert the user
                        alert('Location selected: ' + selectedAddress);
                    }}
                </script>
                <!-- Using only the Maps JavaScript API without additional libraries -->
                <script src="https://maps.googleapis.com/maps/api/js?key={self.google_maps.api_key}&callback=initMap" async defer></script>
            </body>
            </html>
            """
        return html
