"""
Implementation of the BatchSearchManagerBase class for the Czech Property Registry application.

This module provides a concrete implementation of the BatchSearchManagerBase class
with methods for batch searching properties by address, coordinates, or city.
"""

import threading
import time
import logging
import webbrowser
import math
import tkinter as tk
from typing import List, Dict, Any, Optional, Callable, Tuple, Union

from ui.message_boxes import MessageBoxes
from core.batch_search.batch_search_manager_base import BatchSearchManagerBase
from utils.helpers.coordinate_helpers import generate_grid_points, generate_spiral_points

# Set up logging
logger = logging.getLogger(__name__)


class BatchSearchManagerImpl(BatchSearchManagerBase):
    """
    Implementation of the BatchSearchManagerBase class.

    Provides concrete implementations of batch search methods for searching
    properties by address, coordinates, or city.
    """

    def __init__(self, app):
        """
        Initialize the batch search manager implementation.

        Args:
            app: The main application instance
        """
        super().__init__(app)

    def _batch_search_thread(self, address, property_types, radius_meters, max_results, callback):
        """
        Background thread for batch search by address.

        Args:
            address (str): Full address to search in
            property_types (list): List of property types to include
            radius_meters (int): Search radius in meters
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
        """
        try:
            # Get coordinates for the address using Mapy.cz
            if hasattr(self.app, 'mapycz_integration'):
                logger.info(f"Geocoding address with Mapy.cz: {address}")
                result = self.app.mapycz_integration.get_coordinates(address)
            else:
                # Fall back to Google Maps if Mapy.cz is not available
                logger.info(f"Mapy.cz not available, falling back to Google Maps for geocoding: {address}")
                result = self.app.google_maps.geocode(address)

            if not result or 'lat' not in result or 'lng' not in result:
                logger.error(f"Could not geocode address: {address}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "Geocoding Error", f"Could not find coordinates for address: {address}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            lat = result['lat']
            lng = result['lng']
            formatted_address = result.get('formatted_address', address)

            # Fetch properties in the area
            properties = self.fetch_properties_in_area(lat, lng, radius_meters, max_results, property_types)

            # Update the UI in the main thread
            self.app.root.after(0, lambda: self.display_batch_results(properties, formatted_address))

            # Call the callback if provided
            if callback:
                self.app.root.after(0, lambda: callback(properties))

            # Reset search flag
            self.search_in_progress = False

        except Exception as e:
            logger.error(f"Error in batch search thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"An error occurred: {str(e)}"))
            self.app.root.after(0, lambda: self.app.show_status("Ready"))
            self.search_in_progress = False

    def _batch_search_by_coordinates_thread(self, lat, lng, property_types, radius_meters, max_results, callback):
        """
        Background thread for batch search by coordinates.

        Args:
            lat (float): Latitude
            lng (float): Longitude
            property_types (list): List of property types to include
            radius_meters (int): Search radius in meters
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
        """
        try:
            # Fetch properties in the area
            properties = self.fetch_properties_in_area(lat, lng, radius_meters, max_results, property_types)

            # Update the UI in the main thread
            location_name = f"Coordinates ({lat:.6f}, {lng:.6f})"
            self.app.root.after(0, lambda: self.display_batch_results(properties, location_name))

            # Call the callback if provided
            if callback:
                self.app.root.after(0, lambda: callback(properties))

            # Reset search flag
            self.search_in_progress = False

        except Exception as e:
            logger.error(f"Error in batch search by coordinates thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"An error occurred: {str(e)}"))
            self.app.root.after(0, lambda: self.app.show_status("Ready"))
            self.search_in_progress = False

    def _batch_search_by_city_thread(self, city, property_types, max_results, callback, use_mapycz=True):
        """
        Background thread for batch search by city.

        Args:
            city (str): City name
            property_types (list): List of property types to include
            max_results (int): Maximum number of results to return
            callback (callable): Function to call with the results
            use_mapycz (bool): Whether to use Mapy.cz for city boundaries
        """
        try:
            # Get city boundaries from either Mapy.cz or Google Maps
            if use_mapycz and hasattr(self.app, 'mapycz_integration'):
                logger.info(f"Getting city boundaries for {city} from Mapy.cz")
                self.app.show_status(f"Getting city boundaries for {city} from Mapy.cz...")

                # Get city boundaries from Mapy.cz
                boundaries = self.app.mapycz_integration.get_city_boundaries(city)

                if boundaries:
                    # Convert to format expected by fetch_properties_in_city
                    mapycz_boundaries = {
                        'center': {
                            'lat': (boundaries['north'] + boundaries['south']) / 2,
                            'lng': (boundaries['east'] + boundaries['west']) / 2
                        },
                        'viewport': {
                            'northeast': {
                                'lat': boundaries['north'],
                                'lng': boundaries['east']
                            },
                            'southwest': {
                                'lat': boundaries['south'],
                                'lng': boundaries['west']
                            }
                        }
                    }

                    # Generate coordinates within the city
                    coordinates = self.app.mapycz_integration.generate_coordinates_in_city(boundaries)

                    if coordinates:
                        logger.info(f"Generated {len(coordinates)} coordinates within {city} using Mapy.cz")

                        # Fetch properties using the coordinates
                        properties = self.fetch_properties_from_coordinates(coordinates, max_results, property_types)

                        # Update the UI in the main thread
                        self.app.root.after(0, lambda: self.display_batch_results(properties, city))

                        # Call the callback if provided
                        if callback:
                            self.app.root.after(0, lambda: callback(properties))

                        # Reset search flag
                        self.search_in_progress = False
                        return
                    else:
                        logger.warning(f"Could not generate coordinates for {city} using Mapy.cz, falling back to Google Maps")
                else:
                    logger.warning(f"Could not get boundaries for {city} from Mapy.cz, falling back to Google Maps")

            # Fall back to Google Maps if Mapy.cz failed or was not requested
            logger.info(f"Getting city boundaries for {city} from Google Maps")
            self.app.show_status(f"Getting city boundaries for {city} from Google Maps...")

            # Get city boundaries from Google Maps
            boundaries = self.app.google_maps.get_city_boundaries(city)

            if not boundaries:
                logger.error(f"Could not find boundaries for city: {city}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "City Error", f"Could not find boundaries for city: {city}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            # Get the center of the city
            center = boundaries.get('center', {})
            lat = center.get('lat')
            lng = center.get('lng')

            if not lat or not lng:
                logger.error(f"Could not find center coordinates for city: {city}")
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "City Error", f"Could not find center coordinates for city: {city}"))
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.search_in_progress = False
                return

            # Fetch properties in the city area
            properties = self.fetch_properties_in_city(city, boundaries, max_results, property_types)

            # Update the UI in the main thread
            self.app.root.after(0, lambda: self.display_batch_results(properties, city))

            # Call the callback if provided
            if callback:
                self.app.root.after(0, lambda: callback(properties))

            # Reset search flag
            self.search_in_progress = False

        except Exception as e:
            logger.error(f"Error in batch search by city thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"An error occurred: {str(e)}"))
            self.app.root.after(0, lambda: self.app.show_status("Ready"))
            self.search_in_progress = False

    def fetch_properties_in_area(self, lat, lng, radius_meters, max_results=50, property_types=None):
        """
        Fetch properties in the specified area.

        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius_meters (int): Radius in meters
            max_results (int): Maximum number of results to return
            property_types (list, optional): List of property types to include

        Returns:
            list: List of property data dictionaries
        """
        try:
            logger.info(f"Fetching properties in area: {lat}, {lng}, radius: {radius_meters}m")
            self.app.show_status(f"Fetching properties in area: {lat}, {lng}, radius: {radius_meters}m")

            # Generate points in the area
            radius_km = radius_meters / 1000  # Convert to kilometers
            points_data = generate_grid_points(lat, lng, radius_km, grid_size=int(math.sqrt(max_results)))

            # Extract just the coordinates
            points = [(p['lat'], p['lng']) for p in points_data]

            # Limit to max_results
            if len(points) > max_results:
                import random
                # Randomly sample points to reduce to max_results
                points = random.sample(points, max_results)

            logger.info(f"Generated {len(points)} points in the area")

            # Fetch property data for each point
            properties = []
            for i, (point_lat, point_lng) in enumerate(points):
                try:
                    # Update status
                    self.app.show_status(f"Fetching property data for point {i+1}/{len(points)}")

                    # Check if we have RUIAN integration
                    if hasattr(self.app, 'ruian_integration'):
                        # Get RUIAN data for this point
                        ruian_data = self.app.ruian_integration.get_ruian_data_by_coordinates(point_lat, point_lng)

                        if ruian_data:
                            # Extract property information
                            property_data = {
                                'lat': point_lat,
                                'lng': point_lng,
                                'property_type': ruian_data.get('property_type', 'Unknown'),
                                'address': ruian_data.get('address', 'Unknown'),
                                'owner_name': ruian_data.get('owner_name', 'Unknown'),
                                'owner_address': ruian_data.get('owner_address', 'Unknown'),
                                'ruian_id': ruian_data.get('ruian_id', 'Unknown'),
                                'source': 'RUIAN'
                            }

                            # Add to properties list
                            properties.append(property_data)
                    else:
                        # Fallback to OSM if available
                        if hasattr(self.app, 'osm_integration'):
                            # Get OSM data for this point
                            osm_data = self.app.osm_integration.get_osm_data_by_coordinates(point_lat, point_lng)

                            if osm_data:
                                # Extract property information
                                property_data = {
                                    'lat': point_lat,
                                    'lng': point_lng,
                                    'property_type': osm_data.get('property_type', 'Unknown'),
                                    'address': osm_data.get('address', 'Unknown'),
                                    'owner_name': 'Unknown',  # OSM doesn't have owner information
                                    'owner_address': 'Unknown',  # OSM doesn't have owner information
                                    'ruian_id': 'Unknown',  # OSM doesn't have RUIAN IDs
                                    'source': 'OSM'
                                }

                                # Add to properties list
                                properties.append(property_data)
                        else:
                            # No property data available, just add the coordinates
                            property_data = {
                                'lat': point_lat,
                                'lng': point_lng,
                                'property_type': 'Unknown',
                                'address': 'Unknown',
                                'owner_name': 'Unknown',
                                'owner_address': 'Unknown',
                                'ruian_id': 'Unknown',
                                'source': 'Coordinates'
                            }

                            # Add to properties list
                            properties.append(property_data)

                except Exception as e:
                    logger.error(f"Error fetching property data for point {i+1}: {e}")
                    # Continue with the next point

            logger.info(f"Fetched {len(properties)} properties in the area")
            self.app.show_status(f"Fetched {len(properties)} properties in the area")

            return properties

        except Exception as e:
            logger.error(f"Error fetching properties in area: {e}", exc_info=True)
            self.app.show_status("Error fetching properties")
            return []

    def fetch_properties_from_coordinates(self, coordinates, max_results=100, property_types=None):
        """
        Fetch properties from a list of coordinates.

        Args:
            coordinates (list): List of dictionaries with lat and lng keys
            max_results (int): Maximum number of results to return
            property_types (list, optional): List of property types to include

        Returns:
            list: List of property data dictionaries
        """
        try:
            logger.info(f"Fetching properties from {len(coordinates)} coordinates")
            self.app.show_status(f"Fetching properties from {len(coordinates)} coordinates")

            # Limit to max_results
            if len(coordinates) > max_results:
                import random
                # Randomly sample coordinates to reduce to max_results
                coordinates = random.sample(coordinates, max_results)

            logger.info(f"Using {len(coordinates)} coordinates after limiting to max_results")

            # Fetch property data for each coordinate
            properties = []
            for i, coord in enumerate(coordinates):
                try:
                    # Extract coordinates
                    point_lat = coord['lat']
                    point_lng = coord['lng']

                    # Update status
                    self.app.show_status(f"Fetching property data for point {i+1}/{len(coordinates)}")

                    # Check if we have RUIAN integration
                    if hasattr(self.app, 'ruian_integration'):
                        # Get RUIAN data for this point
                        ruian_data = self.app.ruian_integration.get_ruian_data_by_coordinates(point_lat, point_lng)

                        if ruian_data:
                            # Extract property information
                            property_data = {
                                'lat': point_lat,
                                'lng': point_lng,
                                'property_type': ruian_data.get('property_type', 'Unknown'),
                                'address': ruian_data.get('address', 'Unknown'),
                                'owner_name': ruian_data.get('owner_name', 'Unknown'),
                                'owner_address': ruian_data.get('owner_address', 'Unknown'),
                                'ruian_id': ruian_data.get('ruian_id', 'Unknown'),
                                'source': 'RUIAN'
                            }

                            # Add to properties list
                            properties.append(property_data)
                    else:
                        # Fallback to OSM if available
                        if hasattr(self.app, 'osm_integration'):
                            # Get OSM data for this point
                            osm_data = self.app.osm_integration.get_osm_data_by_coordinates(point_lat, point_lng)

                            if osm_data:
                                # Extract property information
                                property_data = {
                                    'lat': point_lat,
                                    'lng': point_lng,
                                    'property_type': osm_data.get('property_type', 'Unknown'),
                                    'address': osm_data.get('address', 'Unknown'),
                                    'owner_name': 'Unknown',  # OSM doesn't have owner information
                                    'owner_address': 'Unknown',  # OSM doesn't have owner information
                                    'ruian_id': 'Unknown',  # OSM doesn't have RUIAN IDs
                                    'source': 'OSM'
                                }

                                # Add to properties list
                                properties.append(property_data)
                        else:
                            # No property data available, just add the coordinates
                            property_data = {
                                'lat': point_lat,
                                'lng': point_lng,
                                'property_type': 'Unknown',
                                'address': 'Unknown',
                                'owner_name': 'Unknown',
                                'owner_address': 'Unknown',
                                'ruian_id': 'Unknown',
                                'source': 'Coordinates'
                            }

                            # Add to properties list
                            properties.append(property_data)

                except Exception as e:
                    logger.error(f"Error fetching property data for point {i+1}: {e}")
                    # Continue with the next point

            logger.info(f"Fetched {len(properties)} properties from coordinates")
            self.app.show_status(f"Fetched {len(properties)} properties from coordinates")

            return properties

        except Exception as e:
            logger.error(f"Error fetching properties from coordinates: {e}", exc_info=True)
            self.app.show_status("Error fetching properties")
            return []

    def fetch_properties_in_city(self, city, boundaries, max_results=100, property_types=None):
        """
        Fetch properties within a city's boundaries.

        Args:
            city (str): City name
            boundaries (dict): City boundaries from Google Maps
            max_results (int): Maximum number of results to return
            property_types (list, optional): List of property types to include

        Returns:
            list: List of property data dictionaries
        """
        try:
            logger.info(f"Fetching properties in city: {city}")
            self.app.show_status(f"Fetching properties in city: {city}")

            # Get the center of the city
            center = boundaries.get('center', {})
            lat = center.get('lat')
            lng = center.get('lng')

            # Get the viewport of the city
            viewport = boundaries.get('viewport', {})
            northeast = viewport.get('northeast', {})
            southwest = viewport.get('southwest', {})

            # Calculate the radius of the city (approximate)
            from utils.helpers.coordinate_helpers import haversine_distance
            radius_km = haversine_distance(
                northeast.get('lat', lat),
                northeast.get('lng', lng),
                southwest.get('lat', lat),
                southwest.get('lng', lng)
            ) / 2

            logger.info(f"City radius: {radius_km}km")

            # Generate points within the city
            points_data = generate_spiral_points(lat, lng, radius_km, num_points=max_results)

            # Extract just the coordinates
            points = [(p['lat'], p['lng']) for p in points_data]

            # Limit to max_results
            if len(points) > max_results:
                import random
                # Randomly sample points to reduce to max_results
                points = random.sample(points, max_results)

            logger.info(f"Generated {len(points)} points in the city")

            # Fetch property data for each point
            properties = []
            for i, (point_lat, point_lng) in enumerate(points):
                try:
                    # Update status
                    self.app.show_status(f"Fetching property data for point {i+1}/{len(points)}")

                    # Check if we have RUIAN integration
                    if hasattr(self.app, 'ruian_integration'):
                        # Get RUIAN data for this point
                        ruian_data = self.app.ruian_integration.get_ruian_data_by_coordinates(point_lat, point_lng)

                        if ruian_data:
                            # Extract property information
                            property_data = {
                                'lat': point_lat,
                                'lng': point_lng,
                                'property_type': ruian_data.get('property_type', 'Unknown'),
                                'address': ruian_data.get('address', 'Unknown'),
                                'owner_name': ruian_data.get('owner_name', 'Unknown'),
                                'owner_address': ruian_data.get('owner_address', 'Unknown'),
                                'ruian_id': ruian_data.get('ruian_id', 'Unknown'),
                                'source': 'RUIAN'
                            }

                            # Add to properties list
                            properties.append(property_data)
                    else:
                        # Fallback to OSM if available
                        if hasattr(self.app, 'osm_integration'):
                            # Get OSM data for this point
                            osm_data = self.app.osm_integration.get_osm_data_by_coordinates(point_lat, point_lng)

                            if osm_data:
                                # Extract property information
                                property_data = {
                                    'lat': point_lat,
                                    'lng': point_lng,
                                    'property_type': osm_data.get('property_type', 'Unknown'),
                                    'address': osm_data.get('address', 'Unknown'),
                                    'owner_name': 'Unknown',  # OSM doesn't have owner information
                                    'owner_address': 'Unknown',  # OSM doesn't have owner information
                                    'ruian_id': 'Unknown',  # OSM doesn't have RUIAN IDs
                                    'source': 'OSM'
                                }

                                # Add to properties list
                                properties.append(property_data)
                        else:
                            # No property data available, just add the coordinates
                            property_data = {
                                'lat': point_lat,
                                'lng': point_lng,
                                'property_type': 'Unknown',
                                'address': 'Unknown',
                                'owner_name': 'Unknown',
                                'owner_address': 'Unknown',
                                'ruian_id': 'Unknown',
                                'source': 'Coordinates'
                            }

                            # Add to properties list
                            properties.append(property_data)

                except Exception as e:
                    logger.error(f"Error fetching property data for point {i+1}: {e}")
                    # Continue with the next point

            logger.info(f"Fetched {len(properties)} properties in the city")
            self.app.show_status(f"Fetched {len(properties)} properties in the city")

            return properties

        except Exception as e:
            logger.error(f"Error fetching properties in city: {e}", exc_info=True)
            self.app.show_status("Error fetching properties")
            return []

    def display_batch_results(self, properties, location_name):
        """
        Display the batch search results in the UI.

        Args:
            properties (list): List of property data dictionaries
            location_name (str): Name of the location for display purposes
        """
        try:
            # Check if we have a batch results display method in the app
            if hasattr(self.app, 'display_batch_results'):
                # Use the app's method
                self.app.display_batch_results(properties, location_name)
                return

            # Check if we have a batch results text widget
            if hasattr(self.app, 'batch_results_text'):
                # Update the batch results text
                self.app.batch_results_text.config(state=tk.NORMAL)
                self.app.batch_results_text.delete(1.0, tk.END)
                self.app.batch_results_text.insert(tk.END, f"Found {len(properties)} properties in {location_name}\n\n")

                # Process each property
                for i, prop in enumerate(properties):
                    # Extract property information
                    owner_name = prop.get('owner_name', 'Unknown')
                    owner_address = prop.get('owner_address', 'Unknown')
                    property_type = prop.get('property_type', 'Unknown')
                    source = prop.get('source', 'Unknown')
                    url = prop.get('url', '')

                    # Add to results
                    result_text = f"Property {i+1}:\n"
                    result_text += f"Type: {property_type}\n"
                    result_text += f"Owner: {owner_name}\n"
                    result_text += f"Address: {owner_address}\n"
                    result_text += f"Source: {source}\n"

                    # Add URL if available
                    if url:
                        result_text += f"URL: {url}\n"

                    result_text += "\n"

                    # Add to text widget
                    self.app.batch_results_text.insert(tk.END, result_text)

                # Make the text widget read-only
                self.app.batch_results_text.config(state=tk.DISABLED)

            # Show status
            self.app.show_status(f"Found {len(properties)} properties in {location_name}")

        except Exception as e:
            logger.error(f"Error displaying batch results: {e}", exc_info=True)
            self.app.show_status("Error displaying batch results")

            # Show error message
            MessageBoxes.show_error("Error", f"Error displaying batch results: {str(e)}")
