"""
Template loader utility for the Czech Property Scraper application.
Handles loading and formatting HTML templates.
"""

import os
import json


class TemplateLoader:
    """Utility class for loading and formatting HTML templates."""
    
    def __init__(self, template_dir='templates'):
        """
        Initialize the template loader.
        
        Args:
            template_dir (str): Directory containing the templates
        """
        self.template_dir = template_dir
        self.templates = {}
    
    def load_template(self, template_name):
        """
        Load a template from the template directory.
        
        Args:
            template_name (str): Name of the template file (without .html extension)
            
        Returns:
            str: The template content
        """
        # Check if the template is already loaded
        if template_name in self.templates:
            return self.templates[template_name]
        
        # Load the template
        template_path = os.path.join(self.template_dir, f"{template_name}.html")
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template = f.read()
            
            # Cache the template
            self.templates[template_name] = template
            
            return template
        except Exception as e:
            print(f"Error loading template {template_name}: {e}")
            return ""
    
    def format_template(self, template_name, **kwargs):
        """
        Format a template with the given parameters.
        
        Args:
            template_name (str): Name of the template file (without .html extension)
            **kwargs: Parameters to format the template with
            
        Returns:
            str: The formatted template
        """
        template = self.load_template(template_name)
        
        # Handle special cases for JSON data
        for key, value in kwargs.items():
            if isinstance(value, (list, dict)):
                kwargs[key] = json.dumps(value)
        
        # Format the template
        try:
            return template.format(**kwargs)
        except KeyError as e:
            print(f"Missing parameter in template {template_name}: {e}")
            return template
        except Exception as e:
            print(f"Error formatting template {template_name}: {e}")
            return template
