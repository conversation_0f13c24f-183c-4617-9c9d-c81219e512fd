"""
Test for CUZK URL generation from random addresses.

This test verifies that the application can:
1. Take a random address from Google Maps
2. Use a random radius
3. Check coordinates with RUIAN
4. Generate valid MapaIdentifikace URLs for CUZK website
"""

import os
import sys
import unittest
import logging
import random
import webbrowser
import tkinter as tk
from unittest.mock import MagicMock, patch

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CUZKUrlGenerationTest")

# Import required modules
from core.smart_batch_search_manager import SmartBatchSearchManager
from api.cuzk.cuzk_integration import CUZKIntegration
from api.google_maps.google_maps_integration import GoogleMapsIntegration


class TestCUZKUrlGeneration(unittest.TestCase):
    """Tests for CUZK URL generation from random addresses."""

    def setUp(self):
        """Initialize test data before each test."""
        # Czech addresses for geocoding tests
        self.czech_addresses = [
            "Václavské náměstí, Praha",
            "Staroměstské náměstí, Praha",
            "Karlův most, Praha",
            "Pražský hrad, Praha",
            "Brno, Czech Republic",
            "Ostrava, Czech Republic",
            "Plzeň, Czech Republic",
            "Liberec, Czech Republic",
            "Olomouc, Czech Republic",
            "České Budějovice, Czech Republic"
        ]

    @patch('webbrowser.open')
    def test_url_generation_from_random_address(self, mock_webbrowser_open):
        """Test generating CUZK URLs from a random address with a random radius."""
        logger.info("Testing CUZK URL generation from random address")

        # Generate test parameters
        random_address = random.choice(self.czech_addresses)
        random_radius = round(random.uniform(0.5, 5.0), 1)  # Between 0.5-5.0 km
        logger.info(f"Using random address: {random_address}, radius: {random_radius}km")

        # Create real instances of required components
        cuzk_integration = CUZKIntegration()
        
        # Create a mock app with real components
        app = MagicMock()
        app.root = MagicMock(spec=tk.Tk)
        app.cuzk_integration = cuzk_integration
        app.root.after.side_effect = lambda delay, callback, *args: callback(*args) if args else callback()
        
        # Use a real Google Maps integration if API key is available
        try:
            app.google_maps = GoogleMapsIntegration()
            # Test if geocoding works
            test_result = app.google_maps.geocode("Prague")
            if not test_result or 'lat' not in test_result:
                # Fall back to mock if API key is not available or not working
                raise ValueError("Google Maps API not working")
        except Exception as e:
            logger.warning(f"Using mock Google Maps due to: {e}")
            # Create a mock Google Maps integration
            app.google_maps = MagicMock()
            app.google_maps.geocode.return_value = {
                'lat': 50.0755,
                'lng': 14.4378,
                'formatted_address': 'Prague, Czech Republic'
            }
        
        # Create the smart batch search manager
        smart_batch_search = SmartBatchSearchManager(app)
        
        # Mock the OSM integration
        app.osm_integration = MagicMock()
        app.osm_integration.find_buildings_by_coordinates.return_value = [
            {
                'type': 'way',
                'id': '123456',
                'lat': 50.0755,
                'lon': 14.4378,
                'tags': {
                    'building': 'residential',
                    'ref:ruian': '87654321',
                    'addr:street': 'Test Street',
                    'addr:housenumber': '123',
                    'addr:city': 'Prague'
                },
                'ruian_ref': '87654321'
            }
        ]
        
        # Create a callback to capture results
        results = []
        def callback(properties, location_name):
            nonlocal results
            results = properties
            logger.info(f"Found {len(properties)} properties near {location_name}")
            
        # Call the batch search method
        smart_batch_search.batch_search_by_address(
            address=random_address,
            property_types=["all"],
            radius=random_radius,
            max_results=10,
            callback=callback
        )
        
        # Verify that we got results
        self.assertTrue(len(results) > 0, "Should find at least one property")
        
        # Verify that each result has a URL
        for prop in results:
            self.assertIn('url', prop, "Property should have a URL")
            url = prop['url']
            self.assertTrue(url.startswith("http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"),
                           "URL should be a MapaIdentifikace URL")
            self.assertIn("l=KN", url, "URL should include the layer parameter")
            self.assertIn("x=", url, "URL should include the x coordinate")
            self.assertIn("y=", url, "URL should include the y coordinate")
            
            # Verify that the URL can be opened
            self.open_url_and_verify(url, mock_webbrowser_open)
            
        logger.info("CUZK URL generation test completed successfully")
        
    def open_url_and_verify(self, url, mock_webbrowser_open):
        """Open a URL and verify it was called correctly."""
        # Extract coordinates from URL
        import re
        x_match = re.search(r'x=(-?\d+)', url)
        y_match = re.search(r'y=(-?\d+)', url)
        
        if x_match and y_match:
            x = x_match.group(1)
            y = y_match.group(1)
            logger.info(f"Opening URL with coordinates: x={x}, y={y}")
            
            # Open the URL
            webbrowser.open(url)
            
            # Verify that webbrowser.open was called with the correct URL
            mock_webbrowser_open.assert_called_with(url)
            return True
        else:
            self.fail(f"Could not extract coordinates from URL: {url}")
            return False

    @patch('webbrowser.open')
    def test_url_format_validation(self, mock_webbrowser_open):
        """Test that generated URLs have the correct format."""
        logger.info("Testing URL format validation")
        
        # Create a CUZK integration
        cuzk_integration = CUZKIntegration()
        
        # Test coordinates (Prague)
        test_lat = 50.0755
        test_lng = 14.4378
        
        # Convert coordinates to S-JTSK
        x, y = cuzk_integration.convert_wgs84_to_sjtsk(test_lat, test_lng)
        
        # Generate URL
        url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
        
        # Verify URL format
        self.assertTrue(url.startswith("http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"),
                       "URL should be a MapaIdentifikace URL")
        self.assertIn("l=KN", url, "URL should include the layer parameter")
        self.assertIn(f"x={x}", url, "URL should include the x coordinate")
        self.assertIn(f"y={y}", url, "URL should include the y coordinate")
        
        # Open the URL
        webbrowser.open(url)
        
        # Verify that webbrowser.open was called with the correct URL
        mock_webbrowser_open.assert_called_with(url)
        
        logger.info(f"URL format validation completed successfully: {url}")


if __name__ == '__main__':
    unittest.main()
