"""
Test for city boundary fallback behavior.

This test verifies that when city boundaries can't be fetched from Google Maps API,
the application uses the user-specified radius instead of defaulting to 5000 meters.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import logging
import threading
import time

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CityBoundaryFallbackTest")

# Import the necessary modules
from core.real_batch_search_manager import RealBatchSearchManager


class TestCityBoundaryFallback(unittest.TestCase):
    """Tests for city boundary fallback behavior."""

    def setUp(self):
        """Set up the test environment."""
        # Create a mock application
        self.app = MagicMock()
        self.app.root = MagicMock()

        # Make the after method execute callbacks immediately
        self.app.root.after.side_effect = lambda _, callback: callback()

        # Create a mock Google Maps integration
        self.app.google_maps = MagicMock()

        # Create the real batch search manager with mocked dependencies
        self.batch_search_manager = RealBatchSearchManager(self.app)

        # Mock the _fetch_real_buildings method to avoid actual API calls
        self.batch_search_manager._fetch_real_buildings = MagicMock(return_value=[])

        # Mock the _fetch_buildings_in_bounding_box method to avoid actual API calls
        self.batch_search_manager._fetch_buildings_in_bounding_box = MagicMock(return_value=[])

        # Mock the _process_buildings method to avoid actual processing
        self.batch_search_manager._process_buildings = MagicMock(return_value=[])

    def test_city_boundary_fallback_uses_user_radius(self):
        """Test that when city boundaries can't be fetched, the user-specified radius is used."""
        # Mock the geocode method to return a city
        self.app.google_maps.geocode.return_value = {
            'lat': 50.0755,
            'lng': 14.4378,
            'formatted_address': 'Prague, Czechia',
            'address_components': [
                {'long_name': 'Prague', 'types': ['locality']}
            ]
        }

        # Mock the get_city_boundaries method to return None (simulating a failure)
        self.app.google_maps.get_city_boundaries.return_value = None

        # Set up a callback to capture the results
        callback = MagicMock()

        # Call the batch search method with a city address and radius 0 to trigger city boundary search
        user_radius = 0  # 0 km - this triggers city boundary search

        # Create a flag to track when the background thread completes
        thread_completed = threading.Event()

        # Override the _batch_search_thread method to set the flag when done
        original_thread_method = self.batch_search_manager._batch_search_thread

        def mock_thread_method(*args, **kwargs):
            try:
                original_thread_method(*args, **kwargs)
            finally:
                thread_completed.set()

        self.batch_search_manager._batch_search_thread = mock_thread_method

        # Call the batch search method
        self.batch_search_manager.batch_search_by_address(
            address="Prague",
            property_types=[],
            radius=user_radius,
            max_results=None,
            callback=callback
        )

        # Wait for the background thread to complete (with timeout)
        thread_completed.wait(timeout=5)

        # Verify that get_city_boundaries was called with the city name
        self.app.google_maps.get_city_boundaries.assert_called_once_with('Prague')

        # Verify that _fetch_real_buildings was called with the user-specified radius (in meters)
        expected_radius_meters = int(user_radius * 1000)  # Convert km to meters

        # Get the actual radius passed to _fetch_real_buildings
        actual_radius = self.batch_search_manager._fetch_real_buildings.call_args[0][2]

        # Verify that the actual radius matches the expected radius
        self.assertEqual(actual_radius, expected_radius_meters,
                         f"Expected radius {expected_radius_meters}m, but got {actual_radius}m")

        # Verify that the radius was not set to 5000 meters (the old default)
        self.assertNotEqual(actual_radius, 5000,
                           "Radius should not be set to 5000 meters (the old default)")

        logger.info(f"Test passed: User-specified radius {user_radius}km ({expected_radius_meters}m) was used instead of the default 5000m")


if __name__ == '__main__':
    unittest.main()
