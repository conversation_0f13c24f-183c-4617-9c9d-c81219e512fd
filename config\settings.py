"""
Configuration settings for the Czech Property Registry application.
"""

import os
import json
import configparser
# No fallback data is used - removed DemoData import

class Settings:
    """Class for handling application settings"""

    def __init__(self, config_file='config.ini'):
        """Initialize settings"""
        self.config_file = config_file
        self.config = configparser.ConfigParser()

        # Load settings
        self.load()

    def load(self):
        """Load settings from config file"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file)

    def save(self):
        """Save settings to config file"""
        with open(self.config_file, 'w') as f:
            self.config.write(f)

    def get(self, section, key, default=None):
        """Get a setting value"""
        if section in self.config and key in self.config[section]:
            return self.config[section][key]
        return default

    def set(self, section, key, value):
        """Set a setting value"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = str(value)

    def get_api_key(self, api_name):
        """Get an API key"""
        # Try to get from environment variable first
        env_var = f"{api_name.upper()}_API_KEY"
        if env_var in os.environ:
            return os.environ[env_var]

        # Try to get from config file
        section = api_name.upper()
        if section in self.config and 'api_key' in self.config[section]:
            return self.config[section]['api_key']

        return None

    def set_api_key(self, api_name, api_key):
        """Set an API key"""
        section = api_name.upper()
        if section not in self.config:
            self.config[section] = {}
        self.config[section]['api_key'] = api_key
        self.save()

    def get_google_maps_api_key(self):
        """Get the Google Maps API key"""
        return self.get_api_key('google_maps')

    def set_google_maps_api_key(self, api_key):
        """Set the Google Maps API key"""
        self.set_api_key('google_maps', api_key)

    def get_openai_api_key(self):
        """Get the OpenAI API key"""
        return self.get_api_key('openai')

    def set_openai_api_key(self, api_key):
        """Set the OpenAI API key"""
        self.set_api_key('openai', api_key)

    def get_ui_settings(self):
        """Get UI settings"""
        if 'UI' in self.config:
            return dict(self.config['UI'])
        return {}

    def set_ui_setting(self, key, value):
        """Set a UI setting"""
        if 'UI' not in self.config:
            self.config['UI'] = {}
        self.config['UI'][key] = str(value)
        self.save()

    def get_window_size(self):
        """Get the window size"""
        # Default window size without using fallback data
        default_width = 1024
        default_height = 768
        width = self.get('UI', 'window_width', str(default_width))
        height = self.get('UI', 'window_height', str(default_height))
        return int(width), int(height)

    def set_window_size(self, width, height):
        """Set the window size"""
        self.set('UI', 'window_width', str(width))
        self.set('UI', 'window_height', str(height))
        self.save()

    def get_data_directory(self):
        """Get the data directory"""
        # Default data directory without using fallback data
        default_directory = "czech_data"
        return self.get('DATA', 'directory', default_directory)

    def set_data_directory(self, directory):
        """Set the data directory"""
        self.set('DATA', 'directory', directory)
        self.save()

    def get_cache_settings(self):
        """Get cache settings"""
        if 'CACHE' in self.config:
            return dict(self.config['CACHE'])
        return {}

    def set_cache_setting(self, key, value):
        """Set a cache setting"""
        if 'CACHE' not in self.config:
            self.config['CACHE'] = {}
        self.config['CACHE'][key] = str(value)
        self.save()

    def get_cache_expiry(self):
        """Get the cache expiry time in hours"""
        return int(self.get('CACHE', 'expiry_hours', '24'))

    def set_cache_expiry(self, hours):
        """Set the cache expiry time in hours"""
        self.set('CACHE', 'expiry_hours', str(hours))
        self.save()
