"""
Simple Autocomplete Entry for the Czech Property Registry application.

This module provides a simplified autocomplete entry widget that works
with minimal dependencies.
"""

import tkinter as tk
from tkinter import ttk
import time

class SimpleAutocompleteEntry(ttk.Frame):
    """
    A simplified autocomplete entry widget.

    This widget provides autocomplete functionality with minimal dependencies.
    It displays suggestions in a dropdown listbox as the user types.
    """

    def __init__(self, master=None, autocomplete_function=None, width=20, **kwargs):
        """
        Initialize the autocomplete entry widget.

        Args:
            master: Parent widget
            autocomplete_function: Function that returns autocomplete suggestions
            width: Width of the entry widget
            **kwargs: Additional arguments for the frame
        """
        super().__init__(master, **kwargs)

        # Create the entry widget
        self.entry = ttk.Entry(self, width=width)
        self.entry.pack(fill=tk.X, expand=True)

        # Create the listbox for suggestions with enhanced styling
        self.listbox = tk.Listbox(
            self.winfo_toplevel(),  # Use the toplevel window to ensure visibility
            width=width,
            height=5,
            font=("Arial", 10),
            borderwidth=2,
            relief="solid",
            selectbackground="#4a6984",
            selectforeground="white"
        )

        # Store the autocomplete function
        self.autocomplete_function = autocomplete_function

        # Create a variable to track changes
        self.var = tk.StringVar()
        self.entry.config(textvariable=self.var)

        # Bind events
        self.var.trace_add("write", self.on_text_changed)
        self.entry.bind("<FocusOut>", self.on_focus_out)
        self.entry.bind("<Down>", self.on_down)
        self.entry.bind("<Return>", self.on_return)
        self.entry.bind("<Tab>", self.on_tab)

        # Bind listbox events
        self.listbox.bind("<ButtonRelease-1>", self.on_listbox_select)
        self.listbox.bind("<Return>", self.on_listbox_select)
        self.listbox.bind("<Double-Button-1>", self.on_listbox_select)

        # Bind global events to ensure dropdown is hidden when clicking elsewhere
        self.winfo_toplevel().bind("<Button-1>", self.check_hide_listbox, add="+")
        self.winfo_toplevel().bind("<Configure>", self.on_window_configure, add="+")

        # Variables for throttling
        self.last_fetch_time = 0
        self.fetch_delay = 0.3  # seconds

        # Debug flag
        self.debug = True

    def on_text_changed(self, *_):
        """Handle text changes in the entry widget."""
        # Get the current text
        text = self.var.get()

        # Hide the listbox if the text is too short
        if not text or len(text) < 2:
            self.hide_listbox()
            return

        # Store the current text for comparison in the fetch function
        self.current_text = text

        # Throttle API calls
        current_time = time.time()
        if current_time - self.last_fetch_time < self.fetch_delay:
            # Cancel any pending fetch
            if hasattr(self, '_pending_fetch_id'):
                self.after_cancel(self._pending_fetch_id)

            # Schedule a new fetch
            self._pending_fetch_id = self.after(
                int(self.fetch_delay * 1000),
                lambda: self._delayed_fetch()
            )
        else:
            # Fetch suggestions immediately
            self.fetch_suggestions(text)
            self.last_fetch_time = current_time

    def _delayed_fetch(self):
        """Perform a delayed fetch of suggestions."""
        # Only fetch if the text hasn't changed since the delay was scheduled
        if hasattr(self, 'current_text'):
            self.fetch_suggestions(self.current_text)
            self.last_fetch_time = time.time()

    def fetch_suggestions(self, text):
        """Fetch suggestions for the given text."""
        if not self.autocomplete_function:
            return

        # Get suggestions from the autocomplete function
        suggestions = self.autocomplete_function(text)

        # If we have suggestions, show them
        if suggestions:
            self.show_suggestions(suggestions)
        else:
            self.hide_listbox()

    def show_suggestions(self, suggestions):
        """Show suggestions in the listbox."""
        if self.debug:
            print(f"DEBUG: show_suggestions called with {len(suggestions)} suggestions")

        # Clear the listbox
        self.listbox.delete(0, tk.END)

        # Add suggestions to the listbox
        for i, suggestion in enumerate(suggestions):
            # Handle both string suggestions and dictionary suggestions
            if isinstance(suggestion, dict) and 'description' in suggestion:
                self.listbox.insert(tk.END, suggestion['description'])
            else:
                self.listbox.insert(tk.END, str(suggestion))

            # Add alternating background colors for better readability
            if i % 2 == 0:
                self.listbox.itemconfig(i, {'bg': '#f0f0f0'})

            if self.debug and i < 3:
                print(f"DEBUG: Added suggestion {i+1}: {self.listbox.get(i)}")

        # If we have suggestions, show the listbox
        if self.listbox.size() > 0:
            if self.debug:
                print(f"DEBUG: Showing listbox with {self.listbox.size()} suggestions")

            # Update the UI to ensure correct positioning
            self.update_idletasks()
            self.winfo_toplevel().update_idletasks()

            # Position the listbox below the entry
            self.position_listbox()

            # Make sure the listbox is visible
            if not self.listbox.winfo_ismapped():
                if self.debug:
                    print(f"DEBUG: Listbox not mapped, repositioning")
                self.position_listbox()

            # Set focus to the entry
            self.entry.focus_set()

            # Ensure the listbox is on top of everything
            self.listbox.lift()
        else:
            # Hide the listbox if there are no suggestions
            if self.debug:
                print(f"DEBUG: No suggestions to show, hiding listbox")
            self.hide_listbox()

    def position_listbox(self):
        """Position the listbox below the entry widget."""
        if self.debug:
            print(f"DEBUG: Positioning listbox for text: '{self.var.get()}'")

        # Get the entry widget's position on screen
        entry_x = self.entry.winfo_rootx()
        entry_y = self.entry.winfo_rooty()
        entry_height = self.entry.winfo_height()
        entry_width = self.entry.winfo_width()

        # Position the listbox below the entry
        self.listbox.place(
            x=entry_x,
            y=entry_y + entry_height,
            width=entry_width
        )

        if self.debug:
            print(f"DEBUG: Positioned listbox at x={entry_x}, y={entry_y + entry_height}, width={entry_width}")

        # Make sure the listbox is visible and on top
        self.listbox.lift()

        # Ensure the listbox is visible by updating the UI
        self.winfo_toplevel().update_idletasks()

    def hide_listbox(self):
        """Hide the listbox."""
        if self.listbox.winfo_ismapped():
            self.listbox.place_forget()

    def on_focus_out(self, _event):
        """Handle focus out events."""
        # Hide the listbox after a short delay
        # This allows clicks on the listbox to register first
        self.after(100, self.hide_listbox)

    def on_down(self, _event):
        """Handle down arrow key events."""
        # If the listbox is visible, focus it and select the first item
        if self.listbox.winfo_ismapped():
            self.listbox.focus_set()
            if self.listbox.size() > 0:
                self.listbox.selection_set(0)
                self.listbox.activate(0)
                self.listbox.see(0)
            return "break"

    def on_return(self, _event):
        """Handle return key events."""
        # If the listbox is visible, select the current item
        if self.listbox.winfo_ismapped():
            self.on_listbox_select(None)
            return "break"

    def on_tab(self, _event):
        """Handle tab key events."""
        # If the listbox is visible, select the current item
        if self.listbox.winfo_ismapped():
            self.on_listbox_select(None)
            return "break"

    def on_listbox_select(self, _event):
        """Handle listbox selection events."""
        # Get the selected item
        if self.listbox.curselection():
            index = self.listbox.curselection()[0]
            value = self.listbox.get(index)

            # Set the entry text to the selected value
            self.var.set(value)

            # Hide the listbox
            self.hide_listbox()

            # Set focus back to the entry
            self.entry.focus_set()

            # Move cursor to the end
            self.entry.icursor(tk.END)

        return "break"

    def get(self):
        """Get the current text in the entry widget."""
        return self.entry.get()

    def set(self, value):
        """Set the text in the entry widget."""
        self.var.set(value)

    def check_hide_listbox(self, event):
        """Check if we should hide the listbox based on click location."""
        if self.listbox.winfo_ismapped():
            # Don't hide if clicking on the listbox itself
            if event.widget == self.listbox:
                return

            # Don't hide if clicking on the entry
            if event.widget == self.entry:
                return

            # Hide in all other cases
            if self.debug:
                print(f"DEBUG: Hiding listbox because clicked on {event.widget}")
            self.hide_listbox()

    def on_window_configure(self, event=None):
        """Handle window configuration (resize, move) events."""
        # Hide the listbox when the window is resized or moved
        if self.listbox.winfo_ismapped():
            if self.debug:
                print(f"DEBUG: Hiding listbox due to window configuration change")
            self.hide_listbox()

    def clear(self):
        """Clear the entry widget."""
        self.var.set("")
