"""
Test script for the batch processing functionality.

This script tests the batch processing functionality of the Czech Property Registry application.
"""

import unittest
import tkinter as tk
import logging
import sys
import os
from unittest.mock import MagicMock, patch

# Add the parent directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the application
from core.batch_processing_manager import BatchProcessingManager
from models.ruian_id import RuianID

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockApp:
    """Mock application for testing."""
    
    def __init__(self):
        """Initialize the mock application."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window
        self.property_list = []
        self.show_status_calls = []
        
        # Mock the CUZK integration
        self.cuzk_integration = MagicMock()
        self.cuzk_integration.get_property_by_ruian_id.return_value = {
            'owner_name': 'Test Owner',
            'owner_address': 'Test Address',
            'property_type': 'Test Type',
            'parcel_number': '123/45',
            'cadastral_territory': 'Test Territory',
            'ownership_share': '1/1',
            'lv_number': '1234',
            'is_sample': False,
            'source': 'test'
        }
        
        # Mock the property search
        self.property_search = MagicMock()
        self.property_search.unified_search.return_value = {
            'owner_name': 'Test Owner',
            'owner_address': 'Test Address',
            'property_type': 'Test Type',
            'parcel_number': '123/45',
            'cadastral_territory': 'Test Territory',
            'ownership_share': '1/1',
            'lv_number': '1234',
            'is_sample': False,
            'source': 'test'
        }
        
    def show_status(self, message):
        """Mock the show_status method."""
        self.show_status_calls.append(message)
        logger.info(f"Status: {message}")


class TestBatchProcessingManager(unittest.TestCase):
    """Test the BatchProcessingManager class."""
    
    def setUp(self):
        """Set up the test."""
        self.app = MockApp()
        self.batch_processing_manager = BatchProcessingManager(self.app)
        
    def tearDown(self):
        """Clean up after the test."""
        self.app.root.destroy()
        
    def test_process_single_building(self):
        """Test processing a single building."""
        # Create a test building
        building = {
            'name': 'Test Building',
            'address': 'Test Address',
            'ruian_ref': '123456',
            'building_type': 'residential',
            'osm_id': '123',
            'osm_type': 'way',
            'lat': '50.0',
            'lng': '14.0',
            'tags': {
                'building': 'residential',
                'name': 'Test Building',
                'addr:street': 'Test Street',
                'addr:housenumber': '123',
                'addr:city': 'Test City',
                'addr:postcode': '12345'
            }
        }
        
        # Process the building
        result = self.batch_processing_manager._process_single_building(building)
        
        # Check the result
        self.assertIsNotNone(result)
        self.assertEqual(result['owner_name'], 'Test Owner')
        self.assertEqual(result['building_name'], 'Test Building')
        self.assertEqual(result['building_type'], 'residential')
        self.assertEqual(result['ruian_id'], '123456')
        
    def test_process_selected_buildings(self):
        """Test processing selected buildings."""
        # Create test buildings
        buildings = [
            {
                'name': 'Test Building 1',
                'address': 'Test Address 1',
                'ruian_ref': '123456',
                'building_type': 'residential',
                'osm_id': '123',
                'osm_type': 'way',
                'lat': '50.0',
                'lng': '14.0',
                'tags': {
                    'building': 'residential',
                    'name': 'Test Building 1',
                    'addr:street': 'Test Street',
                    'addr:housenumber': '123',
                    'addr:city': 'Test City',
                    'addr:postcode': '12345'
                }
            },
            {
                'name': 'Test Building 2',
                'address': 'Test Address 2',
                'ruian_ref': '654321',
                'building_type': 'commercial',
                'osm_id': '456',
                'osm_type': 'way',
                'lat': '50.1',
                'lng': '14.1',
                'tags': {
                    'building': 'commercial',
                    'name': 'Test Building 2',
                    'addr:street': 'Test Street',
                    'addr:housenumber': '456',
                    'addr:city': 'Test City',
                    'addr:postcode': '12345'
                }
            }
        ]
        
        # Mock the callbacks
        progress_callback = MagicMock()
        completion_callback = MagicMock()
        
        # Process the buildings
        self.batch_processing_manager.process_selected_buildings(
            buildings,
            progress_callback,
            completion_callback
        )
        
        # Wait for the processing to complete
        self.app.root.after(1000, self.app.root.quit)
        self.app.root.mainloop()
        
        # Check the results
        self.assertEqual(len(self.batch_processing_manager.processed_properties), 2)
        self.assertEqual(self.batch_processing_manager.processed_properties[0]['building_name'], 'Test Building 1')
        self.assertEqual(self.batch_processing_manager.processed_properties[1]['building_name'], 'Test Building 2')
        
        # Check that the callbacks were called
        self.assertTrue(progress_callback.called)
        self.assertTrue(completion_callback.called)
        
    def test_ruian_id_validation(self):
        """Test RUIAN ID validation."""
        # Valid RUIAN ID
        ruian_id = RuianID.from_string('123456')
        self.assertIsNotNone(ruian_id)
        self.assertEqual(ruian_id.value, '123456')
        
        # Invalid RUIAN ID
        ruian_id = RuianID.from_string('abc')
        self.assertIsNone(ruian_id)
        
        # Empty RUIAN ID
        ruian_id = RuianID.from_string('')
        self.assertIsNone(ruian_id)
        
        # None RUIAN ID
        ruian_id = RuianID.from_string(None)
        self.assertIsNone(ruian_id)


if __name__ == '__main__':
    unittest.main()
