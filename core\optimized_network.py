"""
Optimized network utilities for the Czech Property Scraper application.
Provides enhanced session management, request batching, and performance optimizations.
"""

import requests
import time
import threading
import logging
import queue
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Dict, Any, List, Optional, Callable, Union, Tuple
from concurrent.futures import ThreadPoolExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OptimizedNetwork")


class RateLimiter:
    """Rate limiter for API requests to prevent hitting rate limits."""

    def __init__(self, requests_per_second: float = 2.0):
        """
        Initialize the rate limiter.

        Args:
            requests_per_second (float): Maximum number of requests per second
        """
        self.rate = requests_per_second
        self.last_request_time = 0
        self.lock = threading.Lock()

    def wait(self) -> None:
        """Wait if necessary to comply with the rate limit."""
        with self.lock:
            current_time = time.time()
            time_since_last_request = current_time - self.last_request_time
            min_interval = 1.0 / self.rate

            if time_since_last_request < min_interval:
                sleep_time = min_interval - time_since_last_request
                time.sleep(sleep_time)

            self.last_request_time = time.time()


class DomainRateLimiter:
    """Rate limiter that applies different limits to different domains."""

    def __init__(self, default_rate: float = 2.0):
        """
        Initialize the domain rate limiter.

        Args:
            default_rate (float): Default maximum number of requests per second
        """
        self.default_rate = default_rate
        self.rate_limiters: Dict[str, RateLimiter] = {}
        self.lock = threading.Lock()

    def get_limiter(self, domain: str) -> RateLimiter:
        """
        Get or create a rate limiter for a specific domain.

        Args:
            domain (str): Domain name

        Returns:
            RateLimiter: Rate limiter for the domain
        """
        with self.lock:
            if domain not in self.rate_limiters:
                self.rate_limiters[domain] = RateLimiter(self.default_rate)
            return self.rate_limiters[domain]

    def set_rate(self, domain: str, rate: float) -> None:
        """
        Set the rate limit for a specific domain.

        Args:
            domain (str): Domain name
            rate (float): Maximum number of requests per second
        """
        with self.lock:
            if domain in self.rate_limiters:
                self.rate_limiters[domain].rate = rate
            else:
                self.rate_limiters[domain] = RateLimiter(rate)

    def wait(self, domain: str) -> None:
        """
        Wait if necessary to comply with the rate limit for a domain.

        Args:
            domain (str): Domain name
        """
        limiter = self.get_limiter(domain)
        limiter.wait()


class RequestBatcher:
    """Batches multiple requests to the same endpoint to reduce overhead."""

    def __init__(self, batch_size: int = 10, batch_interval: float = 0.5):
        """
        Initialize the request batcher.

        Args:
            batch_size (int): Maximum number of requests in a batch
            batch_interval (float): Maximum time to wait for a batch to fill up
        """
        self.batch_size = batch_size
        self.batch_interval = batch_interval
        self.batches: Dict[str, Dict[str, Any]] = {}
        self.batch_locks: Dict[str, threading.Lock] = {}
        self.batch_events: Dict[str, threading.Event] = {}
        self.batch_timers: Dict[str, threading.Timer] = {}
        self.lock = threading.Lock()

    def add_request(self, batch_key: str, request_key: str, request_data: Any) -> threading.Event:
        """
        Add a request to a batch.

        Args:
            batch_key (str): Key identifying the batch (e.g., endpoint URL)
            request_key (str): Key identifying the request within the batch
            request_data (Any): Data for the request

        Returns:
            threading.Event: Event that will be set when the batch is processed
        """
        with self.lock:
            # Create batch if it doesn't exist
            if batch_key not in self.batches:
                self.batches[batch_key] = {}
                self.batch_locks[batch_key] = threading.Lock()
                self.batch_events[batch_key] = threading.Event()
                self.batch_timers[batch_key] = None

            # Add request to batch
            with self.batch_locks[batch_key]:
                self.batches[batch_key][request_key] = request_data

                # Start timer if this is the first request in the batch
                if len(self.batches[batch_key]) == 1:
                    self.batch_timers[batch_key] = threading.Timer(
                        self.batch_interval,
                        lambda: self.process_batch(batch_key)
                    )
                    self.batch_timers[batch_key].start()

                # Process batch immediately if it's full
                if len(self.batches[batch_key]) >= self.batch_size:
                    if self.batch_timers[batch_key]:
                        self.batch_timers[batch_key].cancel()
                        self.batch_timers[batch_key] = None
                    threading.Thread(
                        target=self.process_batch,
                        args=(batch_key,),
                        daemon=True
                    ).start()

            return self.batch_events[batch_key]

    def process_batch(self, batch_key: str) -> None:
        """
        Process a batch of requests.

        Args:
            batch_key (str): Key identifying the batch
        """
        # This method should be overridden by subclasses
        raise NotImplementedError("Subclasses must implement process_batch")


class OptimizedSession:
    """Enhanced session with optimized connection pooling and retry capability."""

    def __init__(self, 
                 max_retries: int = 5,
                 backoff_factor: float = 0.5,
                 pool_connections: int = 10,
                 pool_maxsize: int = 20,
                 max_workers: int = 5):
        """
        Initialize the optimized session.

        Args:
            max_retries (int): Maximum number of retries
            backoff_factor (float): Backoff factor for exponential delay
            pool_connections (int): Number of connection pools (one per host)
            pool_maxsize (int): Max connections per pool
            max_workers (int): Maximum number of worker threads
        """
        self.session = self._create_session(max_retries, backoff_factor, pool_connections, pool_maxsize)
        self.domain_rate_limiter = DomainRateLimiter()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.request_queue = queue.PriorityQueue()
        self.processing = False
        self.processing_lock = threading.Lock()
        
        # Set up domain-specific rate limits
        self.domain_rate_limiter.set_rate("nahlizenidokn.cuzk.cz", 1.0)  # 1 request per second for CUZK
        self.domain_rate_limiter.set_rate("overpass-api.de", 0.5)  # 1 request per 2 seconds for Overpass API
        self.domain_rate_limiter.set_rate("maps.googleapis.com", 5.0)  # 5 requests per second for Google Maps
        
        logger.info("OptimizedSession initialized")

    def _create_session(self, max_retries: int, backoff_factor: float, 
                        pool_connections: int, pool_maxsize: int) -> requests.Session:
        """
        Create a requests session with optimized connection pooling and retry capability.
        
        Args:
            max_retries (int): Maximum number of retries
            backoff_factor (float): Backoff factor for exponential delay
            pool_connections (int): Number of connection pools (one per host)
            pool_maxsize (int): Max connections per pool
            
        Returns:
            requests.Session: A configured requests session
        """
        session = requests.Session()
        
        # Create a more robust retry strategy
        retry = Retry(
            total=max_retries,
            backoff_factor=backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504, 507, 524],
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"],
            respect_retry_after_header=True,
            raise_on_redirect=False,
            raise_on_status=False
        )
        
        # Create connection pool adapter with increased max connections
        adapter = HTTPAdapter(
            max_retries=retry,
            pool_connections=pool_connections,
            pool_maxsize=pool_maxsize,
            pool_block=False
        )
        
        # Mount the adapter for both HTTP and HTTPS
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        
        # Set a reasonable user agent to avoid being blocked
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        })
        
        return session

    def _get_domain(self, url: str) -> str:
        """
        Extract the domain from a URL.
        
        Args:
            url (str): URL
            
        Returns:
            str: Domain name
        """
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        return parsed_url.netloc

    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        Send a request with rate limiting.
        
        Args:
            method (str): HTTP method
            url (str): URL
            **kwargs: Additional arguments for requests.request
            
        Returns:
            requests.Response: Response object
        """
        domain = self._get_domain(url)
        self.domain_rate_limiter.wait(domain)
        
        logger.debug(f"Sending {method} request to {url}")
        return self.session.request(method, url, **kwargs)

    def get(self, url: str, **kwargs) -> requests.Response:
        """
        Send a GET request with rate limiting.
        
        Args:
            url (str): URL
            **kwargs: Additional arguments for requests.get
            
        Returns:
            requests.Response: Response object
        """
        return self.request("GET", url, **kwargs)

    def post(self, url: str, **kwargs) -> requests.Response:
        """
        Send a POST request with rate limiting.
        
        Args:
            url (str): URL
            **kwargs: Additional arguments for requests.post
            
        Returns:
            requests.Response: Response object
        """
        return self.request("POST", url, **kwargs)

    def async_request(self, method: str, url: str, callback: Callable = None, 
                     priority: int = 1, **kwargs) -> None:
        """
        Send a request asynchronously.
        
        Args:
            method (str): HTTP method
            url (str): URL
            callback (Callable, optional): Function to call with the response
            priority (int): Request priority (lower number = higher priority)
            **kwargs: Additional arguments for requests.request
        """
        # Add the request to the queue
        self.request_queue.put((priority, (method, url, callback, kwargs)))
        
        # Start processing if not already running
        with self.processing_lock:
            if not self.processing:
                self.processing = True
                threading.Thread(target=self._process_queue, daemon=True).start()

    def _process_queue(self) -> None:
        """Process requests from the queue."""
        while True:
            try:
                # Get the next request from the queue
                priority, (method, url, callback, kwargs) = self.request_queue.get(timeout=1.0)
                
                # Submit the request to the executor
                future = self.executor.submit(self.request, method, url, **kwargs)
                
                # Add callback if provided
                if callback:
                    future.add_done_callback(
                        lambda f: callback(f.result()) if not f.exception() else None
                    )
                
                # Mark the task as done
                self.request_queue.task_done()
            except queue.Empty:
                # No more requests in the queue
                with self.processing_lock:
                    self.processing = False
                    break
            except Exception as e:
                logger.error(f"Error processing request: {e}")

    def close(self) -> None:
        """Close the session and clean up resources."""
        self.executor.shutdown(wait=False)
        self.session.close()
        logger.info("OptimizedSession closed")


def create_optimized_session() -> OptimizedSession:
    """
    Create an optimized session for network requests.
    
    Returns:
        OptimizedSession: An optimized session
    """
    return OptimizedSession()
