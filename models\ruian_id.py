"""
RUIAN ID Model

This module provides a model class for RUIAN IDs.
RUIAN (Registry of Territorial Identification, Addresses and Real Estates) is the Czech Republic's
official system for recording data about territorial identification, addresses, and real estate.
"""

from typing import Optional, Union, Tuple, Dict, Any
from utils.ruian_validator import (
    clean_ruian_id,
    is_valid_ruian_id,
    validate_ruian_id,
    verify_ruian_id_exists,
    ValidationLevel
)


class RuianID:
    """
    A class representing a RUIAN ID.

    RUIAN IDs are numeric identifiers used to uniquely identify buildings and properties
    in the Czech Republic's Registry of Territorial Identification, Addresses and Real Estates.
    """

    def __init__(self, ruian_id: Optional[Union[str, int]] = None, validation_level: int = ValidationLevel.BASIC):
        """
        Initialize a RUIAN ID.

        Args:
            ruian_id (str or int, optional): The RUIAN ID
            validation_level (int, optional): The validation level to use
                - ValidationLevel.BASIC: Basic format validation
                - ValidationLevel.STRICT: Strict format validation
                - ValidationLevel.ONLINE: Online verification against CUZK

        Raises:
            ValueError: If the RUIAN ID is invalid
        """
        self._original_value = ruian_id
        self._value = None
        self._is_valid = False
        self._error_message = None
        self._validation_level = validation_level
        self._verified = False
        self._exists_in_cuzk = False

        # Always validate the RUIAN ID, even if it's None
        self._is_valid, self._value, self._error_message = validate_ruian_id(ruian_id, validation_level)

        # If we used online validation, mark as verified
        if validation_level >= ValidationLevel.ONLINE:
            self._verified = True
            self._exists_in_cuzk = self._is_valid

        if not self._is_valid:
            raise ValueError(self._error_message)

    @property
    def value(self) -> Optional[str]:
        """
        Get the cleaned RUIAN ID value.

        Returns:
            str: The cleaned RUIAN ID value
        """
        return self._value

    @property
    def original_value(self) -> Optional[Union[str, int]]:
        """
        Get the original RUIAN ID value.

        Returns:
            str or int: The original RUIAN ID value
        """
        return self._original_value

    @property
    def is_valid(self) -> bool:
        """
        Check if the RUIAN ID is valid.

        Returns:
            bool: True if the RUIAN ID is valid, False otherwise
        """
        return self._is_valid

    @property
    def error_message(self) -> Optional[str]:
        """
        Get the error message if the RUIAN ID is invalid.

        Returns:
            str: The error message if the RUIAN ID is invalid, None otherwise
        """
        return self._error_message

    @property
    def validation_level(self) -> int:
        """
        Get the validation level used for this RUIAN ID.

        Returns:
            int: The validation level
        """
        return self._validation_level

    @property
    def verified(self) -> bool:
        """
        Check if the RUIAN ID has been verified against the CUZK system.

        Returns:
            bool: True if the RUIAN ID has been verified, False otherwise
        """
        return self._verified

    @property
    def exists_in_cuzk(self) -> bool:
        """
        Check if the RUIAN ID exists in the CUZK system.

        Returns:
            bool: True if the RUIAN ID exists in the CUZK system, False otherwise
        """
        return self._exists_in_cuzk

    def verify(self, use_cache: bool = True) -> bool:
        """
        Verify the RUIAN ID against the CUZK system.

        Args:
            use_cache (bool, optional): Whether to use the cache. Defaults to True.

        Returns:
            bool: True if the RUIAN ID exists in the CUZK system, False otherwise
        """
        if not self._is_valid:
            return False

        if self._verified and use_cache:
            return self._exists_in_cuzk

        # For testing purposes, we need to make sure this function can be mocked
        # So we'll call it through a separate method
        return self._do_verify(use_cache=use_cache)

    def force_verify(self) -> bool:
        """
        Force verification of the RUIAN ID against the CUZK system, bypassing the cache.

        Returns:
            bool: True if the RUIAN ID exists in the CUZK system, False otherwise
        """
        return self.verify(use_cache=False)

    def _do_verify(self, use_cache: bool = True) -> bool:
        """
        Internal method to verify the RUIAN ID against the CUZK system.
        This method is separated to make it easier to mock in tests.

        Args:
            use_cache (bool, optional): Whether to use the cache. Defaults to True.

        Returns:
            bool: True if the RUIAN ID exists in the CUZK system, False otherwise
        """
        exists, error = verify_ruian_id_exists(self._value, use_cache=use_cache)
        self._verified = True
        self._exists_in_cuzk = exists

        if not exists and error:
            self._error_message = error

        return exists

    @classmethod
    def from_string(cls, ruian_id_str: Optional[str], validation_level: int = ValidationLevel.BASIC) -> Optional['RuianID']:
        """
        Create a RUIAN ID from a string.

        Args:
            ruian_id_str (str, optional): The RUIAN ID string
            validation_level (int, optional): The validation level to use
                - ValidationLevel.BASIC: Basic format validation
                - ValidationLevel.STRICT: Strict format validation
                - ValidationLevel.ONLINE: Online verification against CUZK

        Returns:
            RuianID: A RUIAN ID object if the string is valid, None otherwise
        """
        if ruian_id_str is None or ruian_id_str == 'Unknown' or not ruian_id_str.strip():
            return None

        try:
            return cls(ruian_id_str, validation_level)
        except ValueError:
            return None

    @classmethod
    def validate(cls, ruian_id: Optional[Union[str, int]], validation_level: int = ValidationLevel.BASIC) -> bool:
        """
        Validate a RUIAN ID.

        Args:
            ruian_id (str or int, optional): The RUIAN ID to validate
            validation_level (int, optional): The validation level to use
                - ValidationLevel.BASIC: Basic format validation
                - ValidationLevel.STRICT: Strict format validation
                - ValidationLevel.ONLINE: Online verification against CUZK

        Returns:
            bool: True if the RUIAN ID is valid, False otherwise
        """
        return is_valid_ruian_id(ruian_id, validation_level)

    @classmethod
    def is_cached(cls, ruian_id: Optional[Union[str, int]]) -> bool:
        """
        Check if a RUIAN ID is in the cache.

        Args:
            ruian_id (str or int, optional): The RUIAN ID to check

        Returns:
            bool: True if the RUIAN ID is in the cache, False otherwise
        """
        if ruian_id is None:
            return False

        # Clean the RUIAN ID
        clean_id = clean_ruian_id(ruian_id)

        # Check if the cleaned ID is empty
        if not clean_id:
            return False

        # Check if the RUIAN ID is in the cache
        from utils.ruian_cache import get_cache
        cache = get_cache()
        return cache.contains(clean_id)

    @classmethod
    def get_cached_result(cls, ruian_id: Optional[Union[str, int]]) -> Optional[Tuple[bool, Optional[str]]]:
        """
        Get the cached verification result for a RUIAN ID.

        Args:
            ruian_id (str or int, optional): The RUIAN ID to get the result for

        Returns:
            tuple or None: (exists, error) if the RUIAN ID is in the cache, None otherwise
        """
        if ruian_id is None:
            return None

        # Clean the RUIAN ID
        clean_id = clean_ruian_id(ruian_id)

        # Check if the cleaned ID is empty
        if not clean_id:
            return None

        # Get the cached result
        from utils.ruian_cache import get_cache
        cache = get_cache()
        return cache.get(clean_id)

    @classmethod
    def clear_cache(cls) -> None:
        """
        Clear the RUIAN ID cache.
        """
        from utils.ruian_cache import get_cache
        cache = get_cache()
        cache.clear()

    @classmethod
    def remove_from_cache(cls, ruian_id: Optional[Union[str, int]]) -> bool:
        """
        Remove a RUIAN ID from the cache.

        Args:
            ruian_id (str or int, optional): The RUIAN ID to remove

        Returns:
            bool: True if the RUIAN ID was removed, False otherwise
        """
        if ruian_id is None:
            return False

        # Clean the RUIAN ID
        clean_id = clean_ruian_id(ruian_id)

        # Check if the cleaned ID is empty
        if not clean_id:
            return False

        # Remove the RUIAN ID from the cache
        from utils.ruian_cache import get_cache
        cache = get_cache()
        return cache.remove(clean_id)

    @classmethod
    def cleanup_cache(cls) -> int:
        """
        Remove expired entries from the cache.

        Returns:
            int: Number of entries removed
        """
        from utils.ruian_cache import get_cache
        cache = get_cache()
        return cache.cleanup()

    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            dict: Cache statistics
        """
        from utils.ruian_cache import get_cache
        cache = get_cache()
        return cache.get_stats()

    @classmethod
    def clean(cls, ruian_id: Optional[Union[str, int]]) -> str:
        """
        Clean a RUIAN ID.

        Args:
            ruian_id (str or int, optional): The RUIAN ID to clean

        Returns:
            str: The cleaned RUIAN ID
        """
        return clean_ruian_id(ruian_id)

    def __str__(self) -> str:
        """
        Get the string representation of the RUIAN ID.

        Returns:
            str: The string representation of the RUIAN ID
        """
        return self._value if self._value else ""

    def __repr__(self) -> str:
        """
        Get the representation of the RUIAN ID.

        Returns:
            str: The representation of the RUIAN ID
        """
        return f"RuianID('{self._value}')"

    def __eq__(self, other) -> bool:
        """
        Check if two RUIAN IDs are equal.

        Args:
            other: The other RUIAN ID to compare with

        Returns:
            bool: True if the RUIAN IDs are equal, False otherwise
        """
        if isinstance(other, RuianID):
            return self._value == other._value
        elif isinstance(other, str):
            return self._value == clean_ruian_id(other)
        elif isinstance(other, int):
            return self._value == str(other)
        else:
            return False

    def __bool__(self) -> bool:
        """
        Check if the RUIAN ID is valid.

        Returns:
            bool: True if the RUIAN ID is valid, False otherwise
        """
        return self._is_valid
