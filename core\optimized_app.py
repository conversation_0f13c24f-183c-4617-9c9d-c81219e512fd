"""
Optimized version of the PropertyScraperApp class for the Czech Property Registry application.
Provides improved performance, memory usage, and resource management.
"""

import os
import sys
import time
import logging
import threading
import tkinter as tk
from tkinter import ttk
import concurrent.futures
import traceback
from typing import Dict, Any, Optional, List, Tuple, Set, Union, Callable

# Import base class
from core.app_base import AppBase

# Import optimized components
from utils.optimized_db_cache import OptimizedDBCache
from utils.optimized_network import OptimizedSession
from utils.optimized_compression import OptimizedCompressor

# Configure logging
logger = logging.getLogger("OptimizedPropertyScraperApp")


class OptimizedPropertyScraperApp(AppBase):
    """
    Optimized version of the PropertyScraperApp class with improved performance,
    memory usage, and resource management.
    """

    def __init__(self, root=None, config_path="config.ini"):
        """
        Initialize the optimized property scraper application.

        Args:
            root (tk.Tk, optional): Tkinter root window
            config_path (str): Path to the configuration file
        """
        # Call the parent class constructor
        super().__init__(root)

        self.config_path = config_path

        # Set up logging
        self._setup_logging()

        # Initialize optimized components
        self._init_optimized_components()

        # Initialize UI
        self._init_ui()

        # Start background tasks
        self._start_background_tasks()

        # Register cleanup handlers
        self._register_cleanup_handlers()

        logger.info("OptimizedPropertyScraperApp initialized")

    def _setup_logging(self):
        """Set up logging for the application."""
        # Configure root logger
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )

    def _init_optimized_components(self):
        """Initialize optimized components."""
        # Initialize optimized database cache
        self.db_cache = OptimizedDBCache(
            db_path="property_cache.db",
            auto_vacuum=True,
            pragma_optimizations=True,
            memory_cache=True,
            memory_cache_size=10000
        )
        logger.info("Initializing optimized database cache")

        # Create caches with optimized settings
        self._create_optimized_caches()

        # Initialize optimized network session
        self.network = OptimizedSession(
            pool_connections=100,
            pool_maxsize=100,
            max_retries=3,
            backoff_factor=0.3,
            status_forcelist=[500, 502, 503, 504],
            timeout=(5, 30),
            cache_dns=True,
            keep_alive=True
        )
        logger.info("Creating optimized network session")

        # Set rate limits for specific domains
        self.network.set_rate_limit("nahlizenidokn.cuzk.cz", 1.0)
        self.network.set_rate_limit("overpass-api.de", 0.5)
        self.network.set_rate_limit("maps.googleapis.com", 5.0)

        # Initialize optimized data compression
        self.compressor = OptimizedCompressor(
            method="zlib",
            level=6,
            adaptive=True,
            cache_size=1000,
            min_size_to_compress=1024
        )
        logger.info("Initializing optimized data compression")

        # Initialize thread pool
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=10,
            thread_name_prefix="OptimizedWorker"
        )
        logger.info("Initializing optimized thread pool")

        # Initialize memory monitoring
        self._init_memory_monitoring()

    def _create_optimized_caches(self):
        """Create optimized caches with appropriate settings."""
        # Short-lived caches (10 minutes)
        self.db_cache.create_cache("address_suggestions", expiry=600)

        # Medium-lived caches (1 hour)
        self.db_cache.create_cache("street_suggestions", expiry=3600)
        self.db_cache.create_cache("osm_buildings", expiry=3600)

        # Long-lived caches (1 day)
        self.db_cache.create_cache("city_suggestions", expiry=86400)
        self.db_cache.create_cache("postal_suggestions", expiry=86400)

        # Very long-lived caches (1 week)
        self.db_cache.create_cache("gender", expiry=604800)

        # Short-lived security-sensitive caches (5 minutes)
        self.db_cache.create_cache("encrypted_urls", expiry=300)

        # Medium-lived property data cache (30 minutes)
        self.db_cache.create_cache("property_data", expiry=1800)

    def _init_memory_monitoring(self):
        """Initialize memory monitoring."""
        self.memory_monitoring = True
        self.memory_monitor_thread = threading.Thread(
            target=self._monitor_memory,
            daemon=True,
            name="MemoryMonitor"
        )
        self.memory_monitor_thread.start()
        logger.info("Memory monitoring started")

    def _monitor_memory(self):
        """Monitor memory usage and perform cleanup when necessary."""
        import psutil
        import gc

        process = psutil.Process(os.getpid())

        while self.memory_monitoring:
            try:
                # Get memory usage
                memory_info = process.memory_info()
                memory_percent = process.memory_percent()

                # Log memory usage every 5 minutes
                if int(time.time()) % 300 < 1:
                    logger.info(f"Memory usage: {memory_info.rss / (1024 * 1024):.2f} MB ({memory_percent:.1f}%)")

                # If memory usage is high, perform cleanup
                if memory_percent > 80:
                    logger.warning(f"High memory usage detected: {memory_percent:.1f}%")
                    self._perform_memory_cleanup()
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")

            # Sleep for 10 seconds
            time.sleep(10)

    def _perform_memory_cleanup(self):
        """Perform memory cleanup when memory usage is high."""
        import gc

        logger.info("Performing memory cleanup")

        # Clear in-memory caches
        if hasattr(self, 'db_cache'):
            self.db_cache.cleanup()

        # Run garbage collection
        gc.collect()

        logger.info("Memory cleanup completed")

    def _init_ui(self):
        """Initialize the UI components."""
        # Set window title and size
        self.root.title("Czech Property Registry - Optimized")
        self.root.geometry("1200x800")

        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Initialize UI components
        self._init_batch_search_ui()

        # Set up status bar
        self.status_bar = ttk.Label(self.root, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def _init_batch_search_ui(self):
        """Initialize the batch search UI."""
        # Import here to avoid circular imports
        from ui.batch_search import BatchSearchUI

        # Create batch search tab
        self.batch_search_ui = BatchSearchUI(self.notebook, self)
        self.notebook.add(self.batch_search_ui, text="Batch Search")

        # Select the batch search tab by default
        self.notebook.select(0)

    def _start_background_tasks(self):
        """Start background tasks."""
        # Schedule periodic cleanup
        self._schedule_periodic_cleanup()

    def _schedule_periodic_cleanup(self):
        """Schedule periodic cleanup tasks."""
        # Schedule database cleanup every hour
        def cleanup_task():
            try:
                # Clean up expired cache entries
                if hasattr(self, 'db_cache'):
                    cleanup_result = self.db_cache.cleanup()
                    logger.info(f"Cleaned up {sum(cleanup_result.values())} expired cache entries")

                # Schedule next cleanup
                self.root.after(3600000, cleanup_task)  # 1 hour
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
                # Still schedule next cleanup even if this one failed
                self.root.after(3600000, cleanup_task)  # 1 hour

        # Start the first cleanup after 10 minutes
        self.root.after(600000, cleanup_task)  # 10 minutes

    def _register_cleanup_handlers(self):
        """Register cleanup handlers for application exit."""
        # Register Tkinter destroy event
        self.root.protocol("WM_DELETE_WINDOW", self.cleanup_and_exit)

        # Register atexit handler
        import atexit
        atexit.register(self.cleanup_resources)

    def cleanup(self):
        """Clean up resources before exit."""
        logger.info("Cleaning up optimized resources")

        # Stop memory monitoring
        self.memory_monitoring = False
        if hasattr(self, 'memory_monitor_thread') and self.memory_monitor_thread.is_alive():
            self.memory_monitor_thread.join(timeout=1.0)
            logger.info("Memory monitoring stopped")

        # Shut down thread pool
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=False)
            logger.info("Thread pool shut down")

        # Close network session
        if hasattr(self, 'network'):
            self.network.close()
            logger.info("Network session closed")

        # Close database cache
        if hasattr(self, 'db_cache'):
            self.db_cache.close()
            logger.info("Database cache closed")

        # Log statistics
        self._log_statistics()

        # Call parent class cleanup
        super().cleanup()

    def cleanup_resources(self):
        """Legacy method for backward compatibility."""
        self.cleanup()

    def _log_statistics(self):
        """Log performance statistics."""
        # Log cache statistics
        if hasattr(self, 'db_cache'):
            cache_stats = self.db_cache.get_stats()
            logger.info(f"Cache statistics: {cache_stats}")

        # Log network statistics
        if hasattr(self, 'network'):
            network_stats = self.network.get_stats()
            logger.info(f"Network statistics: {network_stats}")

        # Log compression statistics
        if hasattr(self, 'compressor'):
            compression_stats = self.compressor.get_stats()
            logger.info(f"Compression statistics: {compression_stats}")

    def cleanup_and_exit(self):
        """Clean up resources and exit the application."""
        # Clean up resources
        self.cleanup()

        # Destroy the root window
        if self.root:
            self.root.destroy()

    def run(self):
        """Run the application main loop."""
        try:
            # Start the Tkinter main loop
            self.root.mainloop()
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            traceback.print_exc()
        finally:
            # Make sure resources are cleaned up
            self.cleanup()


def create_optimized_app():
    """Create and return an instance of the optimized application."""
    return OptimizedPropertyScraperApp()
