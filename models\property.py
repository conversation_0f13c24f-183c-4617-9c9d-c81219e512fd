"""
Property data models for the Czech Property Registry application.
"""

class Property:
    """Class representing a property"""
    
    def __init__(self, property_id=None, address=None, owner=None, property_type=None, coordinates=None):
        """Initialize a property"""
        self.property_id = property_id
        self.address = address or {}
        self.owner = owner or {}
        self.property_type = property_type
        self.coordinates = coordinates or {'lat': 0, 'lng': 0}
        self.cadastral_info = {}
        self.additional_info = {}
        
    @classmethod
    def from_dict(cls, data):
        """Create a property from a dictionary"""
        property_obj = cls(
            property_id=data.get('property_id'),
            address=data.get('address'),
            owner=data.get('owner'),
            property_type=data.get('property_type'),
            coordinates=data.get('coordinates')
        )
        
        # Add additional data
        if 'cadastral_info' in data:
            property_obj.cadastral_info = data['cadastral_info']
            
        if 'additional_info' in data:
            property_obj.additional_info = data['additional_info']
            
        return property_obj
        
    def to_dict(self):
        """Convert the property to a dictionary"""
        return {
            'property_id': self.property_id,
            'address': self.address,
            'owner': self.owner,
            'property_type': self.property_type,
            'coordinates': self.coordinates,
            'cadastral_info': self.cadastral_info,
            'additional_info': self.additional_info
        }
        
    def get_formatted_address(self):
        """Get the formatted address"""
        if isinstance(self.address, dict):
            # Try to construct from components
            components = []
            
            # Add street and number
            street = self.address.get('street', '')
            number = self.address.get('number', '')
            if street and number:
                components.append(f"{street} {number}")
            elif street:
                components.append(street)
                
            # Add city
            city = self.address.get('city', '')
            if city:
                components.append(city)
                
            # Add postal code
            postal_code = self.address.get('postal_code', '')
            if postal_code:
                components.append(postal_code)
                
            # Add country
            country = self.address.get('country', '')
            if country:
                components.append(country)
                
            # Join components
            if components:
                return ", ".join(components)
                
            # If no components, try formatted_address
            if 'formatted_address' in self.address:
                return self.address['formatted_address']
                
        # If address is a string, return it
        if isinstance(self.address, str):
            return self.address
            
        # Default
        return "Unknown address"
        
    def get_owner_name(self):
        """Get the owner name"""
        if isinstance(self.owner, dict):
            # Try to construct from components
            if 'name' in self.owner:
                return self.owner['name']
                
            # Try first and last name
            first_name = self.owner.get('first_name', '')
            last_name = self.owner.get('last_name', '')
            if first_name and last_name:
                return f"{first_name} {last_name}"
            elif last_name:
                return last_name
                
            # Try company name
            if 'company_name' in self.owner:
                return self.owner['company_name']
                
        # If owner is a string, return it
        if isinstance(self.owner, str):
            return self.owner
            
        # Default
        return "Unknown owner"
        
    def get_property_type_display(self):
        """Get the property type display name"""
        if not self.property_type:
            return "Unknown type"
            
        # Map property types to display names
        type_map = {
            'residential': 'Residential',
            'commercial': 'Commercial',
            'industrial': 'Industrial',
            'land': 'Land',
            'apartment': 'Apartment',
            'house': 'House',
            'building': 'Building'
        }
        
        return type_map.get(self.property_type.lower(), self.property_type)
        
    def __str__(self):
        """String representation"""
        return f"Property({self.property_id}: {self.get_formatted_address()})"
        
    def __repr__(self):
        """Representation"""
        return self.__str__()
