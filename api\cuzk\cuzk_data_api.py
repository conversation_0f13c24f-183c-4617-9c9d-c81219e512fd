"""
CUZK Data API Client

This module provides a client for the CUZK Data API, which provides access to
Czech geographic data such as regions, cities, districts, and cadastral areas.
"""

import requests
import logging
import json
import os
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

class CUZKDataAPI:
    """
    Client for the CUZK Data API.

    This client provides methods to fetch Czech geographic data such as
    regions, cities, districts, and cadastral areas directly from the CUZK API.
    """

    def __init__(self, app=None):
        """
        Initialize the CUZK Data API client.

        Args:
            app: The main application instance
        """
        self.app = app
        self.session = app.session if app and hasattr(app, 'session') else requests.Session()

        # Base URLs for different APIs
        self.cuzk_base_url = "https://services.cuzk.cz/api/v1"
        self.ruian_base_url = "https://ags.cuzk.cz/arcgis/rest/services/RUIAN"

        # Cache for API responses
        self.cache = {}
        self.cache_expiry = 3600  # 1 hour in seconds

        # Create cache directory if it doesn't exist
        os.makedirs("data/api_cache", exist_ok=True)

    def get_regions(self) -> List[Dict[str, Any]]:
        """
        Get a list of Czech regions.

        Returns:
            list: List of region dictionaries
        """
        cache_key = "regions"
        cached_data = self._get_from_cache(cache_key)
        if cached_data:
            return cached_data

        try:
            # For RUIAN, we need to use the ArcGIS REST API
            url = f"{self.ruian_base_url}/Vyhledavaci_sluzba_nad_daty_RUIAN/MapServer/10/query"
            params = {
                "where": "1=1",
                "outFields": "NAZEV,NUTS3,KOD",
                "returnGeometry": "false",
                "f": "json"
            }

            response = self.session.get(url, params=params)

            if response.status_code != 200:
                logger.error(f"Error fetching regions: {response.status_code}")
                return self._fallback_regions()

            data = response.json()

            # Transform the data to our format
            regions = []
            for feature in data.get('features', []):
                attrs = feature.get('attributes', {})
                regions.append({
                    'name_czech': attrs.get('NAZEV', ''),
                    'name_english': self._translate_region_to_english(attrs.get('NAZEV', '')),
                    'code': attrs.get('KOD', ''),
                    'nuts3': attrs.get('NUTS3', '')
                })

            # Cache the results
            self._save_to_cache(cache_key, regions)

            return regions
        except Exception as e:
            logger.error(f"Error fetching regions: {e}")
            return self._fallback_regions()

    def get_cities_in_region(self, region: str) -> List[Dict[str, Any]]:
        """
        Get a list of cities in a region.

        Args:
            region: Region name or code

        Returns:
            list: List of city dictionaries
        """
        cache_key = f"cities_{region}"
        cached_data = self._get_from_cache(cache_key)
        if cached_data:
            return cached_data

        try:
            # First, try to get the region code if a name was provided
            region_code = self._get_region_code(region)

            # For RUIAN, we need to use the ArcGIS REST API
            url = f"{self.ruian_base_url}/Vyhledavaci_sluzba_nad_daty_RUIAN/MapServer/1/query"
            params = {
                "where": f"VUSC_KOD='{region_code}'",
                "outFields": "NAZEV,KOD,ORP_KOD,ORP_NAZEV",
                "returnGeometry": "false",
                "f": "json"
            }

            response = self.session.get(url, params=params)

            if response.status_code != 200:
                logger.error(f"Error fetching cities: {response.status_code}")
                return self._fallback_cities(region)

            data = response.json()

            # Transform the data to our format
            cities = []
            for feature in data.get('features', []):
                attrs = feature.get('attributes', {})
                cities.append({
                    'name': attrs.get('NAZEV', ''),
                    'code': attrs.get('KOD', ''),
                    'district_code': attrs.get('ORP_KOD', ''),
                    'district_name': attrs.get('ORP_NAZEV', '')
                })

            # Cache the results
            self._save_to_cache(cache_key, cities)

            return cities
        except Exception as e:
            logger.error(f"Error fetching cities in region {region}: {e}")
            return self._fallback_cities(region)

    def get_districts_in_city(self, city: str) -> List[Dict[str, Any]]:
        """
        Get a list of districts in a city.

        Args:
            city: City name

        Returns:
            list: List of district dictionaries
        """
        cache_key = f"districts_{city}"
        cached_data = self._get_from_cache(cache_key)
        if cached_data:
            return cached_data

        try:
            # First, try to get the city code if a name was provided
            city_code = self._get_city_code(city)

            # For RUIAN, we need to use the ArcGIS REST API
            url = f"{self.ruian_base_url}/Vyhledavaci_sluzba_nad_daty_RUIAN/MapServer/2/query"
            params = {
                "where": f"OBEC_KOD='{city_code}'",
                "outFields": "NAZEV,KOD",
                "returnGeometry": "false",
                "f": "json"
            }

            response = self.session.get(url, params=params)

            if response.status_code != 200:
                logger.error(f"Error fetching districts: {response.status_code}")
                return self._fallback_districts(city)

            data = response.json()

            # Transform the data to our format
            districts = []
            for feature in data.get('features', []):
                attrs = feature.get('attributes', {})
                districts.append({
                    'name': attrs.get('NAZEV', ''),
                    'code': attrs.get('KOD', '')
                })

            # Cache the results
            self._save_to_cache(cache_key, districts)

            return districts
        except Exception as e:
            logger.error(f"Error fetching districts in city {city}: {e}")
            return self._fallback_districts(city)

    def get_cadastral_areas_in_city(self, city: str, district: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get a list of cadastral areas in a city or district.

        Args:
            city: City name
            district: District name (optional)

        Returns:
            list: List of cadastral area dictionaries
        """
        cache_key = f"cadastral_{city}_{district or ''}"
        cached_data = self._get_from_cache(cache_key)
        if cached_data:
            return cached_data

        try:
            # First, try to get the city code if a name was provided
            city_code = self._get_city_code(city)

            # For RUIAN, we need to use the ArcGIS REST API
            url = f"{self.ruian_base_url}/Vyhledavaci_sluzba_nad_daty_RUIAN/MapServer/8/query"
            params = {
                "where": f"OBEC_KOD='{city_code}'",
                "outFields": "NAZEV,KOD,OBEC_KOD,OBEC_NAZEV",
                "returnGeometry": "false",
                "f": "json"
            }

            # If district is provided, add it to the query
            if district:
                district_code = self._get_district_code(district, city)
                if district_code:
                    params["where"] += f" AND CAST_KOD='{district_code}'"

            response = self.session.get(url, params=params)

            if response.status_code != 200:
                logger.error(f"Error fetching cadastral areas: {response.status_code}")
                return self._fallback_cadastral_areas(city, district)

            data = response.json()

            # Transform the data to our format
            cadastral_areas = []
            for feature in data.get('features', []):
                attrs = feature.get('attributes', {})
                cadastral_areas.append({
                    'name': attrs.get('NAZEV', ''),
                    'code': attrs.get('KOD', ''),
                    'city_code': attrs.get('OBEC_KOD', ''),
                    'city_name': attrs.get('OBEC_NAZEV', '')
                })

            # Cache the results
            self._save_to_cache(cache_key, cadastral_areas)

            return cadastral_areas
        except Exception as e:
            logger.error(f"Error fetching cadastral areas in city {city}, district {district}: {e}")
            return self._fallback_cadastral_areas(city, district)

    def get_property_types(self) -> List[str]:
        """
        Get a list of property types.

        Returns:
            list: List of property type strings
        """
        cache_key = "property_types"
        cached_data = self._get_from_cache(cache_key)
        if cached_data:
            return cached_data

        try:
            # This is a static list as RUIAN doesn't provide a direct API for this
            property_types = [
                "Bytový dům",
                "Rodinný dům",
                "Stavba pro rodinnou rekreaci",
                "Stavba pro obchod",
                "Stavba občanského vybavení",
                "Stavba pro výrobu a skladování",
                "Zemědělská stavba",
                "Stavba pro administrativu",
                "Stavba technického vybavení",
                "Garáž",
                "Jiná stavba"
            ]

            # Cache the results
            self._save_to_cache(cache_key, property_types)

            return property_types
        except Exception as e:
            logger.error(f"Error fetching property types: {e}")
            return []

    def _get_region_code(self, region: str) -> str:
        """
        Get the region code for a region name.

        Args:
            region: Region name

        Returns:
            str: Region code
        """
        # If it looks like a code already, return it
        if region.isdigit():
            return region

        # Get all regions
        regions = self.get_regions()

        # Find the region by name
        for r in regions:
            if r['name_czech'].lower() == region.lower() or r['name_english'].lower() == region.lower():
                return r['code']

        # If not found, return empty string
        return ""

    def _get_city_code(self, city: str) -> str:
        """
        Get the city code for a city name.

        Args:
            city: City name

        Returns:
            str: City code
        """
        # If it looks like a code already, return it
        if city.isdigit():
            return city

        # Try to find the city in all regions
        regions = self.get_regions()
        for region in regions:
            cities = self.get_cities_in_region(region['code'])
            for c in cities:
                if c['name'].lower() == city.lower():
                    return c['code']

        # If not found, return empty string
        return ""

    def _get_district_code(self, district: str, city: str) -> str:
        """
        Get the district code for a district name.

        Args:
            district: District name
            city: City name

        Returns:
            str: District code
        """
        # If it looks like a code already, return it
        if district.isdigit():
            return district

        # Get districts in the city
        districts = self.get_districts_in_city(city)

        # Find the district by name
        for d in districts:
            if d['name'].lower() == district.lower():
                return d['code']

        # If not found, return empty string
        return ""

    def _translate_region_to_english(self, czech_name: str) -> str:
        """
        Translate a Czech region name to English.

        Args:
            czech_name: Czech region name

        Returns:
            str: English region name
        """
        # Mapping of Czech region names to English
        mapping = {
            "hlavní město praha": "Prague",
            "praha": "Prague",
            "středočeský kraj": "Central Bohemian Region",
            "jihočeský kraj": "South Bohemian Region",
            "plzeňský kraj": "Plzeň Region",
            "karlovarský kraj": "Karlovy Vary Region",
            "ústecký kraj": "Ústí nad Labem Region",
            "liberecký kraj": "Liberec Region",
            "královéhradecký kraj": "Hradec Králové Region",
            "pardubický kraj": "Pardubice Region",
            "kraj vysočina": "Vysočina Region",
            "jihomoravský kraj": "South Moravian Region",
            "olomoucký kraj": "Olomouc Region",
            "zlínský kraj": "Zlín Region",
            "moravskoslezský kraj": "Moravian-Silesian Region"
        }

        return mapping.get(czech_name.lower(), czech_name)

    def _get_from_cache(self, key: str) -> Any:
        """
        Get data from cache.

        Args:
            key: Cache key

        Returns:
            Any: Cached data or None if not found
        """
        # First check memory cache
        if key in self.cache:
            return self.cache[key]

        # Then check disk cache
        cache_file = os.path.join("data/api_cache", f"{key}.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Store in memory cache
                    self.cache[key] = data
                    return data
            except Exception as e:
                logger.error(f"Error loading from cache: {e}")

        return None

    def _save_to_cache(self, key: str, data: Any) -> None:
        """
        Save data to cache.

        Args:
            key: Cache key
            data: Data to cache
        """
        # Save to memory cache
        self.cache[key] = data

        # Save to disk cache
        cache_file = os.path.join("data/api_cache", f"{key}.json")
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving to cache: {e}")

    def _fallback_regions(self) -> List[Dict[str, Any]]:
        """
        Fallback method to get regions when API fails.

        Returns:
            list: List of region dictionaries
        """
        # Check if we have a cached file
        cache_file = os.path.join("czech_data", "regions.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # Transform to our format
                    regions = []
                    czech_regions = data.get("regions_czech", [])
                    english_regions = data.get("regions_english", [])
                    region_codes = data.get("region_codes", [])

                    for i in range(len(czech_regions)):
                        regions.append({
                            'name_czech': czech_regions[i],
                            'name_english': english_regions[i] if i < len(english_regions) else "",
                            'code': region_codes[i] if i < len(region_codes) else "",
                            'nuts3': ""
                        })

                    return regions
            except Exception as e:
                logger.error(f"Error loading regions from file: {e}")

        # If all else fails, return an empty list
        return []

    def _fallback_cities(self, region: str) -> List[Dict[str, Any]]:
        """
        Fallback method to get cities when API fails.

        Args:
            region: Region name or code

        Returns:
            list: List of city dictionaries
        """
        # Check if we have a cached file
        cache_file = os.path.join("czech_data", "cities.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # Transform to our format
                    cities = []
                    cities_by_region = data.get("cities_by_region", {})

                    # Try to find cities for this region
                    region_cities = cities_by_region.get(region, [])

                    for city_name in region_cities:
                        cities.append({
                            'name': city_name,
                            'code': "",
                            'district_code': "",
                            'district_name': ""
                        })

                    return cities
            except Exception as e:
                logger.error(f"Error loading cities from file: {e}")

        # If all else fails, return an empty list
        return []

    def _fallback_districts(self, city: str) -> List[Dict[str, Any]]:
        """
        Fallback method to get districts when API fails.

        Args:
            city: City name

        Returns:
            list: List of district dictionaries
        """
        # Check if we have a cached file
        cache_file = os.path.join("czech_data", "districts.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # Transform to our format
                    districts = []
                    districts_by_city = data.get("districts_by_city", {})

                    # Try to find districts for this city
                    city_districts = districts_by_city.get(city, [])

                    for district_name in city_districts:
                        districts.append({
                            'name': district_name,
                            'code': ""
                        })

                    return districts
            except Exception as e:
                logger.error(f"Error loading districts from file: {e}")

        # If all else fails, return an empty list
        return []

    def _fallback_cadastral_areas(self, city: str, district: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Fallback method to get cadastral areas when API fails.

        Args:
            city: City name
            district: District name (optional)

        Returns:
            list: List of cadastral area dictionaries
        """
        # Check if we have a cached file
        cache_file = os.path.join("czech_data", "cadastral_areas.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # Transform to our format
                    cadastral_areas = []

                    # Try to find cadastral areas for this city/district
                    if district:
                        areas_by_district = data.get("cadastral_areas_by_district", {})
                        area_names = areas_by_district.get(district, [])
                    else:
                        areas_by_city = data.get("cadastral_areas_by_city", {})
                        area_names = areas_by_city.get(city, [])

                    for area_name in area_names:
                        cadastral_areas.append({
                            'name': area_name,
                            'code': "",
                            'city_code': "",
                            'city_name': city
                        })

                    return cadastral_areas
            except Exception as e:
                logger.error(f"Error loading cadastral areas from file: {e}")

        # If all else fails, return an empty list
        return []
