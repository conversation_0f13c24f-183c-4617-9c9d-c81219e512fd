import os
import json
# Import necessary modules
import requests
import logging
from typing import List, Dict, Any, Tuple
from bs4 import BeautifulSoup
from ui.message_boxes import MessageBoxes

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DataSourceManager")

class DataSourceManager:
    """
    Manages data sources for the Czech Property Registry application.
    Implements a hierarchy of data sources:
    1. Google Maps API (primary)
    2. Wikipedia API (secondary)

    No fallback data is used - all data must come from real sources.
    """

    def __init__(self, google_maps_api=None, root=None, app=None):
        """
        Initialize the data source manager.

        Args:
            google_maps_api: Google Maps API instance
            root: Tkinter root window for displaying notifications
            app: The main application instance
        """
        self.google_maps_api = google_maps_api
        self.root = root
        self.app = app
        self.data_dir = "czech_data"
        self.data_sources = {
            "regions": {"current": None, "source": None},
            "cities": {"current": None, "source": None},
            "districts": {"current": None, "source": None},
            "streets": {"current": None, "source": None},
            "cadastral_territories": {"current": None, "source": None}
        }

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # Load all data sources
        self._load_all_data()

    def _load_all_data(self):
        """Load all data from JSON files"""
        try:
            # Load regions data
            self._load_json_data("regions")

            # Load cities data
            self._load_json_data("cities")

            # Load districts data
            self._load_json_data("districts")

            # Load addresses data (streets)
            if os.path.exists(os.path.join(self.data_dir, "addresses.json")):
                with open(os.path.join(self.data_dir, "addresses.json"), 'r', encoding='utf-8') as f:
                    addresses_data = json.load(f)
                    self.data_sources["streets"]["current"] = addresses_data.get("streets", [])
                    self.data_sources["streets"]["source"] = "local_json"

            # Load cadastral areas data
            self._load_json_data("cadastral_areas", "cadastral_territories")

        except Exception as e:
            logger.error(f"Error loading data: {e}")

    def _load_json_data(self, file_name, data_key=None):
        """
        Load data from a JSON file.

        Args:
            file_name: Name of the JSON file (without extension)
            data_key: Key to use in data_sources (defaults to file_name)
        """
        if data_key is None:
            data_key = file_name

        file_path = os.path.join(self.data_dir, f"{file_name}.json")
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.data_sources[data_key]["current"] = data
                    self.data_sources[data_key]["source"] = "local_json"
                    logger.info(f"Loaded {data_key} data from {file_path}")
            except Exception as e:
                logger.error(f"Error loading {file_path}: {e}")

    def notify_user(self, message, title="Data Source Notification"):
        """
        Notify the user about data source changes.

        Args:
            message: Message to display
            title: Notification title
        """
        if self.root:
            MessageBoxes.show_info(title, message)
        logger.info(f"{title}: {message}")

    def get_regions(self, language="english") -> Tuple[List[str], str]:
        """
        Get a list of Czech regions.

        Args:
            language: Language of the regions ("english" or "czech")

        Returns:
            Tuple of (regions list, data source)

        Raises:
            ValueError: If regions cannot be retrieved
        """
        # Try to use the CUZK Data API first
        if hasattr(self.app, 'cuzk_data_api'):
            try:
                regions_data = self.app.cuzk_data_api.get_regions()
                if regions_data:
                    # Extract region names based on language
                    key = "name_english" if language == "english" else "name_czech"
                    regions = [region[key] for region in regions_data]
                    if regions and len(regions) > 0:
                        logger.info(f"Using regions data from CUZK API ({len(regions)} regions)")
                        return regions, "cuzk_api"
            except Exception as e:
                logger.warning(f"Error getting regions from CUZK API: {e}")
                # Continue to other sources

        # Try to get regions from data manager next
        if hasattr(self.app, 'data_manager'):
            try:
                regions_data = self.app.data_manager.load_czech_data('regions')
                if regions_data:
                    key = "regions_english" if language == "english" else "regions_czech"
                    regions = regions_data.get(key, [])
                    if regions and len(regions) > 0:
                        logger.info(f"Using cached regions data ({len(regions)} regions)")
                        return regions, "cached"
            except Exception as e:
                logger.warning(f"Error getting regions from data manager: {e}")
                # Continue to other sources

        # Try Google Maps API next
        if self.google_maps_api:
            try:
                regions = self.google_maps_api.get_czech_regions()
                if regions and len(regions) > 0:
                    # Cache the data for future use
                    if hasattr(self.app, 'data_manager'):
                        try:
                            # Force a refresh of regions data
                            self.app.data_manager.force_refresh('regions')
                        except Exception as e:
                            logger.warning(f"Error refreshing regions data: {e}")

                    return regions, "google_maps"
            except Exception as e:
                logger.warning(f"Error getting regions from Google Maps API: {e}")

        # Try Wikipedia API next
        try:
            regions = self._get_regions_from_wikipedia(language)
            if regions and len(regions) > 0:
                return regions, "wikipedia"
        except Exception as e:
            logger.warning(f"Error getting regions from Wikipedia API: {e}")

        # No fallback - if we can't get data from real sources, fail
        error_msg = "No region data available. Please check your internet connection and try again."
        logger.error(error_msg)
        self.notify_user(error_msg)
        raise ValueError(error_msg)

    def _get_regions_from_wikipedia(self, language="english") -> List[str]:
        """
        Get regions from Wikipedia API.

        Args:
            language: Language of the regions ("english" or "czech")

        Returns:
            List of regions
        """
        try:
            # Use the Wikipedia API to get the list of Czech regions
            url = "https://en.wikipedia.org/w/api.php"

            # Parameters for the API request
            params = {
                "action": "parse",
                "page": "Regions of the Czech Republic",
                "format": "json",
                "prop": "text",
                "section": 0  # Get the main section
            }

            # Make the API request
            response = requests.get(url, params=params)
            data = response.json()

            if "parse" in data and "text" in data["parse"]:
                # Parse the HTML content
                html_content = data["parse"]["text"]["*"]
                soup = BeautifulSoup(html_content, "html.parser")

                # Find the table with regions
                region_table = None
                for table in soup.find_all("table", {"class": "wikitable"}):
                    if "Region" in table.text and "Capital" in table.text:
                        region_table = table
                        break

                if region_table:
                    # Extract region names from the table
                    regions = []
                    for row in region_table.find_all("tr")[1:]:  # Skip header row
                        cells = row.find_all("td")
                        if len(cells) >= 2:
                            region_name = cells[0].text.strip()
                            # Clean up the region name
                            region_name = region_name.replace("\n", "").replace("[note 1]", "").strip()
                            if region_name and "Region" in region_name:
                                regions.append(region_name)

                    # Add Prague if it's not already in the list
                    if "Prague" not in regions:
                        regions.append("Prague")

                    if regions:
                        logger.info(f"Found {len(regions)} regions from Wikipedia")
                        return regions

            # If we couldn't get regions from the main approach, try a different one
            # Use the Wikipedia API to search for Czech regions
            params = {
                "action": "query",
                "list": "search",
                "srsearch": "Czech Republic regions",
                "format": "json",
                "srlimit": 20
            }

            response = requests.get(url, params=params)
            data = response.json()

            if "query" in data and "search" in data["query"]:
                # Extract region names from search results
                regions = []
                for result in data["query"]["search"]:
                    title = result["title"]
                    if "Region" in title and "Czech" in title:
                        # Extract just the region name
                        region_name = title.replace("Czech Republic", "").replace("Czech", "").strip()
                        if region_name.endswith("Region"):
                            regions.append(region_name)

                # Add Prague if it's not already in the list
                if "Prague" not in regions:
                    regions.append("Prague")

                if regions:
                    logger.info(f"Found {len(regions)} regions from Wikipedia search")
                    return regions

        except Exception as e:
            logger.error(f"Error getting regions from Wikipedia: {e}")

        # If all approaches failed, return an empty list
        # This will cause the calling method to raise an error
        return []

    def get_cities_in_region(self, region: str) -> Tuple[List[str], str]:
        """
        Get a list of cities in a region.

        Args:
            region: Region name

        Returns:
            Tuple of (cities list, data source)

        Raises:
            ValueError: If cities cannot be retrieved
        """
        # Try to use the CUZK Data API first
        if hasattr(self.app, 'cuzk_data_api'):
            try:
                cities_data = self.app.cuzk_data_api.get_cities_in_region(region)
                if cities_data:
                    # Extract city names
                    cities = [city['name'] for city in cities_data]
                    if cities and len(cities) > 0:
                        logger.info(f"Using cities data from CUZK API for {region} ({len(cities)} cities)")
                        return cities, "cuzk_api"
            except Exception as e:
                logger.warning(f"Error getting cities from CUZK API: {e}")
                # Continue to other sources

        # Try to get cities from data manager next
        if hasattr(self.app, 'data_manager'):
            try:
                cities_data = self.app.data_manager.load_czech_data('cities')
                if cities_data and "cities_by_region" in cities_data:
                    cities = cities_data["cities_by_region"].get(region, [])
                    if cities and len(cities) > 0:
                        logger.info(f"Using cached cities data for {region} ({len(cities)} cities)")
                        return cities, "cached"
            except Exception as e:
                logger.warning(f"Error getting cities from data manager: {e}")
                # Continue to other sources

        # Try Google Maps API next
        if self.google_maps_api:
            try:
                cities = self.google_maps_api.get_cities_in_region(region)
                if cities and len(cities) > 0:
                    # Cache the data for future use
                    if hasattr(self.app, 'data_manager'):
                        try:
                            # Force a refresh of cities data
                            self.app.data_manager.force_refresh('cities')
                        except Exception as e:
                            logger.warning(f"Error refreshing cities data: {e}")

                    return cities, "google_maps"
            except Exception as e:
                logger.warning(f"Error getting cities from Google Maps API: {e}")

        # Try Wikipedia API next
        try:
            cities = self._get_cities_from_wikipedia(region)
            if cities and len(cities) > 0:
                return cities, "wikipedia"
        except Exception as e:
            logger.warning(f"Error getting cities from Wikipedia API: {e}")

        # No fallback - if we can't get data from real sources, fail
        error_msg = f"No city data available for {region}. Please check your internet connection and try again."
        logger.error(error_msg)
        self.notify_user(error_msg)
        raise ValueError(error_msg)

    def _get_cities_from_wikipedia(self, region: str) -> List[str]:
        """
        Get cities from Wikipedia API.

        Args:
            region: Region name

        Returns:
            List of cities
        """
        try:
            # Use the Wikipedia API to search for cities in the region
            url = "https://en.wikipedia.org/w/api.php"

            # Clean up the region name for the search
            search_region = region.replace(" Region", "").strip()

            # Parameters for the API request
            params = {
                "action": "query",
                "list": "search",
                "srsearch": f"cities in {search_region} Czech Republic",
                "format": "json",
                "srlimit": 50
            }

            # Make the API request
            response = requests.get(url, params=params)
            data = response.json()

            if "query" in data and "search" in data["query"]:
                # Extract city names from search results
                cities = []
                for result in data["query"]["search"]:
                    title = result["title"]
                    # Filter out results that don't look like cities
                    if "city" in title.lower() or "town" in title.lower() or "municipality" in title.lower():
                        # Extract just the city name
                        city_name = title.split("(")[0].strip()
                        if city_name and city_name not in cities:
                            cities.append(city_name)

                if cities:
                    logger.info(f"Found {len(cities)} cities in {region} from Wikipedia search")
                    return cities

            # If the search approach didn't work, try to get the region's Wikipedia page
            # and extract cities from there
            if "Prague" in region:
                # Special case for Prague
                params = {
                    "action": "parse",
                    "page": "Prague",
                    "format": "json",
                    "prop": "text",
                    "section": 0  # Get the main section
                }
            else:
                params = {
                    "action": "parse",
                    "page": region,
                    "format": "json",
                    "prop": "text",
                    "section": 0  # Get the main section
                }

            response = requests.get(url, params=params)
            data = response.json()

            if "parse" in data and "text" in data["parse"]:
                # Parse the HTML content
                html_content = data["parse"]["text"]["*"]
                soup = BeautifulSoup(html_content, "html.parser")

                # Look for lists that might contain cities
                cities = []
                for list_elem in soup.find_all(["ul", "ol"]):
                    for item in list_elem.find_all("li"):
                        text = item.text.strip()
                        # Check if this looks like a city
                        if len(text) > 3 and "," not in text and "." not in text:
                            # Extract just the city name
                            city_name = text.split("(")[0].strip()
                            if city_name and city_name not in cities:
                                cities.append(city_name)

                if cities:
                    logger.info(f"Found {len(cities)} cities in {region} from Wikipedia page")
                    return cities

        except Exception as e:
            logger.error(f"Error getting cities from Wikipedia for {region}: {e}")

        # If all approaches failed, return an empty list
        # This will cause the calling method to raise an error
        return []

    def get_districts_in_city(self, city: str) -> Tuple[List[str], str]:
        """
        Get a list of districts in a city.

        Args:
            city: City name

        Returns:
            Tuple of (districts list, data source)

        Raises:
            ValueError: If districts cannot be retrieved
        """
        # Try to use the CUZK Data API first
        if hasattr(self.app, 'cuzk_data_api'):
            try:
                districts_data = self.app.cuzk_data_api.get_districts_in_city(city)
                if districts_data:
                    # Extract district names
                    districts = [district['name'] for district in districts_data]
                    if districts and len(districts) > 0:
                        logger.info(f"Using districts data from CUZK API for {city} ({len(districts)} districts)")
                        return districts, "cuzk_api"
            except Exception as e:
                logger.warning(f"Error getting districts from CUZK API: {e}")
                # Continue to other sources

        # Try to get districts from data manager next
        if hasattr(self.app, 'data_manager'):
            try:
                districts_data = self.app.data_manager.load_czech_data('districts')
                if districts_data and "districts_by_city" in districts_data:
                    districts = districts_data["districts_by_city"].get(city, [])
                    if districts and len(districts) > 0:
                        logger.info(f"Using cached districts data for {city} ({len(districts)} districts)")
                        return districts, "cached"
            except Exception as e:
                logger.warning(f"Error getting districts from data manager: {e}")
                # Continue to other sources

        # Try Google Maps API next
        if self.google_maps_api:
            try:
                # Google Maps doesn't have a direct API for districts, so we'll use a search
                districts = self.google_maps_api.get_place_predictions(
                    f"districts in {city} Czech Republic",
                    types='(regions)'
                )
                if districts and len(districts) > 0:
                    # Cache the data for future use
                    if hasattr(self.app, 'data_manager'):
                        try:
                            # Force a refresh of districts data
                            self.app.data_manager.force_refresh('districts')
                        except Exception as e:
                            logger.warning(f"Error refreshing districts data: {e}")

                    return districts, "google_maps"
            except Exception as e:
                logger.warning(f"Error getting districts from Google Maps API: {e}")

        # No fallback - if we can't get data from real sources, fail
        error_msg = f"No district data available for {city}. Please check your internet connection and try again."
        logger.error(error_msg)
        self.notify_user(error_msg)
        raise ValueError(error_msg)

    def get_cadastral_areas_in_city(self, city: str, district: str = None) -> Tuple[List[str], str]:
        """
        Get a list of cadastral areas in a city or district.

        Args:
            city: City name
            district: District name (optional)

        Returns:
            Tuple of (cadastral areas list, data source)

        Raises:
            ValueError: If cadastral areas cannot be retrieved
        """
        # Try to use the CUZK Data API first
        if hasattr(self.app, 'cuzk_data_api'):
            try:
                cadastral_data = self.app.cuzk_data_api.get_cadastral_areas_in_city(city, district)
                if cadastral_data:
                    # Extract cadastral area names
                    cadastral_areas = [area['name'] for area in cadastral_data]
                    if cadastral_areas and len(cadastral_areas) > 0:
                        logger.info(f"Using cadastral areas data from CUZK API for {city} ({len(cadastral_areas)} areas)")
                        return cadastral_areas, "cuzk_api"
            except Exception as e:
                logger.warning(f"Error getting cadastral areas from CUZK API: {e}")
                # Continue to other sources

        # Try to get cadastral areas from data manager next
        if hasattr(self.app, 'data_manager'):
            try:
                cadastral_data = self.app.data_manager.load_czech_data('cadastral')
                if cadastral_data:
                    if district and "cadastral_areas_by_district" in cadastral_data:
                        areas = cadastral_data["cadastral_areas_by_district"].get(district, [])
                    elif "cadastral_areas_by_city" in cadastral_data:
                        areas = cadastral_data["cadastral_areas_by_city"].get(city, [])
                    else:
                        areas = []

                    if areas and len(areas) > 0:
                        logger.info(f"Using cached cadastral areas data for {city} ({len(areas)} areas)")
                        return areas, "cached"
            except Exception as e:
                logger.warning(f"Error getting cadastral areas from data manager: {e}")
                # Continue to other sources

        # No fallback - if we can't get data from real sources, fail
        error_msg = f"No cadastral area data available for {city}. Please check your internet connection and try again."
        logger.error(error_msg)
        self.notify_user(error_msg)
        raise ValueError(error_msg)

    def get_real_property_data(self, api_client, location: str = None, radius: int = None) -> List[Dict[str, Any]]:
        """
        Get real property data from API instead of generating sample data.

        Args:
            api_client: API client to use for fetching data
            location: Location to search in (optional)
            radius: Search radius in meters (optional)

        Returns:
            List of property data dictionaries from the API

        Raises:
            NotImplementedError: Sample data generation is no longer supported
        """
        if not api_client:
            error_msg = "API client is required to fetch real property data"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # This method should be implemented to use the API client
        # to fetch real property data instead of generating sample data
        logger.info(f"Fetching real property data for location: {location}, radius: {radius}")

        # The implementation would depend on the specific API client
        # and how it fetches property data

        # For now, we'll raise an error to indicate that sample data
        # is no longer supported
        error_msg = "Sample property data generation is no longer supported. Use real data from API."
        logger.error(error_msg)
        raise NotImplementedError(error_msg)
