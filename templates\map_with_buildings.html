<!DOCTYPE html>
<html>
<head>
    <style>
        body { margin: 0; padding: 0; height: 100%; }
        #map { width: 100%; height: 100%; }
        .info-window { max-width: 300px; }
    </style>
</head>
<body>
    <div id="map"></div>
    <script>
        function initMap() {
            var center = {lat: {center_lat}, lng: {center_lng}};
            var map = new google.maps.Map(document.getElementById('map'), {
                zoom: 16,
                center: center
            });

            // Add a marker at the center
            var centerMarker = new google.maps.Marker({
                position: center,
                map: map,
                title: 'Search Location',
                icon: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png'
            });

            // Add markers for each building
            var buildings = {buildings_json};
            var infoWindow = new google.maps.InfoWindow();
            
            buildings.forEach(function(building) {
                if (building.lat && building.lng) {
                    var marker = new google.maps.Marker({
                        position: {lat: parseFloat(building.lat), lng: parseFloat(building.lng)},
                        map: map,
                        title: building.name || 'Building'
                    });
                    
                    marker.addListener('click', function() {
                        infoWindow.setContent(building.info_content);
                        infoWindow.open(map, marker);
                    });
                }
            });
        }
    </script>
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={api_key}&callback=initMap">
    </script>
</body>
</html>
