"""
Simple test for OSM integration.

This script tests the OSM integration to verify that it can fetch buildings
from OpenStreetMap and extract RUIAN IDs.
"""

import os
import sys
import logging

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OSM_Test")

# Import the necessary modules
from api.osm.osm_integration import OSMIntegration
from utils.enhanced_cache_manager import EnhancedCacheManager
from core.optimized_network import create_optimized_session


class MockApp:
    """Mock application for testing."""
    
    def __init__(self):
        """Initialize the mock application."""
        # Create a session
        self.session = create_optimized_session()
        
        # Create a cache manager
        self.cache_manager = EnhancedCacheManager(
            default_expiry=300,  # 5 minutes
            disk_cache_dir="test_cache",
            max_memory_items=100
        )
        
        # Status tracking
        self.status = ""
    
    def show_status(self, message):
        """Show a status message."""
        self.status = message
        logger.info(f"Status: {message}")


def test_osm_integration():
    """Test the OSM integration."""
    try:
        # Create a mock application
        app = MockApp()
        
        # Create the OSM integration
        osm_integration = OSMIntegration(app)
        
        # Set up the app with the integration
        app.osm_integration = osm_integration
        
        # Coordinates for testing (Prague)
        test_lat = 50.0755
        test_lng = 14.4378
        test_radius = 500  # meters
        
        # Test fetching buildings from OSM
        logger.info(f"Testing OSM building fetch at coordinates {test_lat}, {test_lng}")
        buildings = osm_integration.search_buildings_by_coordinates(test_lat, test_lng, test_radius)
        
        # Verify that buildings were found
        if not buildings:
            logger.error("No buildings returned from OSM")
            return
        
        logger.info(f"Found {len(buildings)} buildings from OSM")
        
        # Check if any buildings have RUIAN references
        buildings_with_ruian = [b for b in buildings if 'ruian_ref' in b]
        logger.info(f"Found {len(buildings_with_ruian)} buildings with RUIAN references")
        
        # Print details of buildings with RUIAN references
        for i, building in enumerate(buildings_with_ruian[:5]):  # Show first 5 buildings
            logger.info(f"Building {i+1}:")
            logger.info(f"  Name: {building.get('name', 'Unknown')}")
            logger.info(f"  Address: {building.get('address', 'Unknown')}")
            logger.info(f"  RUIAN ID: {building.get('ruian_ref', 'Unknown')}")
            logger.info(f"  OSM ID: {building.get('osm_id', 'Unknown')}")
            logger.info(f"  Type: {building.get('osm_type', 'Unknown')}")
            logger.info(f"  Coordinates: {building.get('lat', 'Unknown')}, {building.get('lng', 'Unknown')}")
            logger.info("  Tags:")
            for key, value in building.get('tags', {}).items():
                logger.info(f"    {key}: {value}")
            logger.info("")
        
        # Test successful
        logger.info("OSM integration test completed successfully")
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)


if __name__ == '__main__':
    test_osm_integration()
