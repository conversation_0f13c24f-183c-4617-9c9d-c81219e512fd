#!/usr/bin/env python
"""
Launcher for the Czech Property Registry application.

This script serves as the main entry point for the application.
By default, it launches the standalone batch search application.
Use the --full-app flag to launch the full application instead.

Usage:
    python launcher.py [--full-app] [--city CITY]
"""

import sys
import os
import argparse
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("launcher.log"),
        logging.StreamHandler()
    ]
)

# Create a logger for this module
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Czech Property Registry Launcher")

    parser.add_argument(
        "--full-app",
        action="store_true",
        help="Launch the full application instead of the standalone app"
    )

    parser.add_argument(
        "--city",
        type=str,
        help="City to search for (only used with standalone app)"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )

    return parser.parse_args()


def setup_logging(debug=False):
    """Set up logging with the appropriate level."""
    level = logging.DEBUG if debug else logging.INFO

    # Configure root logger
    logging.getLogger().setLevel(level)

    logger.info(f"Logging level set to: {logging.getLevelName(level)}")


def main():
    """Main entry point for the launcher."""
    # Parse command line arguments
    args = parse_arguments()

    # Set up logging
    setup_logging(args.debug)

    # Log startup information
    logger.info("Starting Czech Property Registry Launcher")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current directory: {os.getcwd()}")

    try:
        # Determine which application to launch
        if args.full_app:
            # Launch the full application
            logger.info("Launching full application")
            app_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
            
            # Check if the file exists
            if not os.path.exists(app_path):
                logger.error(f"Full application not found at: {app_path}")
                print(f"Error: Full application not found at: {app_path}")
                return 1
                
            # Build the command
            cmd = [sys.executable, app_path]
            if args.debug:
                cmd.append("--debug")
                
            # Launch the application
            logger.info(f"Executing command: {' '.join(cmd)}")
            process = subprocess.Popen(cmd)
            
            # Wait for the process to complete
            process.wait()
            return process.returncode
        else:
            # Launch the standalone application
            logger.info("Launching standalone application")
            app_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "standalone_app.py")
            
            # Check if the file exists
            if not os.path.exists(app_path):
                logger.error(f"Standalone application not found at: {app_path}")
                print(f"Error: Standalone application not found at: {app_path}")
                return 1
                
            # Build the command
            cmd = [sys.executable, app_path]
            if args.city:
                cmd.extend(["--city", args.city])
            if args.debug:
                cmd.append("--debug")
                
            # Launch the application
            logger.info(f"Executing command: {' '.join(cmd)}")
            process = subprocess.Popen(cmd)
            
            # Wait for the process to complete
            process.wait()
            return process.returncode
    except Exception as e:
        logger.error(f"Error launching application: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
