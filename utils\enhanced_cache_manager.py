"""
Enhanced cache management utilities for the Czech Property Registry application.
Provides a more efficient caching system with tiered storage and better performance.
"""

import threading
import time
import os
import json
import pickle
import hashlib
import logging
import shutil
from typing import Dict, Any, Tuple, List, Optional, Callable, Union, Set
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("EnhancedCacheManager")


class CacheStorageType(Enum):
    """Enum for cache storage types."""
    MEMORY = 1
    DISK = 2
    BOTH = 3


class EnhancedCacheManager:
    """
    Enhanced cache manager with tiered storage and better performance.
    Provides memory and disk caching with configurable policies.
    """

    def __init__(self,
                 default_expiry: int = 300,
                 disk_cache_dir: str = "api_cache",
                 max_memory_items: int = 1000,
                 cleanup_interval: int = 300,
                 always_prefer_api: bool = True):
        """
        Initialize the enhanced cache manager.

        Args:
            default_expiry (int): Default cache expiry time in seconds (default: 300 seconds/5 minutes)
            disk_cache_dir (str): Directory for disk cache storage
            max_memory_items (int): Maximum number of items to keep in memory cache
            cleanup_interval (int): Interval for automatic cache cleanup in seconds
            always_prefer_api (bool): Whether to always prefer API data over cached data
        """
        # Memory cache storage
        self._memory_caches: Dict[str, Dict[str, Tuple[Any, float]]] = {}
        self._memory_access_times: Dict[str, Dict[str, float]] = {}  # Track last access time for LRU
        self._locks: Dict[str, threading.Lock] = {}
        self._cache_expiry: Dict[str, int] = {}  # Store custom expiry times for each cache
        self._default_expiry = default_expiry
        self._max_memory_items = max_memory_items
        self._always_prefer_api = always_prefer_api  # Always prefer API data over cached data
        self._max_cache_age = 7 * 24 * 60 * 60  # Maximum cache age in seconds (7 days)

        # Disk cache storage
        self._disk_cache_dir = disk_cache_dir
        os.makedirs(disk_cache_dir, exist_ok=True)

        # Cache configuration
        self._storage_types: Dict[str, CacheStorageType] = {}
        self._cleanup_scheduled = False
        self._cleanup_interval = cleanup_interval

        # Statistics
        self._stats = {
            'memory_hits': 0,
            'disk_hits': 0,
            'misses': 0,
            'memory_writes': 0,
            'disk_writes': 0
        }
        self._stats_lock = threading.Lock()

        logger.info(f"EnhancedCacheManager initialized with disk cache at {disk_cache_dir}")

        # Schedule cleanup
        self.schedule_cleanup(cleanup_interval)

    def create_cache(self,
                     cache_name: str,
                     expiry: Optional[int] = None,
                     storage_type: CacheStorageType = CacheStorageType.MEMORY) -> None:
        """
        Create a new cache with the given name and configuration.

        Args:
            cache_name (str): Name of the cache to create
            expiry (int, optional): Custom expiry time in seconds for this cache
            storage_type (CacheStorageType): Where to store the cache (memory, disk, or both)
        """
        if cache_name not in self._memory_caches:
            self._memory_caches[cache_name] = {}
            self._memory_access_times[cache_name] = {}
            self._locks[cache_name] = threading.Lock()
            # Set custom expiry time if provided, otherwise use default
            self._cache_expiry[cache_name] = expiry if expiry is not None else self._default_expiry
            self._storage_types[cache_name] = storage_type

            # Create disk cache directory if needed
            if storage_type in (CacheStorageType.DISK, CacheStorageType.BOTH):
                os.makedirs(os.path.join(self._disk_cache_dir, cache_name), exist_ok=True)

            logger.info(f"Created cache '{cache_name}' with storage type {storage_type.name}")

    def _get_disk_path(self, cache_name: str, key: str) -> str:
        """
        Get the disk path for a cache key.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key

        Returns:
            str: Path to the disk cache file
        """
        # Hash the key to create a safe filename
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self._disk_cache_dir, cache_name, f"{key_hash}.cache")

    def get(self, cache_name: str, key: str, force_api: bool = None) -> Optional[Any]:
        """
        Get a value from the cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            force_api (bool, optional): Force API refresh even if cache is available

        Returns:
            Any: The cached value or None if not found or expired
        """
        # Determine if we should use API data
        use_api = force_api if force_api is not None else self._always_prefer_api

        # If we're using API data, return None to force a refresh
        if use_api:
            logger.debug(f"Always preferring API data for {cache_name}/{key}")
            with self._stats_lock:
                self._stats['misses'] += 1
            return None

        if cache_name not in self._memory_caches:
            with self._stats_lock:
                self._stats['misses'] += 1
            return None

        # Try memory cache first
        memory_result = self._get_from_memory(cache_name, key)
        if memory_result is not None:
            with self._stats_lock:
                self._stats['memory_hits'] += 1
            return memory_result

        # If not in memory and disk storage is enabled, try disk
        storage_type = self._storage_types.get(cache_name, CacheStorageType.MEMORY)
        if storage_type in (CacheStorageType.DISK, CacheStorageType.BOTH):
            disk_result = self._get_from_disk(cache_name, key)
            if disk_result is not None:
                # If found on disk, also cache in memory for faster access next time
                if storage_type == CacheStorageType.BOTH:
                    expiry_time = time.time() + self._cache_expiry.get(cache_name, self._default_expiry)
                    self._set_in_memory(cache_name, key, disk_result, expiry_time)

                with self._stats_lock:
                    self._stats['disk_hits'] += 1
                return disk_result

        # Not found in any cache
        with self._stats_lock:
            self._stats['misses'] += 1
        return None

    def set_api_preference(self, prefer_api: bool = True, static_cache_names: List[str] = None) -> None:
        """
        Set whether to always prefer API data over cached data.
        For static data caches, we can keep the preference to use cache first.

        Args:
            prefer_api (bool): Whether to always prefer API data (default: True)
            static_cache_names (List[str], optional): List of cache names that contain static data
                                                     and should always prefer cache first
        """
        previous_value = self._always_prefer_api
        self._always_prefer_api = prefer_api
        logger.info(f"API preference changed: {previous_value} -> {prefer_api}")

        # If we're now preferring API, clear non-static caches to force fresh data
        if prefer_api and not previous_value:
            if static_cache_names:
                logger.info(f"Now preferring API data, clearing all caches except static caches: {static_cache_names}")
                for cache_name in list(self._memory_caches.keys()):
                    if cache_name not in static_cache_names:
                        self.clear(cache_name)
            else:
                logger.info("Now preferring API data, clearing all caches")
                self.clear_all()

    def _get_from_memory(self, cache_name: str, key: str) -> Optional[Any]:
        """
        Get a value from the memory cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key

        Returns:
            Any: The cached value or None if not found or expired
        """
        with self._locks[cache_name]:
            if key in self._memory_caches[cache_name]:
                value, expiry = self._memory_caches[cache_name][key]
                if time.time() < expiry:
                    # Update last access time for LRU
                    self._memory_access_times[cache_name][key] = time.time()
                    return value
                else:
                    # Remove expired entry
                    del self._memory_caches[cache_name][key]
                    if key in self._memory_access_times[cache_name]:
                        del self._memory_access_times[cache_name][key]
        return None

    def _get_from_disk(self, cache_name: str, key: str) -> Optional[Any]:
        """
        Get a value from the disk cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key

        Returns:
            Any: The cached value or None if not found or expired
        """
        disk_path = self._get_disk_path(cache_name, key)
        if not os.path.exists(disk_path):
            return None

        try:
            with open(disk_path, 'rb') as f:
                data = pickle.load(f)

            # Check if expired
            if 'expiry' in data and time.time() >= data['expiry']:
                # Remove expired file
                os.remove(disk_path)
                return None

            return data['value']
        except (IOError, pickle.PickleError, KeyError) as e:
            logger.warning(f"Error reading disk cache for {cache_name}/{key}: {e}")
            # Remove corrupted file
            try:
                os.remove(disk_path)
            except OSError:
                pass
            return None

    def set(self,
            cache_name: str,
            key: str,
            value: Any,
            expiry: Optional[int] = None,
            storage_type: Optional[CacheStorageType] = None) -> None:
        """
        Set a value in the cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            value (Any): Value to cache
            expiry (int, optional): Expiry time in seconds. If None, uses cache-specific or default expiry.
            storage_type (CacheStorageType, optional): Override the storage type for this specific item
        """
        if cache_name not in self._memory_caches:
            self.create_cache(cache_name)

        # Use provided expiry time, or cache-specific expiry, or default expiry
        if expiry is None:
            expiry = self._cache_expiry.get(cache_name, self._default_expiry)

        # Calculate expiry timestamp
        expiry_time = time.time() + expiry

        # Use provided storage type or the cache's default
        actual_storage_type = storage_type or self._storage_types.get(cache_name, CacheStorageType.MEMORY)

        # Store in memory if configured
        if actual_storage_type in (CacheStorageType.MEMORY, CacheStorageType.BOTH):
            self._set_in_memory(cache_name, key, value, expiry_time)
            with self._stats_lock:
                self._stats['memory_writes'] += 1

        # Store on disk if configured
        if actual_storage_type in (CacheStorageType.DISK, CacheStorageType.BOTH):
            self._set_on_disk(cache_name, key, value, expiry_time)
            with self._stats_lock:
                self._stats['disk_writes'] += 1

        # Schedule cleanup if not already scheduled
        if not self._cleanup_scheduled:
            self.schedule_cleanup()

    def _set_in_memory(self, cache_name: str, key: str, value: Any, expiry_time: float) -> None:
        """
        Set a value in the memory cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            value (Any): Value to cache
            expiry_time (float): Expiry timestamp
        """
        with self._locks[cache_name]:
            # Check if we need to evict items to stay under the limit
            if len(self._memory_caches[cache_name]) >= self._max_memory_items:
                self._evict_lru_items(cache_name)

            # Store the value and update access time
            self._memory_caches[cache_name][key] = (value, expiry_time)
            self._memory_access_times[cache_name][key] = time.time()

    def _set_on_disk(self, cache_name: str, key: str, value: Any, expiry_time: float) -> None:
        """
        Set a value in the disk cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            value (Any): Value to cache
            expiry_time (float): Expiry timestamp
        """
        disk_path = self._get_disk_path(cache_name, key)

        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(disk_path), exist_ok=True)

            # Store the value with its expiry time
            data = {
                'value': value,
                'expiry': expiry_time,
                'created': time.time()
            }

            with open(disk_path, 'wb') as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
        except (IOError, pickle.PickleError) as e:
            logger.warning(f"Error writing to disk cache for {cache_name}/{key}: {e}")

    def _evict_lru_items(self, cache_name: str, count: int = 1) -> int:
        """
        Evict least recently used items from memory cache.

        Args:
            cache_name (str): Name of the cache
            count (int): Number of items to evict

        Returns:
            int: Number of items actually evicted
        """
        if cache_name not in self._memory_access_times:
            return 0

        # Sort keys by access time
        sorted_keys = sorted(
            self._memory_access_times[cache_name].items(),
            key=lambda x: x[1]  # Sort by access time
        )

        # Evict the oldest items
        evicted = 0
        for key, _ in sorted_keys[:count]:
            if key in self._memory_caches[cache_name]:
                del self._memory_caches[cache_name][key]
                del self._memory_access_times[cache_name][key]
                evicted += 1

        return evicted

    def get_all_keys(self, cache_name: str) -> List[str]:
        """
        Get all keys from the cache.

        Args:
            cache_name (str): Name of the cache

        Returns:
            List[str]: List of all keys in the cache
        """
        if cache_name not in self._memory_caches:
            return []

        keys = []
        current_time = time.time()

        # First check memory cache
        with self._locks[cache_name]:
            for key, (_, expiry) in self._memory_caches[cache_name].items():
                if current_time < expiry:
                    keys.append(key)

        # Then check disk cache if configured
        storage_type = self._storage_types.get(cache_name, CacheStorageType.MEMORY)
        if storage_type in (CacheStorageType.DISK, CacheStorageType.BOTH):
            cache_dir = os.path.join(self._disk_cache_dir, cache_name)
            if os.path.exists(cache_dir):
                for filename in os.listdir(cache_dir):
                    if filename.endswith('.cache'):
                        try:
                            with open(os.path.join(cache_dir, filename), 'rb') as f:
                                data = pickle.load(f)
                                if 'expiry' in data and current_time < data['expiry']:
                                    if 'key' in data:
                                        keys.append(data['key'])
                        except (IOError, pickle.PickleError, KeyError) as e:
                            logger.warning(f"Error reading disk cache file {filename}: {e}")

        return list(set(keys))  # Return unique keys

    def delete(self, cache_name: str, key: str) -> bool:
        """
        Delete a specific key from the cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Key to delete

        Returns:
            bool: True if the key was deleted, False otherwise
        """
        if cache_name not in self._memory_caches:
            return False

        deleted = False

        # Delete from memory cache
        with self._locks[cache_name]:
            if key in self._memory_caches[cache_name]:
                del self._memory_caches[cache_name][key]
                if key in self._memory_access_times[cache_name]:
                    del self._memory_access_times[cache_name][key]
                deleted = True

        # Delete from disk cache if configured
        storage_type = self._storage_types.get(cache_name, CacheStorageType.MEMORY)
        if storage_type in (CacheStorageType.DISK, CacheStorageType.BOTH):
            cache_dir = os.path.join(self._disk_cache_dir, cache_name)
            if os.path.exists(cache_dir):
                # We need to check all files since the key might be stored in metadata
                for filename in os.listdir(cache_dir):
                    if filename.endswith('.cache'):
                        try:
                            file_path = os.path.join(cache_dir, filename)
                            with open(file_path, 'rb') as f:
                                data = pickle.load(f)
                                if 'key' in data and data['key'] == key:
                                    os.remove(file_path)
                                    deleted = True
                                    break
                        except (IOError, pickle.PickleError, KeyError) as e:
                            logger.warning(f"Error reading disk cache file {filename}: {e}")

        return deleted

    def get_prefix_matches(self, cache_name: str, prefix: str, min_length: int = 3) -> List[Any]:
        """
        Get all values from the cache where the key starts with the given prefix.

        Args:
            cache_name (str): Name of the cache
            prefix (str): Prefix to match
            min_length (int): Minimum length of prefix to consider

        Returns:
            List[Any]: List of matching values
        """
        if cache_name not in self._memory_caches or len(prefix) < min_length:
            return []

        matches = []
        current_time = time.time()

        # First check memory cache
        with self._locks[cache_name]:
            for key, (value, expiry) in self._memory_caches[cache_name].items():
                if key.startswith(prefix) and current_time < expiry:
                    matches.append(value)

        # If disk cache is enabled and we didn't find enough matches, check disk
        if len(matches) == 0 and self._storage_types.get(cache_name) in (CacheStorageType.DISK, CacheStorageType.BOTH):
            # This is a more expensive operation as we need to scan all files
            cache_dir = os.path.join(self._disk_cache_dir, cache_name)
            if os.path.exists(cache_dir):
                for filename in os.listdir(cache_dir):
                    if filename.endswith('.cache'):
                        try:
                            filepath = os.path.join(cache_dir, filename)
                            with open(filepath, 'rb') as f:
                                data = pickle.load(f)

                            # Check if expired
                            if 'expiry' in data and current_time >= data['expiry']:
                                continue

                            # We don't have the original key in the filename, so we need to check metadata
                            if 'key' in data and data['key'].startswith(prefix):
                                matches.append(data['value'])
                        except (IOError, pickle.PickleError, KeyError) as e:
                            logger.warning(f"Error reading disk cache file {filename}: {e}")

        return matches

    def clear(self, cache_name: str) -> int:
        """
        Clear a specific cache.

        Args:
            cache_name (str): Name of the cache to clear

        Returns:
            int: Number of items cleared
        """
        if cache_name not in self._memory_caches:
            return 0

        count = 0

        # Clear memory cache
        with self._locks[cache_name]:
            count = len(self._memory_caches[cache_name])
            self._memory_caches[cache_name].clear()
            self._memory_access_times[cache_name].clear()

        # Clear disk cache if enabled
        if self._storage_types.get(cache_name) in (CacheStorageType.DISK, CacheStorageType.BOTH):
            cache_dir = os.path.join(self._disk_cache_dir, cache_name)
            if os.path.exists(cache_dir):
                try:
                    # Remove all files in the directory
                    for filename in os.listdir(cache_dir):
                        file_path = os.path.join(cache_dir, filename)
                        if os.path.isfile(file_path):
                            os.unlink(file_path)
                            count += 1
                except OSError as e:
                    logger.warning(f"Error clearing disk cache for {cache_name}: {e}")

        return count

    def clear_all(self) -> Dict[str, int]:
        """
        Clear all caches.

        Returns:
            Dict[str, int]: Dictionary mapping cache names to number of items cleared
        """
        result = {}
        for cache_name in list(self._memory_caches.keys()):
            result[cache_name] = self.clear(cache_name)
        return result

    def cleanup(self) -> Dict[str, int]:
        """
        Remove expired entries from all caches.

        Returns:
            Dict[str, int]: Dictionary mapping cache names to number of items removed
        """
        result = {}
        current_time = time.time()

        # Clean memory caches
        for cache_name in self._memory_caches:
            with self._locks[cache_name]:
                count = 0
                keys_to_remove = []

                for key, (_, expiry) in self._memory_caches[cache_name].items():
                    if current_time >= expiry:
                        keys_to_remove.append(key)
                        count += 1

                for key in keys_to_remove:
                    del self._memory_caches[cache_name][key]
                    if key in self._memory_access_times[cache_name]:
                        del self._memory_access_times[cache_name][key]

                result[cache_name] = count

        # Clean disk caches
        for cache_name, storage_type in self._storage_types.items():
            if storage_type in (CacheStorageType.DISK, CacheStorageType.BOTH):
                cache_dir = os.path.join(self._disk_cache_dir, cache_name)
                if os.path.exists(cache_dir):
                    count = 0
                    for filename in os.listdir(cache_dir):
                        if filename.endswith('.cache'):
                            try:
                                filepath = os.path.join(cache_dir, filename)
                                with open(filepath, 'rb') as f:
                                    data = pickle.load(f)

                                # Check if expired
                                if 'expiry' in data and current_time >= data['expiry']:
                                    os.remove(filepath)
                                    count += 1
                            except (IOError, pickle.PickleError, KeyError) as e:
                                # Remove corrupted files
                                try:
                                    os.remove(os.path.join(cache_dir, filename))
                                    count += 1
                                except OSError:
                                    pass

                    # Add to result
                    if cache_name in result:
                        result[cache_name] += count
                    else:
                        result[cache_name] = count

        return result

    def schedule_cleanup(self, interval: int = None) -> None:
        """
        Schedule periodic cache cleanup.

        Args:
            interval (int, optional): Cleanup interval in seconds. If None, uses the default interval.
        """
        if interval is None:
            interval = self._cleanup_interval

        self._cleanup_scheduled = True

        def _cleanup():
            try:
                self.cleanup()
                # Schedule next cleanup
                threading.Timer(interval, _cleanup).start()
            except Exception as e:
                logger.error(f"Error during cache cleanup: {e}")
                # Try to reschedule even if there was an error
                threading.Timer(interval, _cleanup).start()

        # Start the cleanup timer
        threading.Timer(interval, _cleanup).start()

    def get_stats(self) -> Dict[str, Dict[str, int]]:
        """
        Get statistics about the caches.

        Returns:
            Dict[str, Dict[str, Any]]: Dictionary with cache statistics
        """
        stats = {}
        current_time = time.time()

        # Memory cache stats
        for cache_name in self._memory_caches:
            with self._locks[cache_name]:
                total = len(self._memory_caches[cache_name])
                expired = sum(1 for _, expiry in self._memory_caches[cache_name].values() if current_time >= expiry)
                valid = total - expired

                stats[cache_name] = {
                    'memory_total': total,
                    'memory_valid': valid,
                    'memory_expired': expired,
                    'storage_type': self._storage_types.get(cache_name, CacheStorageType.MEMORY).name
                }

        # Disk cache stats
        for cache_name, storage_type in self._storage_types.items():
            if storage_type in (CacheStorageType.DISK, CacheStorageType.BOTH):
                cache_dir = os.path.join(self._disk_cache_dir, cache_name)
                if os.path.exists(cache_dir):
                    # Count files
                    total_files = 0
                    expired_files = 0

                    for filename in os.listdir(cache_dir):
                        if filename.endswith('.cache'):
                            total_files += 1
                            try:
                                filepath = os.path.join(cache_dir, filename)
                                with open(filepath, 'rb') as f:
                                    data = pickle.load(f)

                                # Check if expired
                                if 'expiry' in data and current_time >= data['expiry']:
                                    expired_files += 1
                            except (IOError, pickle.PickleError, KeyError):
                                expired_files += 1  # Count corrupted files as expired

                    # Add to stats
                    if cache_name in stats:
                        stats[cache_name].update({
                            'disk_total': total_files,
                            'disk_valid': total_files - expired_files,
                            'disk_expired': expired_files
                        })
                    else:
                        stats[cache_name] = {
                            'disk_total': total_files,
                            'disk_valid': total_files - expired_files,
                            'disk_expired': expired_files,
                            'storage_type': storage_type.name
                        }

        # Add hit/miss statistics
        with self._stats_lock:
            stats['global'] = self._stats.copy()

        return stats

    def get_size_info(self) -> Dict[str, Dict[str, Union[int, float]]]:
        """
        Get information about the size of the cache.

        Returns:
            Dict[str, Dict[str, Union[int, float]]]: Dictionary with size information
        """
        size_info = {}

        # Memory cache size (approximate)
        for cache_name in self._memory_caches:
            with self._locks[cache_name]:
                # Approximate memory usage
                try:
                    import sys
                    memory_size = sum(sys.getsizeof(key) + sys.getsizeof(value)
                                     for key, (value, _) in self._memory_caches[cache_name].items())
                except:
                    memory_size = -1  # Unable to calculate

                size_info[cache_name] = {
                    'memory_items': len(self._memory_caches[cache_name]),
                    'memory_bytes': memory_size
                }

        # Disk cache size
        for cache_name in self._storage_types:
            cache_dir = os.path.join(self._disk_cache_dir, cache_name)
            if os.path.exists(cache_dir):
                # Calculate disk usage
                disk_size = 0
                file_count = 0

                for dirpath, _, filenames in os.walk(cache_dir):
                    for filename in filenames:
                        file_path = os.path.join(dirpath, filename)
                        if os.path.isfile(file_path):
                            disk_size += os.path.getsize(file_path)
                            file_count += 1

                if cache_name in size_info:
                    size_info[cache_name].update({
                        'disk_items': file_count,
                        'disk_bytes': disk_size,
                        'disk_mb': disk_size / (1024 * 1024)
                    })
                else:
                    size_info[cache_name] = {
                        'disk_items': file_count,
                        'disk_bytes': disk_size,
                        'disk_mb': disk_size / (1024 * 1024)
                    }

        # Total size
        total_memory = sum(info.get('memory_bytes', 0) for info in size_info.values() if info.get('memory_bytes', 0) > 0)
        total_disk = sum(info.get('disk_bytes', 0) for info in size_info.values() if 'disk_bytes' in info)

        size_info['total'] = {
            'memory_bytes': total_memory,
            'memory_mb': total_memory / (1024 * 1024) if total_memory > 0 else 0,
            'disk_bytes': total_disk,
            'disk_mb': total_disk / (1024 * 1024)
        }

        return size_info
