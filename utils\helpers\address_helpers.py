"""
Address-related helper functions for the Czech Property Registry application.

This module provides functions for address formatting, geocoding, and address suggestions.
"""

import re
import time
import logging
from typing import List, Dict, Any, Optional, Callable, Tuple, Union

# Configure logging
logger = logging.getLogger(__name__)


def get_address_suggestions(
    app,
    text: str,
    suggestion_type: str = "address",
    components: Optional[Dict[str, str]] = None,
    prefix: Optional[str] = None
) -> List[Dict[str, str]]:
    """
    Get address suggestions for the given text with caching.

    Args:
        app: The main application instance
        text (str): The text to get suggestions for
        suggestion_type (str): Type of suggestion to get (address, city, street, etc.)
        components (dict, optional): Additional components for the API request
        prefix (str, optional): Cache key prefix for specialized searches

    Returns:
        list: List of address suggestions
    """
    if not text or len(text) < 2:
        return []

    # Default components if not provided
    if components is None:
        components = {"country": "cz"}

    # Create cache key
    cache_key = f"{prefix + ':' if prefix else ''}{text}"

    # Check if we have a cache manager
    if hasattr(app, 'cache_manager'):
        # Use the cache manager
        cache_name = f"{suggestion_type}_suggestions"

        # Check cache first
        cached_result = app.cache_manager.get(cache_name, cache_key)
        if cached_result:
            logger.debug(f"Found cached {suggestion_type} suggestions for '{text}'")
            return cached_result

        # Check for prefix matches to reduce API calls
        if len(text) >= 3:
            prefix_matches = app.cache_manager.get_prefix_matches(cache_name, cache_key)
            if prefix_matches and len(prefix_matches) > 0:
                logger.debug(f"Found prefix match for '{text}'")
                return prefix_matches[0]  # Return the first match

    # If we don't have a cache manager, check the old cache
    elif hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
        with app._address_cache_lock:
            # Check if we have a cached result
            if cache_key in app._address_suggestions_cache:
                cache_entry = app._address_suggestions_cache[cache_key]
                if time.time() < cache_entry['expiry']:
                    logger.debug(f"Found cached {suggestion_type} suggestions for '{text}' in old cache")
                    return cache_entry['data']
                else:
                    # Remove expired entry
                    del app._address_suggestions_cache[cache_key]

    # Get suggestions from the API
    try:
        # Use the Google Maps API if available
        if hasattr(app, 'google_maps') and app.google_maps:
            # Determine the place type based on suggestion_type
            place_type = "address"
            if suggestion_type == "city":
                place_type = "(cities)"
            elif suggestion_type == "street":
                place_type = "address"

            # Use the input text directly
            search_text = text

            # Get suggestions from the API
            logger.debug(f"Getting {suggestion_type} suggestions from Google Maps API for '{text}'")
            suggestions = app.google_maps.get_place_predictions(
                search_text,
                types=place_type,
                components=components
            )

            # If we got suggestions, cache and return them
            if suggestions:
                # Cache the results
                if hasattr(app, 'cache_manager'):
                    app.cache_manager.set(f"{suggestion_type}_suggestions", cache_key, suggestions)
                elif hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
                    with app._address_cache_lock:
                        app._address_suggestions_cache[cache_key] = {
                            'data': suggestions,
                            'expiry': time.time() + getattr(app, '_address_cache_expiry', 300)
                        }

                return suggestions
    except Exception as e:
        logger.error(f"Error getting {suggestion_type} suggestions: {e}")
        # Log the error but don't use fallback data
        if hasattr(app, 'show_status'):
            app.show_status(f"Error getting address suggestions: {e}")

    # Return an empty list when API fails
    return []


def get_prefix_matches(app, text: str, prefix_type: str, min_length: int = 3) -> List[Dict[str, Any]]:
    """
    Get suggestions from cache based on prefix matching.

    Args:
        app: The main application instance
        text (str): The text to match
        prefix_type (str): The type of cache to search (e.g., "address:", "city:")
        min_length (int): Minimum length of text to consider for matching

    Returns:
        list: List of matching suggestions or empty list if none found
    """
    logger.info(f"\n===== ADDRESS HELPERS: PREFIX MATCH SEARCH =====")
    logger.info(f"Searching for text: '{text}' with prefix_type: '{prefix_type}', min_length: {min_length}")

    if len(text) < min_length:
        logger.info(f"Text length ({len(text)}) is less than minimum length ({min_length}). Returning empty list.")
        return []

    # Check if we have a cache manager
    if hasattr(app, 'cache_manager'):
        # Extract the cache name from the prefix type (remove the colon)
        cache_name = prefix_type.rstrip(':')
        logger.info(f"Using cache manager with cache_name: '{cache_name}'")

        # Use the cache manager's prefix matching
        results = app.cache_manager.get_prefix_matches(cache_name, text, min_length)
        logger.info(f"Cache manager returned {len(results)} results")
        return results

    # If we don't have a cache manager, check the old cache
    elif hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
        logger.info(f"Using old cache mechanism (no cache manager available)")

        with app._address_cache_lock:
            current_time = time.time()
            cache_entries_checked = 0
            valid_entries = 0
            suggestions_checked = 0

            for key, entry in app._address_suggestions_cache.items():
                cache_entries_checked += 1

                # Skip expired entries
                if current_time > entry['expiry']:
                    logger.debug(f"Skipping expired cache entry: '{key}', expired {current_time - entry['expiry']:.1f}s ago")
                    continue

                # Check if this is the right type of cache entry
                if not key.startswith(prefix_type):
                    logger.debug(f"Skipping cache entry with wrong prefix: '{key}' (expected prefix: '{prefix_type}')")
                    continue

                valid_entries += 1
                logger.info(f"Found valid cache entry with key: '{key}', expiry: {entry['expiry'] - current_time:.1f}s remaining")
                logger.info(f"Cache entry contains {len(entry['data'])} suggestions")

                # Extract the cached query text
                cached_text = key[len(prefix_type):]
                logger.info(f"Extracted cached query text: '{cached_text}'")

                # If the cached query starts with our current text and is longer
                # it might have relevant suggestions
                if cached_text.startswith(text) and len(cached_text) > len(text):
                    logger.info(f"Cache key '{cached_text}' starts with search text '{text}' - checking suggestions")

                    # Filter the suggestions to only include those relevant to the current text
                    filtered_suggestions = []
                    for suggestion in entry['data']:
                        suggestions_checked += 1

                        # Get suggestion text and ID for better logging
                        suggestion_text = ""
                        suggestion_id = ""

                        if isinstance(suggestion, dict):
                            suggestion_text = suggestion.get('description', '')
                            suggestion_id = suggestion.get('place_id', suggestion.get('id', ''))
                        elif isinstance(suggestion, str):
                            suggestion_text = suggestion
                        else:
                            suggestion_text = str(suggestion)

                        # Log what we're checking
                        logger.info(f"[{suggestions_checked}] Checking suggestion: '{suggestion_text}'")
                        logger.info(f"  - Comparing if '{suggestion_text.lower()}' starts with '{text.lower()}'")

                        # Check if the suggestion description starts with the current text
                        if suggestion_text.lower().startswith(text.lower()):
                            filtered_suggestions.append(suggestion)
                            logger.info(f"  ✓ MATCH FOUND: '{suggestion_text}' (ID: {suggestion_id}) for '{text}'")
                        else:
                            logger.info(f"  ✗ NO MATCH: '{suggestion_text}' does not start with '{text}'")
                            # Additional check to see if it contains the text anywhere (for debugging)
                            if text.lower() in suggestion_text.lower():
                                logger.info(f"  ! NOTE: '{suggestion_text}' contains '{text}' but doesn't start with it")

                    # If we found relevant suggestions, return them
                    if filtered_suggestions:
                        logger.info(f"Found {len(filtered_suggestions)} prefix matches for '{text}' in cache")

                        # Log the matches
                        for i, match in enumerate(filtered_suggestions):
                            match_text = match.get('description', str(match)) if isinstance(match, dict) else str(match)
                            logger.info(f"  Match {i+1}: {match_text}")

                        logger.info(f"===== END PREFIX MATCH SEARCH =====\n")
                        return filtered_suggestions

            # Log summary if no matches found
            logger.info(f"\nSummary:")
            logger.info(f"- Cache entries checked: {cache_entries_checked}")
            logger.info(f"- Valid cache entries: {valid_entries}")
            logger.info(f"- Suggestions checked: {suggestions_checked}")
            logger.info(f"- No matches found")
    else:
        logger.info(f"No cache mechanism available (neither cache_manager nor _address_suggestions_cache)")

    logger.info(f"===== END PREFIX MATCH SEARCH =====\n")
    return []


def parse_address(address: str) -> Dict[str, str]:
    """
    Parse an address string into components.

    Args:
        address (str): The address to parse

    Returns:
        dict: Dictionary of address components
    """
    result = {
        'street': '',
        'number': '',
        'city': '',
        'postal_code': '',
        'country': 'Czech Republic'
    }

    # Try to extract the postal code (PSČ)
    postal_match = re.search(r'(\d{3}\s*\d{2})', address)
    if postal_match:
        result['postal_code'] = postal_match.group(1).replace(" ", "")
        # Remove postal code from the address
        address = address.replace(postal_match.group(0), "").strip()

    # Split the address by commas
    parts = [p.strip() for p in address.split(",")]

    # The last part is usually the country or city
    if parts and parts[-1].lower() in ('czech republic', 'czechia', 'česká republika', 'česko'):
        parts.pop()  # Remove the country

    # Now the last part should be the city
    if parts:
        result['city'] = parts.pop()

    # The first part is usually the street and number
    if parts:
        street_and_number = parts[0]
        # Try to extract the number from the street
        number_match = re.search(r'(\d+\w*|\w+\d+)$', street_and_number)
        if number_match:
            result['number'] = number_match.group(1)
            result['street'] = street_and_number[:number_match.start()].strip()
        else:
            result['street'] = street_and_number

    return result


def format_address(components: Dict[str, str]) -> str:
    """
    Format address components into a full address string.

    Args:
        components (dict): Dictionary of address components

    Returns:
        str: Formatted address string
    """
    parts = []

    # Add street and number
    street_part = ""
    if components.get('street'):
        street_part = components['street']
        if components.get('number'):
            street_part += f" {components['number']}"

    if street_part:
        parts.append(street_part)

    # Add city
    if components.get('city'):
        parts.append(components['city'])

    # Add postal code
    if components.get('postal_code'):
        parts.append(components['postal_code'])

    # Add country
    if components.get('country'):
        parts.append(components['country'])

    return ", ".join(parts)


def clean_address_cache(app):
    """
    Clean expired entries from the address suggestions cache.

    Args:
        app: The main application instance
    """
    try:
        if hasattr(app, '_address_suggestions_cache') and hasattr(app, '_address_cache_lock'):
            with app._address_cache_lock:
                current_time = time.time()
                expired_keys = []

                for key, entry in app._address_suggestions_cache.items():
                    if current_time > entry['expiry']:
                        expired_keys.append(key)

                for key in expired_keys:
                    del app._address_suggestions_cache[key]

                if expired_keys:
                    logger.info(f"Cleaned {len(expired_keys)} expired entries from address cache")

    except Exception as e:
        logger.error(f"Error cleaning address cache: {e}")


__all__ = [
    'get_address_suggestions',
    'get_prefix_matches',
    'parse_address',
    'format_address',
    'clean_address_cache'
]
