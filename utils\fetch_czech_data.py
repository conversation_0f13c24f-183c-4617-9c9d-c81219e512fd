#!/usr/bin/env python3
"""
<PERSON>ript to fetch Czech administrative division data from Wikipedia
and save it to JSON files for use in the Czech Property Registry application.
"""

import os
import json
import re
import requests
from bs4 import BeautifulSoup
import time

# Create directory for data if it doesn't exist
if not os.path.exists('czech_data'):
    os.makedirs('czech_data')

def fetch_wikipedia_page(url):
    """Fetch a Wikipedia page and return the BeautifulSoup object"""
    headers = {
        'User-Agent': 'Czech Property Registry Data Fetcher/1.0 (https://github.com/yourusername/czech-property-registry)'
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return BeautifulSoup(response.text, 'html.parser')

def fetch_regions():
    """Fetch Czech regions from Wikipedia"""
    print("Fetching Czech regions...")
    url = "https://en.wikipedia.org/wiki/Regions_of_the_Czech_Republic"
    soup = fetch_wikipedia_page(url)

    regions_data = {
        "regions_czech": [],
        "regions_english": [],
        "region_mapping": {},
        "region_codes": {},
        "region_coordinates": {},
        "region_name_mapping": {},
        "region_districts": {}
    }

    # Find the table with regions
    region_table = None
    for table in soup.find_all('table', {'class': 'wikitable'}):
        if 'Region' in table.text and 'Capital' in table.text:
            region_table = table
            break

    if not region_table:
        print("Could not find region table on Wikipedia page")
        return regions_data

    # Extract region data from the table
    for row in region_table.find_all('tr')[1:]:  # Skip header row
        cells = row.find_all(['td', 'th'])
        if len(cells) >= 3:
            english_name = cells[0].text.strip()
            czech_name = cells[1].text.strip()

            # Clean up names
            english_name = re.sub(r'\[\d+\]', '', english_name)
            czech_name = re.sub(r'\[\d+\]', '', czech_name)

            # Add to data
            regions_data["regions_english"].append(english_name)
            regions_data["regions_czech"].append(czech_name)
            regions_data["region_mapping"][english_name] = czech_name

            # Add simplified name mapping for easier lookup
            simplified_name = english_name.replace(" Region", "")
            regions_data["region_name_mapping"][english_name] = simplified_name

    # Fetch region codes and coordinates from another source
    # This is a placeholder - in a real implementation, you would fetch this data
    # from an appropriate source
    for english_name, czech_name in regions_data["region_mapping"].items():
        # Generate placeholder region code
        code_prefix = "CZ"
        if "Prague" in english_name:
            code = "010"
        elif "Central" in english_name:
            code = "020"
        elif "South Bohemian" in english_name:
            code = "031"
        elif "Plzeň" in english_name:
            code = "032"
        elif "Karlovy Vary" in english_name:
            code = "041"
        elif "Ústí" in english_name:
            code = "042"
        elif "Liberec" in english_name:
            code = "051"
        elif "Hradec" in english_name:
            code = "052"
        elif "Pardubice" in english_name:
            code = "053"
        elif "Vysočina" in english_name:
            code = "063"
        elif "South Moravian" in english_name:
            code = "064"
        elif "Olomouc" in english_name:
            code = "071"
        elif "Zlín" in english_name:
            code = "072"
        elif "Moravian-Silesian" in english_name:
            code = "080"
        else:
            code = "000"

        regions_data["region_codes"][czech_name] = f"{code_prefix}{code}"

        # Generate placeholder coordinates
        regions_data["region_coordinates"][czech_name] = {
            "lat": 50.0 + (len(regions_data["region_coordinates"]) * 0.1),
            "lng": 14.0 + (len(regions_data["region_coordinates"]) * 0.1)
        }

    # Now fetch districts for each region
    print("Fetching districts for each region...")

    # URL for the list of districts
    url = "https://en.wikipedia.org/wiki/Districts_of_the_Czech_Republic"
    soup = fetch_wikipedia_page(url)

    # Find the table with districts
    district_table = None
    for table in soup.find_all('table', {'class': 'wikitable'}):
        if 'District' in table.text and 'Region' in table.text:
            district_table = table
            break

    if not district_table:
        print("Could not find district table on Wikipedia page")
        return regions_data

    # Initialize districts for each region
    for region in regions_data["regions_english"]:
        regions_data["region_districts"][region] = []

    # Extract district data from the table
    for row in district_table.find_all('tr')[1:]:  # Skip header row
        cells = row.find_all(['td', 'th'])
        if len(cells) >= 3:
            district_name = cells[0].text.strip()
            region_name = cells[2].text.strip()

            # Clean up names
            district_name = re.sub(r'\[\d+\]', '', district_name)
            region_name = re.sub(r'\[\d+\]', '', region_name)

            # Find the matching region in our data
            for english_region in regions_data["regions_english"]:
                if region_name in english_region or english_region in region_name:
                    regions_data["region_districts"][english_region].append(district_name)
                    break

    return regions_data

def fetch_cities():
    """Fetch Czech cities from Wikipedia"""
    print("Fetching Czech cities...")

    cities_data = {
        "cities_by_region": {},
        "cities_by_region_code": {},
        "city_name_mapping": {}
    }

    # First, get the list of regions
    with open('czech_data/regions.json', 'r', encoding='utf-8') as f:
        regions_data = json.load(f)

    # Initialize cities for each region
    for region in regions_data["regions_english"]:
        cities_data["cities_by_region"][region] = []
        # Also add the simplified name
        simplified_name = regions_data["region_name_mapping"].get(region, region)
        if simplified_name != region:
            cities_data["cities_by_region"][simplified_name] = []

    # Initialize cities for each region code
    for czech_name, code in regions_data["region_codes"].items():
        cities_data["cities_by_region_code"][code] = []

    # Try to fetch cities for each region separately
    for region in regions_data["regions_english"]:
        print(f"Fetching cities for {region}...")

        # URL for the region's Wikipedia page
        region_url_name = region.replace(" ", "_")
        url = f"https://en.wikipedia.org/wiki/{region_url_name}"

        try:
            soup = fetch_wikipedia_page(url)

            # Look for sections that might contain city information
            city_sections = []
            for heading in soup.find_all(['h2', 'h3']):
                heading_text = heading.text.lower()
                if 'cities' in heading_text or 'towns' in heading_text or 'municipalities' in heading_text or 'settlements' in heading_text:
                    city_sections.append(heading)

            # For each potential section, look for lists of cities
            for section in city_sections:
                next_element = section.find_next()
                while next_element and next_element.name not in ['h2', 'h3']:
                    if next_element.name == 'ul':
                        for li in next_element.find_all('li'):
                            city_name = li.text.strip()
                            city_name = re.sub(r'\[\d+\]', '', city_name)

                            # Add to city name mapping
                            cities_data["city_name_mapping"][city_name] = city_name

                            # Add to cities by region
                            if city_name not in cities_data["cities_by_region"][region]:
                                cities_data["cities_by_region"][region].append(city_name)

                            # Add to simplified region name too
                            simplified_name = regions_data["region_name_mapping"].get(region, region)
                            if simplified_name != region and city_name not in cities_data["cities_by_region"][simplified_name]:
                                cities_data["cities_by_region"][simplified_name].append(city_name)

                            # Add to cities by region code
                            czech_region = regions_data["region_mapping"].get(region)
                            if czech_region:
                                region_code = regions_data["region_codes"].get(czech_region)
                                if region_code and city_name not in cities_data["cities_by_region_code"][region_code]:
                                    cities_data["cities_by_region_code"][region_code].append(city_name)

                    # Also check for tables
                    if next_element.name == 'table':
                        for row in next_element.find_all('tr')[1:]:  # Skip header row
                            cells = row.find_all(['td', 'th'])
                            if cells:
                                city_name = cells[0].text.strip()
                                city_name = re.sub(r'\[\d+\]', '', city_name)

                                # Add to city name mapping
                                cities_data["city_name_mapping"][city_name] = city_name

                                # Add to cities by region
                                if city_name not in cities_data["cities_by_region"][region]:
                                    cities_data["cities_by_region"][region].append(city_name)

                                # Add to simplified region name too
                                simplified_name = regions_data["region_name_mapping"].get(region, region)
                                if simplified_name != region and city_name not in cities_data["cities_by_region"][simplified_name]:
                                    cities_data["cities_by_region"][simplified_name].append(city_name)

                                # Add to cities by region code
                                czech_region = regions_data["region_mapping"].get(region)
                                if czech_region:
                                    region_code = regions_data["region_codes"].get(czech_region)
                                    if region_code and city_name not in cities_data["cities_by_region_code"][region_code]:
                                        cities_data["cities_by_region_code"][region_code].append(city_name)

                    next_element = next_element.find_next()

        except Exception as e:
            print(f"Error fetching cities for {region}: {e}")

        # If we didn't find any cities, use the districts from the regions data
        if not cities_data["cities_by_region"][region]:
            print(f"Using districts as cities for {region}")
            districts = regions_data.get("region_districts", {}).get(region, [])
            for district in districts:
                # Add to city name mapping
                cities_data["city_name_mapping"][district] = district

                # Add to cities by region
                cities_data["cities_by_region"][region].append(district)

                # Add to simplified region name too
                simplified_name = regions_data["region_name_mapping"].get(region, region)
                if simplified_name != region:
                    cities_data["cities_by_region"][simplified_name].append(district)

                # Add to cities by region code
                czech_region = regions_data["region_mapping"].get(region)
                if czech_region:
                    region_code = regions_data["region_codes"].get(czech_region)
                    if region_code:
                        cities_data["cities_by_region_code"][region_code].append(district)

        # Avoid hitting Wikipedia too frequently
        time.sleep(1)

    # Add fallback data for Karlovy Vary Region
    karlovy_vary_cities = [
        "Karlovy Vary", "Cheb", "Sokolov", "Mariánské Lázně", "Aš", "Ostrov", "Kraslice", "Chodov", "Nejdek",
        "Františkovy Lázně", "Jáchymov", "Loket", "Žlutice", "Toužim", "Bochov", "Horní Slavkov", "Kynšperk nad Ohří",
        "Luby", "Plesná", "Rotava", "Hroznětín", "Lázně Kynžvart", "Nová Role", "Habartov", "Březová", "Krásno",
        "Teplá", "Nové Sedlo", "Abertamy", "Horní Blatná", "Krásné Údolí", "Bečov nad Teplou", "Chyše", "Pernink",
        "Merklín", "Potůčky", "Boží Dar", "Dalovice", "Jenišov", "Kolová", "Sadov", "Otovice", "Hory", "Stružná",
        "Andělská Hora", "Kyselka", "Velichov", "Vojkovice", "Stráž nad Ohří", "Děpoltovice", "Smolné Pece", "Šemnice"
    ]

    for region_name in ["Karlovy Vary Region", "Karlovy Vary"]:
        if region_name in cities_data["cities_by_region"] and not cities_data["cities_by_region"][region_name]:
            print(f"Adding fallback data for {region_name}")
            cities_data["cities_by_region"][region_name] = karlovy_vary_cities

    # Add Czech-English city name mappings
    cities_data["city_name_mapping"].update({
        "Prague": "Praha",
        "Brno": "Brno",
        "Ostrava": "Ostrava",
        "Plzeň": "Plzeň",
        "Liberec": "Liberec",
        "Karlovy Vary": "Karlovy Vary",
        "Cheb": "Cheb",
        "Sokolov": "Sokolov",
        "Mariánské Lázně": "Mariánské Lázně",
        "Marianske Lazne": "Mariánské Lázně",
        "Aš": "Aš",
        "As": "Aš",
        "Ostrov": "Ostrov",
        "Chodov": "Chodov",
        "Nejdek": "Nejdek",
        "Františkovy Lázně": "Františkovy Lázně",
        "Frantiskovy Lazne": "Františkovy Lázně",
        "Jáchymov": "Jáchymov",
        "Jachymov": "Jáchymov",
        "Loket": "Loket",
        "Žlutice": "Žlutice",
        "Zlutice": "Žlutice",
        "Toužim": "Toužim",
        "Touzim": "Toužim",
        "České Budějovice": "České Budějovice",
        "Ceske Budejovice": "České Budějovice",
        "Hradec Králové": "Hradec Králové",
        "Hradec Kralove": "Hradec Králové",
        "Ústí nad Labem": "Ústí nad Labem",
        "Usti nad Labem": "Ústí nad Labem"
    })

    return cities_data

def fetch_districts():
    """Fetch Czech districts (parts of cities) from Wikipedia"""
    print("Fetching Czech districts...")

    districts_data = {
        "districts_by_city": {}
    }

    # First, get the list of cities
    with open('czech_data/cities.json', 'r', encoding='utf-8') as f:
        cities_data = json.load(f)

    # For each major city, try to fetch its districts
    major_cities = ["Prague", "Brno", "Ostrava", "Plzeň", "Liberec", "Olomouc",
                   "Karlovy Vary", "Hradec Králové", "České Budějovice", "Ústí nad Labem"]

    for city in major_cities:
        print(f"Fetching districts for {city}...")

        # Initialize districts for this city
        districts_data["districts_by_city"][city] = []

        # URL for the city's Wikipedia page
        url = f"https://en.wikipedia.org/wiki/{city.replace(' ', '_')}"
        try:
            soup = fetch_wikipedia_page(url)

            # Look for sections that might contain district information
            district_sections = []
            for heading in soup.find_all(['h2', 'h3']):
                heading_text = heading.text.lower()
                if 'district' in heading_text or 'division' in heading_text or 'borough' in heading_text or 'part' in heading_text:
                    district_sections.append(heading)

            # For each potential section, look for lists of districts
            for section in district_sections:
                next_element = section.find_next()
                while next_element and next_element.name not in ['h2', 'h3']:
                    if next_element.name == 'ul':
                        for li in next_element.find_all('li'):
                            district_name = li.text.strip()
                            district_name = re.sub(r'\[\d+\]', '', district_name)
                            if district_name and district_name not in districts_data["districts_by_city"][city]:
                                districts_data["districts_by_city"][city].append(district_name)
                    next_element = next_element.find_next()

            # If we didn't find any districts, try looking for tables
            if not districts_data["districts_by_city"][city]:
                for table in soup.find_all('table', {'class': 'wikitable'}):
                    if 'district' in table.text.lower() or 'borough' in table.text.lower() or 'part' in table.text.lower():
                        for row in table.find_all('tr')[1:]:  # Skip header row
                            cells = row.find_all(['td', 'th'])
                            if cells:
                                district_name = cells[0].text.strip()
                                district_name = re.sub(r'\[\d+\]', '', district_name)
                                if district_name and district_name not in districts_data["districts_by_city"][city]:
                                    districts_data["districts_by_city"][city].append(district_name)

            # If we still didn't find any districts, add some default ones
            if not districts_data["districts_by_city"][city]:
                if city == "Prague":
                    districts_data["districts_by_city"][city] = [f"Prague {i}" for i in range(1, 11)]
                else:
                    districts_data["districts_by_city"][city] = [
                        f"{city}-centrum", f"{city}-sever", f"{city}-jih",
                        f"{city}-východ", f"{city}-západ"
                    ]

            # Add Czech version of the city name if it's different
            czech_name = cities_data["city_name_mapping"].get(city)
            if czech_name and czech_name != city:
                districts_data["districts_by_city"][czech_name] = districts_data["districts_by_city"][city]

        except Exception as e:
            print(f"Error fetching districts for {city}: {e}")
            # Add default districts
            districts_data["districts_by_city"][city] = [
                f"{city}-centrum", f"{city}-sever", f"{city}-jih",
                f"{city}-východ", f"{city}-západ"
            ]

        # Avoid hitting Wikipedia too frequently
        time.sleep(1)

    return districts_data

def fetch_cadastral_areas():
    """Create cadastral areas data based on cities and districts"""
    print("Creating cadastral areas data...")

    cadastral_data = {
        "cadastral_areas_by_city": {},
        "cadastral_areas_by_district": {},
        "cadastral_template": [
            "{name}",
            "{name}-město",
            "{name}-okolí",
            "Katastr {name}"
        ]
    }

    # Load districts data
    with open('czech_data/districts.json', 'r', encoding='utf-8') as f:
        districts_data = json.load(f)

    # For each city with districts, create cadastral areas
    for city, districts in districts_data["districts_by_city"].items():
        # Create cadastral areas for the city
        cadastral_data["cadastral_areas_by_city"][city] = []

        # Add the city itself as a cadastral area
        cadastral_data["cadastral_areas_by_city"][city].append(city)

        # Add some of the districts as cadastral areas
        for district in districts[:9]:  # Limit to 10 total
            cadastral_data["cadastral_areas_by_city"][city].append(district)

        # For each district, create cadastral areas
        for district in districts:
            cadastral_data["cadastral_areas_by_district"][district] = []

            # Add the district itself as a cadastral area
            cadastral_data["cadastral_areas_by_district"][district].append(district)

            # Add some variations
            cadastral_data["cadastral_areas_by_district"][district].append(f"{district}-centrum")
            cadastral_data["cadastral_areas_by_district"][district].append(f"{district}-okolí")
            cadastral_data["cadastral_areas_by_district"][district].append(f"Katastr {district}")

    return cadastral_data

def main():
    """Main function to fetch all data and save to JSON files"""
    # Fetch regions data
    regions_data = fetch_regions()
    with open('czech_data/regions.json', 'w', encoding='utf-8') as f:
        json.dump(regions_data, f, ensure_ascii=False, indent=4)
    print(f"Saved regions data to czech_data/regions.json")

    # Fetch cities data
    cities_data = fetch_cities()
    with open('czech_data/cities.json', 'w', encoding='utf-8') as f:
        json.dump(cities_data, f, ensure_ascii=False, indent=4)
    print(f"Saved cities data to czech_data/cities.json")

    # Fetch districts data
    districts_data = fetch_districts()
    with open('czech_data/districts.json', 'w', encoding='utf-8') as f:
        json.dump(districts_data, f, ensure_ascii=False, indent=4)
    print(f"Saved districts data to czech_data/districts.json")

    # Create cadastral areas data
    cadastral_data = fetch_cadastral_areas()
    with open('czech_data/cadastral_areas.json', 'w', encoding='utf-8') as f:
        json.dump(cadastral_data, f, ensure_ascii=False, indent=4)
    print(f"Saved cadastral areas data to czech_data/cadastral_areas.json")

if __name__ == "__main__":
    main()
