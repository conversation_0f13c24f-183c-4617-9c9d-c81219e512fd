"""
Data compression utilities for the Czech Property Registry application.
Provides mechanisms for compressing data in memory and on disk.
"""

import zlib
import gzip
import lzma
import bz2
import pickle
import json
import base64
import logging
import time
import threading
from typing import Dict, Any, Optional, Tuple, List, Union, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DataCompression")


class CompressionMethod:
    """Enumeration of compression methods."""
    NONE = 'none'
    ZLIB = 'zlib'
    GZIP = 'gzip'
    LZMA = 'lzma'
    BZ2 = 'bz2'


class CompressionLevel:
    """Enumeration of compression levels."""
    NONE = 0
    FAST = 1
    BALANCED = 6
    BEST = 9


class DataCompressor:
    """
    Utility for compressing and decompressing data.
    Supports multiple compression methods and levels.
    """
    
    def __init__(self, 
                 default_method: str = CompressionMethod.ZLIB, 
                 default_level: int = CompressionLevel.BALANCED):
        """
        Initialize the data compressor.
        
        Args:
            default_method (str): Default compression method
            default_level (int): Default compression level
        """
        self.default_method = default_method
        self.default_level = default_level
        
        # Statistics
        self._stats = {
            'compressed_bytes': 0,
            'uncompressed_bytes': 0,
            'compression_time': 0.0,
            'decompression_time': 0.0,
            'compression_count': 0,
            'decompression_count': 0
        }
        self._stats_lock = threading.Lock()
        
        logger.info(f"DataCompressor initialized with {default_method} method at level {default_level}")
    
    def compress(self, 
                 data: bytes, 
                 method: Optional[str] = None, 
                 level: Optional[int] = None) -> Tuple[bytes, Dict[str, Any]]:
        """
        Compress binary data.
        
        Args:
            data (bytes): Data to compress
            method (str, optional): Compression method
            level (int, optional): Compression level
            
        Returns:
            Tuple of (compressed_data, metadata)
        """
        method = method or self.default_method
        level = level or self.default_level
        
        start_time = time.time()
        uncompressed_size = len(data)
        
        try:
            if method == CompressionMethod.NONE:
                compressed = data
            elif method == CompressionMethod.ZLIB:
                compressed = zlib.compress(data, level)
            elif method == CompressionMethod.GZIP:
                compressed = gzip.compress(data, level)
            elif method == CompressionMethod.LZMA:
                compressed = lzma.compress(data, preset=level)
            elif method == CompressionMethod.BZ2:
                compressed = bz2.compress(data, level)
            else:
                raise ValueError(f"Unknown compression method: {method}")
            
            compressed_size = len(compressed)
            compression_ratio = uncompressed_size / compressed_size if compressed_size > 0 else 1.0
            compression_time = time.time() - start_time
            
            metadata = {
                'method': method,
                'level': level,
                'uncompressed_size': uncompressed_size,
                'compressed_size': compressed_size,
                'compression_ratio': compression_ratio,
                'compression_time': compression_time
            }
            
            # Update statistics
            with self._stats_lock:
                self._stats['compressed_bytes'] += compressed_size
                self._stats['uncompressed_bytes'] += uncompressed_size
                self._stats['compression_time'] += compression_time
                self._stats['compression_count'] += 1
            
            return compressed, metadata
        
        except Exception as e:
            logger.error(f"Error compressing data with {method} at level {level}: {e}")
            return data, {'method': CompressionMethod.NONE, 'error': str(e)}
    
    def decompress(self, 
                  compressed_data: bytes, 
                  metadata: Dict[str, Any]) -> bytes:
        """
        Decompress binary data.
        
        Args:
            compressed_data (bytes): Compressed data
            metadata (dict): Metadata from compression
            
        Returns:
            bytes: Decompressed data
        """
        method = metadata.get('method', CompressionMethod.NONE)
        
        start_time = time.time()
        
        try:
            if method == CompressionMethod.NONE:
                decompressed = compressed_data
            elif method == CompressionMethod.ZLIB:
                decompressed = zlib.decompress(compressed_data)
            elif method == CompressionMethod.GZIP:
                decompressed = gzip.decompress(compressed_data)
            elif method == CompressionMethod.LZMA:
                decompressed = lzma.decompress(compressed_data)
            elif method == CompressionMethod.BZ2:
                decompressed = bz2.decompress(compressed_data)
            else:
                raise ValueError(f"Unknown compression method: {method}")
            
            decompression_time = time.time() - start_time
            
            # Update statistics
            with self._stats_lock:
                self._stats['decompression_time'] += decompression_time
                self._stats['decompression_count'] += 1
            
            return decompressed
        
        except Exception as e:
            logger.error(f"Error decompressing data with {method}: {e}")
            return compressed_data
    
    def compress_object(self, 
                       obj: Any, 
                       method: Optional[str] = None, 
                       level: Optional[int] = None) -> Tuple[bytes, Dict[str, Any]]:
        """
        Compress a Python object.
        
        Args:
            obj: Object to compress
            method (str, optional): Compression method
            level (int, optional): Compression level
            
        Returns:
            Tuple of (compressed_data, metadata)
        """
        # Serialize the object
        serialized = pickle.dumps(obj, protocol=pickle.HIGHEST_PROTOCOL)
        
        # Compress the serialized data
        return self.compress(serialized, method, level)
    
    def decompress_object(self, 
                         compressed_data: bytes, 
                         metadata: Dict[str, Any]) -> Any:
        """
        Decompress a Python object.
        
        Args:
            compressed_data (bytes): Compressed data
            metadata (dict): Metadata from compression
            
        Returns:
            The decompressed object
        """
        # Decompress the data
        decompressed = self.decompress(compressed_data, metadata)
        
        # Deserialize the object
        try:
            return pickle.loads(decompressed)
        except Exception as e:
            logger.error(f"Error deserializing object: {e}")
            return None
    
    def compress_json(self, 
                     obj: Any, 
                     method: Optional[str] = None, 
                     level: Optional[int] = None) -> Tuple[str, Dict[str, Any]]:
        """
        Compress an object to a JSON-compatible string.
        
        Args:
            obj: Object to compress
            method (str, optional): Compression method
            level (int, optional): Compression level
            
        Returns:
            Tuple of (compressed_json_string, metadata)
        """
        # Serialize the object to JSON
        json_str = json.dumps(obj)
        
        # Compress the JSON string
        compressed, metadata = self.compress(json_str.encode('utf-8'), method, level)
        
        # Encode the compressed data as base64
        compressed_b64 = base64.b64encode(compressed).decode('ascii')
        
        return compressed_b64, metadata
    
    def decompress_json(self, 
                       compressed_b64: str, 
                       metadata: Dict[str, Any]) -> Any:
        """
        Decompress a JSON-compatible string to an object.
        
        Args:
            compressed_b64 (str): Base64-encoded compressed data
            metadata (dict): Metadata from compression
            
        Returns:
            The decompressed object
        """
        # Decode the base64 string
        compressed = base64.b64decode(compressed_b64.encode('ascii'))
        
        # Decompress the data
        decompressed = self.decompress(compressed, metadata)
        
        # Parse the JSON
        try:
            return json.loads(decompressed.decode('utf-8'))
        except Exception as e:
            logger.error(f"Error parsing JSON: {e}")
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about compression.
        
        Returns:
            Dictionary with statistics
        """
        with self._stats_lock:
            stats = self._stats.copy()
            
            # Calculate derived statistics
            if stats['compression_count'] > 0:
                stats['average_compression_time'] = stats['compression_time'] / stats['compression_count']
            else:
                stats['average_compression_time'] = 0.0
                
            if stats['decompression_count'] > 0:
                stats['average_decompression_time'] = stats['decompression_time'] / stats['decompression_count']
            else:
                stats['average_decompression_time'] = 0.0
                
            if stats['uncompressed_bytes'] > 0:
                stats['overall_compression_ratio'] = stats['uncompressed_bytes'] / stats['compressed_bytes']
            else:
                stats['overall_compression_ratio'] = 1.0
                
            stats['space_saved'] = stats['uncompressed_bytes'] - stats['compressed_bytes']
            stats['space_saved_mb'] = stats['space_saved'] / (1024 * 1024)
            
            return stats
    
    def reset_stats(self) -> None:
        """Reset the compression statistics."""
        with self._stats_lock:
            for key in self._stats:
                self._stats[key] = 0


# Create a global instance for convenience
data_compressor = DataCompressor()
