"""
Standalone Czech Property Registry application with autocomplete.

This is a simplified, standalone version of the Czech Property Registry application
with address autocomplete functionality.
"""

import tkinter as tk
from tkinter import ttk
import webbrowser
import logging
import threading
import sys
import os
import time
from utils.simple_autocomplete import SimpleAutocompleteEntry
from api.simple_google_maps import SimpleGoogleMaps
from api.cuzk.cuzk_integration import CUZKIntegration
from core.simple_batch_search_manager import SimpleBatchSearchManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("autocomplete_app.log"),
        logging.StreamHandler()
    ]
)

# Create a logger for this module
logger = logging.getLogger(__name__)

class AutocompleteApp:
    """
    A simplified Czech Property Registry application with autocomplete.
    """

    def __init__(self):
        """Initialize the application."""
        # Create the main window
        self.root = tk.Tk()
        self.root.title("Czech Property Registry - Autocomplete")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # Initialize APIs and services
        self.google_maps = SimpleGoogleMaps()
        self.cuzk_integration = CUZKIntegration()
        self.batch_search_manager = SimpleBatchSearchManager(self)

        # Create the main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # For compatibility with the batch search manager
        self.root = self.root

        # Method for showing status messages
        self.show_status = self.update_status

        # Create the UI
        self.create_ui()

    def create_ui(self):
        """Create the user interface."""
        # Create a notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create the batch search tab
        self.batch_search_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.batch_search_frame, text="Batch Search")

        # Create the batch search UI
        self.create_batch_search_ui()

    def create_batch_search_ui(self):
        """Create the batch search UI."""
        # Add a label to indicate this is the batch search UI
        ttk.Label(
            self.batch_search_frame,
            text="Batch Search",
            font=("Arial", 16, "bold"),
            foreground="#4CAF50"
        ).pack(side="top", pady=10)

        # Create the search frame
        search_frame = ttk.LabelFrame(self.batch_search_frame, text="Search Parameters")
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        # Address input
        ttk.Label(search_frame, text="Address:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        # Create a function to get address suggestions
        def get_address_suggestions(text):
            if hasattr(self, 'google_maps'):
                suggestions = self.google_maps.get_place_predictions(text)
                return suggestions
            else:
                return []

        # Create the autocomplete entry
        self.address_entry = SimpleAutocompleteEntry(
            search_frame,
            autocomplete_function=get_address_suggestions,
            width=40
        )
        self.address_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)

        # Radius selection
        ttk.Label(search_frame, text="Radius (km):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.radius_var = tk.StringVar(value="1.0")
        radius_values = ["0.0", "0.2", "0.5", "1.0", "2.0", "5.0"]
        self.radius_combobox = ttk.Combobox(search_frame, textvariable=self.radius_var, values=radius_values, width=10)
        self.radius_combobox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Search button
        self.search_button = ttk.Button(search_frame, text="Search", command=self.on_search)
        self.search_button.grid(row=2, column=0, padx=5, pady=10)

        # Status label
        self.status_label = ttk.Label(search_frame, text="Ready")
        self.status_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Progress bar
        self.progress_frame = ttk.Frame(search_frame)
        self.progress_frame.grid(row=4, column=0, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)

        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            orient=tk.HORIZONTAL,
            length=400,
            mode="indeterminate"
        )
        self.progress_bar.pack(fill=tk.X, expand=True)
        self.progress_bar.pack_forget()  # Hide initially

        # Configure grid
        search_frame.columnconfigure(1, weight=1)

        # Create the results frame
        results_frame = ttk.LabelFrame(self.batch_search_frame, text="Search Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a treeview for displaying buildings
        columns = ("id", "lat", "lng", "address", "url")
        self.buildings_tree = ttk.Treeview(results_frame, columns=columns, show="headings")

        # Define column headings
        self.buildings_tree.heading("id", text="#")
        self.buildings_tree.heading("lat", text="Latitude")
        self.buildings_tree.heading("lng", text="Longitude")
        self.buildings_tree.heading("address", text="Address")
        self.buildings_tree.heading("url", text="CUZK URL")

        # Define column widths
        self.buildings_tree.column("id", width=50, anchor=tk.CENTER)
        self.buildings_tree.column("lat", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("lng", width=100, anchor=tk.CENTER)
        self.buildings_tree.column("address", width=300, anchor=tk.W)
        self.buildings_tree.column("url", width=200, anchor=tk.W)

        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.buildings_tree.yview)
        self.buildings_tree.configure(yscrollcommand=y_scrollbar.set)

        # Pack the treeview and scrollbars
        self.buildings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind double-click event to open URL
        self.buildings_tree.bind("<Double-1>", self.on_building_double_click)

        # Create buttons frame
        buttons_frame = ttk.Frame(results_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # Open selected button
        self.open_selected_button = ttk.Button(buttons_frame, text="Open Selected", command=self.on_open_selected)
        self.open_selected_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Results count label
        self.results_count_label = ttk.Label(buttons_frame, text="0 buildings found")
        self.results_count_label.pack(side=tk.RIGHT, padx=5, pady=5)

    def on_search(self):
        """Handle the search button click."""
        # Get the address and radius
        address = self.address_entry.get()
        radius = self.radius_var.get()

        if not address:
            self.show_message("Missing Information", "Please enter an address to search for buildings.")
            return

        try:
            radius_float = float(radius)
            if radius_float < 0:
                self.show_message("Invalid Radius", "Radius must be 0 or greater.")
                return
        except ValueError:
            self.show_message("Invalid Radius", "Please enter a valid number for the radius.")
            return

        # Update UI state
        self.search_button.config(state=tk.DISABLED)
        self.status_label.config(text=f"Searching for buildings near {address}...")

        # Show progress bar
        self.progress_bar.pack(fill=tk.X, expand=True)
        self.progress_bar.start()

        # Clear previous results
        self.clear_results()

        # Use the batch search manager to search for buildings
        self.batch_search_manager.batch_search_by_address(
            address, None, radius_float, None, self.display_search_results, self.update_progress
        )

    def update_progress(self, current, total, status_message=None):
        """
        Update the progress bar and status message.

        Args:
            current: Current progress value
            total: Total progress value
            status_message: Status message to display
        """
        # Update the status label
        if status_message:
            self.status_label.config(text=status_message)

        # Show progress bar if not already visible
        if not self.progress_bar.winfo_ismapped():
            self.progress_bar.pack(fill=tk.X, expand=True)
            self.progress_bar.start()

        # If we have a determinate progress, update the progress bar
        if total > 0 and current <= total:
            self.progress_bar.stop()
            self.progress_bar.config(mode="determinate")
            self.progress_bar["maximum"] = total
            self.progress_bar["value"] = current
        else:
            # Otherwise, use indeterminate mode
            self.progress_bar.config(mode="indeterminate")
            self.progress_bar.start()

    def clear_results(self):
        """Clear the results treeview."""
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)

        self.results_count_label.config(text="0 buildings found")

    def display_search_results(self, buildings):
        """
        Display the search results in the treeview.

        Args:
            buildings (list): List of building data dictionaries
        """
        # Hide progress bar
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

        # Reset UI state
        self.search_button.config(state=tk.NORMAL)

        if not buildings:
            self.status_label.config(text="No buildings found")
            self.show_message("No Results", "No buildings found in the specified area.")
            return

        # Add buildings to the treeview
        for building in buildings:
            # Construct address string if available
            address = ""

            # Check if we have street and housenumber
            if 'street' in building and building['street'] and 'housenumber' in building and building['housenumber']:
                address = f"{building['street']} {building['housenumber']}"

                # Add city if available
                if 'city' in building and building['city']:
                    address += f", {building['city']}"

                # Add postcode if available
                if 'postcode' in building and building['postcode']:
                    address += f" {building['postcode']}"

            # If we don't have street/housenumber but have a name, use that
            elif 'name' in building and building['name']:
                # If we also have a city, include it
                if 'city' in building and building['city']:
                    address = f"{building['name']}, {building['city']}"
                else:
                    address = building['name']

            # If we only have city, use that
            elif 'city' in building and building['city']:
                if 'postcode' in building and building['postcode']:
                    address = f"{building['city']} {building['postcode']}"
                else:
                    address = building['city']

                # If we have a building type, add it
                if 'building_type' in building and building['building_type']:
                    building_type = building['building_type']
                    # Capitalize the first letter of the building type
                    if building_type:
                        building_type = building_type[0].upper() + building_type[1:]
                    address = f"{building_type} in {address}"

            # If we still don't have an address, use the building type if available
            if not address and 'building_type' in building and building['building_type']:
                building_type = building['building_type']
                # Capitalize the first letter of the building type
                if building_type:
                    building_type = building_type[0].upper() + building_type[1:]
                address = building_type

            # If we still don't have an address, use a default value
            if not address:
                address = "Building"

            # Add to treeview
            self.buildings_tree.insert("", tk.END, values=(
                building['id'],
                f"{building['lat']:.6f}",
                f"{building['lng']:.6f}",
                address,
                building.get('url', '')
            ))

        # Update the results count label
        self.results_count_label.config(text=f"{len(buildings)} buildings found")
        self.status_label.config(text="Search completed")

    def handle_error(self, error_msg):
        """
        Handle errors during search.

        Args:
            error_msg: Error message to display
        """
        # Hide progress bar
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

        # Reset UI state
        self.search_button.config(state=tk.NORMAL)
        self.status_label.config(text=f"Error: {error_msg}")

        # Show error message
        self.show_message("Error", error_msg)

    def show_message(self, title, message):
        """Show a message box."""
        from tkinter import messagebox
        messagebox.showinfo(title, message)

    def on_building_double_click(self, event):
        """Handle double-click on a building in the treeview."""
        # Get the selected item
        selected_item = self.buildings_tree.selection()
        if not selected_item:
            return

        # Get the URL from the selected item
        values = self.buildings_tree.item(selected_item[0], "values")
        url = values[4]  # URL is the 5th column (index 4)

        # Open the URL in the browser
        if url:
            import webbrowser
            webbrowser.open(url)

    def on_open_selected(self):
        """Open the selected buildings in the browser."""
        # Get the selected items
        selected_items = self.buildings_tree.selection()
        if not selected_items:
            self.show_message("No Selection", "Please select one or more buildings to open.")
            return

        # Open each selected building
        for item in selected_items:
            values = self.buildings_tree.item(item, "values")
            url = values[4]  # URL is the 5th column (index 4)

            if url:
                import webbrowser
                webbrowser.open(url)

    def update_status(self, status_text):
        """Update the status label."""
        self.status_label.config(text=status_text)

    def run(self):
        """Run the application."""
        # Start the main loop
        self.root.mainloop()


def main():
    """Main entry point for the application."""
    try:
        # Create and run the application
        app = AutocompleteApp()
        app.run()
        return 0
    except Exception as e:
        logger.error(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
