# Code Refragmentation Plan

## 1. Consolidate App Implementations

### Current Issues
- Multiple app implementations (`PropertyScraperApp` and `OptimizedPropertyScraperApp`)
- Duplicate initialization code
- Inconsistent optimization usage

### Refactoring Steps
1. Create a base `AppBase` class with common functionality
2. Refactor `PropertyScraperApp` to inherit from `AppBase`
3. Refactor `OptimizedPropertyScraperApp` to inherit from `AppBase`
4. Move common initialization code to the base class
5. Create a factory method to instantiate the appropriate app version

```python
# core/app_base.py
class AppBase:
    """Base class for all app implementations"""
    
    def __init__(self, root):
        self.root = root
        # Common initialization code
        
    def initialize_ui(self):
        """Initialize UI components"""
        pass  # To be implemented by subclasses
        
    # Other common methods
```

## 2. Unify Batch Search Managers

### Current Issues
- Multiple batch search manager implementations with overlapping functionality
- Inconsistent method signatures and behavior
- Duplicate code across managers

### Refactoring Steps
1. Create a base `BatchSearchManagerBase` class
2. Refactor all batch search managers to inherit from the base class
3. Standardize method signatures and behavior
4. Extract common functionality to the base class
5. Create a factory method to instantiate the appropriate manager

```python
# core/batch_search/batch_search_manager_base.py
class BatchSearchManagerBase:
    """Base class for all batch search managers"""
    
    def __init__(self, app):
        self.app = app
        
    def batch_search_by_address(self, address, radius, max_results=100):
        """Base implementation for batch search by address"""
        pass  # To be implemented by subclasses
        
    # Other common methods
```

## 3. Refactor Large Initialization Methods

### Current Issues
- Large `__init__` methods in app classes
- Too many responsibilities in single methods
- Difficult to understand and maintain

### Refactoring Steps
1. Break down large initialization methods into smaller, focused methods
2. Group related initialization code
3. Use lazy initialization where appropriate
4. Create dedicated initialization classes for complex subsystems

Example refactoring:
```python
# Before
def __init__(self, root):
    self.root = root
    # 100+ lines of initialization code
    
# After
def __init__(self, root):
    self.root = root
    self._init_logging()
    self._init_ui_components()
    self._init_services()
    self._init_data_sources()
    
def _init_logging(self):
    # Logging initialization code
    
def _init_ui_components(self):
    # UI initialization code
    
# etc.
```

## 4. Centralize Message Box Handling

### Current Issues
- Message boxes defined in multiple places
- Inconsistent usage patterns
- Duplicate code

### Refactoring Steps
1. Ensure all message boxes are defined in `ui/message_boxes.py`
2. Remove duplicate message box definitions
3. Standardize message box usage patterns
4. Create application-specific message box methods in the `MessageBoxes` class

## 5. Organize Helper Functions

### Current Issues
- Helper functions scattered across multiple files
- Inconsistent naming and usage patterns
- Duplicate functionality

### Refactoring Steps
1. Group related helper functions into dedicated modules
2. Create a consistent naming scheme for helper functions
3. Remove duplicate functionality
4. Document helper functions with clear docstrings

## 6. Implement Consistent Error Handling

### Current Issues
- Inconsistent error handling patterns
- Some errors swallowed without proper logging
- Unclear error messages

### Refactoring Steps
1. Create a centralized error handling module
2. Define standard error handling patterns
3. Ensure all errors are properly logged
4. Provide clear error messages to users

## 7. Optimize Imports and Dependencies

### Current Issues
- Circular dependencies
- Excessive imports
- Inconsistent import patterns

### Refactoring Steps
1. Analyze and resolve circular dependencies
2. Optimize imports to reduce loading time
3. Standardize import patterns
4. Use lazy loading for heavy dependencies

## 8. Improve Code Documentation

### Current Issues
- Inconsistent documentation
- Missing docstrings
- Unclear code purpose

### Refactoring Steps
1. Ensure all classes and methods have clear docstrings
2. Document complex algorithms and business logic
3. Add code examples where appropriate
4. Update README and other documentation

## Implementation Order

1. Create base classes and refactor inheritance hierarchy
2. Centralize message box handling
3. Organize helper functions
4. Refactor large initialization methods
5. Implement consistent error handling
6. Optimize imports and dependencies
7. Improve code documentation
