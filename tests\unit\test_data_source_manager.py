#!/usr/bin/env python3
"""
Test script for the DataSourceManager class.
"""

import os
import sys
import unittest
import tkinter as tk

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from utils.data_source_manager import DataSourceManager
from api.google_maps.google_maps_integration import GoogleMapsIntegration

class TestDataSourceManager(unittest.TestCase):
    """Test cases for the DataSourceManager class."""

    def setUp(self):
        """Set up the test environment."""
        # Create a root window (needed for the DataSourceManager)
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window

        # Initialize Google Maps integration
        self.google_maps = GoogleMapsIntegration()

        # Initialize the DataSourceManager
        self.data_source_manager = DataSourceManager(google_maps_api=self.google_maps, root=self.root)

    def tearDown(self):
        """Clean up after the test."""
        self.root.destroy()

    def test_get_regions(self):
        """Test getting regions."""
        # Get regions
        regions, source = self.data_source_manager.get_regions(language="english")

        # Check that regions is a list
        self.assertIsInstance(regions, list)

        # Print some debug info
        print(f"Got {len(regions)} regions from {source}")
        if regions:
            print(f"First 5 regions: {regions[:5]}")

    def test_get_cities_in_region(self):
        """Test getting cities in a region."""
        # Get regions first
        regions, _ = self.data_source_manager.get_regions(language="english")

        # Skip the test if no regions are found
        if not regions:
            self.skipTest("No regions found, skipping city test")

        # Get cities for the first region
        test_region = regions[0]
        cities, source = self.data_source_manager.get_cities_in_region(test_region)

        # Check that cities is a list
        self.assertIsInstance(cities, list)

        # Print some debug info
        print(f"Got {len(cities)} cities from {source} for region {test_region}")
        if cities:
            print(f"First 5 cities: {cities[:5]}")


if __name__ == "__main__":
    unittest.main()
