"""
Test for OSM and CUZK integration.

This module tests the integration between OpenStreetMap and CUZK,
verifying that the application can fetch buildings from OSM and
open the CUZK website with the retrieved RUIAN IDs.
"""

import os
import sys
import unittest
import logging
import webbrowser
import requests
from unittest.mock import MagicMock, patch

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OSM_CUZK_Test")

# Import the necessary modules
from api.osm.osm_integration import OSMIntegration
from api.cuzk.cuzk_integration import CUZKIntegration
from utils.enhanced_cache_manager import EnhancedCacheManager
from core.optimized_network import create_optimized_session


class TestOSMCUZKIntegration(unittest.TestCase):
    """Test the integration between OSM and CUZK."""

    @classmethod
    def setUpClass(cls):
        """Set up the test environment."""
        # Create a mock application
        cls.app = MagicMock()

        # Create a session
        cls.app.session = create_optimized_session()

        # Create a cache manager
        cls.app.cache_manager = EnhancedCacheManager(
            default_expiry=300,  # 5 minutes
            disk_cache_dir="test_cache",
            max_memory_items=100
        )

        # Create the OSM integration
        cls.osm_integration = OSMIntegration(cls.app)

        # Create the CUZK integration
        cls.cuzk_integration = CUZKIntegration()

        # Set up the app with the integrations
        cls.app.osm_integration = cls.osm_integration
        cls.app.cuzk_integration = cls.cuzk_integration

        # Coordinates for testing (Prague)
        cls.test_lat = 50.0755
        cls.test_lng = 14.4378
        cls.test_radius = 500  # meters

    @classmethod
    def tearDownClass(cls):
        """Clean up after the tests."""
        # Clean up the cache
        if hasattr(cls.app, 'cache_manager'):
            cls.app.cache_manager.clear_all()

    def test_fetch_buildings_from_osm(self):
        """Test fetching buildings from OSM."""
        logger.info(f"Testing OSM building fetch at coordinates {self.test_lat}, {self.test_lng}")

        # Fetch buildings from OSM
        buildings = self.osm_integration.search_buildings_by_coordinates(
            self.test_lat,
            self.test_lng,
            self.test_radius
        )

        # Verify that buildings were found
        self.assertIsNotNone(buildings, "No buildings returned from OSM")
        logger.info(f"Found {len(buildings)} buildings from OSM")

        # Check if any buildings have RUIAN references
        buildings_with_ruian = [b for b in buildings if 'ruian_ref' in b]
        logger.info(f"Found {len(buildings_with_ruian)} buildings with RUIAN references")

        # We should have at least one building with a RUIAN reference
        # Note: This test might fail if there are no buildings with RUIAN references in the area
        # In a real test, we would use a known location with RUIAN references
        self.assertTrue(len(buildings_with_ruian) > 0, "No buildings with RUIAN references found")

        # Return the buildings for use in other tests
        return buildings_with_ruian

    def test_generate_cuzk_url(self):
        """Test generating a CUZK URL from coordinates."""
        logger.info("Testing CUZK URL generation")

        # Generate a CUZK URL from coordinates
        url = self.cuzk_integration.generate_cuzk_url(self.test_lat, self.test_lng)

        # Verify that a URL was generated
        self.assertIsNotNone(url, "No URL generated")
        self.assertTrue(url.startswith("https://nahlizenidokn.cuzk.cz/"), "URL does not point to CUZK website")

        logger.info(f"Generated CUZK URL: {url}")

        # Return the URL for use in other tests
        return url

    @patch('webbrowser.open')
    def test_open_cuzk_with_ruian_id(self, mock_open):
        """Test opening the CUZK website with a RUIAN ID."""
        # First, get buildings with RUIAN references
        buildings = self.test_fetch_buildings_from_osm()

        # Skip the test if no buildings with RUIAN references were found
        if not buildings:
            self.skipTest("No buildings with RUIAN references found")

        # Get the first building with a RUIAN reference
        building = buildings[0]
        ruian_id = building['ruian_ref']

        logger.info(f"Testing opening CUZK with RUIAN ID: {ruian_id}")

        # Open the CUZK website with the RUIAN ID
        self.cuzk_integration.open_cuzk_in_browser(building_id=ruian_id)

        # Verify that webbrowser.open was called with the correct URL
        mock_open.assert_called_once()
        called_url = mock_open.call_args[0][0]
        self.assertTrue("nahlizenidokn.cuzk.cz" in called_url, "URL does not point to CUZK website")
        self.assertTrue(str(ruian_id) in called_url, "RUIAN ID not found in URL")

        logger.info(f"Successfully opened CUZK with RUIAN ID: {ruian_id}")

    def test_cuzk_url_accessibility(self):
        """Test that the CUZK website is accessible."""
        logger.info("Testing CUZK website accessibility")

        # Generate a CUZK URL
        url = self.test_generate_cuzk_url()

        # Try to access the URL
        try:
            response = requests.get(url, timeout=10)
            self.assertEqual(response.status_code, 200, "CUZK website returned non-200 status code")
            logger.info("CUZK website is accessible")
        except requests.RequestException as e:
            logger.error(f"Error accessing CUZK website: {e}")
            self.fail(f"Error accessing CUZK website: {e}")


if __name__ == '__main__':
    unittest.main()
