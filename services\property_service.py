"""
Property data services for the Czech Property Registry application.

This module provides functionality for handling property data,
including fetching, formatting, and displaying property information.
"""

import tkinter as tk
from tkinter import ttk
from ui.message_boxes import MessageBoxes


class PropertyService:
    """
    Service for handling property data operations.

    This class provides methods for fetching, formatting, and displaying
    property information from various sources.
    """

    def __init__(self, app):
        """
        Initialize the PropertyService.

        Args:
            app: The main application instance for callbacks and UI access
        """
        self.app = app
        self.property_list = []

    def format_property_result(self, property_data, index=0):
        """
        Format property data for display in the results text area.

        Args:
            property_data (dict): Property data dictionary
            index (int): Index of the property in a list

        Returns:
            str: Formatted property information
        """
        # Extract property information with defaults
        owner_name = property_data.get('owner_name', 'Unknown')
        owner_address = property_data.get('owner_address', 'Unknown')
        prop_type = property_data.get('property_type', 'Unknown')
        parcel_number = property_data.get('parcel_number', 'Unknown')
        cadastral_territory = property_data.get('cadastral_territory', 'Unknown')

        # Build the result text
        result_text = f"Property {index+1}:\n"
        result_text += f"Owner: {owner_name}\n"
        result_text += f"Address: {owner_address}\n"
        result_text += f"Type: {prop_type}\n"
        result_text += f"Parcel: {parcel_number}\n"
        result_text += f"Cadastral Territory: {cadastral_territory}\n\n"

        return result_text

    def fetch_properties_by_address(self, address):
        """
        Fetch properties at a specific address from the CUZK website.

        Args:
            address (str): Address to search for

        Returns:
            list: List of property data dictionaries
        """
        try:
            # Show status
            self.app.show_status(f"Searching for properties at {address}...")

            # Use the CUZK scraper to search by address
            if hasattr(self.app, 'cuzk_scraper'):
                return self.app.cuzk_scraper.search_by_address(address)

            # No fallback to sample data - show an error message
            MessageBoxes.show_error(
                "Error: API Not Available",
                "This application does not use demo or fallback data.\n\n"
                "Please configure the application with valid API keys and try again."
            )
            return []

        except Exception as e:
            print(f"Error fetching properties by address: {e}")
            return None

    def display_properties(self, properties):
        """
        Display a list of properties in the results frame.

        Args:
            properties (list): List of property data dictionaries
        """
        try:
            # Clear the results frame
            for widget in self.app.results_frame.winfo_children():
                widget.destroy()

            # If no properties were found, show a message
            if not properties or len(properties) == 0:
                ttk.Label(
                    self.app.results_frame,
                    text="No properties found.",
                    font=("Arial", 12, "bold"),
                    foreground="red"
                ).pack(pady=20)
                return

            # Create a frame for the property list
            property_frame = ttk.Frame(self.app.results_frame)
            property_frame.pack(fill="both", expand=True, padx=5, pady=5)

            # Create a treeview for the properties
            self.app.property_tree = ttk.Treeview(
                property_frame,
                columns=("type", "owner", "address", "property_type", "parcel"),
                show="headings",
                height=10
            )

            # Define column headings
            self.app.property_tree.heading("type", text="Data")
            self.app.property_tree.heading("owner", text="Owner")
            self.app.property_tree.heading("address", text="Address")
            self.app.property_tree.heading("property_type", text="Type")
            self.app.property_tree.heading("parcel", text="Parcel")

            # Define column widths
            self.app.property_tree.column("type", width=30, anchor="center")
            self.app.property_tree.column("owner", width=150)
            self.app.property_tree.column("address", width=200)
            self.app.property_tree.column("property_type", width=100)
            self.app.property_tree.column("parcel", width=100)

            # Configure row tags for styling
            self.app.property_tree.tag_configure("sample", background="#ffe0e0")
            self.app.property_tree.tag_configure("real", background="#e0ffe0")

            # Add a scrollbar
            scrollbar = ttk.Scrollbar(property_frame, orient="vertical", command=self.app.property_tree.yview)
            self.app.property_tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side="right", fill="y")
            self.app.property_tree.pack(side="left", fill="both", expand=True)

            # Add touchpad scrolling support for the property tree
            if hasattr(self.app, 'scroll_manager'):
                self.app.property_tree.bind("<MouseWheel>",
                    lambda event: self.app.scroll_manager._on_treeview_mousewheel(event))  # Windows
                self.app.property_tree.bind("<Button-4>",
                    lambda event: self.app.scroll_manager._on_treeview_mousewheel(event))  # Linux scroll up
                self.app.property_tree.bind("<Button-5>",
                    lambda event: self.app.scroll_manager._on_treeview_mousewheel(event))  # Linux scroll down

            # Store the property list for later use
            self.property_list = properties
            self.app.property_list = properties  # For backward compatibility

            # Add properties to the treeview
            for i, prop in enumerate(properties):
                owner_name = prop.get('owner_name', 'Unknown')
                owner_address = prop.get('owner_address', 'Unknown')
                property_type = prop.get('property_type', 'Unknown')
                parcel_number = prop.get('parcel_number', 'Unknown')

                # Check if this is sample data
                is_sample = prop.get('is_sample', False) or prop.get('source') == 'sample'
                data_type = "⚠️" if is_sample else "✓"

                item_id = self.app.property_tree.insert("", "end", values=(
                    data_type,
                    owner_name,
                    owner_address,
                    property_type,
                    parcel_number
                ))

                # Set the row background color based on the data type
                if is_sample:
                    self.app.property_tree.item(item_id, tags=("sample",))
                else:
                    self.app.property_tree.item(item_id, tags=("real",))

            # Add a button to generate a letter for the selected property
            button_frame = ttk.Frame(self.app.results_frame)
            button_frame.pack(fill="x", padx=5, pady=5)

            ttk.Button(
                button_frame,
                text="Generate Letter for Selected",
                command=self.generate_letter_for_selected
            ).pack(side="left", padx=5)

            ttk.Button(
                button_frame,
                text="Export Selected",
                command=self.export_selected_property
            ).pack(side="right", padx=5)

            # Show a message about the data source
            source_frame = ttk.Frame(self.app.results_frame)
            source_frame.pack(fill="x", padx=5, pady=5)

            # Count real and sample data
            real_count = sum(1 for p in properties if not (p.get('is_sample', False) or p.get('source') == 'sample'))
            sample_count = len(properties) - real_count

            if sample_count > 0:
                ttk.Label(
                    source_frame,
                    text=f"⚠️ {sample_count} of {len(properties)} properties are sample data.",
                    foreground="orange"
                ).pack(side="left")

            # Show status
            self.app.show_status(f"Found {len(properties)} properties.")

        except Exception as e:
            print(f"Error displaying properties: {e}")
            MessageBoxes.show_error("Error", f"An error occurred while displaying properties: {str(e)}")

    def generate_letter_for_selected(self):
        """Generate a letter for the selected property"""
        # Get the selected property
        if not hasattr(self.app, 'property_tree'):
            MessageBoxes.show_info("No Selection", "Please select a property first.")
            return

        selected_items = self.app.property_tree.selection()
        if not selected_items:
            MessageBoxes.show_info("No Selection", "Please select a property first.")
            return

        # Get the selected property data
        selected_index = self.app.property_tree.index(selected_items[0])
        if selected_index >= len(self.property_list):
            MessageBoxes.show_error("Error", "Invalid selection.")
            return

        selected_property = self.property_list[selected_index]

        # Generate the letter
        if hasattr(self.app, 'generate_letter_for_property'):
            self.app.generate_letter_for_property(selected_property)
        else:
            MessageBoxes.show_info("Not Implemented", "Letter generation is not implemented.")

    def export_selected_property(self):
        """Export the selected property to a CSV file"""
        # Get the selected property
        if not hasattr(self.app, 'property_tree'):
            MessageBoxes.show_info("No Selection", "Please select a property first.")
            return

        selected_items = self.app.property_tree.selection()
        if not selected_items:
            MessageBoxes.show_info("No Selection", "Please select a property first.")
            return

        # Get the selected property data
        selected_index = self.app.property_tree.index(selected_items[0])
        if selected_index >= len(self.property_list):
            MessageBoxes.show_error("Error", "Invalid selection.")
            return

        selected_property = self.property_list[selected_index]

        # Export the property
        if hasattr(self.app, 'export_property'):
            self.app.export_property(selected_property)
        else:
            MessageBoxes.show_info("Not Implemented", "Export is not implemented.")

    def generate_sample_properties(self, **kwargs):
        """
        This method is deprecated and should not be used.
        The application does not use sample or fallback data.

        Args:
            **kwargs: Deprecated parameters (not used)

        Returns:
            list: Empty list as sample data is not supported
        """
        # Show an error message
        MessageBoxes.show_error(
            "Error: Sample Data Not Available",
            "This application does not use demo or fallback data.\n\n"
            "Please configure the application with valid API keys and try again."
        )

        # Return an empty list
        return []
