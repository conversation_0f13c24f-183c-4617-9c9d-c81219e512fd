"""
Minimal Loading Window for the Czech Property Registry application.

This module provides a very simple splash screen that appears during application startup.
"""

import tkinter as tk
from tkinter import ttk
import logging

# Configure logging
logger = logging.getLogger(__name__)

class MinimalLoadingWindow:
    """
    A minimal splash screen window that shows loading progress during application startup.
    
    This window displays a progress bar and status messages to inform the user
    about what's happening during the initialization process.
    """
    
    def __init__(self, parent=None, app_title="Czech Property Registry"):
        """
        Initialize the loading window.
        
        Args:
            parent: Parent window (if None, creates a new Toplevel window)
            app_title: Title of the application
        """
        # Create the window
        self.window = tk.Toplevel() if parent else tk.Tk()
        
        # Configure the window
        self.window.title(f"Loading {app_title}")
        self.window.geometry("500x300")
        self.window.resizable(False, False)
        
        # Center the window on screen
        self._center_window()
        
        # Create UI elements
        self._create_ui()
        
        # Make sure the window is shown
        self.window.update()
        
    def _center_window(self):
        """Center the window on the screen."""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def _create_ui(self):
        """Create the UI elements for the loading window."""
        # Create main frame
        main_frame = ttk.Frame(self.window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Application title
        title_label = ttk.Label(
            main_frame, 
            text="Czech Property Registry", 
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Loading text
        logo_label = ttk.Label(
            main_frame, 
            text="LOADING...", 
            font=("Arial", 24, "bold")
        )
        logo_label.pack(pady=(0, 20))
        
        # Current task label
        self.task_label = ttk.Label(
            main_frame, 
            text="Initializing application...",
            font=("Arial", 10)
        )
        self.task_label.pack(pady=(0, 10))
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            orient=tk.HORIZONTAL, 
            length=460, 
            mode="indeterminate"
        )
        self.progress_bar.pack(pady=(0, 10), fill=tk.X)
        self.progress_bar.start()
        
        # Status message
        self.status_label = ttk.Label(
            main_frame, 
            text="Please wait...",
            font=("Arial", 9, "italic")
        )
        self.status_label.pack(pady=(10, 0))
        
    def set_task(self, task_name):
        """
        Set the current task name.
        
        Args:
            task_name: Name of the current task
        """
        self.task_label.config(text=task_name)
        self.window.update()
        
    def set_status(self, message):
        """
        Set the status message.
        
        Args:
            message: Status message to display
        """
        self.status_label.config(text=message)
        self.window.update()
        
    def close(self):
        """Close the loading window."""
        try:
            self.window.destroy()
        except:
            pass  # Window might already be closed
