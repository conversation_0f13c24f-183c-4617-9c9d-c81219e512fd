"""
Simple test script to verify the optimizations.
"""

import time
import logging
import os
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SimpleTest")

# Import the optimized components
from utils.enhanced_cache_manager import EnhancedCacheManager, CacheStorageType
from utils.thread_manager import ThreadManager

def test_cache():
    """Test the enhanced cache manager."""
    logger.info("Testing enhanced cache manager...")
    
    # Create a cache manager
    cache_manager = EnhancedCacheManager(
        default_expiry=300,
        disk_cache_dir="test_cache",
        max_memory_items=1000,
        cleanup_interval=300
    )
    
    # Create a test cache
    cache_manager.create_cache(
        'test_cache',
        expiry=60,
        storage_type=CacheStorageType.BOTH
    )
    
    # Test writing to cache
    start_time = time.time()
    for i in range(100):
        cache_manager.set('test_cache', f"key_{i}", f"value_{i}")
    write_time = time.time() - start_time
    logger.info(f"Time to write 100 items to cache: {write_time:.4f} seconds")
    
    # Test reading from cache
    start_time = time.time()
    for i in range(100):
        value = cache_manager.get('test_cache', f"key_{i}")
        assert value == f"value_{i}", f"Expected 'value_{i}', got '{value}'"
    read_time = time.time() - start_time
    logger.info(f"Time to read 100 items from cache: {read_time:.4f} seconds")
    
    # Test cache statistics
    stats = cache_manager.get_stats()
    logger.info(f"Cache statistics: {stats}")
    
    return {
        'write_time': write_time,
        'read_time': read_time
    }

def test_thread_manager():
    """Test the thread manager."""
    logger.info("Testing thread manager...")
    
    # Create a thread manager
    thread_manager = ThreadManager(max_workers=5)
    
    # Create a simple task
    def task(task_id, sleep_time):
        time.sleep(sleep_time)
        return f"Task {task_id} completed after {sleep_time} seconds"
    
    # Test submitting tasks
    start_time = time.time()
    futures = []
    for i in range(10):
        future = thread_manager.submit(
            f"task_{i}",
            task,
            f"task_{i}",
            0.1  # 100ms sleep
        )
        futures.append((i, future))
    submit_time = time.time() - start_time
    logger.info(f"Time to submit 10 tasks: {submit_time:.4f} seconds")
    
    # Test waiting for tasks to complete
    start_time = time.time()
    results = []
    for i, future in futures:
        try:
            result = future.result(timeout=2.0)
            results.append(result)
        except Exception as e:
            logger.error(f"Error in task {i}: {e}")
    wait_time = time.time() - start_time
    logger.info(f"Time to wait for 10 tasks: {wait_time:.4f} seconds")
    logger.info(f"Completed {len(results)} tasks")
    
    # Shut down the thread manager
    thread_manager.shutdown()
    
    return {
        'submit_time': submit_time,
        'wait_time': wait_time
    }

def main():
    """Run the tests."""
    logger.info("Running simple tests...")
    
    # Test the cache
    cache_results = test_cache()
    
    # Test the thread manager
    thread_results = test_thread_manager()
    
    # Print the results
    logger.info("Test results:")
    logger.info(f"Cache: {cache_results}")
    logger.info(f"Thread Manager: {thread_results}")

if __name__ == "__main__":
    main()
