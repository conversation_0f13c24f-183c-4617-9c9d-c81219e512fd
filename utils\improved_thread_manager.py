"""
Improved Thread Manager for the Czech Property Registry application.

This module provides a robust thread manager that properly handles background tasks
and ensures the UI remains responsive.
"""

import threading
import queue
import time
import logging
from typing import Dict, Any, Callable, Optional, List, Tuple
from concurrent.futures import ThreadPoolExecutor, Future

# Configure logging
logger = logging.getLogger(__name__)

class ImprovedThreadManager:
    """
    A robust thread manager that properly handles background tasks.
    
    This class provides methods for running tasks in background threads while
    ensuring the UI remains responsive. It includes features like task cancellation,
    progress reporting, and error handling.
    """
    
    def __init__(self, max_workers: int = 5):
        """
        Initialize the thread manager.
        
        Args:
            max_workers: Maximum number of worker threads
        """
        self.executor = ThreadPoolExecutor(max_workers=max_workers, 
                                          thread_name_prefix="PropertyRegistry")
        self.tasks: Dict[str, Future] = {}
        self.tasks_lock = threading.Lock()
        self.running = True
        
        # For monitoring thread health
        self.health_check_interval = 5  # seconds
        self.start_health_monitor()
        
        logger.info(f"ImprovedThreadManager initialized with {max_workers} workers")
        
    def start_health_monitor(self):
        """Start a thread to monitor the health of the thread pool."""
        def monitor_health():
            while self.running:
                try:
                    # Log the current state of tasks
                    with self.tasks_lock:
                        active_tasks = sum(1 for future in self.tasks.values() if not future.done())
                        total_tasks = len(self.tasks)
                    
                    logger.debug(f"Thread pool health: {active_tasks}/{total_tasks} active tasks")
                    
                    # Sleep for the check interval
                    time.sleep(self.health_check_interval)
                except Exception as e:
                    logger.error(f"Error in thread health monitor: {e}")
        
        # Start the monitor thread
        threading.Thread(target=monitor_health, daemon=True, 
                         name="ThreadHealthMonitor").start()
    
    def submit_task(self, task_id: str, func: Callable, *args, 
                   on_done: Optional[Callable] = None,
                   on_error: Optional[Callable] = None,
                   on_progress: Optional[Callable] = None,
                   **kwargs) -> Future:
        """
        Submit a task to be executed in a background thread.
        
        Args:
            task_id: Unique identifier for the task
            func: Function to execute
            *args: Arguments to pass to the function
            on_done: Callback to execute when the task is done
            on_error: Callback to execute when the task raises an exception
            on_progress: Callback to receive progress updates
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            Future: Future object representing the task
        """
        # Cancel any existing task with the same ID
        self.cancel_task(task_id)
        
        # Create a wrapper function that handles callbacks
        def task_wrapper():
            try:
                logger.debug(f"Starting task {task_id}")
                
                # Add progress callback if provided
                if on_progress:
                    if 'progress_callback' in kwargs:
                        # Replace the existing progress callback
                        original_callback = kwargs['progress_callback']
                        
                        def combined_callback(*cb_args, **cb_kwargs):
                            # Call the original callback
                            if original_callback:
                                original_callback(*cb_args, **cb_kwargs)
                            
                            # Call our progress callback
                            on_progress(*cb_args, **cb_kwargs)
                        
                        kwargs['progress_callback'] = combined_callback
                    else:
                        # Add a new progress callback
                        kwargs['progress_callback'] = on_progress
                
                # Execute the function
                result = func(*args, **kwargs)
                
                logger.debug(f"Task {task_id} completed successfully")
                
                # Call the done callback if provided
                if on_done:
                    on_done(result)
                
                return result
            except Exception as e:
                logger.error(f"Error in task {task_id}: {e}", exc_info=True)
                
                # Call the error callback if provided
                if on_error:
                    on_error(e)
                
                # Re-raise the exception
                raise
        
        # Submit the task to the executor
        with self.tasks_lock:
            future = self.executor.submit(task_wrapper)
            self.tasks[task_id] = future
            logger.debug(f"Submitted task {task_id}")
            return future
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a task if it's still running.
        
        Args:
            task_id: ID of the task to cancel
            
        Returns:
            bool: True if the task was cancelled, False otherwise
        """
        with self.tasks_lock:
            if task_id in self.tasks:
                future = self.tasks[task_id]
                if not future.done():
                    cancelled = future.cancel()
                    if cancelled:
                        logger.debug(f"Cancelled task {task_id}")
                    else:
                        logger.debug(f"Failed to cancel task {task_id}")
                    return cancelled
        return False
    
    def cancel_all_tasks(self) -> int:
        """
        Cancel all running tasks.
        
        Returns:
            int: Number of tasks cancelled
        """
        cancelled_count = 0
        with self.tasks_lock:
            for task_id, future in list(self.tasks.items()):
                if not future.done():
                    if future.cancel():
                        cancelled_count += 1
                        logger.debug(f"Cancelled task {task_id}")
        
        logger.info(f"Cancelled {cancelled_count} tasks")
        return cancelled_count
    
    def is_task_running(self, task_id: str) -> bool:
        """
        Check if a task is currently running.
        
        Args:
            task_id: ID of the task to check
            
        Returns:
            bool: True if the task is running, False otherwise
        """
        with self.tasks_lock:
            if task_id in self.tasks:
                return not self.tasks[task_id].done()
        return False
    
    def get_running_tasks(self) -> List[str]:
        """
        Get a list of all running task IDs.
        
        Returns:
            list: List of running task IDs
        """
        with self.tasks_lock:
            return [task_id for task_id, future in self.tasks.items() 
                   if not future.done()]
    
    def shutdown(self, wait: bool = True):
        """
        Shut down the thread manager.
        
        Args:
            wait: Whether to wait for tasks to complete
        """
        logger.info("Shutting down ImprovedThreadManager")
        self.running = False
        
        # Cancel all tasks if not waiting
        if not wait:
            self.cancel_all_tasks()
        
        # Shut down the executor
        self.executor.shutdown(wait=wait)
        logger.info("ImprovedThreadManager shut down complete")
