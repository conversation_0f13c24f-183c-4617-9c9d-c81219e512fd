"""
Test script to verify that Mapy.cz integration is working correctly.

This script tests the Mapy.cz integration by:
1. Initializing the Mapy.cz integration
2. Testing geocoding functionality
3. Testing reverse geocoding functionality
4. Testing place predictions (autocomplete)
5. Testing city boundary detection
6. Testing CUZK URL generation
"""

import logging
import sys
import time
import tkinter as tk
from tkinter import ttk

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("MapyCZIntegrationTest")

# Import the necessary modules
try:
    from api.mapycz.mapycz_integration import MapyCZIntegration
    from api.cuzk.cuzk_integration import CUZKIntegration
except ImportError as e:
    logger.error(f"Error importing modules: {e}")
    logger.error("Make sure you're running this script from the project root directory")
    sys.exit(1)

def test_geocoding():
    """Test geocoding functionality"""
    logger.info("\n=== Testing Geocoding ===")
    
    # Initialize the Mapy.cz integration
    mapycz = MapyCZIntegration()
    
    # Test addresses
    test_addresses = [
        "Prague, Czech Republic",
        "Brno, Czech Republic",
        "Bušovice, Czech Republic",
        "Klecany, Czech Republic",
        "Václavské náměstí, Prague, Czech Republic"
    ]
    
    results = {}
    for address in test_addresses:
        logger.info(f"\nGeocoding address: {address}")
        result = mapycz.get_coordinates(address)
        results[address] = result
        
        if result and 'lat' in result and 'lng' in result:
            logger.info(f"Success: {result['lat']}, {result['lng']}")
            logger.info(f"Formatted address: {result.get('formatted_address', 'N/A')}")
        else:
            logger.error(f"Failed to geocode address: {address}")
    
    return results

def test_reverse_geocoding(geocoding_results):
    """Test reverse geocoding functionality"""
    logger.info("\n=== Testing Reverse Geocoding ===")
    
    # Initialize the Mapy.cz integration
    mapycz = MapyCZIntegration()
    
    # Use the results from geocoding test
    for address, result in geocoding_results.items():
        if result and 'lat' in result and 'lng' in result:
            lat = result['lat']
            lng = result['lng']
            
            logger.info(f"\nReverse geocoding coordinates: {lat}, {lng}")
            reverse_result = mapycz.reverse_geocode(lat, lng)
            
            if reverse_result and 'formatted_address' in reverse_result:
                logger.info(f"Success: {reverse_result['formatted_address']}")
            else:
                logger.error(f"Failed to reverse geocode coordinates: {lat}, {lng}")

def test_place_predictions():
    """Test place predictions functionality"""
    logger.info("\n=== Testing Place Predictions ===")
    
    # Initialize the Mapy.cz integration
    mapycz = MapyCZIntegration()
    
    # Test queries
    test_queries = [
        "Prague",
        "Brno",
        "Karlovy",
        "Václavské",
        "Náměstí"
    ]
    
    results = {}
    for query in test_queries:
        logger.info(f"\nGetting place predictions for '{query}'...")
        predictions = mapycz.get_place_predictions(query)
        results[query] = predictions
        
        if predictions:
            logger.info(f"Found {len(predictions)} predictions:")
            for i, prediction in enumerate(predictions[:3]):  # Show only first 3
                logger.info(f"  {i+1}. {prediction.get('description', 'N/A')}")
        else:
            logger.warning(f"No predictions found for '{query}'")
    
    return results

def test_city_boundaries():
    """Test city boundary detection"""
    logger.info("\n=== Testing City Boundaries ===")
    
    # Initialize the Mapy.cz integration
    mapycz = MapyCZIntegration()
    
    # Test cities
    test_cities = [
        "Prague",
        "Brno",
        "Bušovice",
        "Klecany"
    ]
    
    results = {}
    for city in test_cities:
        logger.info(f"\nGetting boundaries for city: {city}")
        boundaries = mapycz.get_city_boundaries(city)
        results[city] = boundaries
        
        if boundaries:
            logger.info(f"Success: {boundaries}")
            
            # Generate coordinates within the city
            coordinates = mapycz.generate_coordinates_in_city(boundaries)
            logger.info(f"Generated {len(coordinates)} coordinates within the city")
        else:
            logger.error(f"Failed to get boundaries for city: {city}")
    
    return results

def test_cuzk_url_generation(geocoding_results):
    """Test CUZK URL generation"""
    logger.info("\n=== Testing CUZK URL Generation ===")
    
    # Initialize the Mapy.cz integration
    mapycz = MapyCZIntegration()
    
    # Initialize the CUZK integration
    cuzk = CUZKIntegration()
    
    # Use the results from geocoding test
    for address, result in geocoding_results.items():
        if result and 'lat' in result and 'lng' in result:
            lat = result['lat']
            lng = result['lng']
            
            logger.info(f"\nGenerating CUZK URL for coordinates: {lat}, {lng}")
            
            # Generate CUZK URL using Mapy.cz integration
            url = mapycz.generate_cuzk_url(lat, lng)
            logger.info(f"Mapy.cz CUZK URL: {url}")
            
            # Generate CUZK URL using CUZK integration
            cuzk_url = cuzk.generate_cuzk_url(lat, lng, use_mapa_identifikace=True)
            logger.info(f"CUZK integration URL: {cuzk_url}")
            
            # Compare the two URLs
            if url == cuzk_url:
                logger.info(f"URLs match: Mapy.cz integration is being used correctly")
            else:
                logger.warning(f"URLs do not match!")
                logger.warning(f"Mapy.cz URL: {url}")
                logger.warning(f"CUZK URL: {cuzk_url}")

def main():
    """Main function"""
    logger.info("Starting Mapy.cz integration test")
    
    # Test geocoding
    geocoding_results = test_geocoding()
    
    # Test reverse geocoding
    test_reverse_geocoding(geocoding_results)
    
    # Test place predictions
    test_place_predictions()
    
    # Test city boundaries
    test_city_boundaries()
    
    # Test CUZK URL generation
    test_cuzk_url_generation(geocoding_results)
    
    logger.info("\nMapy.cz integration test completed")

if __name__ == "__main__":
    main()
