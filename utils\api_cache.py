"""
API Cache Module for Czech Property Registry

This module provides caching functionality for API calls to reduce the number of requests
and improve performance.
"""

import os
import json
import time
import hashlib
import threading

class APICache:
    """Cache for API responses to reduce API calls and improve performance"""

    def __init__(self, cache_dir='api_cache', expiration_hours=24, always_prefer_api=True):
        """
        Initialize the API cache

        Args:
            cache_dir (str): Directory to store cache files
            expiration_hours (int): Number of hours before cache entries expire
            always_prefer_api (bool): Whether to always prefer API data over cached data
        """
        self.cache_dir = cache_dir
        self.expiration_hours = expiration_hours
        self.always_prefer_api = always_prefer_api
        self.max_cache_age = 7 * 24  # Maximum cache age in hours (7 days)
        self.ensure_cache_dir()

    def ensure_cache_dir(self):
        """Ensure the cache directory exists"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

    def get_cache_key(self, url, params=None):
        """
        Generate a unique cache key for a URL and parameters

        Args:
            url (str): The API URL
            params (dict, optional): Query parameters

        Returns:
            str: A unique cache key
        """
        # Create a string representation of the URL and parameters
        key_data = url
        if params:
            key_data += json.dumps(params, sort_keys=True)

        # Create a hash of the key data
        return hashlib.md5(key_data.encode('utf-8')).hexdigest()

    def get_cache_path(self, cache_key):
        """
        Get the file path for a cache key

        Args:
            cache_key (str): The cache key

        Returns:
            str: Path to the cache file
        """
        return os.path.join(self.cache_dir, f"{cache_key}.json")

    # In-memory cache for frequently accessed items
    _memory_cache = {}
    _memory_cache_lock = threading.Lock()

    def get(self, url, params=None, force_api=None):
        """
        Get a cached response if available and not expired

        Args:
            url (str): The API URL
            params (dict, optional): Query parameters
            force_api (bool, optional): Force API refresh even if cache is available

        Returns:
            dict: The cached response or None if not found or expired
        """
        # Determine if we should use API data
        use_api = force_api if force_api is not None else self.always_prefer_api

        # If we're using API data, return None to force a refresh
        if use_api:
            print(f"Always preferring API data for {url}")
            return None

        cache_key = self.get_cache_key(url, params)

        # First check in-memory cache (faster than disk)
        with APICache._memory_cache_lock:
            if cache_key in APICache._memory_cache:
                cache_entry = APICache._memory_cache[cache_key]
                # Check if the in-memory cache has expired
                if time.time() < cache_entry['expiration_time']:
                    return cache_entry['data']
                else:
                    # Remove expired entry from memory cache
                    del APICache._memory_cache[cache_key]

        # If not in memory, check disk cache
        cache_path = self.get_cache_path(cache_key)
        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                # Check if the cache has expired
                timestamp = cache_data.get('timestamp', 0)
                expiration_time = timestamp + (self.expiration_hours * 3600)

                # Check if cache is too old (beyond max_cache_age)
                if time.time() - timestamp > (self.max_cache_age * 3600):
                    print(f"Cache for {url} is too old (beyond max_cache_age), forcing refresh")
                    return None

                if time.time() < expiration_time:
                    # Store in memory cache for faster future access
                    with APICache._memory_cache_lock:
                        APICache._memory_cache[cache_key] = {
                            'data': cache_data.get('data'),
                            'expiration_time': expiration_time
                        }
                    return cache_data.get('data')
            except Exception as e:
                # Use a more concise error message
                print(f"Cache read error ({cache_key[:8]}...): {e}")

        return None

    def set_api_preference(self, prefer_api=True):
        """
        Set whether to always prefer API data over cached data

        Args:
            prefer_api (bool): Whether to always prefer API data

        Returns:
            bool: Previous value of always_prefer_api
        """
        previous_value = self.always_prefer_api
        self.always_prefer_api = prefer_api
        print(f"API preference changed: {previous_value} -> {prefer_api}")

        # If we're now preferring API, clear all cache entries
        if prefer_api and not previous_value:
            print("Now preferring API data, clearing all cache entries")
            self.clear()

        return previous_value

    def set(self, url, params=None, data=None):
        """
        Cache a response

        Args:
            url (str): The API URL
            params (dict, optional): Query parameters
            data (dict): The response data to cache

        Returns:
            bool: True if successful, False otherwise
        """
        if data is None:
            return False

        cache_key = self.get_cache_key(url, params)
        cache_path = self.get_cache_path(cache_key)
        current_time = time.time()
        expiration_time = current_time + (self.expiration_hours * 3600)

        try:
            # Update in-memory cache first (faster access)
            with APICache._memory_cache_lock:
                APICache._memory_cache[cache_key] = {
                    'data': data,
                    'expiration_time': expiration_time
                }

                # Limit memory cache size to prevent memory leaks
                if len(APICache._memory_cache) > 1000:  # Arbitrary limit
                    # Remove oldest 20% of entries when limit is reached
                    entries = list(APICache._memory_cache.items())
                    entries.sort(key=lambda x: x[1]['expiration_time'])
                    for old_key, _ in entries[:200]:  # Remove oldest 20%
                        del APICache._memory_cache[old_key]

            # Then update disk cache
            cache_data = {
                'timestamp': current_time,
                'url': url,
                'params': params,
                'data': data
            }

            # Use a temporary file to avoid corruption if the process is interrupted
            temp_path = f"{cache_path}.tmp"
            with open(temp_path, 'w', encoding='utf-8') as f:
                # Remove indent for faster writing and smaller files
                json.dump(cache_data, f, ensure_ascii=False)

            # Atomic rename to avoid corruption
            os.replace(temp_path, cache_path)
            return True
        except Exception as e:
            print(f"Cache write error ({cache_key[:8]}...): {e}")
            return False

    def clear(self, url=None, params=None):
        """
        Clear cache entries

        Args:
            url (str, optional): The API URL to clear (or all if None)
            params (dict, optional): Query parameters

        Returns:
            int: Number of cache entries cleared
        """
        if url is None:
            # Clear all cache entries
            count = 0
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.json'):
                    os.remove(os.path.join(self.cache_dir, filename))
                    count += 1
            return count
        else:
            # Clear specific cache entry
            cache_key = self.get_cache_key(url, params)
            cache_path = self.get_cache_path(cache_key)

            if os.path.exists(cache_path):
                os.remove(cache_path)
                return 1
            return 0

    def clear_expired(self):
        """
        Clear expired cache entries from both memory and disk

        Returns:
            int: Number of expired cache entries cleared
        """
        count = 0
        current_time = time.time()
        expiration_seconds = self.expiration_hours * 3600

        # First clear expired entries from memory cache (faster and doesn't need I/O)
        with APICache._memory_cache_lock:
            memory_keys = list(APICache._memory_cache.keys())
            for key in memory_keys:
                if current_time >= APICache._memory_cache[key]['expiration_time']:
                    del APICache._memory_cache[key]
                    count += 1

        # Then clear expired entries from disk cache
        # Use a separate thread for disk operations to avoid blocking
        def clear_disk_cache():
            disk_count = 0
            # Get all JSON files at once to reduce directory access
            try:
                json_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.json')]
            except Exception as e:
                print(f"Error listing cache directory: {e}")
                return

            # Process files in batches to avoid long-running operations
            batch_size = 100
            for i in range(0, len(json_files), batch_size):
                batch = json_files[i:i+batch_size]
                for filename in batch:
                    cache_path = os.path.join(self.cache_dir, filename)
                    try:
                        # Use file modification time as a quick check before opening the file
                        file_mtime = os.path.getmtime(cache_path)
                        if current_time - file_mtime > expiration_seconds:
                            # File is older than expiration time based on mtime, likely expired
                            os.remove(cache_path)
                            disk_count += 1
                            continue

                        # If file is newer, check the actual timestamp in the file
                        # Only read the first part of the file to get the timestamp
                        with open(cache_path, 'r', encoding='utf-8') as f:
                            # Read first 100 bytes which should contain the timestamp
                            header = f.read(100)
                            # Extract timestamp using a simple string search
                            timestamp_start = header.find('"timestamp":')
                            if timestamp_start > 0:
                                timestamp_end = header.find(',', timestamp_start)
                                if timestamp_end > 0:
                                    timestamp_str = header[timestamp_start+12:timestamp_end].strip()
                                    try:
                                        timestamp = float(timestamp_str)
                                        if current_time >= timestamp + expiration_seconds:
                                            os.remove(cache_path)
                                            disk_count += 1
                                    except ValueError:
                                        # If we can't parse the timestamp, fall back to reading the whole file
                                        f.seek(0)
                                        cache_data = json.load(f)
                                        timestamp = cache_data.get('timestamp', 0)
                                        if current_time >= timestamp + expiration_seconds:
                                            os.remove(cache_path)
                                            disk_count += 1
                            else:
                                # If we can't find the timestamp in the header, read the whole file
                                f.seek(0)
                                cache_data = json.load(f)
                                timestamp = cache_data.get('timestamp', 0)
                                if current_time >= timestamp + expiration_seconds:
                                    os.remove(cache_path)
                                    disk_count += 1
                    except Exception as e:
                        # More concise error message
                        print(f"Cache expiration check error ({filename[:8]}...): {e}")

            print(f"Cleared {disk_count} expired disk cache entries")

        # Start disk cache clearing in a background thread
        threading.Thread(target=clear_disk_cache, daemon=True).start()

        return count  # Return count of memory cache entries cleared
