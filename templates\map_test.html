<!DOCTYPE html>
<html>
<head>
    <title>Google Maps API Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    <div id="map"></div>
    <div style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;">
        <h3>API Test Results:</h3>
        <div id="geocode-result">Geocoding: Testing...</div>
        <div id="places-result">Places API: Testing...</div>
    </div>
    <script>
        function initMap() {
            // Create a map centered on Prague
            var map = new google.maps.Map(document.getElementById('map'), {
                center: {lat: {center_lat}, lng: {center_lng}},
                zoom: {zoom}
            });

            // Add a marker
            var marker = new google.maps.Marker({
                position: {lat: {marker_lat}, lng: {marker_lng}},
                map: map,
                title: '{marker_title}',
                draggable: true
            });

            // Create a geocoder
            var geocoder = new google.maps.Geocoder();

            // Test geocoding
            geocoder.geocode({'location': {lat: {geocode_lat}, lng: {geocode_lng}}}, function(results, status) {
                if (status === 'OK') {
                    if (results[0]) {
                        console.log('Geocoding successful:', results[0].formatted_address);
                        document.getElementById('geocode-result').textContent = 'Address: ' + results[0].formatted_address;
                    } else {
                        console.log('No geocoding results found');
                        document.getElementById('geocode-result').textContent = 'No address found';
                    }
                } else {
                    console.error('Geocoder failed due to:', status);
                    document.getElementById('geocode-result').textContent = 'Geocoder failed: ' + status;
                }
            });

            // Test Places API
            var service = new google.maps.places.PlacesService(map);
            service.findPlaceFromQuery({
                query: '{place_query}',
                fields: ['name', 'geometry']
            }, function(results, status) {
                if (status === google.maps.places.PlacesServiceStatus.OK) {
                    console.log('Places API successful:', results);
                    document.getElementById('places-result').textContent = 'Found: ' + results[0].name;
                } else {
                    console.error('Places API failed due to:', status);
                    document.getElementById('places-result').textContent = 'Places API failed: ' + status;
                }
            });
        }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key={api_key}&libraries=places,geocoding&callback=initMap" async defer></script>
</body>
</html>
