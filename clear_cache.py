"""
Clear Cache Script for Czech Property Registry

This script clears all caches in the Czech Property Registry application
to ensure fresh data is fetched from APIs.
"""

import os
import sys
import shutil
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clear_api_cache():
    """Clear the API cache directory"""
    cache_dir = "api_cache"
    if os.path.exists(cache_dir):
        logger.info(f"Clearing API cache directory: {cache_dir}")
        try:
            # Remove all files in the directory
            for filename in os.listdir(cache_dir):
                file_path = os.path.join(cache_dir, filename)
                if os.path.isfile(file_path):
                    os.unlink(file_path)
                    logger.info(f"Removed file: {file_path}")
            logger.info("API cache cleared successfully")
        except Exception as e:
            logger.error(f"Error clearing API cache: {e}")
    else:
        logger.info(f"API cache directory not found: {cache_dir}")

def clear_data_cache():
    """Clear the data cache directory"""
    cache_dir = "data/cache"
    if os.path.exists(cache_dir):
        logger.info(f"Clearing data cache directory: {cache_dir}")
        try:
            # Remove all files in the directory
            for filename in os.listdir(cache_dir):
                file_path = os.path.join(cache_dir, filename)
                if os.path.isfile(file_path):
                    os.unlink(file_path)
                    logger.info(f"Removed file: {file_path}")
            logger.info("Data cache cleared successfully")
        except Exception as e:
            logger.error(f"Error clearing data cache: {e}")
    else:
        logger.info(f"Data cache directory not found: {cache_dir}")

def clear_property_cache_db():
    """Clear the property cache database"""
    db_file = "property_cache.db"
    if os.path.exists(db_file):
        logger.info(f"Clearing property cache database: {db_file}")
        try:
            # Rename the file (safer than deleting)
            backup_file = f"{db_file}.bak"
            shutil.move(db_file, backup_file)
            logger.info(f"Renamed {db_file} to {backup_file}")
        except Exception as e:
            logger.error(f"Error clearing property cache database: {e}")
    else:
        logger.info(f"Property cache database not found: {db_file}")

def main():
    """Main function"""
    logger.info("Starting cache clearing process")
    
    # Clear all caches
    clear_api_cache()
    clear_data_cache()
    clear_property_cache_db()
    
    logger.info("Cache clearing process completed")
    print("All caches have been cleared. Please restart the application.")

if __name__ == "__main__":
    main()
