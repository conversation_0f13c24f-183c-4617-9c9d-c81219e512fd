"""
Simple Loading Window for the Czech Property Registry application.

This module provides a splash screen with loading progress information
that appears during application startup.
"""

import tkinter as tk
from tkinter import ttk
import threading
import logging

# Configure logging
logger = logging.getLogger(__name__)

class SimpleLoadingWindow:
    """
    A simple splash screen window that shows loading progress during application startup.

    This window displays a progress bar and status messages to inform the user
    about what's happening during the initialization process.
    """

    def __init__(self, parent=None, app_title="Czech Property Registry"):
        """
        Initialize the loading window.

        Args:
            parent: Parent window (if None, creates a new Toplevel window)
            app_title: Title of the application
        """
        self.parent = parent
        self.app_title = app_title

        # Create the window
        if parent is None:
            self.window = tk.Tk()
        else:
            self.window = tk.Toplevel(parent)

        # Configure the window
        self.window.title(f"Loading {app_title}")
        self.window.geometry("500x300")
        self.window.resizable(False, False)
        self.window.configure(bg="#f0f0f0")

        # Center the window on screen
        self._center_window()

        # Create UI elements
        self._create_ui()

        # Initialize progress variables
        self.progress_value = 0
        self.current_task = "Initializing..."

    def _center_window(self):
        """Center the window on the screen."""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def _create_ui(self):
        """Create the UI elements for the loading window."""
        # Create main frame
        main_frame = ttk.Frame(self.window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Application title
        title_label = ttk.Label(
            main_frame,
            text=self.app_title,
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=(0, 20))

        # Loading image or logo (placeholder)
        logo_frame = ttk.Frame(main_frame, width=200, height=100)
        logo_frame.pack(pady=(0, 20))
        logo_frame.pack_propagate(False)

        logo_label = ttk.Label(
            logo_frame,
            text="LOADING",
            font=("Arial", 24, "bold")
        )
        logo_label.pack(fill=tk.BOTH, expand=True)

        # Current task label
        self.task_label = ttk.Label(
            main_frame,
            text="Initializing application...",
            font=("Arial", 10)
        )
        self.task_label.pack(pady=(0, 10), anchor=tk.W)

        # Progress bar
        self.progress_bar = ttk.Progressbar(
            main_frame,
            orient=tk.HORIZONTAL,
            length=460,
            mode="determinate"
        )
        self.progress_bar.pack(pady=(0, 10), fill=tk.X)

        # Progress percentage
        self.percentage_label = ttk.Label(
            main_frame,
            text="0%",
            font=("Arial", 9)
        )
        self.percentage_label.pack(anchor=tk.E)

        # Status message
        self.status_label = ttk.Label(
            main_frame,
            text="Starting up...",
            font=("Arial", 9, "italic")
        )
        self.status_label.pack(pady=(10, 0), anchor=tk.W)

    def set_task(self, task_name):
        """
        Set the current task name.

        Args:
            task_name: Name of the current task
        """
        self.current_task = task_name
        self.task_label.config(text=task_name)
        self.window.update()

    def set_progress(self, value):
        """
        Set the progress bar value.

        Args:
            value: Progress value (0-100)
        """
        self.progress_value = max(0, min(value, 100))
        self.progress_bar["value"] = self.progress_value
        self.percentage_label.config(text=f"{self.progress_value}%")
        self.window.update()

    def set_status(self, message):
        """
        Set the status message.

        Args:
            message: Status message to display
        """
        self.status_label.config(text=message)
        self.window.update()

    def close(self):
        """Close the loading window."""
        try:
            self.window.destroy()
        except:
            pass  # Window might already be closed

    def finish(self):
        """Complete the loading process and close the window."""
        self.set_progress(100)
        self.set_task("Loading complete")
        self.set_status("Application is ready")
        self.window.update()

    def run_tasks_sequentially(self, tasks, on_complete=None):
        """
        Run a list of tasks sequentially, updating the UI after each task.

        Args:
            tasks: List of (task_name, function, status_message) tuples
            on_complete: Function to call when all tasks are complete
        """
        def run_tasks_thread():
            try:
                total_tasks = len(tasks)

                for i, (task_name, task_func, status_msg) in enumerate(tasks):
                    # Update the UI - use a safer approach to update the UI from a background thread
                    try:
                        # Use a queue or event to signal the main thread instead of direct after() calls
                        # For now, we'll just log what we're doing
                        logger.debug(f"Running task: {task_name}, Status: {status_msg}")

                        # Run the task
                        task_func()

                        # Log progress
                        progress = int(((i + 1) / total_tasks) * 100)
                        logger.debug(f"Task completed. Progress: {progress}%")
                    except Exception as e:
                        logger.error(f"Error in task '{task_name}': {e}", exc_info=True)

                # Log completion
                logger.debug("All tasks completed")

                # We'll handle completion in the main thread
                if on_complete:
                    # Instead of using after(), we'll set a flag that the main thread can check
                    logger.debug("Tasks complete, ready for on_complete callback")
            except Exception as e:
                logger.error(f"Error in run_tasks_thread: {e}", exc_info=True)

        # Update the UI in the main thread before starting the background thread
        self.set_task("Starting initialization...")
        self.set_status("Please wait while the application initializes...")
        self.set_progress(0)

        # Start the tasks in a background thread
        thread = threading.Thread(target=run_tasks_thread, daemon=True)
        thread.start()

        # Process the first few tasks in the main thread to avoid threading issues
        try:
            # Process the first task in the main thread
            if tasks:
                task_name, task_func, status_msg = tasks[0]
                self.set_task(task_name)
                self.set_status(status_msg)
                task_func()
                self.set_progress(int((1 / len(tasks)) * 100))
        except Exception as e:
            logger.error(f"Error processing first task: {e}", exc_info=True)

    def _update_ui(self, task_name, status_msg):
        """Update the UI with task information."""
        self.set_task(task_name)
        if status_msg:
            self.set_status(status_msg)
