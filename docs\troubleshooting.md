# Troubleshooting Guide

This guide provides solutions to common issues you might encounter when using the Czech Property Registry application.

## Installation Issues

### Missing Dependencies

**Issue**: Error message about missing dependencies when running the application.

**Solution**: Make sure you've installed all required dependencies:

```bash
pip install -r requirements.txt
```

If you're still having issues, try installing the dependencies individually:

```bash
pip install requests beautifulsoup4 tkinter pillow tkinterweb openai
```

### Python Version Compatibility

**Issue**: Error messages related to Python version compatibility.

**Solution**: Ensure you're using Python 3.8 or higher:

```bash
python --version
```

If you have an older version, download and install the latest Python from [python.org](https://www.python.org/downloads/).

## Configuration Issues

### API Key Issues

**Issue**: Error messages about invalid or missing API keys.

**Solution**: 

1. Check that you've created a `config.ini` file based on the `config.ini.sample` template
2. Verify that your API keys are correctly entered in the `config.ini` file
3. Make sure there are no extra spaces or characters in your API keys
4. Try setting the API keys as environment variables:

```bash
# On Windows
set OPENAI_API_KEY=your_api_key_here
set GOOGLE_MAPS_API_KEY=your_api_key_here

# On macOS/Linux
export OPENAI_API_KEY=your_api_key_here
export GOOGLE_MAPS_API_KEY=your_api_key_here
```

### File Path Issues

**Issue**: Error messages about missing files or directories.

**Solution**:

1. Check that all required directories exist:
   - `data/`
   - `data/cache/`
   - `data/templates/`
   - `data/czech_data/`

2. Create any missing directories:

```bash
mkdir -p data/cache data/templates data/czech_data
```

## Runtime Issues

### Application Crashes

**Issue**: The application crashes unexpectedly.

**Solution**:

1. Run the application from the command line to see error messages:

```bash
python main.py
```

2. Check the log file for error messages:

```bash
cat app.log
```

3. Try running in development mode by setting `development_mode = true` in your `config.ini` file.

### Search Not Working

**Issue**: Searches return no results or error messages.

**Solution**:

1. Check your internet connection
2. Verify that your API keys are valid
3. Try using a different search method (address, location, coordinates)
4. Check if the CUZK website is accessible in your browser
5. Try clearing the cache:

```bash
rm -rf data/cache/*
```

### UI Issues

**Issue**: UI elements are not displaying correctly or are unresponsive.

**Solution**:

1. Check that you have Tkinter installed correctly:

```bash
python -c "import tkinter; tkinter._test()"
```

2. Try adjusting the font scale in the `config.ini` file:

```ini
[UI]
font_scale = 1.0
```

3. Try running the application with a different theme:

```ini
[UI]
theme = clam  # Try: clam, alt, default, classic
```

## API-Specific Issues

### Google Maps API Issues

**Issue**: Google Maps functionality not working.

**Solution**:

1. Verify that your Google Maps API key is valid
2. Check that you've enabled the required APIs in the Google Cloud Console:
   - Google Maps JavaScript API
   - Geocoding API
   - Places API
3. Check for any usage limits or billing issues in the Google Cloud Console

### OpenAI API Issues

**Issue**: Gender determination or other AI features not working.

**Solution**:

1. Verify that your OpenAI API key is valid
2. Check your OpenAI account for any usage limits or billing issues
3. Try using a different model in the `config.ini` file:

```ini
[OPENAI]
model = gpt-3.5-turbo  # Try: gpt-4, gpt-3.5-turbo
```

### CUZK Website Issues

**Issue**: Unable to retrieve property information from the CUZK website.

**Solution**:

1. Check if the CUZK website is accessible in your browser
2. The CUZK website might have changed its structure; check for application updates
3. Try using a different search method
4. Check if your IP address is being rate-limited by the CUZK website

## Performance Issues

### Slow Searches

**Issue**: Searches take a long time to complete.

**Solution**:

1. Enable caching in the `config.ini` file:

```ini
[CACHE]
enabled = true
```

2. Increase the cache expiry time:

```ini
[CACHE]
expiry_hours = 72  # 3 days
```

3. Try more specific searches with complete address information

### High Memory Usage

**Issue**: The application uses a lot of memory.

**Solution**:

1. Limit the batch size in the `config.ini` file:

```ini
[DEFAULT]
max_batch_size = 50
```

2. Clear the cache regularly:

```ini
[CACHE]
cleanup_on_start = true
```

3. Close and restart the application after extended use

## Getting Help

If you're still experiencing issues after trying these solutions, please:

1. Check the [GitHub Issues](https://github.com/yourusername/czech-property-registry/issues) for similar problems and solutions
2. Create a new issue with detailed information about your problem
3. Include error messages, screenshots, and steps to reproduce the issue
