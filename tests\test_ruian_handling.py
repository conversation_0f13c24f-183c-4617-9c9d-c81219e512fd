"""
Test for RUIAN ID handling in the Czech Property Registry application.

This script tests the improved RUIAN ID handling functionality.
"""

import os
import sys
import logging

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RUIANTest")

# Import the necessary modules
from api.cuzk.cuzk_integration import CUZKIntegration


def test_ruian_id_cleaning():
    """Test the RUIAN ID cleaning functionality."""
    try:
        # Test cases
        test_cases = [
            ("123456", "123456"),  # Already clean
            ("123-456", "123456"),  # With hyphen
            ("RUIAN:123456", "123456"),  # With prefix
            ("ref:ruian=123456", "123456"),  # OSM tag format
            ("Not a RUIAN ID", ""),  # Invalid
            ("", ""),  # Empty
            (None, ""),  # None
        ]
        
        for input_id, expected_output in test_cases:
            # Clean the RUIAN ID
            if input_id is not None:
                clean_id = ''.join(filter(str.isdigit, str(input_id)))
            else:
                clean_id = ""
                
            # Check if the cleaning worked as expected
            if clean_id == expected_output:
                logger.info(f"PASS: '{input_id}' -> '{clean_id}'")
            else:
                logger.error(f"FAIL: '{input_id}' -> '{clean_id}' (expected '{expected_output}')")
        
        logger.info("RUIAN ID cleaning test completed")
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)


def test_cuzk_url_construction():
    """Test the CUZK URL construction."""
    try:
        # Create the CUZK integration
        cuzk_integration = CUZKIntegration()
        
        # Test cases
        test_cases = [
            ("123456", "https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id=123456"),
            ("123-456", "https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id=123456"),
            ("RUIAN:123456", "https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id=123456"),
        ]
        
        for input_id, expected_url in test_cases:
            # Clean the RUIAN ID
            clean_id = ''.join(filter(str.isdigit, str(input_id)))
            
            # Construct the URL
            url = f"https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id={clean_id}"
            
            # Check if the URL construction worked as expected
            if url == expected_url:
                logger.info(f"PASS: '{input_id}' -> '{url}'")
            else:
                logger.error(f"FAIL: '{input_id}' -> '{url}' (expected '{expected_url}')")
        
        logger.info("CUZK URL construction test completed")
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)


if __name__ == '__main__':
    logger.info("Starting RUIAN handling tests")
    test_ruian_id_cleaning()
    test_cuzk_url_construction()
    logger.info("All tests completed")
