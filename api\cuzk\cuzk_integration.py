"""
CUZK (Czech Property Registry) integration for the Czech Property Registry application.
"""

import os
import re
import json
import requests
from bs4 import BeautifulSoup
from typing import Dict, Any, List, Optional, Tuple, Union
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import webbrowser
import urllib.parse
# No fallback data is used - removed DemoData import
from models.ruian_id import RuianID
from api.mapycz.mapycz_integration import MapyCZIntegration


class CUZKIntegration:
    """
    Class for integrating with the Czech Property Registry (CUZK).
    """

    def __init__(self):
        """
        Initialize the CUZK integration
        """
        # Set up a session with browser spoofing and retry capability
        try:
            # Import the BrowserSpoofer
            from utils.browser_spoofer import BrowserSpoofer

            # Create a session with browser spoofing
            self.session = BrowserSpoofer.create_spoofed_session(
                retry_total=3,
                backoff_factor=0.5,
                status_forcelist=[429, 500, 502, 503, 504],
                rotate_user_agent=True
            )

            print("Created session with browser spoofing for CUZK integration")

        except ImportError:
            # Fall back to the original implementation if BrowserSpoofer is not available
            print("BrowserSpoofer not available, using standard session for CUZK integration")
            self.session = requests.Session()

            # Configure retry strategy
            retry_strategy = Retry(
                total=3,
                backoff_factor=0.5,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "POST"]
            )

            adapter = HTTPAdapter(max_retries=retry_strategy)
            self.session.mount("http://", adapter)
            self.session.mount("https://", adapter)

        # Cache for encrypted URLs
        self.encrypted_url_cache = {}

        # Initialize Mapy.cz integration for better coordinate conversion
        self.mapycz = MapyCZIntegration()

    def search_property_by_coordinates(self, x: str, y: str) -> Optional[Dict[str, Any]]:
        """
        Search for a property by coordinates in the S-JTSK coordinate system.

        Args:
            x (str): X coordinate in S-JTSK
            y (str): Y coordinate in S-JTSK

        Returns:
            dict: Property details or None if not found
        """
        # URL for the search page
        search_url = "https://nahlizenidokn.cuzk.cz/VyberSouradnice.aspx"

        try:
            # Get the search page
            response = self.session.get(search_url)
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract form data
            viewstate_elem = soup.find('input', {'name': '__VIEWSTATE'})
            viewstategenerator_elem = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
            eventvalidation_elem = soup.find('input', {'name': '__EVENTVALIDATION'})

            if not viewstate_elem or not viewstategenerator_elem or not eventvalidation_elem:
                print("Could not find form elements")
                return None

            viewstate = viewstate_elem['value']
            viewstategenerator = viewstategenerator_elem['value']
            eventvalidation = eventvalidation_elem['value']

            # Prepare search data for coordinates
            search_data = {
                '__VIEWSTATE': viewstate,
                '__VIEWSTATEGENERATOR': viewstategenerator,
                '__EVENTVALIDATION': eventvalidation,
                'ctl00$bodyPlaceHolder$txtX': x,
                'ctl00$bodyPlaceHolder$txtY': y,
                'ctl00$bodyPlaceHolder$btnVyhledat': 'Vyhledat'
            }

            # Submit the search form
            search_response = self.session.post(search_url, data=search_data)

            # Check if search was successful and extract property details
            if search_response.status_code == 200:
                return self.extract_property_details(search_response.text)
            else:
                print(f"Search request failed: {search_response.status_code}")
                return None

        except Exception as e:
            print(f"Error searching property by coordinates: {e}")
            return None

    def search_property_by_parcel(self, parcel_number: str, cadastral_territory: str) -> Optional[Dict[str, Any]]:
        """
        Search for a property by parcel number and cadastral territory.

        Args:
            parcel_number (str): Parcel number
            cadastral_territory (str): Cadastral territory

        Returns:
            dict: Property details or None if not found
        """
        # URL for the search page
        search_url = "https://nahlizenidokn.cuzk.cz/VyberParcelu.aspx"

        try:
            # Get the search page
            response = self.session.get(search_url)
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract form data
            viewstate_elem = soup.find('input', {'name': '__VIEWSTATE'})
            viewstategenerator_elem = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
            eventvalidation_elem = soup.find('input', {'name': '__EVENTVALIDATION'})

            if not viewstate_elem or not viewstategenerator_elem or not eventvalidation_elem:
                print("Could not find form elements")
                return None

            viewstate = viewstate_elem['value']
            viewstategenerator = viewstategenerator_elem['value']
            eventvalidation = eventvalidation_elem['value']

            # Prepare search data for parcel number
            search_data = {
                '__VIEWSTATE': viewstate,
                '__VIEWSTATEGENERATOR': viewstategenerator,
                '__EVENTVALIDATION': eventvalidation,
                'ctl00$bodyPlaceHolder$txtParcela': parcel_number,
                'ctl00$bodyPlaceHolder$btnVyhledat': 'Vyhledat'
            }

            # Submit the search form
            search_response = self.session.post(search_url, data=search_data)

            # Check if search was successful and extract property details
            if search_response.status_code == 200:
                return self.extract_property_details(search_response.text)
            else:
                print(f"Search request failed: {search_response.status_code}")
                return None

        except Exception as e:
            print(f"Error searching property by parcel: {e}")
            return None

    def extract_property_details(self, html_content: str) -> Optional[Dict[str, Any]]:
        """
        Extract property details from the HTML content of the property page.

        Args:
            html_content (str): HTML content of the property page

        Returns:
            dict: Property details or None if not found
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Check if the page contains property details
            if "Informace o parcele" not in html_content and "Informace o stavbě" not in html_content:
                print("No property details found in the page")
                return None

            # Extract property details
            property_details = {}

            # Extract parcel number
            parcel_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblParcelniCislo'})
            if parcel_elem:
                property_details['parcel_number'] = parcel_elem.text.strip()

            # Extract cadastral territory
            cadastral_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblNazevKU'})
            if cadastral_elem:
                property_details['cadastral_territory'] = cadastral_elem.text.strip()

            # Extract property type
            type_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblZpusobVyuziti'})
            if type_elem:
                property_details['property_type'] = type_elem.text.strip()

            # Extract owner information
            owner_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblVlastnik'})
            if owner_elem:
                property_details['owner_name'] = owner_elem.text.strip()

            # Extract owner address
            address_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblAdresa'})
            if address_elem:
                property_details['owner_address'] = address_elem.text.strip()

            # Extract ownership share
            share_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblPodil'})
            if share_elem:
                property_details['ownership_share'] = share_elem.text.strip()

            # Extract LV number
            lv_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblCisloLV'})
            if lv_elem:
                property_details['lv_number'] = lv_elem.text.strip()

            return property_details

        except Exception as e:
            print(f"Error extracting property details: {e}")
            return None

    def generate_cuzk_url(self, lat: float, lng: float, parcel_id: Optional[str] = None,
                         building_id: Optional[str] = None, cadastral_territory: Optional[str] = None,
                         use_mapa_identifikace: bool = False, verify_coordinates: bool = False) -> str:
        """
        Generate a URL for the CUZK website based on coordinates or property identifiers.
        Uses Mapy.cz to get the correct S-JTSK coordinates without conversion.

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84
            parcel_id (str, optional): Parcel ID
            building_id (str, optional): Building ID
            cadastral_territory (str, optional): Cadastral territory
            use_mapa_identifikace (bool, optional): Whether to use MapaIdentifikace.aspx format (like ikatastr)
            verify_coordinates (bool, optional): Whether to verify and potentially fix coordinates (not used with Mapy.cz)

        Returns:
            str: URL for the CUZK website
        """
        # If we have specific property information, try to generate a direct URL
        if parcel_id and cadastral_territory:
            # Try to use an encrypted URL first
            encrypted_url = self.get_encrypted_url(parcel_id, cadastral_territory)
            if encrypted_url:
                return encrypted_url

            # Fall back to a search URL
            return f"https://nahlizenidokn.cuzk.cz/VyberParcelu.aspx?parcelni_cislo={parcel_id}&ku={cadastral_territory}"

        # If we have coordinates, use them
        if lat and lng:
            # Use Mapy.cz integration to generate a CUZK URL with the correct coordinates
            # This fetches S-JTSK coordinates directly from Mapy.cz without conversion
            url = self.mapycz.generate_cuzk_url(lat, lng)

            # Log the URL generation
            print(f"Generated CUZK URL using Mapy.cz integration: {url}")

            # If we're not using MapaIdentifikace format, modify the URL
            if not use_mapa_identifikace and "MapaIdentifikace.aspx" in url:
                # Extract the x and y parameters from the URL
                import re
                match = re.search(r'x=(\d+)&y=(\d+)', url)
                if match:
                    x, y = match.groups()
                    url = f"https://nahlizenidokn.cuzk.cz/VyberSouradnice.aspx?x={x}&y={y}"
                    print(f"Generated VyberSouradnice URL: {url}")

            return url

        # Default URL
        return "https://nahlizenidokn.cuzk.cz/"

    def verify_and_fix_coordinates(self, lat: float, lng: float) -> Tuple[float, float]:
        """
        Verify and potentially fix coordinates by checking the conversion accuracy.

        This method converts WGS84 coordinates to S-JTSK and back to check for conversion errors.
        If the reconverted coordinates are significantly different, it may indicate a conversion issue.

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84

        Returns:
            tuple: Potentially fixed (lat, lng) coordinates in WGS84
        """
        try:
            print(f"Verifying coordinates: WGS84({lat}, {lng})")

            # Step 1: Convert WGS84 to S-JTSK
            x, y = self.convert_wgs84_to_sjtsk(lat, lng)
            print(f"Converted to S-JTSK({x}, {y})")

            # Step 2: Convert back to WGS84
            from utils.helpers.coordinate_helpers import convert_sjtsk_to_wgs84
            reconverted_lat, reconverted_lng = convert_sjtsk_to_wgs84(x, y)

            # Calculate the difference
            lat_diff = abs(lat - reconverted_lat)
            lng_diff = abs(lng - reconverted_lng)
            print(f"Reconverted to WGS84({reconverted_lat}, {reconverted_lng})")
            print(f"Coordinate difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")

            # Check if the difference is significant (more than 0.001 degrees, roughly 100 meters)
            if lat_diff > 0.001 or lng_diff > 0.001:
                print(f"Significant coordinate conversion error detected!")
                print(f"Original WGS84: {lat}, {lng}")
                print(f"Reconverted WGS84: {reconverted_lat}, {reconverted_lng}")
                print(f"Difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")

                # For now, we'll use the original coordinates, but log the issue
                # In a future version, we might implement more sophisticated correction
                return lat, lng

            return lat, lng

        except Exception as e:
            print(f"Error verifying coordinates: {e}")
            # Return the original coordinates if there's an error
            return lat, lng

    def get_encrypted_url(self, parcel_id: str, cadastral_territory: str) -> Optional[str]:
        """
        Get an encrypted URL for the CUZK website based on parcel ID and cadastral territory.

        Args:
            parcel_id (str): Parcel ID
            cadastral_territory (str): Cadastral territory

        Returns:
            str: Encrypted URL or None if not available
        """
        # Check if we have this URL in the cache
        cache_key = f"{parcel_id}_{cadastral_territory}"
        if cache_key in self.encrypted_url_cache:
            return self.encrypted_url_cache[cache_key]

        # Try to load from encrypted_urls.json
        if os.path.exists('czech_data/encrypted_urls.json'):
            try:
                with open('czech_data/encrypted_urls.json', 'r', encoding='utf-8') as f:
                    encrypted_urls = json.load(f)
                    if cache_key in encrypted_urls:
                        url = encrypted_urls[cache_key]
                        self.encrypted_url_cache[cache_key] = url
                        return url
            except Exception as e:
                print(f"Error loading encrypted URLs: {e}")

        return None

    def convert_wgs84_to_sjtsk(self, lat: float, lng: float) -> Tuple[int, int]:
        """
        Convert WGS84 coordinates to S-JTSK using the official EPSG:5514 transformation.

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84

        Returns:
            tuple: (x, y) coordinates in S-JTSK as integers
        """
        # Use the coordinate_helpers function which uses the official PyProj transformation
        from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk
        x, y = convert_wgs84_to_sjtsk(lat, lng)

        # Log the conversion
        print(f"Converted WGS84({lat}, {lng}) to S-JTSK({int(x)}, {int(y)}) using official EPSG transformation")

        # CUZK MapaIdentifikace.aspx expects integers
        return (int(x), int(y))

    def get_property_by_ruian_id(self, ruian_id: Union[str, int, RuianID]) -> Optional[Dict[str, Any]]:
        """
        Get property information by RUIAN ID.

        Args:
            ruian_id (str, int, or RuianID): RUIAN ID of the building

        Returns:
            dict: Property details or None if not found
        """
        try:
            # If ruian_id is already a RuianID object, use it directly
            if isinstance(ruian_id, RuianID):
                if not ruian_id.is_valid:
                    print(f"Invalid RUIAN ID: {ruian_id.original_value}")
                    return None
                clean_ruian_id = ruian_id.value
            else:
                # Try to create a RuianID object
                try:
                    ruian_id_obj = RuianID(ruian_id)
                    clean_ruian_id = ruian_id_obj.value
                except ValueError as e:
                    print(f"Invalid RUIAN ID: {ruian_id} - {str(e)}")
                    return None

            # Try different URL formats
            urls_to_try = [
                # Standard building URL
                f"https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id={clean_ruian_id}",
                # Alternative format
                f"https://nahlizenidokn.cuzk.cz/VyberBudovu.aspx?typ=Stavba&id={clean_ruian_id}"
            ]

            property_data = None

            # Try each URL until we get a valid response
            for url in urls_to_try:
                print(f"Trying URL: {url}")

                # Make the request
                response = self.session.get(url)

                if response.status_code != 200:
                    print(f"Failed to load URL {url}: {response.status_code}")
                    continue

                # Check if the response contains an error message
                if "Špatná identifikace objektu" in response.text:
                    print(f"Error in response: Bad object identification")
                    continue

                # Extract property details from the response
                property_data = self.extract_property_details(response.text)

                # If we got property data, break the loop
                if property_data:
                    break

            # Add the RUIAN ID to the property data
            if property_data:
                property_data['ruian_id'] = clean_ruian_id
                return property_data
            else:
                print(f"No property data found for RUIAN ID: {ruian_id}")
                return None

        except Exception as e:
            print(f"Error getting property by RUIAN ID: {e}")
            return None

    def open_cuzk_in_browser(self, lat: Optional[float] = None, lng: Optional[float] = None,
                            parcel_id: Optional[str] = None, building_id: Optional[str] = None,
                            cadastral_territory: Optional[str] = None, use_mapa_identifikace: bool = False) -> None:
        """
        Open the CUZK website in the default browser.

        Args:
            lat (float, optional): Latitude in WGS84
            lng (float, optional): Longitude in WGS84
            parcel_id (str, optional): Parcel ID
            building_id (str, optional): Building ID
            cadastral_territory (str, optional): Cadastral territory
            use_mapa_identifikace (bool, optional): Whether to use MapaIdentifikace.aspx format (like ikatastr)
        """
        # If coordinates are not provided and no other identifiers are provided, show an error
        if (lat is None or lng is None) and not parcel_id and not building_id:
            print("Error: No coordinates or property identifiers provided")
            from ui.message_boxes import MessageBoxes
            MessageBoxes.show_error("CUZK Error", "No coordinates or property identifiers provided")
            return

        # Generate the URL
        url = self.generate_cuzk_url(lat, lng, parcel_id, building_id, cadastral_territory, use_mapa_identifikace)

        # Open the URL in the default browser
        webbrowser.open(url)

    def get_property_by_coordinates(self, x: int, y: int) -> Dict[str, Any]:
        """
        Generate property data with URL for the given coordinates without scraping the website.
        This method is used for batch search to avoid overwhelming the CUZK website.

        Args:
            x (int): X coordinate in S-JTSK
            y (int): Y coordinate in S-JTSK

        Returns:
            dict: Property data with URL
        """
        # Ensure coordinates are integers (CUZK requires whole numbers)
        x_int = int(x)
        y_int = int(y)

        # Convert S-JTSK coordinates to WGS84
        from utils.helpers.coordinate_helpers import convert_sjtsk_to_wgs84
        lat, lng = convert_sjtsk_to_wgs84(x_int, y_int)

        # Generate CUZK URL directly using the S-JTSK coordinates
        # This avoids any conversion issues by using the coordinates we already have
        url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"

        # Log the direct URL generation
        print(f"Generated CUZK URL directly from S-JTSK coordinates: {url}")

        # Create a basic property data dictionary with the URL
        property_data = {
            'property_type': 'Building',
            'ruian_id': f"coord_{x_int}_{y_int}",  # Use coordinates as a pseudo-RUIAN ID
            'x_sjtsk': x_int,
            'y_sjtsk': y_int,
            'url': url,
            'lat': lat,
            'lng': lng
        }

        # Log the generated URL for debugging
        print(f"Generated CUZK URL using Mapy.cz integration: {url}")

        return property_data

    def generate_batch_cuzk_urls(self, center_lat: float, center_lng: float, radius: float,
                                num_points: int = 9, use_mapa_identifikace: bool = True) -> List[Dict[str, Any]]:
        """
        Generate a batch of CUZK URLs for properties around a central point.

        Args:
            center_lat (float): Center latitude in WGS84
            center_lng (float): Center longitude in WGS84
            radius (float): Radius in meters to search around the center point
            num_points (int, optional): Number of points to generate (default: 9)
            use_mapa_identifikace (bool, optional): Whether to use MapaIdentifikace.aspx format (default: True)

        Returns:
            list: List of dictionaries with coordinates and URLs
        """
        import math

        # Convert radius from meters to degrees (approximate)
        # 1 degree of latitude is approximately 111,000 meters
        radius_lat = radius / 111000
        # 1 degree of longitude varies with latitude
        radius_lng = radius / (111000 * math.cos(math.radians(center_lat)))

        # Generate points in a grid around the center
        points = []

        # Determine grid size based on num_points
        grid_size = math.ceil(math.sqrt(num_points))

        # Calculate step size
        lat_step = 2 * radius_lat / (grid_size - 1) if grid_size > 1 else 0
        lng_step = 2 * radius_lng / (grid_size - 1) if grid_size > 1 else 0

        # Generate grid points
        count = 0
        for i in range(grid_size):
            if count >= num_points:
                break

            for j in range(grid_size):
                if count >= num_points:
                    break

                # Calculate coordinates for this point
                point_lat = center_lat - radius_lat + i * lat_step
                point_lng = center_lng - radius_lng + j * lng_step

                # Use Mapy.cz integration to generate a CUZK URL with the correct coordinates
                # This fetches S-JTSK coordinates directly from Mapy.cz without conversion
                url = self.mapycz.generate_cuzk_url(point_lat, point_lng)

                # Extract x and y from the URL for reference
                import re
                match = re.search(r'x=(\d+)&y=(\d+)', url)
                if match:
                    x_int, y_int = int(match.group(1)), int(match.group(2))

                    # If we're not using MapaIdentifikace format, modify the URL
                    if not use_mapa_identifikace:
                        url = f"https://nahlizenidokn.cuzk.cz/VyberSouradnice.aspx?x={x_int}&y={y_int}"
                        print(f"Generated VyberSouradnice URL: {url}")
                else:
                    # Fallback if URL parsing fails
                    x, y = self.convert_wgs84_to_sjtsk(point_lat, point_lng)
                    x_int, y_int = int(x), int(y)
                    print(f"Warning: Failed to extract coordinates from URL, using fallback conversion")

                # Add to points list
                points.append({
                    'lat': point_lat,
                    'lng': point_lng,
                    'x': x_int,
                    'y': y_int,
                    'url': url
                })

                count += 1

        return points

    def open_batch_cuzk_in_browser(self, center_lat: float, center_lng: float, radius: float,
                                  num_points: int = 9, use_mapa_identifikace: bool = True,
                                  max_tabs: int = 5) -> List[Dict[str, Any]]:
        """
        Open multiple CUZK URLs in the browser for properties around a central point.

        Args:
            center_lat (float): Center latitude in WGS84
            center_lng (float): Center longitude in WGS84
            radius (float): Radius in meters to search around the center point
            num_points (int, optional): Number of points to generate (default: 9)
            use_mapa_identifikace (bool, optional): Whether to use MapaIdentifikace.aspx format (default: True)
            max_tabs (int, optional): Maximum number of browser tabs to open (default: 5)

        Returns:
            list: List of dictionaries with coordinates and URLs
        """
        import time
        import random

        # Generate the batch of URLs
        points = self.generate_batch_cuzk_urls(center_lat, center_lng, radius, num_points, use_mapa_identifikace)

        # Limit the number of tabs to open
        points_to_open = points[:min(max_tabs, len(points))]

        # Open each URL in the browser with a random delay to appear more human-like
        for i, point in enumerate(points_to_open):
            # Add a longer delay between opening tabs to avoid triggering bot detection
            if i > 0:
                # Random delay between 2-5 seconds
                delay = random.uniform(2.0, 5.0)
                print(f"Waiting {delay:.2f} seconds before opening next tab...")
                time.sleep(delay)

            print(f"Opening URL: {point['url']}")
            webbrowser.open(point['url'])

        return points
