"""
Progressive loading utilities for the Czech Property Registry application.
Provides mechanisms for loading data in chunks to provide faster initial rendering.
"""

import threading
import time
import logging
import queue
from typing import Dict, List, Any, Optional, Callable, Tuple, Set, Union, TypeVar, Generic, Iterator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ProgressiveLoader")

# Type variable for generic types
T = TypeVar('T')


class ChunkInfo:
    """Information about a data chunk."""
    
    def __init__(self, 
                 chunk_id: str, 
                 start_index: int, 
                 end_index: int, 
                 total_items: int,
                 is_loaded: bool = False,
                 load_time: Optional[float] = None):
        """
        Initialize chunk information.
        
        Args:
            chunk_id (str): Unique identifier for the chunk
            start_index (int): Start index of the chunk
            end_index (int): End index of the chunk
            total_items (int): Total number of items
            is_loaded (bool): Whether the chunk is loaded
            load_time (float, optional): Time taken to load the chunk
        """
        self.chunk_id = chunk_id
        self.start_index = start_index
        self.end_index = end_index
        self.total_items = total_items
        self.is_loaded = is_loaded
        self.load_time = load_time
        self.size = end_index - start_index
        self.progress = 0.0


class ProgressiveLoader(Generic[T]):
    """
    Loads data in chunks to provide faster initial rendering.
    Prioritizes visible chunks and loads others in the background.
    """
    
    def __init__(self, 
                 chunk_size: int = 100, 
                 preload_chunks: int = 2,
                 max_concurrent_loads: int = 3):
        """
        Initialize the progressive loader.
        
        Args:
            chunk_size (int): Number of items per chunk
            preload_chunks (int): Number of chunks to preload around the visible chunk
            max_concurrent_loads (int): Maximum number of concurrent chunk loads
        """
        self.chunk_size = chunk_size
        self.preload_chunks = preload_chunks
        self.max_concurrent_loads = max_concurrent_loads
        
        self._data: Dict[int, T] = {}
        self._chunks: Dict[str, ChunkInfo] = {}
        self._chunk_data: Dict[str, List[T]] = {}
        self._visible_range: Tuple[int, int] = (0, 0)
        self._total_items = 0
        self._data_provider: Optional[Callable[[int, int], List[T]]] = None
        self._load_queue = queue.PriorityQueue()
        self._loading_chunks: Set[str] = set()
        self._lock = threading.Lock()
        self._loading_lock = threading.Lock()
        self._loading_thread: Optional[threading.Thread] = None
        self._stop_loading = threading.Event()
        
        # Callbacks
        self._callbacks: Dict[str, List[Callable]] = {
            'chunk_loaded': [],
            'all_chunks_loaded': [],
            'chunk_progress': []
        }
        
        # Statistics
        self._stats = {
            'chunks_loaded': 0,
            'items_loaded': 0,
            'total_load_time': 0.0,
            'average_chunk_load_time': 0.0,
            'max_chunk_load_time': 0.0,
            'min_chunk_load_time': float('inf')
        }
        self._stats_lock = threading.Lock()
        
        logger.info(f"ProgressiveLoader initialized with chunk size {chunk_size}")
    
    def set_data_provider(self, provider: Callable[[int, int], List[T]]) -> None:
        """
        Set the data provider function.
        
        Args:
            provider: Function that takes (start_index, end_index) and returns a list of items
        """
        self._data_provider = provider
    
    def set_total_items(self, total_items: int) -> None:
        """
        Set the total number of items.
        
        Args:
            total_items (int): Total number of items
        """
        with self._lock:
            self._total_items = total_items
            self._create_chunks()
    
    def _create_chunks(self) -> None:
        """Create chunks based on the total number of items."""
        self._chunks.clear()
        
        # Calculate the number of chunks
        num_chunks = (self._total_items + self.chunk_size - 1) // self.chunk_size
        
        # Create chunks
        for i in range(num_chunks):
            start_index = i * self.chunk_size
            end_index = min(start_index + self.chunk_size, self._total_items)
            chunk_id = f"chunk_{i}"
            
            self._chunks[chunk_id] = ChunkInfo(
                chunk_id=chunk_id,
                start_index=start_index,
                end_index=end_index,
                total_items=self._total_items
            )
        
        logger.info(f"Created {num_chunks} chunks for {self._total_items} items")
    
    def start_loading(self) -> None:
        """Start the background loading thread."""
        if self._loading_thread is not None and self._loading_thread.is_alive():
            return
        
        self._stop_loading.clear()
        self._loading_thread = threading.Thread(
            target=self._loading_loop,
            name="ProgressiveLoader",
            daemon=True
        )
        self._loading_thread.start()
        
        logger.info("Started background loading thread")
    
    def stop_loading(self) -> None:
        """Stop the background loading thread."""
        if self._loading_thread is None or not self._loading_thread.is_alive():
            return
        
        self._stop_loading.set()
        self._loading_thread.join(timeout=1.0)
        self._loading_thread = None
        
        logger.info("Stopped background loading thread")
    
    def _loading_loop(self) -> None:
        """Main loop for the background loading thread."""
        while not self._stop_loading.is_set():
            try:
                # Get the next chunk to load
                try:
                    _, chunk_id = self._load_queue.get(timeout=0.1)
                except queue.Empty:
                    continue
                
                # Check if we're already loading this chunk
                with self._loading_lock:
                    if chunk_id in self._loading_chunks:
                        self._load_queue.task_done()
                        continue
                    
                    # Check if the chunk is already loaded
                    with self._lock:
                        if chunk_id in self._chunks and self._chunks[chunk_id].is_loaded:
                            self._load_queue.task_done()
                            continue
                    
                    # Mark the chunk as loading
                    self._loading_chunks.add(chunk_id)
                
                # Load the chunk
                self._load_chunk(chunk_id)
                
                # Mark the chunk as no longer loading
                with self._loading_lock:
                    self._loading_chunks.discard(chunk_id)
                
                # Mark the task as done
                self._load_queue.task_done()
                
                # Check if all chunks are loaded
                with self._lock:
                    all_loaded = all(chunk.is_loaded for chunk in self._chunks.values())
                
                if all_loaded:
                    # Notify that all chunks are loaded
                    for callback in self._callbacks['all_chunks_loaded']:
                        try:
                            callback()
                        except Exception as e:
                            logger.error(f"Error in all_chunks_loaded callback: {e}")
            
            except Exception as e:
                logger.error(f"Error in loading thread: {e}")
    
    def _load_chunk(self, chunk_id: str) -> None:
        """
        Load a chunk of data.
        
        Args:
            chunk_id (str): ID of the chunk to load
        """
        with self._lock:
            if chunk_id not in self._chunks:
                return
            
            chunk = self._chunks[chunk_id]
            
            # Skip if already loaded
            if chunk.is_loaded:
                return
            
            # Check if we have a data provider
            if self._data_provider is None:
                logger.warning("No data provider set")
                return
        
        try:
            # Notify progress start
            self._notify_chunk_progress(chunk_id, 0.0)
            
            # Load the data
            start_time = time.time()
            data = self._data_provider(chunk.start_index, chunk.end_index)
            load_time = time.time() - start_time
            
            # Store the data
            with self._lock:
                self._chunk_data[chunk_id] = data
                
                # Update the data dictionary
                for i, item in enumerate(data):
                    self._data[chunk.start_index + i] = item
                
                # Update chunk info
                chunk.is_loaded = True
                chunk.load_time = load_time
                chunk.progress = 1.0
            
            # Update statistics
            with self._stats_lock:
                self._stats['chunks_loaded'] += 1
                self._stats['items_loaded'] += len(data)
                self._stats['total_load_time'] += load_time
                self._stats['average_chunk_load_time'] = (
                    self._stats['total_load_time'] / self._stats['chunks_loaded']
                )
                self._stats['max_chunk_load_time'] = max(
                    self._stats['max_chunk_load_time'], load_time
                )
                self._stats['min_chunk_load_time'] = min(
                    self._stats['min_chunk_load_time'], load_time
                )
            
            logger.debug(f"Loaded chunk {chunk_id} with {len(data)} items in {load_time:.4f} seconds")
            
            # Notify that the chunk is loaded
            self._notify_chunk_loaded(chunk_id)
            
            # Notify progress complete
            self._notify_chunk_progress(chunk_id, 1.0)
        
        except Exception as e:
            logger.error(f"Error loading chunk {chunk_id}: {e}")
            
            # Notify progress error
            self._notify_chunk_progress(chunk_id, 0.0)
    
    def _notify_chunk_loaded(self, chunk_id: str) -> None:
        """
        Notify that a chunk has been loaded.
        
        Args:
            chunk_id (str): ID of the loaded chunk
        """
        for callback in self._callbacks['chunk_loaded']:
            try:
                callback(chunk_id)
            except Exception as e:
                logger.error(f"Error in chunk_loaded callback: {e}")
    
    def _notify_chunk_progress(self, chunk_id: str, progress: float) -> None:
        """
        Notify chunk loading progress.
        
        Args:
            chunk_id (str): ID of the chunk
            progress (float): Progress value (0.0 to 1.0)
        """
        # Update chunk progress
        with self._lock:
            if chunk_id in self._chunks:
                self._chunks[chunk_id].progress = progress
        
        # Notify callbacks
        for callback in self._callbacks['chunk_progress']:
            try:
                callback(chunk_id, progress)
            except Exception as e:
                logger.error(f"Error in chunk_progress callback: {e}")
    
    def set_visible_range(self, start_index: int, end_index: int) -> None:
        """
        Set the currently visible range of items.
        This will prioritize loading chunks in and around the visible range.
        
        Args:
            start_index (int): Start index of the visible range
            end_index (int): End index of the visible range
        """
        with self._lock:
            self._visible_range = (start_index, end_index)
            
            # Prioritize chunks in and around the visible range
            self._prioritize_chunks()
    
    def _prioritize_chunks(self) -> None:
        """Prioritize chunks based on the visible range."""
        start_index, end_index = self._visible_range
        
        # Find the chunks that contain the visible range
        visible_chunks = []
        preload_chunks = []
        other_chunks = []
        
        for chunk_id, chunk in self._chunks.items():
            # Skip already loaded chunks
            if chunk.is_loaded:
                continue
            
            # Check if the chunk overlaps with the visible range
            if chunk.end_index > start_index and chunk.start_index < end_index:
                visible_chunks.append(chunk_id)
            elif (chunk.end_index > start_index - self.preload_chunks * self.chunk_size and 
                  chunk.start_index < end_index + self.preload_chunks * self.chunk_size):
                preload_chunks.append(chunk_id)
            else:
                other_chunks.append(chunk_id)
        
        # Clear the queue
        while not self._load_queue.empty():
            try:
                self._load_queue.get_nowait()
                self._load_queue.task_done()
            except queue.Empty:
                break
        
        # Add chunks to the queue with priorities
        for chunk_id in visible_chunks:
            self._load_queue.put((0, chunk_id))  # Highest priority
        
        for chunk_id in preload_chunks:
            self._load_queue.put((1, chunk_id))  # Medium priority
        
        for chunk_id in other_chunks:
            self._load_queue.put((2, chunk_id))  # Lowest priority
    
    def get_item(self, index: int) -> Optional[T]:
        """
        Get an item by index.
        
        Args:
            index (int): Item index
            
        Returns:
            The item or None if not loaded
        """
        with self._lock:
            return self._data.get(index)
    
    def get_items(self, start_index: int, end_index: int) -> List[T]:
        """
        Get a range of items.
        
        Args:
            start_index (int): Start index
            end_index (int): End index
            
        Returns:
            List of items (may contain None for unloaded items)
        """
        with self._lock:
            return [self._data.get(i) for i in range(start_index, end_index)]
    
    def is_loaded(self, index: int) -> bool:
        """
        Check if an item is loaded.
        
        Args:
            index (int): Item index
            
        Returns:
            bool: True if the item is loaded
        """
        with self._lock:
            return index in self._data
    
    def is_range_loaded(self, start_index: int, end_index: int) -> bool:
        """
        Check if a range of items is loaded.
        
        Args:
            start_index (int): Start index
            end_index (int): End index
            
        Returns:
            bool: True if all items in the range are loaded
        """
        with self._lock:
            return all(i in self._data for i in range(start_index, end_index))
    
    def get_chunk_info(self, chunk_id: str) -> Optional[ChunkInfo]:
        """
        Get information about a chunk.
        
        Args:
            chunk_id (str): Chunk ID
            
        Returns:
            ChunkInfo or None if not found
        """
        with self._lock:
            return self._chunks.get(chunk_id)
    
    def get_all_chunk_info(self) -> Dict[str, ChunkInfo]:
        """
        Get information about all chunks.
        
        Returns:
            Dictionary mapping chunk IDs to chunk information
        """
        with self._lock:
            return self._chunks.copy()
    
    def get_loading_progress(self) -> float:
        """
        Get the overall loading progress.
        
        Returns:
            float: Progress value (0.0 to 1.0)
        """
        with self._lock:
            if not self._chunks:
                return 0.0
            
            loaded_chunks = sum(1 for chunk in self._chunks.values() if chunk.is_loaded)
            return loaded_chunks / len(self._chunks)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the progressive loader.
        
        Returns:
            Dictionary with statistics
        """
        with self._stats_lock:
            stats = self._stats.copy()
        
        with self._lock:
            stats['total_chunks'] = len(self._chunks)
            stats['loaded_chunks'] = sum(1 for chunk in self._chunks.values() if chunk.is_loaded)
            stats['loading_chunks'] = len(self._loading_chunks)
            stats['total_items'] = self._total_items
            stats['loaded_items'] = len(self._data)
            stats['loading_progress'] = self.get_loading_progress()
        
        return stats
    
    def register_callback(self, event: str, callback: Callable) -> None:
        """
        Register a callback for an event.
        
        Args:
            event (str): Event name ('chunk_loaded', 'all_chunks_loaded', 'chunk_progress')
            callback: Function to call when the event occurs
        """
        if event in self._callbacks:
            self._callbacks[event].append(callback)
    
    def clear(self) -> None:
        """Clear all data and chunks."""
        with self._lock:
            self._data.clear()
            self._chunks.clear()
            self._chunk_data.clear()
            self._visible_range = (0, 0)
            self._total_items = 0
        
        # Clear the queue
        while not self._load_queue.empty():
            try:
                self._load_queue.get_nowait()
                self._load_queue.task_done()
            except queue.Empty:
                break
        
        with self._loading_lock:
            self._loading_chunks.clear()
        
        logger.info("Cleared all data and chunks")


# Create a global instance for convenience
progressive_loader = ProgressiveLoader()
