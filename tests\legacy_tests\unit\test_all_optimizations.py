"""
Test script to verify all optimizations.
This script runs a series of tests to measure the performance improvements.
"""

import time
import logging
import tkinter as tk
import threading
import os
import sys
import gc
import psutil
import json
import random

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OptimizationTest")

# Import the optimized components
from utils.lazy_module_loader import lazy_import, lazy_import_all
from utils.enhanced_cache_manager import EnhancedCacheManager, CacheStorageType
from utils.thread_manager import ThreadManager
from utils.db_cache import DBCache
from utils.memory_manager import memory_monitor, weak_cache
from utils.lazy_loader import LazyDataLoader, lazy_property
from utils.profiler import function_profiler, Timer, MemoryProfiler
from utils.data_compression import data_compressor, CompressionMethod
from utils.adaptive_rate_limiter import adaptive_rate_limiter
from utils.background_processor import background_processor
from utils.progressive_loader import progressive_loader
from ui.virtualized_list import VirtualizedList
from ui.virtualized_tree import VirtualizedTree


class AllOptimizationsTest:
    """Class to test all performance optimizations."""
    
    def __init__(self):
        """Initialize the optimization test."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window
        self.root.title("All Optimizations Test")
        self.root.geometry("800x600")
        
        # Create a frame for the tests
        self.frame = tk.Frame(self.root)
        self.frame.pack(fill="both", expand=True)
        
        # Create a label for the results
        self.results_label = tk.Label(self.frame, text="Running tests...", justify="left", anchor="nw")
        self.results_label.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Initialize components
        self._init_components()
        
        logger.info("All optimizations test initialized")
    
    def _init_components(self):
        """Initialize the components to test."""
        # Initialize all optimized components
        self._init_lazy_module_loading()
        self._init_caching()
        self._init_threading()
        self._init_memory_management()
        self._init_data_compression()
        self._init_rate_limiting()
        self._init_background_processing()
        self._init_progressive_loading()
        self._init_ui_virtualization()
    
    def _init_lazy_module_loading(self):
        """Initialize lazy module loading."""
        logger.info("Initializing lazy module loading")
        
        # Register a callback for module loading
        lazy_import_all([
            'json', 'pickle', 'zlib', 'gzip', 'lzma', 'bz2',
            'tkinter.ttk', 'tkinter.font', 'tkinter.filedialog',
            'threading', 'queue', 'time', 'os', 'sys', 'gc', 'psutil'
        ])
    
    def _init_caching(self):
        """Initialize caching components."""
        logger.info("Initializing caching components")
        
        # Initialize the enhanced cache manager
        self.enhanced_cache = EnhancedCacheManager(
            default_expiry=300,
            disk_cache_dir="test_cache",
            max_memory_items=1000,
            cleanup_interval=300
        )
        
        # Initialize the database cache
        self.db_cache = DBCache(db_path="test_db_cache.db")
        
        # Create test caches
        self.enhanced_cache.create_cache(
            'test_cache',
            expiry=60,
            storage_type=CacheStorageType.BOTH
        )
        
        self.db_cache.create_cache('test_cache', expiry=60)
    
    def _init_threading(self):
        """Initialize threading components."""
        logger.info("Initializing threading components")
        
        # Initialize the thread manager
        self.thread_manager = ThreadManager(max_workers=5)
    
    def _init_memory_management(self):
        """Initialize memory management components."""
        logger.info("Initializing memory management components")
        
        # Start memory monitoring
        memory_monitor.start()
    
    def _init_data_compression(self):
        """Initialize data compression components."""
        logger.info("Initializing data compression components")
        
        # Configure default compression method and level
        data_compressor.default_method = CompressionMethod.ZLIB
        data_compressor.default_level = 6  # Balanced compression
    
    def _init_rate_limiting(self):
        """Initialize rate limiting components."""
        logger.info("Initializing rate limiting components")
        
        # Configure domain-specific rate limits
        adaptive_rate_limiter.set_domain_rate("example.com", 5.0)
        adaptive_rate_limiter.set_domain_rate("api.example.com", 2.0)
        adaptive_rate_limiter.set_domain_rate("data.example.com", 1.0)
    
    def _init_background_processing(self):
        """Initialize background processing components."""
        logger.info("Initializing background processing components")
        
        # Start the background processor
        if not background_processor._running:
            background_processor.start()
    
    def _init_progressive_loading(self):
        """Initialize progressive loading components."""
        logger.info("Initializing progressive loading components")
        
        # Configure the progressive loader
        def data_provider(start_index, end_index):
            # Simulate data loading
            time.sleep(0.01)
            return [f"Item {i}" for i in range(start_index, end_index)]
        
        progressive_loader.set_data_provider(data_provider)
        progressive_loader.set_total_items(1000)
        
        # Start loading
        progressive_loader.start_loading()
    
    def _init_ui_virtualization(self):
        """Initialize UI virtualization components."""
        logger.info("Initializing UI virtualization components")
        
        # Initialize the virtualized list
        self.virtualized_list = VirtualizedList(
            self.frame,
            item_height=30,
            buffer_items=5,
            width=400,
            height=300
        )
        
        # Initialize the virtualized tree
        self.virtualized_tree = VirtualizedTree(
            self.frame,
            item_height=30,
            buffer_items=5,
            width=400,
            height=300,
            indent_width=20
        )
    
    def run_tests(self):
        """Run all optimization tests."""
        results = {}
        
        # Run the tests
        with Timer("All Tests"):
            # Test lazy module loading
            results["lazy_module_loading"] = self.test_lazy_module_loading()
            
            # Test caching
            results["caching"] = self.test_caching()
            
            # Test threading
            results["threading"] = self.test_threading()
            
            # Test memory management
            results["memory"] = self.test_memory_management()
            
            # Test data compression
            results["compression"] = self.test_data_compression()
            
            # Test rate limiting
            results["rate_limiting"] = self.test_rate_limiting()
            
            # Test background processing
            results["background_processing"] = self.test_background_processing()
            
            # Test progressive loading
            results["progressive_loading"] = self.test_progressive_loading()
            
            # Test UI virtualization
            results["ui_virtualization"] = self.test_ui_virtualization()
        
        # Display the results
        self._display_results(results)
        
        return results
    
    def test_lazy_module_loading(self):
        """Test lazy module loading."""
        logger.info("Testing lazy module loading...")
        results = {}
        
        # Test importing modules lazily
        with Timer("Lazy Import") as t1:
            # Import some modules lazily
            json_module = lazy_import('json')
            pickle_module = lazy_import('pickle')
            zlib_module = lazy_import('zlib')
            
            # Access the modules to trigger loading
            json_module.dumps({"test": "data"})
            pickle_module.dumps("test")
            zlib_module.compress(b"test")
        
        results["lazy_import"] = t1.elapsed()
        
        # Get module loading statistics
        from utils.lazy_module_loader import lazy_module_loader
        results["loaded_modules"] = lazy_module_loader.get_loaded_modules()
        results["load_times"] = lazy_module_loader.get_load_times()
        
        logger.info(f"Lazy module loading test results: {t1.elapsed():.4f}s")
        return results
    
    def test_caching(self):
        """Test caching optimizations."""
        logger.info("Testing caching optimizations...")
        results = {}
        
        # Generate test data
        test_data = {f"key_{i}": f"value_{i}" for i in range(1000)}
        
        # Test enhanced cache
        with Timer("Enhanced Cache") as t1:
            # Write to the cache
            for key, value in test_data.items():
                self.enhanced_cache.set('test_cache', key, value)
            
            # Read from the cache
            for key, expected_value in test_data.items():
                value = self.enhanced_cache.get('test_cache', key)
                assert value == expected_value, f"Expected {expected_value}, got {value}"
        
        results["enhanced_cache"] = t1.elapsed()
        
        # Test database cache
        with Timer("Database Cache") as t2:
            # Write to the cache
            for key, value in test_data.items():
                self.db_cache.set('test_cache', key, value)
            
            # Read from the cache
            for key, expected_value in test_data.items():
                value = self.db_cache.get('test_cache', key)
                assert value == expected_value, f"Expected {expected_value}, got {value}"
        
        results["db_cache"] = t2.elapsed()
        
        logger.info(f"Caching test results: Enhanced Cache: {t1.elapsed():.4f}s, DB Cache: {t2.elapsed():.4f}s")
        return results
    
    def test_threading(self):
        """Test threading optimizations."""
        logger.info("Testing threading optimizations...")
        results = {}
        
        # Define a simple task
        def task(task_id, sleep_time):
            time.sleep(sleep_time)
            return f"Task {task_id} completed after {sleep_time} seconds"
        
        # Test thread manager
        with Timer("Thread Manager") as t1:
            futures = []
            for i in range(20):
                future = self.thread_manager.submit(
                    f"task_{i}",
                    task,
                    f"task_{i}",
                    0.05  # 50ms sleep
                )
                futures.append((i, future))
            
            # Wait for all tasks to complete
            for i, future in futures:
                result = future.result(timeout=2.0)
        
        results["thread_manager"] = t1.elapsed()
        
        logger.info(f"Threading test results: {t1.elapsed():.4f}s")
        return results
    
    def test_memory_management(self):
        """Test memory management optimizations."""
        logger.info("Testing memory management optimizations...")
        results = {}
        
        # Test weak cache
        with Timer("Weak Cache") as t1:
            # Create some objects
            objects = [object() for _ in range(1000)]
            
            # Store them in the weak cache
            for i, obj in enumerate(objects):
                weak_cache.set(f"obj_{i}", obj)
            
            # Access them from the weak cache
            for i in range(1000):
                obj = weak_cache.get(f"obj_{i}")
                assert obj is objects[i], f"Expected {objects[i]}, got {obj}"
            
            # Clear references to some objects
            for i in range(0, 1000, 2):
                objects[i] = None
            
            # Force garbage collection
            gc.collect()
            
            # Check which objects are still in the cache
            count = 0
            for i in range(1000):
                obj = weak_cache.get(f"obj_{i}")
                if obj is not None:
                    count += 1
        
        results["weak_cache"] = t1.elapsed()
        results["remaining_objects"] = count
        
        # Test memory monitoring
        with Timer("Memory Monitoring") as t2:
            # Get memory info
            memory_info = memory_monitor.get_memory_info()
            
            # Force garbage collection
            gc_stats = memory_monitor.force_garbage_collection()
        
        results["memory_monitoring"] = t2.elapsed()
        results["memory_info"] = memory_info
        results["gc_stats"] = gc_stats
        
        logger.info(f"Memory management test results: Weak Cache: {t1.elapsed():.4f}s, Memory Monitoring: {t2.elapsed():.4f}s")
        return results
    
    def test_data_compression(self):
        """Test data compression optimizations."""
        logger.info("Testing data compression optimizations...")
        results = {}
        
        # Generate test data
        binary_data = b"x" * 10000
        json_data = {"data": ["x" * 100 for _ in range(100)]}
        object_data = [object() for _ in range(100)]
        
        # Test binary compression
        with Timer("Binary Compression") as t1:
            compressed, metadata = data_compressor.compress(binary_data)
            decompressed = data_compressor.decompress(compressed, metadata)
            assert decompressed == binary_data, "Binary decompression failed"
        
        results["binary_compression"] = t1.elapsed()
        results["binary_compression_ratio"] = metadata['compression_ratio']
        
        # Test JSON compression
        with Timer("JSON Compression") as t2:
            compressed, metadata = data_compressor.compress_json(json_data)
            decompressed = data_compressor.decompress_json(compressed, metadata)
            assert decompressed == json_data, "JSON decompression failed"
        
        results["json_compression"] = t2.elapsed()
        results["json_compression_ratio"] = metadata['compression_ratio']
        
        # Test object compression
        with Timer("Object Compression") as t3:
            compressed, metadata = data_compressor.compress_object(object_data)
            decompressed = data_compressor.decompress_object(compressed, metadata)
            assert len(decompressed) == len(object_data), "Object decompression failed"
        
        results["object_compression"] = t3.elapsed()
        
        logger.info(f"Data compression test results: Binary: {t1.elapsed():.4f}s, JSON: {t2.elapsed():.4f}s, Object: {t3.elapsed():.4f}s")
        return results
    
    def test_rate_limiting(self):
        """Test rate limiting optimizations."""
        logger.info("Testing rate limiting optimizations...")
        results = {}
        
        # Test global rate limiting
        with Timer("Global Rate Limiting") as t1:
            for i in range(10):
                adaptive_rate_limiter.wait()
        
        results["global_rate_limiting"] = t1.elapsed()
        
        # Test domain-specific rate limiting
        with Timer("Domain Rate Limiting") as t2:
            for i in range(5):
                adaptive_rate_limiter.wait_for_domain("example.com")
                adaptive_rate_limiter.wait_for_domain("api.example.com")
                adaptive_rate_limiter.wait_for_domain("data.example.com")
        
        results["domain_rate_limiting"] = t2.elapsed()
        
        # Test rate adaptation
        with Timer("Rate Adaptation") as t3:
            for i in range(10):
                # Simulate successful requests with varying response times
                adaptive_rate_limiter.update(True, 0.1)
            
            for i in range(5):
                # Simulate failed requests
                adaptive_rate_limiter.update(False, 0.5)
        
        results["rate_adaptation"] = t3.elapsed()
        results["final_rate"] = adaptive_rate_limiter.get_rate()
        
        logger.info(f"Rate limiting test results: Global: {t1.elapsed():.4f}s, Domain: {t2.elapsed():.4f}s, Adaptation: {t3.elapsed():.4f}s")
        return results
    
    def test_background_processing(self):
        """Test background processing optimizations."""
        logger.info("Testing background processing optimizations...")
        results = {}
        
        # Define a test task
        def test_task(task_id, sleep_time, progress_callback=None):
            for i in range(10):
                time.sleep(sleep_time / 10)
                if progress_callback:
                    progress_callback((i + 1) / 10)
            return f"Task {task_id} completed"
        
        # Test task submission
        with Timer("Task Submission") as t1:
            task_ids = []
            for i in range(5):
                task_id = background_processor.submit(
                    test_task,
                    f"task_{i}",
                    0.1,  # 100ms total sleep
                    priority=i
                )
                task_ids.append(task_id)
        
        results["task_submission"] = t1.elapsed()
        
        # Wait for tasks to complete
        with Timer("Task Completion") as t2:
            for task_id in task_ids:
                while True:
                    task_info = background_processor.get_task(task_id)
                    if task_info['status'] in ('completed', 'failed', 'cancelled'):
                        break
                    time.sleep(0.01)
        
        results["task_completion"] = t2.elapsed()
        results["task_stats"] = background_processor.get_stats()
        
        logger.info(f"Background processing test results: Submission: {t1.elapsed():.4f}s, Completion: {t2.elapsed():.4f}s")
        return results
    
    def test_progressive_loading(self):
        """Test progressive loading optimizations."""
        logger.info("Testing progressive loading optimizations...")
        results = {}
        
        # Test setting visible range
        with Timer("Set Visible Range") as t1:
            for i in range(0, 1000, 100):
                progressive_loader.set_visible_range(i, i + 100)
                time.sleep(0.01)  # Give time for loading to start
        
        results["set_visible_range"] = t1.elapsed()
        
        # Wait for some chunks to load
        with Timer("Wait for Loading") as t2:
            start_time = time.time()
            timeout = 2.0  # 2 second timeout
            
            while time.time() - start_time < timeout:
                progress = progressive_loader.get_loading_progress()
                if progress > 0.5:  # Wait until at least 50% loaded
                    break
                time.sleep(0.1)
        
        results["wait_for_loading"] = t2.elapsed()
        results["loading_progress"] = progressive_loader.get_loading_progress()
        results["loader_stats"] = progressive_loader.get_stats()
        
        logger.info(f"Progressive loading test results: Set Range: {t1.elapsed():.4f}s, Wait: {t2.elapsed():.4f}s")
        return results
    
    def test_ui_virtualization(self):
        """Test UI virtualization optimizations."""
        logger.info("Testing UI virtualization optimizations...")
        results = {}
        
        # Test virtualized list
        with Timer("Virtualized List") as t1:
            # Set up the list renderer
            self.virtualized_list.set_item_renderer(
                create_widget_func=lambda: tk.Frame(self.virtualized_list._content_frame),
                render_func=lambda item, widget: tk.Label(widget, text=str(item)).pack()
            )
            
            # Add items to the list
            items = [f"Item {i}" for i in range(10000)]
            self.virtualized_list.set_items(items)
            
            # Scroll through the list
            for i in range(0, 10000, 1000):
                self.virtualized_list.scroll_to_item(i)
        
        results["virtualized_list"] = t1.elapsed()
        
        # Test virtualized tree
        with Timer("Virtualized Tree") as t2:
            # Set up the tree renderer
            self.virtualized_tree.set_node_renderer(
                create_widget_func=lambda: tk.Frame(self.virtualized_tree._content_frame),
                render_func=lambda node, widget, level: tk.Label(widget, text=str(node.data)).pack()
            )
            
            # Add nodes to the tree
            for i in range(10):
                self.virtualized_tree.add_node(f"node_{i}", f"Node {i}")
                
                # Add child nodes
                for j in range(10):
                    child_id = f"node_{i}_{j}"
                    self.virtualized_tree.add_node(child_id, f"Child {j} of Node {i}", f"node_{i}")
                    
                    # Add grandchild nodes
                    for k in range(5):
                        grandchild_id = f"node_{i}_{j}_{k}"
                        self.virtualized_tree.add_node(grandchild_id, f"Grandchild {k} of Child {j}", child_id)
            
            # Expand all nodes
            self.virtualized_tree.expand_all()
            
            # Scroll through the tree
            for i in range(0, 10, 2):
                self.virtualized_tree.scroll_to_node(f"node_{i}")
        
        results["virtualized_tree"] = t2.elapsed()
        
        logger.info(f"UI virtualization test results: List: {t1.elapsed():.4f}s, Tree: {t2.elapsed():.4f}s")
        return results
    
    def _display_results(self, results):
        """Display the test results."""
        # Format the results
        text = "All Optimizations Test Results:\n\n"
        
        # Lazy module loading results
        text += "Lazy Module Loading:\n"
        text += f"  Import Time: {results['lazy_module_loading']['lazy_import']:.4f}s\n"
        text += f"  Loaded Modules: {len(results['lazy_module_loading']['loaded_modules'])}\n\n"
        
        # Caching results
        text += "Caching Optimizations:\n"
        text += f"  Enhanced Cache: {results['caching']['enhanced_cache']:.4f}s\n"
        text += f"  Database Cache: {results['caching']['db_cache']:.4f}s\n\n"
        
        # Threading results
        text += "Threading Optimizations:\n"
        text += f"  Thread Manager: {results['threading']['thread_manager']:.4f}s\n\n"
        
        # Memory management results
        text += "Memory Management Optimizations:\n"
        text += f"  Weak Cache: {results['memory']['weak_cache']:.4f}s\n"
        text += f"  Memory Monitoring: {results['memory']['memory_monitoring']:.4f}s\n\n"
        
        # Data compression results
        text += "Data Compression Optimizations:\n"
        text += f"  Binary Compression: {results['compression']['binary_compression']:.4f}s (Ratio: {results['compression']['binary_compression_ratio']:.2f}x)\n"
        text += f"  JSON Compression: {results['compression']['json_compression']:.4f}s (Ratio: {results['compression']['json_compression_ratio']:.2f}x)\n"
        text += f"  Object Compression: {results['compression']['object_compression']:.4f}s\n\n"
        
        # Rate limiting results
        text += "Rate Limiting Optimizations:\n"
        text += f"  Global Rate Limiting: {results['rate_limiting']['global_rate_limiting']:.4f}s\n"
        text += f"  Domain Rate Limiting: {results['rate_limiting']['domain_rate_limiting']:.4f}s\n"
        text += f"  Rate Adaptation: {results['rate_limiting']['rate_adaptation']:.4f}s\n\n"
        
        # Background processing results
        text += "Background Processing Optimizations:\n"
        text += f"  Task Submission: {results['background_processing']['task_submission']:.4f}s\n"
        text += f"  Task Completion: {results['background_processing']['task_completion']:.4f}s\n\n"
        
        # Progressive loading results
        text += "Progressive Loading Optimizations:\n"
        text += f"  Set Visible Range: {results['progressive_loading']['set_visible_range']:.4f}s\n"
        text += f"  Wait for Loading: {results['progressive_loading']['wait_for_loading']:.4f}s\n"
        text += f"  Loading Progress: {results['progressive_loading']['loading_progress']:.2%}\n\n"
        
        # UI virtualization results
        text += "UI Virtualization Optimizations:\n"
        text += f"  Virtualized List: {results['ui_virtualization']['virtualized_list']:.4f}s\n"
        text += f"  Virtualized Tree: {results['ui_virtualization']['virtualized_tree']:.4f}s\n"
        
        # Update the label
        self.results_label.config(text=text)
    
    def cleanup(self):
        """Clean up resources."""
        # Stop memory monitoring
        memory_monitor.stop()
        
        # Close the database cache
        self.db_cache.close()
        
        # Shut down the thread manager
        self.thread_manager.shutdown()
        
        # Stop background processor
        background_processor.stop(wait=False)
        
        # Stop progressive loader
        progressive_loader.stop_loading()
        
        # Destroy the window
        self.root.destroy()


if __name__ == "__main__":
    test = AllOptimizationsTest()
    try:
        test.root.after(100, test.run_tests)
        test.root.deiconify()  # Show the window
        test.root.mainloop()
    finally:
        test.cleanup()
