"""
Factory for creating application instances for the Czech Property Registry application.

This module provides a factory for creating the appropriate application instance
based on the configuration and requirements.
"""

import logging
import tkinter as tk
from typing import Dict, Any, Optional

from core.app_base import AppBase

# Configure logging
logger = logging.getLogger(__name__)


class AppFactory:
    """
    Factory for creating application instances.
    
    Provides methods for creating the appropriate application instance
    based on the configuration and requirements.
    """
    
    @staticmethod
    def create_app(root: Optional[tk.Tk] = None, app_type: str = "standard", 
                  config: Optional[Dict[str, Any]] = None) -> AppBase:
        """
        Create an application instance.
        
        Args:
            root (tk.Tk, optional): The root Tkinter window
            app_type (str): The type of application to create
            config (dict, optional): Configuration for the application
            
        Returns:
            AppBase: The created application instance
        """
        logger.info(f"Creating application of type: {app_type}")
        
        # Create the root window if not provided
        if root is None:
            root = tk.Tk()
            
        # Use default configuration if not provided
        if config is None:
            config = {}
            
        if app_type == "standard":
            # Import here to avoid circular imports
            from core.app import PropertyScraperApp
            return PropertyScraperApp(root)
        elif app_type == "optimized":
            # Import here to avoid circular imports
            from core.optimized_app import OptimizedPropertyScraperApp
            return OptimizedPropertyScraperApp(root)
        else:
            logger.warning(f"Unknown application type: {app_type}, using standard")
            # Import here to avoid circular imports
            from core.app import PropertyScraperApp
            return PropertyScraperApp(root)
    
    @staticmethod
    def create_app_from_config(config_path: str = "config.ini") -> AppBase:
        """
        Create an application instance from a configuration file.
        
        Args:
            config_path (str): Path to the configuration file
            
        Returns:
            AppBase: The created application instance
        """
        logger.info(f"Creating application from config: {config_path}")
        
        # Load the configuration
        import configparser
        config = configparser.ConfigParser()
        config.read(config_path)
        
        # Get the application type
        app_type = config.get("App", "type", fallback="standard")
        
        # Create the application
        return AppFactory.create_app(app_type=app_type, config=config)
    
    @staticmethod
    def get_app_class(app_type: str) -> type:
        """
        Get the application class for the specified type.
        
        Args:
            app_type (str): The type of application
            
        Returns:
            type: The application class
        """
        if app_type == "standard":
            # Import here to avoid circular imports
            from core.app import PropertyScraperApp
            return PropertyScraperApp
        elif app_type == "optimized":
            # Import here to avoid circular imports
            from core.optimized_app import OptimizedPropertyScraperApp
            return OptimizedPropertyScraperApp
        else:
            logger.warning(f"Unknown application type: {app_type}, using standard")
            # Import here to avoid circular imports
            from core.app import PropertyScraperApp
            return PropertyScraperApp
