<!DOCTYPE html>
<html>
<head>
    <style>
        body { margin: 0; padding: 0; height: 100%; }
        #map { width: 100%; height: 100%; }
    </style>
</head>
<body>
    <div id="map"></div>
    <script>
        function initMap() {
            var center = {lat: {lat}, lng: {lng}};
            var map = new google.maps.Map(document.getElementById('map'), {
                zoom: {zoom},
                center: center
            });

            // Add a marker at the center
            var marker = new google.maps.Marker({
                position: center,
                map: map,
                title: '{title}'
            });
        }
    </script>
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={api_key}&callback=initMap">
    </script>
</body>
</html>
