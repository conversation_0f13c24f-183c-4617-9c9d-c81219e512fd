"""
RUIAN ID Cache Module

This module provides a caching system for RUIAN IDs to improve performance
when working with RUIAN IDs, especially when verifying them against the CUZK system.
"""

import os
import json
import time
import logging
from typing import Dict, Any, Optional, Tuple, Set
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

class RuianIDCache:
    """
    A cache for RUIAN IDs to improve performance when working with RUIAN IDs.
    
    This cache stores verification results for RUIAN IDs to avoid making
    repeated network requests to the CUZK system.
    """
    
    # Default cache file path
    DEFAULT_CACHE_FILE = "data/ruian_cache.json"
    
    # Default cache expiration time (in days)
    DEFAULT_EXPIRATION_DAYS = 30
    
    def __init__(self, cache_file: Optional[str] = None, expiration_days: int = DEFAULT_EXPIRATION_DAYS):
        """
        Initialize the RUIAN ID cache.
        
        Args:
            cache_file (str, optional): Path to the cache file. If None, uses the default path.
            expiration_days (int, optional): Number of days after which cache entries expire.
        """
        self._cache_file = cache_file or self.DEFAULT_CACHE_FILE
        self._expiration_days = expiration_days
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._hits = 0
        self._misses = 0
        self._load_cache()
    
    def _load_cache(self) -> None:
        """
        Load the cache from the cache file.
        """
        try:
            if os.path.exists(self._cache_file):
                with open(self._cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._cache = data.get('cache', {})
                    logger.info(f"Loaded {len(self._cache)} RUIAN IDs from cache")
            else:
                # Create the directory if it doesn't exist
                os.makedirs(os.path.dirname(self._cache_file), exist_ok=True)
                logger.info(f"Cache file {self._cache_file} does not exist, starting with empty cache")
        except Exception as e:
            logger.error(f"Error loading cache: {e}")
            self._cache = {}
    
    def _save_cache(self) -> None:
        """
        Save the cache to the cache file.
        """
        try:
            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(self._cache_file), exist_ok=True)
            
            # Save the cache
            with open(self._cache_file, 'w', encoding='utf-8') as f:
                data = {
                    'cache': self._cache,
                    'last_saved': datetime.now().isoformat(),
                    'stats': {
                        'hits': self._hits,
                        'misses': self._misses,
                        'size': len(self._cache)
                    }
                }
                json.dump(data, f, indent=2)
            
            logger.info(f"Saved {len(self._cache)} RUIAN IDs to cache")
        except Exception as e:
            logger.error(f"Error saving cache: {e}")
    
    def add(self, ruian_id: str, exists: bool, error: Optional[str] = None) -> None:
        """
        Add a RUIAN ID to the cache.
        
        Args:
            ruian_id (str): The RUIAN ID to add
            exists (bool): Whether the RUIAN ID exists in the CUZK system
            error (str, optional): Error message if verification failed
        """
        self._cache[ruian_id] = {
            'exists': exists,
            'error': error,
            'timestamp': time.time(),
            'date': datetime.now().isoformat()
        }
        self._save_cache()
    
    def get(self, ruian_id: str) -> Optional[Tuple[bool, Optional[str]]]:
        """
        Get a RUIAN ID from the cache.
        
        Args:
            ruian_id (str): The RUIAN ID to get
        
        Returns:
            tuple or None: (exists, error) if the RUIAN ID is in the cache, None otherwise
        """
        if ruian_id in self._cache:
            # Check if the cache entry has expired
            entry = self._cache[ruian_id]
            timestamp = entry.get('timestamp', 0)
            expiration_time = time.time() - (self._expiration_days * 24 * 60 * 60)
            
            if timestamp > expiration_time:
                self._hits += 1
                return entry['exists'], entry.get('error')
            else:
                # Remove expired entry
                logger.info(f"Cache entry for RUIAN ID {ruian_id} has expired")
                del self._cache[ruian_id]
                self._save_cache()
        
        self._misses += 1
        return None
    
    def contains(self, ruian_id: str) -> bool:
        """
        Check if a RUIAN ID is in the cache.
        
        Args:
            ruian_id (str): The RUIAN ID to check
        
        Returns:
            bool: True if the RUIAN ID is in the cache, False otherwise
        """
        if ruian_id in self._cache:
            # Check if the cache entry has expired
            entry = self._cache[ruian_id]
            timestamp = entry.get('timestamp', 0)
            expiration_time = time.time() - (self._expiration_days * 24 * 60 * 60)
            
            return timestamp > expiration_time
        
        return False
    
    def clear(self) -> None:
        """
        Clear the cache.
        """
        self._cache = {}
        self._save_cache()
        logger.info("Cache cleared")
    
    def remove(self, ruian_id: str) -> bool:
        """
        Remove a RUIAN ID from the cache.
        
        Args:
            ruian_id (str): The RUIAN ID to remove
        
        Returns:
            bool: True if the RUIAN ID was removed, False otherwise
        """
        if ruian_id in self._cache:
            del self._cache[ruian_id]
            self._save_cache()
            logger.info(f"Removed RUIAN ID {ruian_id} from cache")
            return True
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            dict: Cache statistics
        """
        expired_count = 0
        expiration_time = time.time() - (self._expiration_days * 24 * 60 * 60)
        
        for entry in self._cache.values():
            timestamp = entry.get('timestamp', 0)
            if timestamp <= expiration_time:
                expired_count += 1
        
        return {
            'size': len(self._cache),
            'hits': self._hits,
            'misses': self._misses,
            'hit_ratio': self._hits / (self._hits + self._misses) if (self._hits + self._misses) > 0 else 0,
            'expired': expired_count,
            'active': len(self._cache) - expired_count,
            'expiration_days': self._expiration_days
        }
    
    def cleanup(self) -> int:
        """
        Remove expired entries from the cache.
        
        Returns:
            int: Number of entries removed
        """
        expiration_time = time.time() - (self._expiration_days * 24 * 60 * 60)
        expired_ids = []
        
        for ruian_id, entry in self._cache.items():
            timestamp = entry.get('timestamp', 0)
            if timestamp <= expiration_time:
                expired_ids.append(ruian_id)
        
        for ruian_id in expired_ids:
            del self._cache[ruian_id]
        
        if expired_ids:
            self._save_cache()
            logger.info(f"Removed {len(expired_ids)} expired entries from cache")
        
        return len(expired_ids)
    
    def get_all_ids(self) -> Set[str]:
        """
        Get all RUIAN IDs in the cache.
        
        Returns:
            set: Set of all RUIAN IDs in the cache
        """
        return set(self._cache.keys())
    
    def get_valid_ids(self) -> Set[str]:
        """
        Get all valid RUIAN IDs in the cache.
        
        Returns:
            set: Set of all valid RUIAN IDs in the cache
        """
        return {ruian_id for ruian_id, entry in self._cache.items() if entry.get('exists', False)}
    
    def get_invalid_ids(self) -> Set[str]:
        """
        Get all invalid RUIAN IDs in the cache.
        
        Returns:
            set: Set of all invalid RUIAN IDs in the cache
        """
        return {ruian_id for ruian_id, entry in self._cache.items() if not entry.get('exists', False)}


# Create a singleton instance of the cache
_cache_instance = None

def get_cache() -> RuianIDCache:
    """
    Get the singleton instance of the RUIAN ID cache.
    
    Returns:
        RuianIDCache: The singleton instance of the RUIAN ID cache
    """
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = RuianIDCache()
    
    return _cache_instance
