"""
Optimized batch search manager for the Czech Property Registry application.
Provides improved performance for batch searching of properties.
"""

import os
import sys
import time
import logging
import threading
import concurrent.futures
import random
import math
from typing import Dict, Any, Optional, List, Tuple, Set, Union, Callable

# Configure logging
logger = logging.getLogger("OptimizedBatchSearchManager")


class OptimizedBatchSearchManager:
    """
    Optimized batch search manager with improved performance for batch searching of properties.
    """

    def __init__(self, app):
        """
        Initialize the optimized batch search manager.

        Args:
            app: The application instance
        """
        self.app = app

        # Thread pool for parallel processing
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=5,
            thread_name_prefix="BatchSearchWorker"
        )

        # Cache for search results
        self.search_cache = {}
        self.search_cache_lock = threading.Lock()
        self.always_prefer_api = True  # Always prefer API data over cached data
        self.max_cache_age = 30 * 60  # Maximum cache age in seconds (30 minutes)

        # Statistics
        self.stats = {
            "batch_searches": 0,
            "properties_found": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "total_search_time": 0.0,
            "search_times": []
        }
        self.stats_lock = threading.Lock()

        logger.info("OptimizedBatchSearchManager initialized")

    def batch_search_by_address(self, address: str, radius_km: float, max_results: int = 100,
                               property_types: List[str] = None, callback: Callable = None) -> List[Dict[str, Any]]:
        """
        Perform a batch search for properties around an address with optimized performance.

        Args:
            address (str): Address to search around
            radius_km (float): Search radius in kilometers
            max_results (int): Maximum number of results to return
            property_types (List[str], optional): Types of properties to include
            callback (Callable, optional): Callback function to call with results

        Returns:
            List[Dict[str, Any]]: List of property data dictionaries
        """
        start_time = time.time()

        logger.info(f"Batch searching by address: {address}, with radius {radius_km}km")

        # Check if we should always use API data
        if self.always_prefer_api:
            logger.info(f"Always preferring API data for batch search: {address}")
        # Otherwise, check cache first
        else:
            cache_key = f"{address}:{radius_km}:{max_results}:{','.join(property_types or [])}"
            with self.search_cache_lock:
                if cache_key in self.search_cache:
                    cached_result = self.search_cache[cache_key]
                    # Check if cache entry is still valid (less than max_cache_age)
                    if time.time() - cached_result["timestamp"] < self.max_cache_age:
                        with self.stats_lock:
                            self.stats["cache_hits"] += 1

                        # Call callback if provided
                        if callback:
                            self.app.root.after(0, lambda: callback(cached_result["properties"]))

                        logger.info(f"Returned {len(cached_result['properties'])} properties from cache")
                        # Show notification that cached data is being used
                        self._show_cache_notification(address, len(cached_result["properties"]))
                        return cached_result["properties"]

        # Cache miss, perform the search
        with self.stats_lock:
            self.stats["cache_misses"] += 1
            self.stats["batch_searches"] += 1

        # Geocode the address
        coordinates = self._geocode_address(address)
        if not coordinates:
            logger.warning(f"Could not geocode address: {address}")
            return []

        lat, lng = coordinates

        # Convert radius from km to meters
        radius_meters = int(radius_km * 1000)

        # Fetch properties in the area
        properties = self._fetch_properties_in_area(lat, lng, radius_meters, max_results, property_types, callback)

        # Cache the results
        with self.search_cache_lock:
            self.search_cache[cache_key] = {
                "properties": properties,
                "timestamp": time.time()
            }

            # Limit cache size to 100 entries
            if len(self.search_cache) > 100:
                # Remove oldest entries
                oldest_key = min(self.search_cache.items(), key=lambda x: x[1]["timestamp"])[0]
                del self.search_cache[oldest_key]

        # Update statistics
        search_time = time.time() - start_time
        with self.stats_lock:
            self.stats["properties_found"] += len(properties)
            self.stats["total_search_time"] += search_time
            self.stats["search_times"].append(search_time)

            # Keep only the last 100 search times
            if len(self.stats["search_times"]) > 100:
                self.stats["search_times"] = self.stats["search_times"][-100:]

        logger.info(f"Found {len(properties)} properties in {search_time:.2f} seconds")

        return properties

    def _geocode_address(self, address: str) -> Optional[Tuple[float, float]]:
        """
        Geocode an address to get coordinates with caching.

        Args:
            address (str): Address to geocode

        Returns:
            Optional[Tuple[float, float]]: Latitude and longitude coordinates, or None if geocoding failed
        """
        # Check if we have Google Maps integration
        if hasattr(self.app, 'google_maps'):
            try:
                # Add country if not specified
                if "czech republic" not in address.lower() and "czechia" not in address.lower():
                    address = f"{address}, Czech Republic"

                logger.info(f"Geocoding address: {address}")

                # Try direct geocoding
                result = self.app.google_maps.geocode(address)

                if result:
                    lat = result.get('lat')
                    lng = result.get('lng')

                    if lat and lng:
                        logger.info(f"Geocoded {address} to coordinates: {lat}, {lng}")
                        return lat, lng
            except Exception as e:
                logger.error(f"Error geocoding address: {e}")

        # Return None if geocoding failed
        logger.warning(f"Could not geocode address: {address}")
        return None

    def _fetch_properties_in_area(self, lat: float, lng: float, radius: int, max_results: int,
                                 property_types: List[str] = None, callback: Callable = None) -> List[Dict[str, Any]]:
        """
        Fetch properties in the specified area using RUIAN/CUZK with optimized performance.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            radius (int): Search radius in meters
            max_results (int): Maximum number of results to return
            property_types (List[str], optional): Types of properties to include
            callback (Callable, optional): Callback function to call with results

        Returns:
            List[Dict[str, Any]]: List of property data dictionaries
        """
        logger.info(f"Fetching properties in area at coordinates {lat}, {lng} with radius {radius}m")

        # Calculate the bounding box for the search
        # 1 degree of latitude is approximately 111 km
        # 1 degree of longitude varies with latitude, approximately 111 km * cos(latitude)
        lat_offset = radius / 111000
        lng_offset = radius / (111000 * math.cos(math.radians(lat)))

        min_lat = lat - lat_offset
        max_lat = lat + lat_offset
        min_lng = lng - lng_offset
        max_lng = lng + lng_offset

        # Generate a grid of points within the bounding box
        points = self._generate_search_grid(min_lat, max_lat, min_lng, max_lng, lat, lng, radius, max_results)

        # Fetch properties for each point in parallel
        properties = self._fetch_properties_for_points(points, property_types)

        # Filter by property types if specified
        if property_types and len(property_types) > 0:
            filtered_properties = []
            for prop in properties:
                prop_type = prop.get('property_type', '').lower()
                if any(t.lower() in prop_type for t in property_types):
                    filtered_properties.append(prop)
            properties = filtered_properties

        # Limit to max_results
        if len(properties) > max_results:
            properties = properties[:max_results]

        # Generate CUZK URLs for each property
        properties = self._generate_property_urls(properties)

        # Call the callback function if provided
        if callback:
            self.app.root.after(0, lambda: callback(properties))

        logger.info(f"Generated {len(properties)} RUIAN property URLs")

        return properties

    def _generate_search_grid(self, min_lat: float, max_lat: float, min_lng: float, max_lng: float,
                             center_lat: float, center_lng: float, radius: int, max_points: int) -> List[Tuple[float, float]]:
        """
        Generate a grid of points within the bounding box for searching.

        Args:
            min_lat (float): Minimum latitude
            max_lat (float): Maximum latitude
            min_lng (float): Minimum longitude
            max_lng (float): Maximum longitude
            center_lat (float): Center latitude
            center_lng (float): Center longitude
            radius (int): Search radius in meters
            max_points (int): Maximum number of points to generate

        Returns:
            List[Tuple[float, float]]: List of (latitude, longitude) points
        """
        # Calculate the number of points to generate based on the area
        area = (max_lat - min_lat) * (max_lng - min_lng)

        # Adjust the number of points based on the area and max_points
        num_points = min(max(int(area * 1000000), 10), max_points)

        # Generate a spiral pattern of points starting from the center
        points = [(center_lat, center_lng)]  # Start with the center point

        # Add points in a spiral pattern
        angle = 0.0
        radius_step = radius / (num_points * 2)
        current_radius = radius_step

        while len(points) < num_points:
            # Calculate the next point in the spiral
            angle += 0.5
            current_radius += radius_step / angle

            # Convert to Cartesian coordinates
            x = current_radius * math.cos(angle)
            y = current_radius * math.sin(angle)

            # Convert back to latitude and longitude
            point_lat = center_lat + (y / 111000)
            point_lng = center_lng + (x / (111000 * math.cos(math.radians(center_lat))))

            # Check if the point is within the bounding box
            if min_lat <= point_lat <= max_lat and min_lng <= point_lng <= max_lng:
                # Check if the point is within the search radius
                distance = self._haversine_distance(center_lat, center_lng, point_lat, point_lng)
                if distance <= radius / 1000:  # Convert radius to km
                    points.append((point_lat, point_lng))

            # Break if we've gone too far
            if current_radius > radius * 2:
                break

        return points

    def _haversine_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """
        Calculate the Haversine distance between two points in kilometers.

        Args:
            lat1 (float): Latitude of point 1
            lng1 (float): Longitude of point 1
            lat2 (float): Latitude of point 2
            lng2 (float): Longitude of point 2

        Returns:
            float: Distance in kilometers
        """
        # Convert latitude and longitude from degrees to radians
        lat1_rad = math.radians(lat1)
        lng1_rad = math.radians(lng1)
        lat2_rad = math.radians(lat2)
        lng2_rad = math.radians(lng2)

        # Haversine formula
        dlat = lat2_rad - lat1_rad
        dlng = lng2_rad - lng1_rad
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = 6371 * c  # Earth radius in kilometers

        return distance

    def _fetch_properties_for_points(self, points: List[Tuple[float, float]], property_types: List[str] = None) -> List[Dict[str, Any]]:
        """
        Fetch properties for each point in parallel.

        Args:
            points (List[Tuple[float, float]]): List of (latitude, longitude) points
            property_types (List[str], optional): Types of properties to include

        Returns:
            List[Dict[str, Any]]: List of property data dictionaries
        """
        # Create a list to store the results
        all_properties = []

        # Submit tasks to the thread pool
        futures = []
        for lat, lng in points:
            future = self.thread_pool.submit(self._fetch_properties_at_point, lat, lng, property_types)
            futures.append(future)

        # Wait for all tasks to complete
        for future in concurrent.futures.as_completed(futures):
            try:
                properties = future.result()
                all_properties.extend(properties)
            except Exception as e:
                logger.error(f"Error fetching properties: {e}")

        # Remove duplicates based on RUIAN ID
        unique_properties = {}
        for prop in all_properties:
            ruian_id = prop.get('ruian_id')
            if ruian_id and ruian_id not in unique_properties:
                unique_properties[ruian_id] = prop

        return list(unique_properties.values())

    def _fetch_properties_at_point(self, lat: float, lng: float, _: List[str] = None) -> List[Dict[str, Any]]:
        """
        Fetch properties at a specific point.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            _ (List[str], optional): Types of properties to include (unused in this method)

        Returns:
            List[Dict[str, Any]]: List of property data dictionaries
        """
        # Check if we have RUIAN integration
        if hasattr(self.app, 'ruian'):
            try:
                # Fetch properties from RUIAN
                properties = self.app.ruian.get_properties_at_coordinates(lat, lng)

                # Add coordinates to each property
                for prop in properties:
                    prop['lat'] = lat
                    prop['lng'] = lng

                return properties
            except Exception as e:
                logger.error(f"Error fetching properties from RUIAN: {e}")

        # Return an empty list if RUIAN integration is not available or fails
        logger.warning(f"RUIAN integration not available or failed for coordinates: {lat}, {lng}")
        return []

    def _generate_dummy_properties(self, lat: float, lng: float) -> List[Dict[str, Any]]:
        """
        Generate dummy properties for testing.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate

        Returns:
            List[Dict[str, Any]]: List of dummy property data dictionaries
        """
        # Generate a random RUIAN ID
        ruian_id = f"{random.randint(1000000, 9999999)}"

        # Generate a dummy property
        property_types = ["Building", "Land", "Apartment", "House", "Commercial"]
        property_type = random.choice(property_types)

        return [{
            'ruian_id': ruian_id,
            'property_type': property_type,
            'lat': lat,
            'lng': lng
        }]

    def _generate_property_urls(self, properties: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate CUZK URLs for each property using Mapy.cz integration.

        Args:
            properties (List[Dict[str, Any]]): List of property data dictionaries

        Returns:
            List[Dict[str, Any]]: List of property data dictionaries with URLs
        """
        # Check if we have CUZK integration with Mapy.cz
        if hasattr(self.app, 'cuzk_integration') and hasattr(self.app.cuzk_integration, 'mapycz'):
            logger.info("Using CUZK integration with Mapy.cz to generate URLs")

            for prop in properties:
                # Use the CUZK integration to generate the URL
                # This will use Mapy.cz to get the correct S-JTSK coordinates
                url = self.app.cuzk_integration.generate_cuzk_url(
                    prop['lat'],
                    prop['lng'],
                    use_mapa_identifikace=True
                )

                # Add the URL to the property
                prop['url'] = url

                # Extract S-JTSK coordinates from the URL for reference
                import re
                match = re.search(r'x=(\d+)&y=(\d+)', url)
                if match:
                    prop['x_sjtsk'] = int(match.group(1))
                    prop['y_sjtsk'] = int(match.group(2))
        else:
            # Fallback to the old method if CUZK integration is not available
            logger.warning("CUZK integration not available, using fallback method")

            for prop in properties:
                # Convert coordinates to the format expected by CUZK
                # This is a very rough approximation and should be avoided
                x = int(-prop['lng'] * 100000)
                y = int(-prop['lat'] * 100000)

                # Generate the URL
                url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"

                # Add the URL to the property
                prop['url'] = url

                # Store the S-JTSK coordinates for reference
                prop['x_sjtsk'] = x
                prop['y_sjtsk'] = y

                logger.warning(f"Using fallback coordinate conversion for {prop['lat']}, {prop['lng']} -> {x}, {y}")

        return properties

    def _show_cache_notification(self, address: str, property_count: int) -> None:
        """
        Show a notification that cached batch search data is being used.

        Args:
            address: Address that was searched
            property_count: Number of properties in the cached result
        """
        # Only show notification if we have a notification banner
        if hasattr(self.app, 'notification_banner'):
            # Create the notification message
            message = f"Using cached batch search data for {address} ({property_count} properties). Real API data is preferred but not available."

            # Show the notification
            self.app.notification_banner.show(
                message=message,
                notification_type="fallback",
                auto_hide=True,
                duration=5000,
                key=f"cache_batch_search_{address}"
            )

            # Also update the status bar
            self.app.show_status(f"Using cached batch search data for {address}")

    def set_api_preference(self, prefer_api: bool = True) -> None:
        """
        Set whether to always prefer API data over cached data.

        Args:
            prefer_api (bool): Whether to always prefer API data (default: True)
        """
        previous_value = self.always_prefer_api
        self.always_prefer_api = prefer_api
        logger.info(f"API preference changed: {previous_value} -> {prefer_api}")

        # If we're now preferring API, clear the cache
        if prefer_api and not previous_value:
            logger.info("Now preferring API data, clearing search cache")
            with self.search_cache_lock:
                self.search_cache.clear()

    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the batch search manager.

        Returns:
            Dict[str, Any]: Dictionary with batch search statistics
        """
        with self.stats_lock:
            stats = self.stats.copy()

            # Calculate average search time
            if stats["batch_searches"] > 0:
                stats["average_search_time"] = stats["total_search_time"] / stats["batch_searches"]
            else:
                stats["average_search_time"] = 0.0

            # Calculate average properties per search
            if stats["batch_searches"] > 0:
                stats["average_properties_per_search"] = stats["properties_found"] / stats["batch_searches"]
            else:
                stats["average_properties_per_search"] = 0.0

            # Add API preference information
            stats["always_prefer_api"] = self.always_prefer_api
            stats["max_cache_age_seconds"] = self.max_cache_age
            stats["cache_size"] = len(self.search_cache)

            return stats
