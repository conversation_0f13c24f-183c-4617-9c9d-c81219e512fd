"""
Rate Limiter Module for Czech Property Registry

This module provides rate limiting functionality for API calls to avoid hitting API quotas.
"""

import time
import threading
import random
from collections import defaultdict, deque

class RateLimiter:
    """Rate limiter for API calls to avoid hitting API quotas"""

    def __init__(self, requests_per_second=5, requests_per_day=1000):
        """
        Initialize the rate limiter

        Args:
            requests_per_second (int): Maximum number of requests per second
            requests_per_day (int): Maximum number of requests per day
        """
        self.requests_per_second = requests_per_second
        self.requests_per_day = requests_per_day
        self.request_times = defaultdict(lambda: deque(maxlen=max(requests_per_second, requests_per_day)))
        self.lock = threading.Lock()

    def _can_make_request(self, endpoint):
        """
        Check if a request can be made based on rate limits

        Args:
            endpoint (str): The API endpoint

        Returns:
            bool: True if request can be made, False otherwise
        """
        with self.lock:
            now = time.time()
            request_times = self.request_times[endpoint]

            # If no requests have been made yet, allow it
            if not request_times:
                return True

            # Check daily limit - more efficient implementation
            # Only keep timestamps within the last 24 hours
            day_ago = now - 86400  # 24 hours in seconds

            # Use a more efficient approach by filtering the deque in-place
            # This avoids creating a new list and is more memory efficient
            daily_requests = 0
            for t in request_times:
                if t > day_ago:
                    daily_requests += 1

            if daily_requests >= self.requests_per_day:
                return False

            # Check per-second limit - more efficient implementation
            second_ago = now - 1
            recent_requests = 0

            # Start from the end of the deque (most recent requests)
            # and stop counting once we find a request older than 1 second
            for t in reversed(list(request_times)):
                if t > second_ago:
                    recent_requests += 1
                    if recent_requests >= self.requests_per_second:
                        return False
                else:
                    # No need to check older requests
                    break

            return True

    def _record_request(self, endpoint):
        """
        Record a request for rate limiting

        Args:
            endpoint (str): The API endpoint
        """
        with self.lock:
            self.request_times[endpoint].append(time.time())

    def wait_if_needed(self, endpoint):
        """
        Wait if necessary to comply with rate limits

        Args:
            endpoint (str): The API endpoint

        Returns:
            float: Time waited in seconds, or -1 if hit hard limit
        """
        # First check if we can make the request without waiting
        if self._can_make_request(endpoint):
            self._record_request(endpoint)
            return 0

        # Calculate how long we need to wait based on existing requests
        wait_time = 0

        # Use adaptive backoff strategy
        with self.lock:
            now = time.time()
            request_times = list(self.request_times[endpoint])

            if not request_times:
                # No previous requests, should be able to make this one
                self._record_request(endpoint)
                return 0

            # Check per-second limit first (shorter wait time)
            second_ago = now - 1
            recent_requests = [t for t in request_times if t > second_ago]

            # Check daily limit
            day_ago = now - 86400  # 24 hours in seconds
            daily_requests = sum(1 for t in request_times if t > day_ago)

            # Determine wait strategy based on limits
            if daily_requests >= self.requests_per_day:
                # Daily limit reached - check if we need to wait a long time
                oldest_request = min(request_times)
                wait_for_day = max(0, oldest_request + 86400 - now)

                if wait_for_day > 60:  # More than a minute
                    # Use exponential backoff for retries
                    # Store the last retry time and count in a class variable
                    if not hasattr(self, '_retry_info'):
                        self._retry_info = {}

                    if endpoint not in self._retry_info:
                        self._retry_info[endpoint] = {'count': 0, 'last_retry': 0}

                    retry_info = self._retry_info[endpoint]
                    retry_count = retry_info['count']

                    # Calculate backoff time (1s, 2s, 4s, 8s, etc.)
                    backoff_time = min(60, 2 ** retry_count)

                    # Update retry count for next time
                    retry_info['count'] = retry_count + 1
                    retry_info['last_retry'] = now

                    print(f"Daily limit reached for {endpoint}. Using backoff strategy: {backoff_time:.1f}s")
                    return -1  # Indicate we've hit a hard limit

                # If wait time is reasonable, wait and then proceed
                if wait_for_day > 0:
                    time.sleep(wait_for_day)
                    wait_time += wait_for_day

            elif len(recent_requests) >= self.requests_per_second:
                # Per-second limit reached
                # Calculate time until oldest recent request is 1 second old
                oldest_recent = min(recent_requests)
                wait_for_second = max(0, oldest_recent + 1 - now)

                # Add a small random jitter to avoid thundering herd problem
                jitter = random.uniform(0, 0.1)  # 0-100ms jitter

                if wait_for_second > 0:
                    time.sleep(wait_for_second + jitter)
                    wait_time += wait_for_second + jitter

        # Record the request after waiting
        self._record_request(endpoint)

        # Reset retry count if successful
        if hasattr(self, '_retry_info') and endpoint in self._retry_info:
            self._retry_info[endpoint]['count'] = 0

        return wait_time

    def wait(self, endpoint="default"):
        """
        Wait if necessary to comply with rate limits

        This is a simplified version of wait_if_needed for backward compatibility

        Args:
            endpoint (str, optional): The API endpoint. Defaults to "default".
        """
        self.wait_if_needed(endpoint)

    def get_usage_stats(self):
        """
        Get current API usage statistics

        Returns:
            dict: API usage statistics
        """
        stats = {}
        now = time.time()

        with self.lock:
            for endpoint, times in self.request_times.items():
                day_ago = now - 86400  # 24 hours in seconds
                daily_requests = sum(1 for t in times if t > day_ago)

                second_ago = now - 1
                recent_requests = sum(1 for t in times if t > second_ago)

                stats[endpoint] = {
                    'daily_requests': daily_requests,
                    'daily_limit': self.requests_per_day,
                    'daily_remaining': self.requests_per_day - daily_requests,
                    'recent_requests': recent_requests,
                    'per_second_limit': self.requests_per_second,
                    'per_second_remaining': self.requests_per_second - recent_requests
                }

        return stats
