"""
File-related helper functions for the Czech Property Registry application.

This module provides functions for file operations, such as reading and writing
files, checking file existence, and handling file paths.
"""

import os
import json
import csv
import logging
from typing import Any, Dict, List, Optional, Tuple, Union

# Configure logging
logger = logging.getLogger(__name__)


def ensure_directory_exists(directory_path: str) -> bool:
    """
    Ensure that a directory exists, creating it if necessary.

    Args:
        directory_path (str): Path to the directory

    Returns:
        bool: True if the directory exists or was created, False otherwise
    """
    try:
        if not os.path.exists(directory_path):
            os.makedirs(directory_path)
            logger.info(f"Created directory: {directory_path}")
        return True
    except Exception as e:
        logger.error(f"Error creating directory {directory_path}: {e}")
        return False


def read_json_file(file_path: str, default: Any = None) -> Any:
    """
    Read a JSON file.

    Args:
        file_path (str): Path to the JSON file
        default (any, optional): Default value to return if the file doesn't exist or can't be read

    Returns:
        any: The contents of the JSON file, or the default value
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"JSON file not found: {file_path}")
            return default

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.debug(f"Read JSON file: {file_path}")
        return data
    except Exception as e:
        logger.error(f"Error reading JSON file {file_path}: {e}")
        return default


def write_json_file(file_path: str, data: Any, indent: int = 4) -> bool:
    """
    Write data to a JSON file.

    Args:
        file_path (str): Path to the JSON file
        data (any): Data to write
        indent (int, optional): Indentation level

    Returns:
        bool: True if the file was written successfully, False otherwise
    """
    try:
        # Ensure the directory exists
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
        logger.debug(f"Wrote JSON file: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error writing JSON file {file_path}: {e}")
        return False


def read_csv_file(file_path: str, delimiter: str = ',', has_header: bool = True) -> List[Dict[str, str]]:
    """
    Read a CSV file.

    Args:
        file_path (str): Path to the CSV file
        delimiter (str, optional): CSV delimiter
        has_header (bool, optional): Whether the CSV file has a header row

    Returns:
        list: List of dictionaries, one for each row
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"CSV file not found: {file_path}")
            return []

        with open(file_path, 'r', encoding='utf-8') as f:
            if has_header:
                reader = csv.DictReader(f, delimiter=delimiter)
                rows = list(reader)
            else:
                reader = csv.reader(f, delimiter=delimiter)
                rows = []
                for row in reader:
                    rows.append({f"column_{i}": value for i, value in enumerate(row)})
        logger.debug(f"Read CSV file: {file_path}")
        return rows
    except Exception as e:
        logger.error(f"Error reading CSV file {file_path}: {e}")
        return []


def write_csv_file(file_path: str, data: List[Dict[str, Any]], fieldnames: Optional[List[str]] = None,
                  delimiter: str = ',') -> bool:
    """
    Write data to a CSV file.

    Args:
        file_path (str): Path to the CSV file
        data (list): List of dictionaries, one for each row
        fieldnames (list, optional): List of field names for the CSV header
        delimiter (str, optional): CSV delimiter

    Returns:
        bool: True if the file was written successfully, False otherwise
    """
    try:
        # Ensure the directory exists
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)

        # If fieldnames is not provided, use the keys from the first row
        if fieldnames is None and data:
            fieldnames = list(data[0].keys())

        with open(file_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, delimiter=delimiter)
            writer.writeheader()
            writer.writerows(data)
        logger.debug(f"Wrote CSV file: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error writing CSV file {file_path}: {e}")
        return False


def get_file_extension(file_path: str) -> str:
    """
    Get the extension of a file.

    Args:
        file_path (str): Path to the file

    Returns:
        str: The file extension (without the dot)
    """
    return os.path.splitext(file_path)[1][1:].lower()


def is_file_newer_than(file_path: str, seconds: int) -> bool:
    """
    Check if a file is newer than a specified number of seconds.

    Args:
        file_path (str): Path to the file
        seconds (int): Number of seconds

    Returns:
        bool: True if the file is newer than the specified number of seconds, False otherwise
    """
    try:
        if not os.path.exists(file_path):
            return False

        file_time = os.path.getmtime(file_path)
        import time
        current_time = time.time()
        return current_time - file_time < seconds
    except Exception as e:
        logger.error(f"Error checking file age {file_path}: {e}")
        return False


def get_file_size(file_path: str) -> int:
    """
    Get the size of a file in bytes.

    Args:
        file_path (str): Path to the file

    Returns:
        int: The file size in bytes, or -1 if the file doesn't exist or can't be accessed
    """
    try:
        if not os.path.exists(file_path):
            return -1

        return os.path.getsize(file_path)
    except Exception as e:
        logger.error(f"Error getting file size {file_path}: {e}")
        return -1


def list_files_in_directory(directory_path: str, extension: Optional[str] = None) -> List[str]:
    """
    List all files in a directory.

    Args:
        directory_path (str): Path to the directory
        extension (str, optional): Filter by file extension

    Returns:
        list: List of file paths
    """
    try:
        if not os.path.exists(directory_path):
            logger.warning(f"Directory not found: {directory_path}")
            return []

        files = []
        for file_name in os.listdir(directory_path):
            file_path = os.path.join(directory_path, file_name)
            if os.path.isfile(file_path):
                if extension is None or file_name.lower().endswith(f".{extension.lower()}"):
                    files.append(file_path)
        return files
    except Exception as e:
        logger.error(f"Error listing files in directory {directory_path}: {e}")
        return []


__all__ = [
    'ensure_directory_exists',
    'read_json_file',
    'write_json_file',
    'read_csv_file',
    'write_csv_file',
    'get_file_extension',
    'is_file_newer_than',
    'get_file_size',
    'list_files_in_directory'
]
