"""
Test for radius search with tabular results display.

This test demonstrates searching for properties within different radiuses around an address
and displaying the results in a table format, including coordinates, RUIAN data, and CUZK URLs.
"""

import os
import sys
import unittest
import logging
import tkinter as tk
from tkinter import ttk
from unittest.mock import MagicMock, patch
import webbrowser
import csv
import tempfile
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RadiusSearchTableTest")

# Import required modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from api.cuzk.cuzk_integration import CUZKIntegration


class RadiusSearchTableTest(unittest.TestCase):
    """Test for radius search with tabular results display."""

    def setUp(self):
        """Initialize test components."""
        # Create a mock app
        self.app = MagicMock()
        self.app.root = tk.Tk()
        self.app.root.withdraw()  # Hide the root window
        
        # Initialize real components
        self.google_maps = GoogleMapsIntegration()
        self.cuzk_integration = CUZKIntegration()
        
        # Attach real components to the mock app
        self.app.google_maps = self.google_maps
        self.app.cuzk_integration = self.cuzk_integration
        
        # Create a mock OSM integration
        self.app.osm_integration = MagicMock()
        self.app.osm_integration.find_buildings_by_coordinates = MagicMock(return_value=[
            {
                'type': 'way',
                'id': '123456',
                'lat': 50.0755,
                'lon': 14.4378,
                'tags': {
                    'building': 'residential',
                    'ref:ruian': '87654321',
                    'addr:street': 'Test Street',
                    'addr:housenumber': '123',
                    'addr:city': 'Prague'
                },
                'ruian_ref': '87654321'
            }
        ])
        
        # Mock the CUZK integration's get_property_by_coordinates method
        self.cuzk_integration.get_property_by_coordinates = MagicMock(return_value={
            'property_type': 'Building',
            'ruian_id': '12345678',
            'owner_address': 'Test Address 123, Prague',
            'owner_name': 'Test Owner',
            'cadastral_territory': 'Prague',
            'parcel_number': '123/456'
        })
        
        # Test address
        self.test_address = "Prague, Czech Republic"
        
        # Test radiuses (in km)
        self.test_radiuses = [0.1, 0.5, 1.0]
        
        # Create a temporary directory for output files
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up after tests."""
        self.app.root.destroy()
        
    def test_radius_search_with_table_display(self):
        """Test searching with different radiuses and displaying results in a table."""
        logger.info("Testing radius search with table display")
        
        # Skip test if Google Maps API is not available
        try:
            # Test if geocoding works with a simple query
            test_result = self.google_maps.geocode("Prague")
            if not test_result or 'lat' not in test_result:
                self.skipTest("Google Maps API not available or not working")
        except Exception as e:
            self.skipTest(f"Google Maps API error: {e}")
        
        # Get coordinates for the test address
        geocode_result = self.google_maps.geocode(self.test_address)
        self.assertIsNotNone(geocode_result, "Geocoding should return a result")
        
        # Extract coordinates
        center_lat = geocode_result['lat']
        center_lng = geocode_result['lng']
        location_name = geocode_result.get('formatted_address', self.test_address)
        
        logger.info(f"Geocoded {self.test_address} to coordinates: {center_lat}, {center_lng} ({location_name})")
        
        # Create a new window for the table
        table_window = tk.Toplevel(self.app.root)
        table_window.title(f"Property Search Results - {location_name}")
        table_window.geometry("1200x600")
        
        # Create a notebook for tabs (one tab per radius)
        notebook = ttk.Notebook(table_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Dictionary to store results for each radius
        all_results = {}
        
        # Search with each radius and create a tab
        for radius in self.test_radiuses:
            logger.info(f"Searching with radius: {radius} km")
            
            # Create a frame for this radius
            radius_frame = ttk.Frame(notebook)
            notebook.add(radius_frame, text=f"Radius: {radius} km")
            
            # Create a table (Treeview) for the results
            columns = ("property_type", "ruian_id", "address", "lat", "lng", "x_sjtsk", "y_sjtsk", "url")
            results_table = ttk.Treeview(
                radius_frame, 
                columns=columns,
                show="headings",
                selectmode="browse"
            )
            
            # Define column headings
            results_table.heading("property_type", text="Property Type")
            results_table.heading("ruian_id", text="RUIAN ID")
            results_table.heading("address", text="Address")
            results_table.heading("lat", text="Latitude")
            results_table.heading("lng", text="Longitude")
            results_table.heading("x_sjtsk", text="X (S-JTSK)")
            results_table.heading("y_sjtsk", text="Y (S-JTSK)")
            results_table.heading("url", text="CUZK URL")
            
            # Define column widths
            results_table.column("property_type", width=100)
            results_table.column("ruian_id", width=100)
            results_table.column("address", width=200)
            results_table.column("lat", width=100)
            results_table.column("lng", width=100)
            results_table.column("x_sjtsk", width=100)
            results_table.column("y_sjtsk", width=100)
            results_table.column("url", width=300)
            
            # Add a scrollbar
            scrollbar = ttk.Scrollbar(radius_frame, orient=tk.VERTICAL, command=results_table.yview)
            results_table.configure(yscrollcommand=scrollbar.set)
            
            # Pack the table and scrollbar
            results_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Bind double-click event to open URL
            results_table.bind("<Double-1>", lambda event, table=results_table: self.on_result_double_click(event, table))
            
            # Search for properties within this radius
            radius_meters = int(radius * 1000)
            properties = self.search_properties_within_radius(center_lat, center_lng, radius_meters)
            
            # Store the results
            all_results[radius] = properties
            
            # Add the results to the table
            for i, prop in enumerate(properties):
                # Get the property data
                property_type = prop.get('property_type', 'Building')
                ruian_id = prop.get('ruian_id', 'Unknown')
                address = prop.get('owner_address', 'Unknown')
                lat = prop.get('lat', '')
                lng = prop.get('lng', '')
                x_sjtsk = prop.get('x_sjtsk', '')
                y_sjtsk = prop.get('y_sjtsk', '')
                url = prop.get('url', '')
                
                # Add the row to the table
                results_table.insert(
                    "", 
                    tk.END, 
                    values=(property_type, ruian_id, address, lat, lng, x_sjtsk, y_sjtsk, url),
                    tags=(str(i),)
                )
            
            # Add a status label
            status_label = ttk.Label(
                radius_frame, 
                text=f"Found {len(properties)} properties within {radius} km radius of {location_name}"
            )
            status_label.pack(side=tk.BOTTOM, padx=5, pady=5)
        
        # Create a frame for buttons
        button_frame = ttk.Frame(table_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Add export button
        export_button = ttk.Button(
            button_frame, 
            text="Export All Results", 
            command=lambda: self.export_all_results(all_results, location_name)
        )
        export_button.pack(side=tk.LEFT, padx=5)
        
        # Add open URL button
        open_url_button = ttk.Button(
            button_frame, 
            text="Open Selected URL", 
            command=lambda: self.open_selected_url(notebook, all_results)
        )
        open_url_button.pack(side=tk.LEFT, padx=5)
        
        # Run the main loop for a short time to process events
        self.app.root.after(2000, table_window.destroy)  # Close after 2 seconds for testing
        self.app.root.mainloop()
        
        # Verify that we got results for each radius
        for radius in self.test_radiuses:
            self.assertIn(radius, all_results, f"Should have results for radius {radius}")
            self.assertTrue(len(all_results[radius]) > 0, f"Should have at least one result for radius {radius}")
        
        logger.info("Radius search with table display test completed successfully")
    
    def search_properties_within_radius(self, center_lat, center_lng, radius_meters, max_points=10):
        """
        Search for properties within a radius around a center point.
        
        Args:
            center_lat (float): Center latitude in WGS84
            center_lng (float): Center longitude in WGS84
            radius_meters (int): Radius in meters
            max_points (int): Maximum number of points to check
            
        Returns:
            list: List of property data dictionaries
        """
        import math
        
        # Convert radius from meters to degrees (approximate)
        # 1 degree of latitude is approximately 111,000 meters
        radius_lat = radius_meters / 111000
        # 1 degree of longitude varies with latitude
        radius_lng = radius_meters / (111000 * math.cos(math.radians(center_lat)))
        
        # Generate points in a grid around the center
        points = []
        
        # Add the center point first
        points.append({'lat': center_lat, 'lng': center_lng})
        
        # Generate concentric circles of points
        num_circles = min(5, max_points // 2)  # Maximum 5 circles
        
        for circle in range(1, num_circles + 1):
            # Calculate the radius of this circle
            circle_radius_lat = (radius_lat * circle) / num_circles
            circle_radius_lng = (radius_lng * circle) / num_circles
            
            # Calculate the number of points on this circle
            # More points on outer circles
            num_points_on_circle = min(max_points // num_circles, 8 * circle)
            
            for i in range(num_points_on_circle):
                angle = 2 * math.pi * i / num_points_on_circle
                
                point_lat = center_lat + circle_radius_lat * math.sin(angle)
                point_lng = center_lng + circle_radius_lng * math.cos(angle)
                
                points.append({'lat': point_lat, 'lng': point_lng})
                
                # Check if we've reached the maximum number of points
                if len(points) >= max_points:
                    break
            
            if len(points) >= max_points:
                break
        
        # Search for properties at each point
        properties = []
        
        for point in points:
            # Get coordinates
            lat = point['lat']
            lng = point['lng']
            
            # Convert WGS84 to S-JTSK
            x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)
            
            # Try to get property data from CUZK
            property_data = self.cuzk_integration.get_property_by_coordinates(x, y)
            
            if property_data:
                # If we found a single property, convert it to a list
                if isinstance(property_data, dict):
                    property_data = [property_data]
                
                for prop in property_data:
                    # Add coordinates to the property data
                    prop['lat'] = lat
                    prop['lng'] = lng
                    prop['x_sjtsk'] = x
                    prop['y_sjtsk'] = y
                    
                    # Generate the MapaIdentifikace URL
                    prop['url'] = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
                    
                    # Add to the results
                    properties.append(prop)
            else:
                # If no property found, try OSM
                osm_buildings = self.app.osm_integration.find_buildings_by_coordinates(lat, lng, 50)
                
                if osm_buildings:
                    for building in osm_buildings:
                        ruian_ref = building.get('ruian_ref')
                        
                        if ruian_ref:
                            # Create property data dictionary
                            prop = {
                                'ruian_id': ruian_ref,
                                'source': 'openstreetmap',
                                'lat': lat,
                                'lng': lng,
                                'x_sjtsk': x,
                                'y_sjtsk': y,
                                'property_type': building['tags'].get('building', 'Building'),
                                'parcel_number': building['tags'].get('addr:conscriptionnumber', ''),
                                'cadastral_territory': building['tags'].get('addr:city', 'Location'),
                                'owner_name': 'Property Owner (view details on CUZK)',
                                'owner_address': building['tags'].get('addr:street', '') + ' ' + 
                                                building['tags'].get('addr:housenumber', '') or 'Property Address',
                                'url': f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
                            }
                            
                            # Add to the results
                            properties.append(prop)
        
        return properties
    
    def on_result_double_click(self, event, table):
        """Handle double-click events on the results table."""
        # Get the selected item
        selection = table.selection()
        if selection:
            item = selection[0]
            values = table.item(item, "values")
            
            # Get the URL from the values (last column)
            url = values[-1]
            
            # Open the URL in the default browser
            if url:
                logger.info(f"Opening URL: {url}")
                webbrowser.open(url)
    
    def open_selected_url(self, notebook, all_results):
        """Open the URL of the selected property in the current tab."""
        # Get the current tab
        current_tab = notebook.index(notebook.select())
        radius = self.test_radiuses[current_tab]
        
        # Get the table in the current tab
        table = notebook.winfo_children()[current_tab].winfo_children()[0]
        
        # Get the selected item
        selection = table.selection()
        if selection:
            item = selection[0]
            values = table.item(item, "values")
            
            # Get the URL from the values (last column)
            url = values[-1]
            
            # Open the URL in the default browser
            if url:
                logger.info(f"Opening URL: {url}")
                webbrowser.open(url)
    
    def export_all_results(self, all_results, location_name):
        """Export all results to a CSV file."""
        # Create a timestamp for the filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create the filename
        filename = os.path.join(self.temp_dir, f"property_search_{location_name.replace(' ', '_')}_{timestamp}.csv")
        
        logger.info(f"Exporting all results to {filename}")
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            # Create a CSV writer
            writer = csv.writer(csvfile)
            
            # Write the header row
            writer.writerow([
                "Radius (km)", "Property Type", "RUIAN ID", "Address", 
                "Latitude", "Longitude", "X (S-JTSK)", "Y (S-JTSK)", "CUZK URL"
            ])
            
            # Write the data rows for each radius
            for radius, properties in all_results.items():
                for prop in properties:
                    writer.writerow([
                        radius,
                        prop.get('property_type', 'Building'),
                        prop.get('ruian_id', 'Unknown'),
                        prop.get('owner_address', 'Unknown'),
                        prop.get('lat', ''),
                        prop.get('lng', ''),
                        prop.get('x_sjtsk', ''),
                        prop.get('y_sjtsk', ''),
                        prop.get('url', '')
                    ])
        
        logger.info(f"Results exported to {filename}")


if __name__ == '__main__':
    unittest.main()
