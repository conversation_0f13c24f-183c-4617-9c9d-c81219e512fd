"""
Very simple test for OSM integration.

This script tests the OSM integration to verify that it can fetch buildings
from OpenStreetMap and extract RUIAN IDs.
"""

import os
import sys
import requests
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SimpleOSMTest")


def fetch_buildings_from_osm(lat, lng, radius=500):
    """
    Fetch buildings from OpenStreetMap.
    
    Args:
        lat (float): Latitude
        lng (float): Longitude
        radius (int): Search radius in meters
        
    Returns:
        list: List of buildings
    """
    # Create a session
    session = requests.Session()
    
    # Overpass API endpoint
    overpass_url = "https://overpass-api.de/api/interpreter"
    
    # Create a bounding box around the coordinates
    # Convert radius from meters to degrees (approximate)
    radius_deg = radius / 111000  # 1 degree is approximately 111 km
    
    # Create the Overpass query
    overpass_query = f"""
    [out:json];
    (
      way["building"](around:{radius},{lat},{lng});
      relation["building"](around:{radius},{lat},{lng});
    );
    out body;
    >;
    out skel qt;
    """
    
    try:
        # Send the query to Overpass API
        response = session.post(overpass_url, data={"data": overpass_query})
        
        # Check if the request was successful
        if response.status_code != 200:
            logger.error(f"Error fetching data from Overpass API: {response.status_code}")
            return []
        
        # Parse the response
        data = response.json()
        
        # Extract buildings
        buildings = []
        
        # Process ways (most buildings are ways)
        for element in data.get("elements", []):
            if element.get("type") == "way" and "tags" in element and "building" in element.get("tags", {}):
                # Extract building information
                building = {
                    "osm_id": element.get("id"),
                    "osm_type": "way",
                    "tags": element.get("tags", {}),
                }
                
                # Extract name
                if "name" in element.get("tags", {}):
                    building["name"] = element["tags"]["name"]
                
                # Extract address
                address_parts = []
                if "addr:street" in element.get("tags", {}):
                    address_parts.append(element["tags"]["addr:street"])
                if "addr:housenumber" in element.get("tags", {}):
                    address_parts.append(element["tags"]["addr:housenumber"])
                if "addr:city" in element.get("tags", {}):
                    address_parts.append(element["tags"]["addr:city"])
                
                if address_parts:
                    building["address"] = " ".join(address_parts)
                
                # Extract RUIAN reference
                if "ref:ruian" in element.get("tags", {}):
                    building["ruian_ref"] = element["tags"]["ref:ruian"]
                elif "ref:ruian:building" in element.get("tags", {}):
                    building["ruian_ref"] = element["tags"]["ref:ruian:building"]
                
                # Add the building to the list
                buildings.append(building)
        
        return buildings
        
    except Exception as e:
        logger.error(f"Error fetching buildings from OSM: {e}")
        return []


def main():
    """Main function."""
    try:
        # Coordinates for testing (Prague)
        test_lat = 50.0755
        test_lng = 14.4378
        test_radius = 500  # meters
        
        # Test fetching buildings from OSM
        logger.info(f"Testing OSM building fetch at coordinates {test_lat}, {test_lng}")
        buildings = fetch_buildings_from_osm(test_lat, test_lng, test_radius)
        
        # Verify that buildings were found
        if not buildings:
            logger.error("No buildings returned from OSM")
            return
        
        logger.info(f"Found {len(buildings)} buildings from OSM")
        
        # Check if any buildings have RUIAN references
        buildings_with_ruian = [b for b in buildings if 'ruian_ref' in b]
        logger.info(f"Found {len(buildings_with_ruian)} buildings with RUIAN references")
        
        # Print details of buildings with RUIAN references
        for i, building in enumerate(buildings_with_ruian[:5]):  # Show first 5 buildings
            logger.info(f"Building {i+1}:")
            logger.info(f"  Name: {building.get('name', 'Unknown')}")
            logger.info(f"  Address: {building.get('address', 'Unknown')}")
            logger.info(f"  RUIAN ID: {building.get('ruian_ref', 'Unknown')}")
            logger.info(f"  OSM ID: {building.get('osm_id', 'Unknown')}")
            logger.info(f"  Type: {building.get('osm_type', 'Unknown')}")
            logger.info("  Tags:")
            for key, value in building.get('tags', {}).items():
                logger.info(f"    {key}: {value}")
            logger.info("")
        
        # Test successful
        logger.info("OSM test completed successfully")
        
        # Ask if the user wants to open the CUZK website for a building
        if buildings_with_ruian:
            building = buildings_with_ruian[0]
            ruian_id = building['ruian_ref']
            
            open_browser = input(f"Open CUZK website with RUIAN ID {ruian_id}? (y/n): ")
            
            if open_browser.lower() == 'y':
                import webbrowser
                # Open the CUZK website with the RUIAN ID
                url = f"https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id={ruian_id}"
                logger.info(f"Opening URL: {url}")
                webbrowser.open(url)
                
                # Wait for the user to confirm
                input("Press Enter after the CUZK website has opened...")
                
                logger.info(f"Successfully opened CUZK with RUIAN ID: {ruian_id}")
            else:
                logger.info("Skipping browser open test")
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)


if __name__ == "__main__":
    main()
