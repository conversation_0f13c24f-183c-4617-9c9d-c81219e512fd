"""
Test for city boundaries and coordinate generation.

This test checks if we can get city boundaries for Mariánské Lázně and generate coordinates within them.
"""

import os
import sys
import json
import logging
import configparser

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from api.cuzk.cuzk_integration import CUZKIntegration
from api.google_maps.city_boundaries import get_city_boundaries

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('city_boundaries_test.log', mode='w')
    ]
)

logger = logging.getLogger("CityBoundariesTest")

def load_api_key():
    """Load Google Maps API key from config file"""
    config = configparser.ConfigParser()
    if os.path.exists('config.ini'):
        config.read('config.ini')
        if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
            return config['GOOGLE_MAPS']['api_key']
    return None

def generate_coordinates_in_city(boundaries, density=None):
    """Generate coordinates within the city boundaries"""
    # Extract boundaries
    north = boundaries['north']
    south = boundaries['south']
    east = boundaries['east']
    west = boundaries['west']

    # Calculate area dimensions
    width = east - west
    height = north - south
    area = width * height

    logger.info(f"City area dimensions: Width={width:.6f}, Height={height:.6f}, Area={area:.6f}")

    # Calculate adaptive density based on city size
    # Larger cities should have more points, not fewer
    # We'll use a base density and scale it by the area
    if density is None:
        # Base density for a medium-sized city
        base_density = 100

        # Scale factor based on area
        # For reference: Prague is about 0.11 in area units
        # Mariánské Lázně is about 0.015 in area units
        if area < 0.01:  # Very small city
            # For small cities, we want points closer together
            # but not too many total points
            density = int(base_density * 1.5)
            logger.info(f"Using density {density} for small city (area: {area:.6f})")
        elif area < 0.05:  # Small to medium city
            density = int(base_density * 2)
            logger.info(f"Using density {density} for medium city (area: {area:.6f})")
        else:  # Large city
            # For large cities, we want more total points
            # Scale based on area compared to a reference city (Prague)
            scale = (area / 0.11) * 2.5
            density = int(base_density * scale)
            # Cap at a reasonable maximum
            density = min(density, 500)
            logger.info(f"Using density {density} for large city (area: {area:.6f})")

    # Calculate step sizes
    lat_step = height / density
    lng_step = width / density

    # Generate coordinates
    coordinates = []
    for i in range(density + 1):
        for j in range(density + 1):
            lat = south + i * lat_step
            lng = west + j * lng_step
            coordinates.append({'lat': lat, 'lng': lng})

    logger.info(f"Generated {len(coordinates)} coordinates")
    return coordinates

def convert_coordinates_to_urls(coordinates, cuzk_integration):
    """Convert coordinates to S-JTSK and create CUZK URLs"""
    logger.info(f"Converting {len(coordinates)} coordinates and creating CUZK URLs")

    results = []
    for coord in coordinates:
        lat = coord['lat']
        lng = coord['lng']

        # Convert WGS84 to S-JTSK
        x, y = cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)

        # Ensure coordinates are integers
        x_int = int(x)
        y_int = int(y)

        # Generate CUZK URL
        url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"

        # Create result dictionary
        result = {
            'lat': lat,
            'lng': lng,
            'x_sjtsk': x_int,
            'y_sjtsk': y_int,
            'url': url
        }

        results.append(result)

    logger.info(f"Created {len(results)} CUZK URLs")
    return results

def test_city_boundaries(city_name):
    """Test getting city boundaries and generating coordinates"""
    logger.info(f"Testing city boundaries for {city_name}")

    # Initialize Google Maps integration
    google_maps = GoogleMapsIntegration()

    # Initialize CUZK integration
    cuzk_integration = CUZKIntegration()

    # Get city boundaries
    boundaries = get_city_boundaries(city_name, "Czech Republic", google_maps)

    if not boundaries:
        logger.error(f"Could not get boundaries for {city_name}")
        return False

    logger.info(f"Boundaries for {city_name}: {boundaries}")

    # Generate coordinates within the city using adaptive density
    coordinates = generate_coordinates_in_city(boundaries)

    if not coordinates:
        logger.error(f"Could not generate coordinates for {city_name}")
        return False

    logger.info(f"Generated {len(coordinates)} coordinates for {city_name}")

    # Convert coordinates to URLs
    results = convert_coordinates_to_urls(coordinates, cuzk_integration)

    if not results:
        logger.error(f"Could not convert coordinates to URLs for {city_name}")
        return False

    logger.info(f"Generated {len(results)} URLs for {city_name}")

    # Save the results to a file for analysis
    with open(f'{city_name.lower().replace(" ", "_")}_urls.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)

    logger.info(f"Results saved to {city_name.lower().replace(' ', '_')}_urls.json")

    # Print some sample results
    logger.info("Sample results:")
    for i, result in enumerate(results[:5]):
        logger.info(f"  {i+1}. WGS84({result['lat']}, {result['lng']}) -> S-JTSK({result['x_sjtsk']}, {result['y_sjtsk']})")
        logger.info(f"     URL: {result['url']}")

    return True

if __name__ == "__main__":
    # Test Mariánské Lázně
    success = test_city_boundaries("Mariánské Lázně")

    # Test Prague for comparison
    test_city_boundaries("Prague")

    sys.exit(0 if success else 1)
