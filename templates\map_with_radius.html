<!DOCTYPE html>
<html>
<head>
    <style>
        body { margin: 0; padding: 0; height: 100%; }
        #map { width: 100%; height: 100%; }
    </style>
</head>
<body>
    <div id="map"></div>
    <script>
        function initMap() {
            var center = {lat: {lat}, lng: {lng}};
            var map = new google.maps.Map(document.getElementById('map'), {
                zoom: 14,
                center: center
            });

            // Add a marker at the center
            var marker = new google.maps.Marker({
                position: center,
                map: map,
                title: 'Selected Location'
            });

            // Add a circle with the specified radius
            var circle = new google.maps.Circle({
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.1,
                map: map,
                center: center,
                radius: {radius}
            });
        }
    </script>
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={api_key}&callback=initMap">
    </script>
</body>
</html>
