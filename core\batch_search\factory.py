"""
Factory for creating batch search managers for the Czech Property Registry application.

This module provides a factory for creating the appropriate batch search manager
based on the application's configuration and requirements.
"""

import logging
from typing import Dict, Any, Optional

from core.batch_search.batch_search_manager_base import BatchSearchManagerBase
from core.batch_search.batch_search_manager_impl import BatchSearchManagerImpl

# Configure logging
logger = logging.getLogger(__name__)


class BatchSearchManagerFactory:
    """
    Factory for creating batch search managers.
    
    Provides methods for creating the appropriate batch search manager
    based on the application's configuration and requirements.
    """
    
    @staticmethod
    def create_batch_search_manager(app, manager_type: str = "default") -> BatchSearchManagerBase:
        """
        Create a batch search manager.
        
        Args:
            app: The main application instance
            manager_type (str): The type of batch search manager to create
            
        Returns:
            BatchSearchManagerBase: The created batch search manager
        """
        logger.info(f"Creating batch search manager of type: {manager_type}")
        
        if manager_type == "default":
            return BatchSearchManagerImpl(app)
        elif manager_type == "optimized":
            # Import here to avoid circular imports
            from core.batch_search.optimized_batch_search_manager import OptimizedBatchSearchManager
            return OptimizedBatchSearchManager(app)
        elif manager_type == "smart":
            # Import here to avoid circular imports
            from core.batch_search.smart_batch_search_manager import SmartBatchSearchManager
            return SmartBatchSearchManager(app)
        elif manager_type == "real":
            # Import here to avoid circular imports
            from core.batch_search.real_batch_search_manager import RealBatchSearchManager
            return RealBatchSearchManager(app)
        else:
            logger.warning(f"Unknown batch search manager type: {manager_type}, using default")
            return BatchSearchManagerImpl(app)
    
    @staticmethod
    def create_batch_search_managers(app, config: Optional[Dict[str, Any]] = None) -> Dict[str, BatchSearchManagerBase]:
        """
        Create all required batch search managers.
        
        Args:
            app: The main application instance
            config (dict, optional): Configuration for the batch search managers
            
        Returns:
            dict: Dictionary of batch search managers
        """
        logger.info("Creating batch search managers")
        
        # Use default configuration if not provided
        if config is None:
            config = {
                "default": True,
                "optimized": False,
                "smart": False,
                "real": False
            }
            
        # Create the batch search managers
        managers = {}
        
        if config.get("default", True):
            managers["default"] = BatchSearchManagerFactory.create_batch_search_manager(app, "default")
            
        if config.get("optimized", False):
            managers["optimized"] = BatchSearchManagerFactory.create_batch_search_manager(app, "optimized")
            
        if config.get("smart", False):
            managers["smart"] = BatchSearchManagerFactory.create_batch_search_manager(app, "smart")
            
        if config.get("real", False):
            managers["real"] = BatchSearchManagerFactory.create_batch_search_manager(app, "real")
            
        logger.info(f"Created {len(managers)} batch search managers: {', '.join(managers.keys())}")
        
        return managers
