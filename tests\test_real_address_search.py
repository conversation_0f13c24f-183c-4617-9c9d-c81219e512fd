"""
Test for real address search and CUZK URL generation.

This test uses the Google Maps API to get real addresses, then searches for properties
within a radius around those addresses, checks with RUIAN, and generates CUZK URLs.
"""

import os
import sys
import unittest
import logging
import random
import webbrowser
import tkinter as tk
from unittest.mock import MagicMock, patch

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RealAddressSearchTest")

# Import required modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from api.cuzk.cuzk_integration import CUZKIntegration
from api.osm.osm_integration import OSMIntegration
from ui.message_boxes import MessageBoxes


class TestRealAddressSearch(unittest.TestCase):
    """Tests for real address search and CUZK URL generation."""

    def setUp(self):
        """Initialize test components."""
        # Create a mock app
        self.app = MagicMock()
        self.app.root = MagicMock(spec=tk.Tk)

        # Make callbacks execute immediately
        self.app.root.after.side_effect = lambda delay, callback, *args: callback(*args) if args else callback()

        # Initialize real components
        self.google_maps = GoogleMapsIntegration()
        self.cuzk_integration = CUZKIntegration()

        # Attach real components to the mock app
        self.app.google_maps = self.google_maps
        self.app.cuzk_integration = self.cuzk_integration

        # Create a mock OSM integration
        self.app.osm_integration = MagicMock()
        # Add the find_buildings_by_coordinates method to the mock
        self.app.osm_integration.find_buildings_by_coordinates = MagicMock(return_value=[
            {
                'type': 'way',
                'id': '123456',
                'lat': 50.0755,
                'lon': 14.4378,
                'tags': {
                    'building': 'residential',
                    'ref:ruian': '87654321',
                    'addr:street': 'Test Street',
                    'addr:housenumber': '123',
                    'addr:city': 'Prague'
                },
                'ruian_ref': '87654321'
            }
        ])

        # Czech cities for testing
        self.czech_cities = [
            "Prague", "Brno", "Ostrava", "Plzeň", "Liberec",
            "Olomouc", "České Budějovice", "Hradec Králové", "Ústí nad Labem", "Pardubice"
        ]

    @patch('webbrowser.open')
    def test_real_address_search(self, mock_webbrowser_open):
        """Test searching for properties around a real address from Google Maps."""
        logger.info("Testing real address search")

        # Skip test if Google Maps API is not available
        try:
            # Test if geocoding works with a simple query
            test_result = self.google_maps.geocode("Prague")
            if not test_result or 'lat' not in test_result:
                self.skipTest("Google Maps API not available or not working")
        except Exception as e:
            self.skipTest(f"Google Maps API error: {e}")

        # Choose a random Czech city
        city = random.choice(self.czech_cities)
        logger.info(f"Using city: {city}")

        # Get coordinates for the city
        geocode_result = self.google_maps.geocode(f"{city}, Czech Republic")
        self.assertIsNotNone(geocode_result, "Geocoding should return a result")
        self.assertIn('lat', geocode_result, "Geocoding result should include latitude")
        self.assertIn('lng', geocode_result, "Geocoding result should include longitude")

        # Extract coordinates
        lat = geocode_result['lat']
        lng = geocode_result['lng']
        logger.info(f"Geocoded {city} to coordinates: {lat}, {lng}")

        # Convert coordinates to S-JTSK
        x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)
        logger.info(f"Converted to S-JTSK: x={x}, y={y}")

        # Generate a MapaIdentifikace URL
        url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
        logger.info(f"Generated URL: {url}")

        # Verify URL format
        self.assertTrue(url.startswith("http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"),
                       "URL should be a MapaIdentifikace URL")
        self.assertIn("l=KN", url, "URL should include the layer parameter")
        self.assertIn(f"x={x}", url, "URL should include the x coordinate")
        self.assertIn(f"y={y}", url, "URL should include the y coordinate")

        # Open the URL (mocked)
        webbrowser.open(url)
        mock_webbrowser_open.assert_called_with(url)

        logger.info("Real address search test completed successfully")

    @patch('webbrowser.open')
    def test_multiple_points_around_address(self, mock_webbrowser_open):
        """Test generating multiple points around a real address and checking RUIAN data."""
        logger.info("Testing multiple points around a real address")

        # Skip test if Google Maps API is not available
        try:
            # Test if geocoding works with a simple query
            test_result = self.google_maps.geocode("Prague")
            if not test_result or 'lat' not in test_result:
                self.skipTest("Google Maps API not available or not working")
        except Exception as e:
            self.skipTest(f"Google Maps API error: {e}")

        # Choose a random Czech city
        city = random.choice(self.czech_cities)
        logger.info(f"Using city: {city}")

        # Get coordinates for the city
        geocode_result = self.google_maps.geocode(f"{city}, Czech Republic")
        self.assertIsNotNone(geocode_result, "Geocoding should return a result")

        # Extract coordinates
        center_lat = geocode_result['lat']
        center_lng = geocode_result['lng']
        logger.info(f"Geocoded {city} to coordinates: {center_lat}, {center_lng}")

        # Generate points in a grid around the center
        radius_meters = 500  # 500 meters radius
        points = self._generate_grid_points(center_lat, center_lng, radius_meters, 5)
        logger.info(f"Generated {len(points)} points around {city}")

        # Check each point with RUIAN and generate URLs
        urls = []
        for point in points:
            # Convert coordinates to S-JTSK
            x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(point['lat'], point['lng'])

            # Generate URL
            url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
            urls.append(url)

            # Verify URL format
            self.assertTrue(url.startswith("http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"),
                           "URL should be a MapaIdentifikace URL")

        # Open the first URL (mocked)
        if urls:
            webbrowser.open(urls[0])
            mock_webbrowser_open.assert_called_with(urls[0])

        logger.info(f"Generated {len(urls)} URLs for points around {city}")
        logger.info("Multiple points test completed successfully")

    def _generate_grid_points(self, center_lat, center_lng, radius, num_points):
        """Generate a grid of points within a radius around a center point."""
        import math

        # Convert radius from meters to degrees (approximate)
        # 1 degree of latitude is approximately 111,000 meters
        radius_lat = radius / 111000
        # 1 degree of longitude varies with latitude
        radius_lng = radius / (111000 * math.cos(math.radians(center_lat)))

        points = []

        # Add the center point first
        points.append({'lat': center_lat, 'lng': center_lng})

        # Generate points in a grid
        if num_points > 1:
            # Generate points in a circle around the center
            for i in range(num_points - 1):
                angle = 2 * math.pi * i / (num_points - 1)
                point_lat = center_lat + radius_lat * math.sin(angle)
                point_lng = center_lng + radius_lng * math.cos(angle)
                points.append({'lat': point_lat, 'lng': point_lng})

        return points


if __name__ == '__main__':
    unittest.main()
