"""
Map view components for the Czech Property Registry application.

This module provides map display functionality with multiple rendering options:
- Embedded maps using tkinterweb (if available)
- Static map images using PIL (if available)
- External browser-based maps (fallback)

Maps can display:
- Single locations with markers
- Multiple buildings with info windows
- Areas with radius circles for property searches
"""

import tkinter as tk  # Used for Toplevel windows
from tkinter import ttk
import webbrowser
import tempfile
import os
import json  # For loading JSON files

from utils.template_loader import TemplateLoader
# No fallback data is used - removed DemoData import

# Try to import tkinterweb for embedded browser
try:
    from tkinterweb import HtmlFrame
    TKINTERWEB_AVAILABLE = True
except ImportError:
    TKINTERWEB_AVAILABLE = False
    print("tkinterweb not available. Will use external browser for maps.")

# For map display without tkinterweb
try:
    from PIL import Image, ImageTk
    import io
    import urllib.request
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


class MapView:
    """
    A class for displaying maps in the application.

    Provides methods for showing maps with different display options:
    - Embedded maps within the application window
    - External maps in the default web browser
    - Static map images when dynamic maps aren't available

    Supports multiple map types including single markers, building collections,
    and radius-based search areas.
    """

    def __init__(self, app_or_frame):
        """
        Initialize the map view.

        Detects whether it's being initialized with the main app or just a frame,
        and sets up the appropriate container for the map display.
        Creates fallback UI elements for when maps can't be displayed.

        Args:
            app_or_frame: Either the main application instance or a frame to use as the map container
        """
        # Check if the argument is a frame or an app
        if hasattr(app_or_frame, 'winfo_class'):
            # This is a frame
            self.app = None
            self.map_frame = app_or_frame
        else:
            # This is the app
            self.app = app_or_frame
            self.map_frame = app_or_frame.map_frame

        self.template_loader = TemplateLoader()

        # Initialize the map display
        self.html_frame = None
        self.map_image_label = None
        self.current_lat = None
        self.current_lng = None

        # Create a label for when no map is available, but don't display it yet
        self.no_map_label = ttk.Label(
            self.map_frame,
            text="Map not available. Click 'Show on Map' to view in browser.",
            anchor="center"
        )

        # Create a button to open the map in browser, but don't display it yet
        self.open_browser_button = ttk.Button(
            self.map_frame,
            text="Open in Browser",
            command=self.open_in_browser
        )

        # Show a map of Prague by default if we have the capability
        if TKINTERWEB_AVAILABLE or PIL_AVAILABLE:
            # Default coordinates for Prague
            self.show_map(50.0755, 14.4378, zoom=12)
        else:
            # Only show the message if we can't display maps
            self.no_map_label.pack(fill="both", expand=True, padx=10, pady=10)
            self.open_browser_button.pack(side="bottom", padx=5, pady=5)

    def show_map(self, lat, lng, zoom=15):
        """
        Show a map at the specified coordinates.

        Attempts to display a map using the best available method:
        1. Embedded map using tkinterweb if available
        2. Static map image using PIL if available
        3. Fallback to showing instructions for external browser

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            zoom (int): Zoom level (1-20, higher is more zoomed in)
        """
        self.current_lat = lat
        self.current_lng = lng

        # If tkinterweb is available, use it
        if TKINTERWEB_AVAILABLE:
            self._show_map_in_tkinterweb(lat, lng, zoom)
        # If PIL is available, use static map image
        elif PIL_AVAILABLE:
            self._show_static_map(lat, lng, zoom)
        # Otherwise, just show the no map label
        else:
            self.no_map_label.pack(fill="both", expand=True, padx=10, pady=10)
            self.open_browser_button.pack(side="bottom", padx=5, pady=5)

    def _show_map_in_tkinterweb(self, lat, lng, zoom):
        """
        Show a map using tkinterweb embedded browser.

        Creates an HTML frame and loads a Google Maps static image.
        Uses the application's Google Maps API key if available.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            zoom (int): Zoom level
        """
        # Clear the map frame
        for widget in self.map_frame.winfo_children():
            widget.pack_forget()

        # Create the HTML frame if it doesn't exist
        if not self.html_frame:
            self.html_frame = HtmlFrame(self.map_frame)

        # Pack the HTML frame
        self.html_frame.pack(fill="both", expand=True)

        # Get the API key
        api_key = ''
        if self.app and hasattr(self.app, 'google_maps'):
            api_key = getattr(self.app.google_maps, 'api_key', '')

        # Create the HTML for the map using the template
        html = self.template_loader.format_template(
            'static_map',
            lat=lat,
            lng=lng,
            static_map_url=f"https://maps.googleapis.com/maps/api/staticmap?center={lat},{lng}&zoom={zoom}&size=800x700&maptype=roadmap&markers=color:red%7C{lat},{lng}&key={api_key}"
        )

        # Load the HTML
        self.html_frame.load_html(html)

    def _show_static_map(self, lat, lng, zoom):
        """
        Show a static map image using PIL.

        Downloads a static map image from Google Maps API and
        displays it in a label. Used when tkinterweb is not available.

        Args:
            lat (float): Latitude coordinate
            lng (float): Longitude coordinate
            zoom (int): Zoom level
        """
        # Clear the map frame
        for widget in self.map_frame.winfo_children():
            widget.pack_forget()

        # Try to get a static map image
        try:
            # Construct the URL for a static map
            url = f"https://maps.googleapis.com/maps/api/staticmap?center={lat},{lng}&zoom={zoom}&size=600x400&markers=color:red%7C{lat},{lng}"

            # Download the image
            with urllib.request.urlopen(url) as response:
                image_data = response.read()

            # Convert the image data to a PIL image
            image = Image.open(io.BytesIO(image_data))

            # Convert the PIL image to a Tkinter image
            tk_image = ImageTk.PhotoImage(image)

            # Create a label to display the image
            if not self.map_image_label:
                self.map_image_label = ttk.Label(self.map_frame)

            # Update the image
            self.map_image_label.configure(image=tk_image)
            self.map_image_label.image = tk_image  # Keep a reference

            # Pack the label
            self.map_image_label.pack(fill="both", expand=True)

            # Pack the button
            self.open_browser_button.pack(side="bottom", padx=5, pady=5)
        except Exception as e:
            print(f"Error showing static map: {e}")
            # Show the no map label
            self.no_map_label.pack(fill="both", expand=True, padx=10, pady=10)
            self.open_browser_button.pack(side="bottom", padx=5, pady=5)

    def open_in_browser(self):
        """
        Open the map in the default web browser.

        Uses the current coordinates if available, otherwise
        shows a map of the Czech Republic using coordinates
        using default coordinates for the Czech Republic.
        """
        if self.current_lat and self.current_lng:
            url = f"https://www.google.com/maps?q={self.current_lat},{self.current_lng}"
            webbrowser.open(url)
        else:
            # Use default Czech Republic coordinates without fallback data
            lat = 49.8175
            lng = 15.4730
            url = f"https://www.google.com/maps?q={lat},{lng}"
            webbrowser.open(url)

    def show_location(self, location):
        """
        Show a location on the map.

        Geocodes the location using Google Maps API and displays
        the resulting coordinates on the map.

        Args:
            location (str): The location to show on the map
        """
        # Check if we have access to Google Maps API
        if hasattr(self.app, 'google_maps'):
            # Geocode the location to get coordinates
            result = self.app.google_maps.geocode(location)
            if result:
                lat = result.get('lat')
                lng = result.get('lng')
                # Show the map with the coordinates
                self.show_map(lat, lng, zoom=15)
            else:
                # If geocoding failed, show an error message
                print(f"Could not find coordinates for {location}")
        else:
            # If Google Maps integration is not available, show an error message
            print("Google Maps integration is not available")

    def create_html_map(self, lat, lng, zoom=15, markers=None):
        """
        Create an HTML file with a map and return the path.

        Generates a temporary HTML file with a Google Map centered
        at the specified coordinates. Can include markers if provided.
        Used for creating maps that will be opened in external browser.

        Args:
            lat (float): Latitude coordinate for map center
            lng (float): Longitude coordinate for map center
            zoom (int): Zoom level (1-20, higher is more zoomed in)
            markers (list): Optional list of marker objects with lat, lng, and title

        Returns:
            str: Path to the temporary HTML file
        """
        # Create a temporary file
        fd, path = tempfile.mkstemp(suffix=".html")

        # Get the API key
        api_key = ''
        if self.app and hasattr(self.app, 'google_maps'):
            api_key = getattr(self.app.google_maps, 'api_key', '')

        # Prepare markers data for the template if provided
        buildings_data = []
        if markers:
            for marker in markers:
                buildings_data.append({
                    'lat': marker['lat'],
                    'lng': marker['lng'],
                    'name': marker.get('title', 'Marker').replace("'", "\\'"),
                    'info_content': f"<div>{marker.get('title', 'Marker')}</div>"
                })

        # Create the HTML for the map using the template
        if markers:
            html = self.template_loader.format_template(
                'map_with_buildings',
                center_lat=lat,
                center_lng=lng,
                buildings_json=buildings_data,
                api_key=api_key
            )
        else:
            html = self.template_loader.format_template(
                'static_map',
                lat=lat,
                lng=lng,
                static_map_url=f"https://maps.googleapis.com/maps/api/staticmap?center={lat},{lng}&zoom={zoom}&size=800x700&maptype=roadmap&markers=color:red%7C{lat},{lng}&key={api_key}"
            )

        # Write the HTML to the file
        with os.fdopen(fd, 'w') as f:
            f.write(html)

        return path

    def show_map_with_radius(self, lat, lng, radius):
        """
        Show a map with a radius circle around a point.

        Creates a map centered on the specified coordinates with
        a circle showing the search radius. Uses embedded browser
        if available, otherwise opens in external browser.

        Args:
            lat (float): Latitude coordinate of center point
            lng (float): Longitude coordinate of center point
            radius (float): Radius in meters to display as a circle
        """
        self.current_lat = lat
        self.current_lng = lng

        # Get the API key
        api_key = ''
        if self.app and hasattr(self.app, 'google_maps'):
            api_key = getattr(self.app.google_maps, 'api_key', '')

        # Create the HTML for the map using the template
        html = self.template_loader.format_template(
            'map_with_radius',
            lat=lat,
            lng=lng,
            radius=radius,
            api_key=api_key
        )

        # If tkinterweb is available, use it
        if TKINTERWEB_AVAILABLE:
            # Clear the map frame
            for widget in self.map_frame.winfo_children():
                widget.pack_forget()

            # Create the HTML frame if it doesn't exist
            if not self.html_frame:
                self.html_frame = HtmlFrame(self.map_frame)

            # Pack the HTML frame
            self.html_frame.pack(fill="both", expand=True)

            # Load the HTML
            self.html_frame.load_html(html)
        else:
            # Create a temporary HTML file
            fd, path = tempfile.mkstemp(suffix=".html")
            with os.fdopen(fd, 'w') as f:
                f.write(html)

            # Open the HTML file in the default browser
            webbrowser.open('file://' + path)

    def show_buildings_on_map(self, buildings, center_lat, center_lng):
        """
        Show multiple buildings on the map with info windows.

        Creates a map centered on the specified coordinates with
        markers for each building. Each marker has an info window
        with building details. Handles different OSM node types.

        Args:
            buildings (list): List of building dictionaries from OSM
            center_lat (float): Latitude coordinate for map center
            center_lng (float): Longitude coordinate for map center
        """
        self.current_lat = center_lat
        self.current_lng = center_lng

        # Get the API key
        api_key = ''
        if self.app and hasattr(self.app, 'google_maps'):
            api_key = getattr(self.app.google_maps, 'api_key', '')

        # Prepare buildings data for the template
        buildings_data = []
        for building in buildings:
            # Get building coordinates
            if building.get('type') == 'node':
                lat = building.get('lat')
                lng = building.get('lon')
            elif 'center' in building:
                lat = building.get('center', {}).get('lat')
                lng = building.get('center', {}).get('lon')
            else:
                # Skip buildings without coordinates
                continue

            # Get building name and type
            name = building['tags'].get('name', 'Building')
            building_type = building['tags'].get('building', 'Unknown')
            ruian_id = building.get('ruian_ref', building['tags'].get('ref:ruian', 'Unknown'))

            # Create info window content
            info_content = self.template_loader.format_template(
                'building_info_window',
                name=name.replace("'", "\\'"),
                ruian_id=ruian_id,
                building_type=building_type
            )

            # Add building to the list
            buildings_data.append({
                'lat': lat,
                'lng': lng,
                'name': name.replace("'", "\\'"),
                'info_content': info_content
            })

        # Create the HTML for the map using the template
        html = self.template_loader.format_template(
            'map_with_buildings',
            center_lat=center_lat,
            center_lng=center_lng,
            buildings_json=buildings_data,
            api_key=api_key
        )

        # If tkinterweb is available, use it
        if TKINTERWEB_AVAILABLE:
            # Clear the map frame
            for widget in self.map_frame.winfo_children():
                widget.pack_forget()

            # Create the HTML frame if it doesn't exist
            if not self.html_frame:
                self.html_frame = HtmlFrame(self.map_frame)

            # Pack the HTML frame
            self.html_frame.pack(fill="both", expand=True)

            # Load the HTML
            self.html_frame.load_html(html)
        else:
            # Create a temporary HTML file
            fd, path = tempfile.mkstemp(suffix=".html")
            with os.fdopen(fd, 'w') as f:
                f.write(html)

            # Open the HTML file in the default browser
            webbrowser.open('file://' + path)
