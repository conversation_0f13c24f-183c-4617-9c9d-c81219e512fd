#!/usr/bin/env python3
"""
Test script for Google Maps API key validation.
This script tests if the Google Maps API key is valid and if the required APIs are enabled.
"""

import requests
import configparser
import sys
import json

def load_api_key():
    """Load Google Maps API key from config file"""
    config = configparser.ConfigParser()
    try:
        config.read('config.ini')
        if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
            api_key = config['GOOGLE_MAPS']['api_key']
            print(f"API key found: {api_key[:5]}..." if api_key else "API key is empty")
            return api_key
        else:
            print("API key not found in config.ini")
            return None
    except Exception as e:
        print(f"Error loading API key: {e}")
        return None

def test_geocoding_api(api_key):
    """Test the Geocoding API"""
    print("\n=== Testing Geocoding API ===")
    url = f"https://maps.googleapis.com/maps/api/geocode/json?address=Prague&key={api_key}"
    
    try:
        print(f"Making request to: {url}")
        response = requests.get(url, timeout=10)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response status: {data.get('status', 'Unknown')}")
            
            if data.get('status') == 'OK':
                print("Geocoding API is working correctly!")
                result = data.get('results', [])[0]
                print(f"Found: {result.get('formatted_address')}")
                return True
            else:
                print(f"Error: {data.get('error_message', 'Unknown error')}")
                print(f"Full response: {json.dumps(data, indent=2)}")
                return False
        else:
            print(f"HTTP error: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
    except Exception as e:
        print(f"Exception: {e}")
        return False

def test_places_api(api_key):
    """Test the Places API"""
    print("\n=== Testing Places API ===")
    url = f"https://maps.googleapis.com/maps/api/place/autocomplete/json?input=Prague&key={api_key}"
    
    try:
        print(f"Making request to: {url}")
        response = requests.get(url, timeout=10)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response status: {data.get('status', 'Unknown')}")
            
            if data.get('status') == 'OK':
                print("Places API is working correctly!")
                predictions = data.get('predictions', [])
                if predictions:
                    print(f"Found {len(predictions)} predictions")
                    print(f"First prediction: {predictions[0].get('description')}")
                return True
            else:
                print(f"Error: {data.get('error_message', 'Unknown error')}")
                print(f"Full response: {json.dumps(data, indent=2)}")
                return False
        else:
            print(f"HTTP error: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
    except Exception as e:
        print(f"Exception: {e}")
        return False

def test_maps_javascript_api(api_key):
    """Test the Maps JavaScript API"""
    print("\n=== Testing Maps JavaScript API ===")
    # This is a bit harder to test directly, but we can check if the API key is authorized for Maps JavaScript API
    url = f"https://maps.googleapis.com/maps/api/js?key={api_key}&callback=initMap"
    
    try:
        print(f"Making request to: {url}")
        response = requests.get(url, timeout=10)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            # If we get a 200 response, the API key is likely authorized for Maps JavaScript API
            print("Maps JavaScript API appears to be working correctly!")
            # Don't print the full response as it's a large JavaScript file
            print(f"Response size: {len(response.text)} bytes")
            return True
        else:
            print(f"HTTP error: {response.status_code}")
            # Print just the beginning of the response to see error messages
            print(f"Response content (first 500 chars): {response.text[:500]}")
            return False
    except Exception as e:
        print(f"Exception: {e}")
        return False

def main():
    """Main function"""
    print("Google Maps API Key Tester")
    print("=========================")
    
    api_key = load_api_key()
    if not api_key:
        print("No API key found. Please check your config.ini file.")
        sys.exit(1)
    
    # Test each API
    geocoding_ok = test_geocoding_api(api_key)
    places_ok = test_places_api(api_key)
    maps_js_ok = test_maps_javascript_api(api_key)
    
    # Summary
    print("\n=== Summary ===")
    print(f"Geocoding API: {'OK' if geocoding_ok else 'FAILED'}")
    print(f"Places API: {'OK' if places_ok else 'FAILED'}")
    print(f"Maps JavaScript API: {'OK' if maps_js_ok else 'FAILED'}")
    
    if geocoding_ok and places_ok and maps_js_ok:
        print("\nAll APIs are working correctly!")
    else:
        print("\nSome APIs are not working. Please check the Google Cloud Console to ensure they are enabled.")
        print("Go to: https://console.cloud.google.com/apis/dashboard")
        print("Make sure the following APIs are enabled:")
        print("1. Geocoding API")
        print("2. Places API")
        print("3. Maps JavaScript API")
        print("\nAlso check if there are any API restrictions (domain, IP, etc.) that might be blocking the requests.")

if __name__ == "__main__":
    main()
