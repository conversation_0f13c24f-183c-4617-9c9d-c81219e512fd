# Usage Guide

This guide explains how to use the Czech Property Registry application.

## Starting the Application

Run the application using Python:

```bash
python main.py
```

## Main Interface

The main interface consists of several tabs for different search methods:

1. **Address Search**: Search by address (city, street, number)
2. **Location Search**: Search by location name
3. **Coordinate Search**: Search by coordinates
4. **Batch Search**: Search for multiple properties at once

### Address Search

The Address Search tab allows you to search for properties by address:

1. Enter the city name in the "City" field
2. Enter the street name in the "Street" field
3. Enter the building number in the "Number" field
4. Click the "Search" button

The application will search for properties at the specified address and display the results.

### Location Search

The Location Search tab allows you to search for properties by location name:

1. Enter the location name in the "Location" field
2. Click the "Search" button

The application will search for properties in the specified location and display the results.

### Coordinate Search

The Coordinate Search tab allows you to search for properties by coordinates:

1. Enter the latitude in the "Latitude" field
2. Enter the longitude in the "Longitude" field
3. Click the "Search" button

The application will search for properties at the specified coordinates and display the results.

### Batch Search

The Batch Search tab allows you to search for multiple properties at once:

1. Select a region from the "Region" dropdown
2. Select a city from the "City" dropdown
3. Click the "Search" button to find properties in the selected city
4. Select the properties you want to include in the batch
5. Click the "Get Owner Info" button to retrieve owner information for the selected properties

## Property Results

When you search for properties, the application displays a list of properties matching your search criteria. You can:

1. Click on a property to view its details
2. Select multiple properties for batch processing
3. Click the "Get Owner Info" button to retrieve owner information for the selected properties

## Owner Information

When you retrieve owner information for a property, the application displays:

1. Owner name
2. Owner address
3. Property type
4. Parcel number
5. Cadastral territory

You can use this information to generate letters or for other purposes.

## Map View

The application includes a map view that shows the location of properties:

1. Click on a property in the results list to see its location on the map
2. Use the map controls to zoom in/out and pan
3. Click on a property on the map to view its details

## Generating Letters

To generate a letter for a property owner:

1. Search for a property and retrieve its owner information
2. Click the "Generate Letter" button
3. Select a letter template
4. Enter any additional information required by the template
5. Click the "Generate" button
6. The application will generate a letter addressed to the property owner

## Saving Results

To save search results:

1. Search for properties and retrieve owner information
2. Click the "Save Results" button
3. Select a file format (CSV, Excel, etc.)
4. Choose a location to save the file
5. Click the "Save" button

## Keyboard Shortcuts

The application supports the following keyboard shortcuts:

- **Ctrl+F**: Focus the search field
- **Ctrl+S**: Save results
- **Ctrl+P**: Print results
- **Ctrl+Q**: Quit the application
- **F1**: Show help
- **F5**: Refresh data
