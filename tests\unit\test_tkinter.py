"""
Simple test script to check if tkin<PERSON> is working correctly.
"""

import tkinter as tk
from tkinter import ttk
import sys

def main():
    """Main entry point for the test application."""
    # Create the root window
    root = tk.Tk()
    root.title("Tkinter Test")
    root.geometry("400x300")
    
    # Create a label
    label = ttk.Label(root, text="If you can see this, tkinter is working correctly.")
    label.pack(padx=20, pady=20)
    
    # Create a button
    button = ttk.Button(root, text="Click Me", command=lambda: print("Button clicked!"))
    button.pack(padx=20, pady=20)
    
    # Print Python version and tkinter info
    print(f"Python version: {sys.version}")
    print(f"Tkinter version: {tk.TkVersion}")
    
    # Start the main loop
    root.mainloop()

if __name__ == "__main__":
    main()
