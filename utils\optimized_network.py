"""
Optimized network utilities for the Czech Property Registry application.
Provides improved HTTP session management with connection pooling, retry logic,
and performance optimizations.
"""

import requests
import time
import logging
import threading
import functools
import random
from typing import Dict, Any, Optional, List, Tuple, Set, Union, Callable
from urllib.parse import urlparse
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# Configure logging
logger = logging.getLogger("OptimizedNetwork")


class OptimizedSession:
    """
    An optimized HTTP session with connection pooling, retry logic, and performance optimizations.
    """

    def __init__(self,
                 pool_connections: int = 100,
                 pool_maxsize: int = 100,
                 max_retries: int = 3,
                 backoff_factor: float = 0.3,
                 status_forcelist: List[int] = [500, 502, 503, 504],
                 timeout: Tuple[int, int] = (5, 30),
                 cache_dns: bool = True,
                 keep_alive: bool = True):
        """
        Initialize the optimized HTTP session.

        Args:
            pool_connections (int): Number of connection pools to cache
            pool_maxsize (int): Maximum number of connections to save in the pool
            max_retries (int): Maximum number of retries for failed requests
            backoff_factor (float): Backoff factor for retries
            status_forcelist (List[int]): HTTP status codes to retry on
            timeout (Tuple[int, int]): Connection and read timeouts
            cache_dns (bool): Whether to cache DNS lookups
            keep_alive (bool): Whether to use HTTP keep-alive
        """
        self.session = requests.Session()
        self.timeout = timeout

        # Configure retry strategy
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=backoff_factor,
            status_forcelist=status_forcelist,
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT"],
            raise_on_redirect=False,
            raise_on_status=False
        )

        # Configure connection pooling
        adapter = HTTPAdapter(
            pool_connections=pool_connections,
            pool_maxsize=pool_maxsize,
            max_retries=retry_strategy
        )

        # Mount the adapter for both HTTP and HTTPS
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # Set default headers
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive" if keep_alive else "close"
        })

        # Domain-specific rate limiting
        self.rate_limits = {}
        self.rate_limit_lock = threading.Lock()
        self.last_request_time = {}

        # DNS cache
        self.dns_cache = {}
        self.dns_cache_lock = threading.Lock()
        self.cache_dns = cache_dns

        # Statistics
        self.stats = {
            "requests": 0,
            "retries": 0,
            "failures": 0,
            "dns_cache_hits": 0,
            "dns_cache_misses": 0,
            "total_request_time": 0,
            "request_times": {}
        }
        self.stats_lock = threading.Lock()

        logger.info("OptimizedSession initialized")

    def set_rate_limit(self, domain: str, requests_per_second: float) -> None:
        """
        Set a rate limit for a specific domain.

        Args:
            domain (str): Domain to rate limit
            requests_per_second (float): Maximum requests per second
        """
        with self.rate_limit_lock:
            self.rate_limits[domain] = 1.0 / requests_per_second
            logger.info(f"Set rate limit for {domain} to {requests_per_second} req/s")

    def _apply_rate_limiting(self, url: str) -> None:
        """
        Apply rate limiting for the given URL.

        Args:
            url (str): URL to rate limit
        """
        domain = urlparse(url).netloc

        with self.rate_limit_lock:
            # Check if we have a rate limit for this domain
            if domain in self.rate_limits:
                # Calculate how long to wait
                min_interval = self.rate_limits[domain]
                last_time = self.last_request_time.get(domain, 0)
                current_time = time.time()
                elapsed = current_time - last_time

                if elapsed < min_interval:
                    # Add some jitter to avoid thundering herd
                    sleep_time = min_interval - elapsed + (random.random() * 0.1)
                    time.sleep(sleep_time)

                # Update last request time
                self.last_request_time[domain] = time.time()

    def _resolve_dns(self, url: str) -> str:
        """
        Resolve DNS for the given URL and cache the result.

        Args:
            url (str): URL to resolve

        Returns:
            str: Original URL
        """
        if not self.cache_dns:
            return url

        domain = urlparse(url).netloc

        with self.dns_cache_lock:
            if domain in self.dns_cache:
                with self.stats_lock:
                    self.stats["dns_cache_hits"] += 1
                return url

            # We don't actually resolve the DNS here, as requests will do it for us
            # But we mark it as cached so we know we've seen it before
            self.dns_cache[domain] = True

            with self.stats_lock:
                self.stats["dns_cache_misses"] += 1

            return url

    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        Send an HTTP request with optimizations.

        Args:
            method (str): HTTP method
            url (str): URL to request
            **kwargs: Additional arguments to pass to requests

        Returns:
            requests.Response: HTTP response
        """
        # Apply rate limiting
        self._apply_rate_limiting(url)

        # Resolve DNS
        url = self._resolve_dns(url)

        # Set default timeout if not provided
        if "timeout" not in kwargs:
            kwargs["timeout"] = self.timeout

        # Send the request and measure time
        start_time = time.time()
        try:
            response = self.session.request(method, url, **kwargs)

            # Update statistics
            with self.stats_lock:
                self.stats["requests"] += 1
                request_time = time.time() - start_time
                self.stats["total_request_time"] += request_time

                # Track request times by domain
                domain = urlparse(url).netloc
                if domain not in self.stats["request_times"]:
                    self.stats["request_times"][domain] = []
                self.stats["request_times"][domain].append(request_time)

                # Keep only the last 100 request times
                if len(self.stats["request_times"][domain]) > 100:
                    self.stats["request_times"][domain] = self.stats["request_times"][domain][-100:]

            return response
        except Exception as e:
            # Update statistics
            with self.stats_lock:
                self.stats["failures"] += 1

            # Re-raise the exception
            raise

    def get(self, url: str, **kwargs) -> requests.Response:
        """
        Send a GET request.

        Args:
            url (str): URL to request
            **kwargs: Additional arguments to pass to requests

        Returns:
            requests.Response: HTTP response
        """
        return self.request("GET", url, **kwargs)

    def post(self, url: str, **kwargs) -> requests.Response:
        """
        Send a POST request.

        Args:
            url (str): URL to request
            **kwargs: Additional arguments to pass to requests

        Returns:
            requests.Response: HTTP response
        """
        return self.request("POST", url, **kwargs)

    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the session.

        Returns:
            Dict[str, Any]: Dictionary with session statistics
        """
        with self.stats_lock:
            stats = self.stats.copy()

            # Calculate average request time
            if stats["requests"] > 0:
                stats["average_request_time"] = stats["total_request_time"] / stats["requests"]
            else:
                stats["average_request_time"] = 0

            # Calculate average request time by domain
            stats["average_request_time_by_domain"] = {}
            for domain, times in stats["request_times"].items():
                if times:
                    stats["average_request_time_by_domain"][domain] = sum(times) / len(times)

            return stats

    def close(self) -> None:
        """Close the session."""
        self.session.close()
        logger.info("OptimizedSession closed")

    def __del__(self) -> None:
        """Close the session when the object is deleted."""
        self.close()


# Create a global instance for convenience
optimized_session = OptimizedSession()