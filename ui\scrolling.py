"""
Scrolling utilities for the Czech Property Registry application.

This module provides scrolling functionality for tkinter widgets,
including touchpad scrolling support and adaptive scroll speed.
"""

import time
import tkinter as tk
from tkinter import ttk


class ScrollManager:
    """
    Manages scrolling behavior for tkinter widgets.

    Provides support for:
    - Mouse wheel scrolling
    - Touchpad scrolling (two-finger scroll)
    - Horizontal scrolling with Shift+wheel
    - Adaptive scroll speed based on content size
    """

    def __init__(self, root, canvas, content_frame):
        """
        Initialize the ScrollManager.

        Args:
            root: The root tkinter window
            canvas: The canvas widget to scroll
            content_frame: The frame inside the canvas containing the content
        """
        self.root = root
        self.canvas = canvas
        self.content_frame = content_frame

        # Initialize scroll-related variables
        self._last_scroll_time = 0
        self._last_h_scroll_time = 0
        self._last_touchpad_time = 0
        self._last_motion_time = 0
        self._scroll_debounce_ms = 10  # Debounce time in milliseconds

        # Initialize processing flags
        self._processing_scroll = False
        self._processing_h_scroll = False
        self._processing_touchpad = False
        self._processing_motion_scroll = False

        # Bind mouse wheel events to the canvas for vertical scrolling
        self.canvas.bind("<MouseWheel>", self._on_mousewheel_vertical)  # Windows
        self.canvas.bind("<Button-4>", self._on_mousewheel_vertical)    # Linux scroll up
        self.canvas.bind("<Button-5>", self._on_mousewheel_vertical)    # Linux scroll down

        # Bind Shift+MouseWheel for horizontal scrolling
        self.canvas.bind("<Shift-MouseWheel>", self._on_mousewheel_horizontal)  # Windows

        # Add special bindings for touchpad scrolling
        self.canvas.bind("<Control-MouseWheel>", self._on_touchpad_scroll)  # Windows touchpad with Ctrl

        # Bind to all child widgets to ensure scrolling works everywhere
        self._bind_recursive(self.content_frame)

        # Also bind to the root window to ensure scrolling works when mouse is over any part
        self.root.bind_all("<MouseWheel>", self._on_mousewheel_vertical)  # Windows
        self.root.bind_all("<Button-4>", self._on_mousewheel_vertical)    # Linux scroll up
        self.root.bind_all("<Button-5>", self._on_mousewheel_vertical)    # Linux scroll down
        self.root.bind_all("<Shift-MouseWheel>", self._on_mousewheel_horizontal)  # Windows

        # Add special bindings for touchpad scrolling
        # These help ensure touchpad scrolling works consistently across the application
        self.root.bind_all("<Control-MouseWheel>", self._on_touchpad_scroll)  # Windows touchpad with Ctrl

        # Initialize touchpad tracking variables
        self._touchpad_last_time = 0
        self._touchpad_events_count = 0
        self._touchpad_mode = False

    def _bind_recursive(self, widget):
        """Recursively bind mouse wheel events to all children"""
        # Skip binding to widgets that already have their own scroll handling
        widget_class = widget.winfo_class()

        # These widget types should use their own scroll handlers
        skip_classes = ('Text', 'Listbox', 'Treeview', 'Canvas', 'TCombobox', 'Entry')

        if widget_class not in skip_classes:
            # Bind to this widget
            widget.bind("<MouseWheel>", self._on_mousewheel_vertical, add="+")  # Windows
            widget.bind("<Button-4>", self._on_mousewheel_vertical, add="+")    # Linux scroll up
            widget.bind("<Button-5>", self._on_mousewheel_vertical, add="+")    # Linux scroll down
            widget.bind("<Shift-MouseWheel>", self._on_mousewheel_horizontal, add="+")  # Windows

            # Special binding for touchpad scrolling (Windows)
            # This helps capture touchpad events that might be missed by the regular MouseWheel binding
            widget.bind("<B1-Motion>", self._check_touchpad_scroll, add="+")
            widget.bind("<B2-Motion>", self._check_touchpad_scroll, add="+")

        # Recursively bind to all children
        for child in widget.winfo_children():
            self._bind_recursive(child)

    def _on_touchpad_scroll(self, event):
        """
        Special handler for touchpad scrolling with Control key
        This is specifically designed to handle Windows touchpad events
        """
        # Check if we're already processing a touchpad scroll event
        if hasattr(self, '_processing_touchpad') and self._processing_touchpad:
            return "break"

        # Apply debounce to prevent too many scroll events
        current_time = time.time() * 1000  # Convert to milliseconds
        if hasattr(self, '_last_touchpad_time') and (current_time - self._last_touchpad_time) < self._scroll_debounce_ms:
            return "break"

        self._last_touchpad_time = current_time
        self._processing_touchpad = True

        try:
            # Calculate delta using the common helper with touchpad mode enabled
            delta = self._calculate_scroll_delta(event, is_touchpad_mode=True)
            if delta == 0:
                self._processing_touchpad = False
                return "break"

            # Determine scroll direction (vertical or horizontal)
            if event.state & 0x4:  # Control key is pressed
                # Horizontal scrolling with Control
                self.canvas.xview_scroll(int(-delta), "units")
            else:
                # Vertical scrolling
                self.canvas.yview_scroll(int(-delta), "units")

            # Update the canvas immediately for smoother scrolling
            self.canvas.update_idletasks()
            self._processing_touchpad = False
            return "break"  # Prevent event propagation
        except Exception as e:
            # If any error occurs, make sure to clear the processing flag
            print(f"Error in touchpad scroll: {e}")
            self._processing_touchpad = False
            return "break"
        finally:
            # Schedule another reset of the flag as a backup
            self.root.after(50, lambda: setattr(self, '_processing_touchpad', False))

    def _check_touchpad_scroll(self, event):
        """
        Special handler to detect touchpad scrolling gestures
        This helps with two-finger scrolling on touchpads
        """
        # Check if we're already processing a motion scroll event
        if hasattr(self, '_processing_motion_scroll') and self._processing_motion_scroll:
            return "break"

        # Apply debounce to prevent too many scroll events
        current_time = time.time() * 1000  # Convert to milliseconds
        if hasattr(self, '_last_motion_time') and (current_time - self._last_motion_time) < self._scroll_debounce_ms:
            return "break"

        self._last_motion_time = current_time
        self._processing_motion_scroll = True

        try:
            # Store the last position to detect scrolling motion
            if not hasattr(self, '_last_touchpad_y'):
                self._last_touchpad_y = event.y
                self._last_touchpad_x = event.x
                return "break"

            # Calculate the deltas
            delta_y = self._last_touchpad_y - event.y
            delta_x = self._last_touchpad_x - event.x

            # Update the last position
            self._last_touchpad_y = event.y
            self._last_touchpad_x = event.x

            # Use a common scaling factor for both directions
            scaling_factor = 0.2  # Reduced multiplier for less aggressive scrolling

            # Handle vertical scrolling if movement is significant
            if abs(delta_y) > 5:
                scaled_delta_y = int(delta_y * scaling_factor)
                self.canvas.yview_scroll(scaled_delta_y, "units")

            # Handle horizontal scrolling if shift is pressed or horizontal movement is dominant
            if event.state & 0x1 or (abs(delta_x) > abs(delta_y) and abs(delta_x) > 5):
                scaled_delta_x = int(delta_x * scaling_factor)
                self.canvas.xview_scroll(scaled_delta_x, "units")

            # Update the canvas immediately for smoother scrolling
            self.canvas.update_idletasks()
            self._processing_motion_scroll = False
            return "break"  # Prevent event propagation
        except Exception as e:
            print(f"Error in motion scroll: {e}")
            self._processing_motion_scroll = False
            return "break"
        finally:
            # Schedule another reset of the flag as a backup
            self.root.after(50, lambda: setattr(self, '_processing_motion_scroll', False))

    def _calculate_scroll_delta(self, event, is_touchpad_mode=False):
        """
        Calculate scroll delta from event with consistent handling across platforms

        Args:
            event: The scroll event
            is_touchpad_mode (bool): Whether to use touchpad sensitivity settings

        Returns:
            float: Normalized scroll delta value
        """
        # Get the delta (different handling for Windows and Linux)
        if hasattr(event, 'num') and event.num in (4, 5):
            # Linux scroll events
            delta = 1 if event.num == 4 else -1  # Up (4) or Down (5)
        elif hasattr(event, 'delta'):  # Windows
            # Get the raw delta value
            raw_delta = event.delta

            # For touchpads on Windows, the delta can be much smaller than 120
            if abs(raw_delta) < 120 or is_touchpad_mode:
                # This is likely a touchpad gesture - use reduced sensitivity
                delta = raw_delta / 200
            else:
                # This is likely a mouse wheel - use standard sensitivity
                delta = raw_delta / 120

            # Apply a minimum threshold for small movements
            if abs(delta) < 0.05:
                return 0

            # Normalize to a reasonable range
            if delta > 0:
                delta = min(3, max(0.1, delta))
            else:
                delta = max(-3, min(-0.1, delta))
        else:
            # Unknown event type
            return 0

        return delta

    def _calculate_scroll_multiplier(self, content_size, visible_size, default=1.5):
        """
        Calculate scroll speed multiplier based on content vs visible size

        Args:
            content_size (int): Total content size (width or height)
            visible_size (int): Visible area size
            default (float): Default multiplier if calculation fails

        Returns:
            float: Scroll speed multiplier
        """
        try:
            if content_size > visible_size * 3:  # Very large content
                return 2
            elif content_size > visible_size * 2:  # Moderately large content
                return 1.5
            else:  # Small content
                return 1
        except (TypeError, ValueError):
            return default

    def _on_mousewheel_vertical(self, event):
        """Handle vertical scrolling with mouse wheel or touchpad"""
        # Check if we're already processing a scroll event to prevent stuttering
        if hasattr(self, '_processing_scroll') and self._processing_scroll:
            return "break"

        # Apply debounce to prevent too many scroll events
        current_time = time.time() * 1000  # Convert to milliseconds
        if hasattr(self, '_last_scroll_time') and (current_time - self._last_scroll_time) < self._scroll_debounce_ms:
            return "break"

        self._last_scroll_time = current_time
        self._processing_scroll = True

        try:
            # Calculate delta using the common helper
            delta = self._calculate_scroll_delta(event)
            if delta == 0:
                self._processing_scroll = False
                return "break"

            # Get content and visible sizes for multiplier calculation
            scroll_region = self.canvas.cget("scrollregion")
            multiplier = 1.5  # Default multiplier

            if scroll_region:
                try:
                    _, _, _, height = map(int, scroll_region.split())
                    visible_height = self.canvas.winfo_height()
                    multiplier = self._calculate_scroll_multiplier(height, visible_height)
                except (ValueError, IndexError):
                    pass

            # Scroll the canvas
            self.canvas.yview_scroll(int(-delta * multiplier), "units")
            self.canvas.update_idletasks()
            self._processing_scroll = False
            return "break"  # Prevent event propagation

        except Exception as e:
            print(f"Error in vertical scroll: {e}")
            self._processing_scroll = False
            return "break"
        finally:
            # Schedule another reset of the flag as a backup
            self.root.after(50, lambda: setattr(self, '_processing_scroll', False))

    def _on_mousewheel_horizontal(self, event):
        """Handle horizontal scrolling with Shift+mouse wheel or touchpad"""
        # Check if we're already processing a scroll event to prevent stuttering
        if hasattr(self, '_processing_h_scroll') and self._processing_h_scroll:
            return "break"

        # Apply debounce to prevent too many scroll events
        current_time = time.time() * 1000  # Convert to milliseconds
        if hasattr(self, '_last_h_scroll_time') and (current_time - self._last_h_scroll_time) < self._scroll_debounce_ms:
            return "break"

        self._last_h_scroll_time = current_time
        self._processing_h_scroll = True

        try:
            # Calculate delta using the common helper
            delta = self._calculate_scroll_delta(event)
            if delta == 0:
                self._processing_h_scroll = False
                return "break"

            # Get content and visible sizes for multiplier calculation
            scroll_region = self.canvas.cget("scrollregion")
            multiplier = 1.5  # Default multiplier

            if scroll_region:
                try:
                    _, _, width, _ = map(int, scroll_region.split())
                    visible_width = self.canvas.winfo_width()
                    multiplier = self._calculate_scroll_multiplier(width, visible_width)
                except (ValueError, IndexError):
                    pass

            # Scroll the canvas
            self.canvas.xview_scroll(int(-delta * multiplier), "units")
            self.canvas.update_idletasks()
            self._processing_h_scroll = False
            return "break"  # Prevent event propagation

        except Exception as e:
            print(f"Error in horizontal scroll: {e}")
            self._processing_h_scroll = False
            return "break"
        finally:
            # Schedule another reset of the flag as a backup
            self.root.after(50, lambda: setattr(self, '_processing_h_scroll', False))

    def _on_text_mousewheel_vertical(self, event):
        """Handle vertical scrolling in text widgets"""
        # Check if we're already processing a scroll event to prevent stuttering
        widget_id = str(event.widget)
        processing_attr = f'_processing_text_scroll_{widget_id}'

        if hasattr(self, processing_attr) and getattr(self, processing_attr):
            return "break"

        setattr(self, processing_attr, True)

        try:
            # Calculate delta using the common helper
            delta = self._calculate_scroll_delta(event)
            if delta == 0:
                return "break"

            # Get text widget content size for adaptive scrolling
            try:
                # Get the total number of lines
                total_lines = int(event.widget.index('end-1c').split('.')[0])
                visible_lines = event.widget.winfo_height() / event.widget.dlineinfo('1.0')[3]

                # Use the common multiplier calculation
                multiplier = self._calculate_scroll_multiplier(total_lines, visible_lines, default=2)
            except (TypeError, IndexError, AttributeError, ZeroDivisionError):
                # Default if we can't calculate
                multiplier = 2

            # Scroll the text widget with adaptive speed
            event.widget.yview_scroll(int(-delta * multiplier), "units")
            return "break"  # Prevent event propagation
        finally:
            # Clear the processing flag after a short delay
            self.root.after(10, lambda: setattr(self, processing_attr, False))

    def _on_text_mousewheel_horizontal(self, event):
        """Handle horizontal scrolling in text widgets"""
        # Check if we're already processing a scroll event to prevent stuttering
        widget_id = str(event.widget)
        processing_attr = f'_processing_text_h_scroll_{widget_id}'

        if hasattr(self, processing_attr) and getattr(self, processing_attr):
            return "break"

        setattr(self, processing_attr, True)

        try:
            # Calculate delta using the common helper
            delta = self._calculate_scroll_delta(event)
            if delta == 0:
                return "break"

            # Use a moderate fixed multiplier for horizontal scrolling
            multiplier = 2

            # Scroll the text widget horizontally
            event.widget.xview_scroll(int(-delta * multiplier), "units")
            return "break"  # Prevent event propagation
        finally:
            # Clear the processing flag after a short delay
            self.root.after(10, lambda: setattr(self, processing_attr, False))

    def _on_treeview_mousewheel(self, event):
        """Handle scrolling in treeview widgets"""
        # Check if we're already processing a scroll event to prevent stuttering
        widget_id = str(event.widget)
        processing_attr = f'_processing_treeview_scroll_{widget_id}'

        if hasattr(self, processing_attr) and getattr(self, processing_attr):
            return "break"

        setattr(self, processing_attr, True)

        try:
            # Calculate delta using the common helper
            delta = self._calculate_scroll_delta(event)
            if delta == 0:
                return "break"

            # Get treeview content size for adaptive scrolling
            try:
                # Try to determine the number of items in the treeview
                children_count = len(event.widget.get_children())
                visible_count = event.widget.winfo_height() / 20  # Approximate row height

                # Use the common multiplier calculation
                multiplier = self._calculate_scroll_multiplier(children_count, visible_count, default=2)
            except (TypeError, AttributeError, ZeroDivisionError):
                # Default if we can't calculate
                multiplier = 2

            # Scroll the treeview with adaptive speed
            event.widget.yview_scroll(int(-delta * multiplier), "units")
            return "break"  # Prevent event propagation
        finally:
            # Clear the processing flag after a short delay
            self.root.after(10, lambda: setattr(self, processing_attr, False))
