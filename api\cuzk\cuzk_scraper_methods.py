"""
CUZK scraping methods for the Czech Property Registry application.

This module provides methods for scraping property data from the CUZK website.
"""

import webbrowser

from ui.message_boxes import MessageBoxes


class CUZKScraperMethods:
    """
    Methods for scraping property data from the CUZK website.
    """

    def __init__(self, app):
        """
        Initialize the CUZK scraper methods.

        Args:
            app: The main application instance
        """
        self.app = app
        self.session = app.session
        self.captcha_url = None
        self.original_captcha_url = None
        self.captcha_solution = None

    def direct_cuzk_scrape_by_coordinates(self, x, y):
        """
        Scrape property data from CUZK website by coordinates using direct browser access.

        Args:
            x (str): X coordinate (latitude)
            y (str): Y coordinate (longitude)

        Returns:
            dict: Property data or None if scraping failed
        """
        try:
            # Convert coordinates to SJTSK if needed
            x_coord, y_coord = x, y

            # Construct the URL for the CUZK website
            url = f"https://nahlizenidokn.cuzk.cz/VyberBudovu.aspx?typ=Souradnice&x={x_coord}&y={y_coord}"

            # Open the URL in the default browser
            webbrowser.open(url)

            # Ask the user to complete the CAPTCHA and navigate to the property details
            MessageBoxes.show_info(
                "Direct Browser Scraping",
                "The CUZK website has been opened in your browser.\n\n"
                "Please complete any CAPTCHA that appears and navigate to the property details page.\n\n"
                "Once you are on the property details page, click OK to continue."
            )

            # Ask the user to copy the property details
            property_data = self.app.property_ui_helpers.create_property_input_dialog(
                self.app.root,
                "Enter Property Details",
                {
                    'owner_name': '',
                    'owner_address': '',
                    'property_type': '',
                    'parcel_number': '',
                    'cadastral_territory': '',
                    'ownership_share': '',
                    'lv_number': '',
                    'additional_notes': '',
                }
            )

            if property_data:
                # Add source information
                property_data['source'] = 'direct_browser_input'
                return property_data

            return None
        except Exception as e:
            print(f"Error in direct CUZK scrape by coordinates: {e}")
            return None

    def direct_cuzk_scrape_by_parcel(self, parcel_number):
        """
        Scrape property data from CUZK website by parcel number using direct browser access.

        Args:
            parcel_number (str): Parcel number

        Returns:
            dict: Property data or None if scraping failed
        """
        try:
            # Construct the URL for the CUZK website
            url = f"https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Parcela&id={parcel_number}"

            # Open the URL in the default browser
            webbrowser.open(url)

            # Ask the user to complete the CAPTCHA and navigate to the property details
            MessageBoxes.show_info(
                "Direct Browser Scraping",
                "The CUZK website has been opened in your browser.\n\n"
                "Please complete any CAPTCHA that appears and navigate to the property details page.\n\n"
                "Once you are on the property details page, click OK to continue."
            )

            # Ask the user to copy the property details
            property_data = self.app.property_ui_helpers.create_property_input_dialog(
                self.app.root,
                "Enter Property Details",
                {
                    'owner_name': '',
                    'owner_address': '',
                    'property_type': '',
                    'parcel_number': parcel_number,
                    'cadastral_territory': '',
                    'ownership_share': '',
                    'lv_number': '',
                    'additional_notes': '',
                }
            )

            if property_data:
                # Add source information
                property_data['source'] = 'direct_browser_input'
                return property_data

            return None
        except Exception as e:
            print(f"Error in direct CUZK scrape by parcel: {e}")
            return None

    def is_captcha_page(self, soup):
        """
        Check if the current page contains a CAPTCHA

        Args:
            soup (BeautifulSoup): BeautifulSoup object of the page

        Returns:
            bool: True if the page contains a CAPTCHA, False otherwise
        """
        # Check for common CAPTCHA indicators
        captcha_indicators = [
            # Image CAPTCHA
            soup.find('img', {'id': lambda x: x and 'captcha' in x.lower()}),
            soup.find('img', {'src': lambda x: x and 'captcha' in x.lower()}),
            # Text mentioning CAPTCHA
            soup.find(string=lambda text: text and 'captcha' in text.lower()),
            # Input field for CAPTCHA
            soup.find('input', {'id': lambda x: x and 'captcha' in x.lower()}),
            # reCAPTCHA
            soup.find('div', {'class': 'g-recaptcha'})
        ]

        return any(captcha_indicators)
