"""
Direct main entry point for the Czech Property Registry application.

This file serves as a simplified entry point for the application.
It creates the main window and initializes the application with minimal complexity.
"""

import tkinter as tk
import sys
import os
import logging
import argparse
import time
import threading
from ui.minimal_loading_window import MinimalLoadingWindow

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)

# Create a logger for this module
logger = logging.getLogger(__name__)

# Add the current directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Czech Property Registry")

    parser.add_argument(
        "--optimized",
        action="store_true",
        help="Use the optimized application implementation"
    )

    parser.add_argument(
        "--config",
        type=str,
        default="config.ini",
        help="Path to the configuration file"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )

    return parser.parse_args()


def setup_logging(debug=False):
    """Set up logging with the appropriate level."""
    level = logging.DEBUG if debug else logging.INFO

    # Configure root logger
    logging.getLogger().setLevel(level)

    # Configure specific loggers
    for logger_name in ["core", "api", "ui", "utils"]:
        logging.getLogger(logger_name).setLevel(level)

    logger.info(f"Logging level set to: {logging.getLevelName(level)}")


def main():
    """Main entry point for the application."""
    # Parse command line arguments
    args = parse_arguments()

    # Set up logging
    setup_logging(args.debug)

    # Log startup information
    logger.info("Starting Czech Property Registry")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current directory: {os.getcwd()}")

    try:
        # Create and show the loading window
        loading = MinimalLoadingWindow()
        loading.set_task("Loading dependencies")
        loading.set_status("Importing required modules...")
        
        # Import dependencies
        loading.window.update()
        from core.app_factory import AppFactory
        
        # Create the application
        loading.set_task("Creating application")
        loading.set_status("Initializing application components...")
        loading.window.update()
        
        # Determine the app type
        app_type = "optimized" if args.optimized else "standard"
        
        # Create the application
        app = AppFactory.create_app(app_type=app_type, config={"config_path": args.config})
        
        # Initialize APIs
        loading.set_task("Connecting to APIs")
        loading.set_status("Establishing connections to external services...")
        loading.window.update()
        
        if hasattr(app, 'google_maps'):
            app.google_maps.validate_api_key()
        
        # Prepare UI
        loading.set_task("Preparing user interface")
        loading.set_status("Setting up the user interface...")
        loading.window.update()
        
        # Close the loading window
        loading.close()
        
        # Run the application
        app.run()
        
        logger.info("Application exited normally")
        return 0
    except Exception as e:
        # Show error and close loading window if it exists
        logger.error(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            if 'loading' in locals():
                loading.close()
        except:
            pass
            
        return 1


if __name__ == "__main__":
    sys.exit(main())
