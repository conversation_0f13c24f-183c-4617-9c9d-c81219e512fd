"""
Batch search methods for the Czech Property Registry application.

This module provides methods for batch searching of properties
by type in a specific area.
"""

import tkinter as tk
import webbrowser
import random
from ui.message_boxes import MessageBoxes
# No fallback data is used - removed DemoData import


class BatchSearchMethods:
    """
    Methods for batch property search functionality.

    This class provides methods for searching multiple properties
    by type in a specific area.
    """

    def __init__(self, app):
        """
        Initialize the batch search methods.

        Args:
            app: The main application instance for callbacks
        """
        self.app = app

    def direct_batch_scrape(self, city, region, property_type, radius, max_results):
        """
        Use direct browser scraping for batch property search

        Args:
            city (str): City name
            region (str): Region name
            property_type (str): Type of property to search for
            radius (int): Search radius in meters
            max_results (int): Maximum number of results to return
        """
        try:
            # Clear previous results
            if hasattr(self.app, 'batch_results_text'):
                self.app.batch_results_text.config(state="normal")
                self.app.batch_results_text.delete(1.0, tk.END)

            # Show status
            self.app.show_status(f"Searching for {property_type} properties in {city if city else region}...")

            # Construct the search query
            query = city
            if region and not city:
                query = region
            elif region and city:
                query = f"{city}, {region}"

            # Show the location on the map
            if hasattr(self.app, "show_location_on_map"):
                self.app.show_location_on_map(query)

            # Open the CUZK website in the browser
            url = f"https://nahlizenidokn.cuzk.cz/"
            webbrowser.open(url)

            # Ask the user to complete the search manually
            MessageBoxes.show_info(
                "Direct Browser Scraping",
                f"The CUZK website has been opened in your browser.\n\n"
                f"Please search for {property_type} properties in {query} and complete any CAPTCHA that appears.\n\n"
                f"Once you have found the properties, click OK to continue."
            )

            # Generate sample properties for demonstration
            sample_properties = self.generate_sample_properties(city, region, property_type, max_results)

            # Update the UI with the sample properties
            if hasattr(self.app, 'batch_results_text'):
                self.app.batch_results_text.config(state="normal")

                # Add header
                header = f"Found {len(sample_properties)} {property_type} properties in {city if city else region}\n\n"
                self.app.batch_results_text.insert(tk.END, header)

                # Add each property
                for i, property_data in enumerate(sample_properties):
                    owner_name = property_data.get('owner_name', 'Unknown')
                    owner_address = property_data.get('owner_address', 'Unknown')
                    prop_type = property_data.get('property_type', 'Unknown')

                    # Add to results
                    result_text = f"Property {i+1}:\n"
                    result_text += f"Owner: {owner_name}\n"
                    result_text += f"Address: {owner_address}\n"
                    result_text += f"Type: {prop_type}\n\n"

                    # Store the property data for later use
                    self.app.property_list.append(property_data)

                    # Update the UI
                    self.app.batch_results_text.insert(tk.END, result_text)
                    self.app.batch_results_text.update_idletasks()

                # Add completion message
                self.app.batch_results_text.insert(tk.END, "Search completed successfully.")
                self.app.show_status(f"Generated {len(sample_properties)} sample properties in {city if city else region}")

            # Update the property tree if it exists
            if hasattr(self.app, 'property_tree'):
                self.app.update_property_tree()

        except Exception as e:
            error_message = f"Error: {e}"
            if hasattr(self.app, 'batch_results_text'):
                self.app.batch_results_text.insert(tk.END, error_message)
            self.app.show_status(error_message)
            print(f"Error in direct batch scrape: {e}")
        finally:
            # Disable the text widget after updating
            if hasattr(self.app, 'batch_results_text'):
                self.app.batch_results_text.config(state="disabled")

    def generate_sample_properties(self, city, region, property_type, num_properties=5):
        """
        Generate sample property data for testing

        Args:
            city (str): City name
            region (str): Region name
            property_type (str): Type of property to search for
            num_properties (int): Number of properties to generate

        Returns:
            list: List of sample property dictionaries
        """
        # This method should not be used in production
        # Instead, real API data should be used

        # Show an error message
        MessageBoxes.show_error(
            "Error: Demo Data Not Available",
            "This application does not use demo or fallback data.\n\n"
            "Please configure the application with valid API keys and try again."
        )

        # Return an empty list
        return []
