#!/usr/bin/env python3
"""
Script to check if the CI setup is working correctly.
This script verifies that the necessary files and directories exist for CI.
"""

import os
import sys
import yaml
import subprocess


def check_file_exists(file_path, required=True):
    """Check if a file exists."""
    exists = os.path.isfile(file_path)
    status = "✅" if exists else "❌" if required else "⚠️"
    print(f"{status} {file_path}")
    return exists


def check_directory_exists(dir_path, required=True):
    """Check if a directory exists."""
    exists = os.path.isdir(dir_path)
    status = "✅" if exists else "❌" if required else "⚠️"
    print(f"{status} {dir_path}")
    return exists


def check_yaml_valid(file_path):
    """Check if a YAML file is valid."""
    try:
        with open(file_path, 'r') as f:
            yaml.safe_load(f)
        print(f"✅ {file_path} is valid YAML")
        return True
    except Exception as e:
        print(f"❌ {file_path} is not valid YAML: {e}")
        return False


def check_tests_runnable():
    """Check if tests can be run."""
    try:
        result = subprocess.run(
            [sys.executable, "tests/run_tests.py", "--type", "unit", "--test", "test_region_manager"],
            capture_output=True,
            text=True,
            check=True
        )
        if "Test Summary:" in result.stdout and "Failures: 0" in result.stdout and "Errors: 0" in result.stdout:
            print("✅ Tests are runnable")
            return True
        else:
            print(f"❌ Tests failed: {result.stdout}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ Tests failed to run: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def main():
    """Main function."""
    print("Checking CI setup...")

    # Check GitHub Actions workflow files
    workflow_dir = ".github/workflows"
    check_directory_exists(workflow_dir)
    ci_yml = f"{workflow_dir}/ci.yml"
    check_file_exists(ci_yml)
    check_yaml_valid(ci_yml)

    # Check GitHub issue templates
    issue_template_dir = ".github/ISSUE_TEMPLATE"
    check_directory_exists(issue_template_dir, required=False)
    check_file_exists(f"{issue_template_dir}/bug_report.md", required=False)
    check_file_exists(f"{issue_template_dir}/feature_request.md", required=False)

    # Check GitHub PR template
    check_file_exists(".github/pull_request_template.md", required=False)

    # Check test files
    check_directory_exists("tests")
    check_directory_exists("tests/unit")
    check_file_exists("tests/run_tests.py")
    check_file_exists("tests/conftest.py")
    check_file_exists("pytest.ini")

    # Check if tests can be run
    check_tests_runnable()

    print("\nCI setup check complete!")


if __name__ == "__main__":
    main()
