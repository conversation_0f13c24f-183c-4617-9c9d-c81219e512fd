"""
Test script to verify that Mapy.cz city boundary detection works correctly with the application.

This script:
1. Initializes the application
2. Tests batch searching by city using Mapy.cz
3. Verifies that the results are correct
"""

import logging
import sys
import time
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("MapyCZCitySearchTest")

# Import the necessary modules
try:
    from core.app_factory import AppFactory
except ImportError as e:
    logger.error(f"Error importing modules: {e}")
    logger.error("Make sure you're running this script from the project root directory")
    sys.exit(1)

def test_city_search():
    """Test batch searching by city using Mapy.cz"""
    logger.info("Testing batch searching by city using Mapy.cz")
    
    # Create the application
    app = AppFactory.create_app()
    
    # Test cities
    test_cities = [
        "Praha",
        "Brno",
        "Plzeň",
        "Bušovice",
        "Klecany"
    ]
    
    results = {}
    
    # Test each city
    for city_name in test_cities:
        logger.info(f"\nTesting city: {city_name}")
        
        # Define a callback to capture the results
        def callback(properties):
            results[city_name] = properties
        
        # Perform batch search by city using Mapy.cz
        app.batch_search_manager.batch_search_by_city(
            city=city_name,
            property_types=None,
            max_results=10,
            callback=callback,
            use_mapycz=True
        )
        
        # Wait for the search to complete
        while app.batch_search_manager.search_in_progress:
            time.sleep(0.1)
            app.root.update()
        
        # Add a small delay to ensure the callback has been processed
        time.sleep(0.5)
        app.root.update()
    
    # Print summary
    logger.info("\n\nSummary of results:")
    for city_name, properties in results.items():
        logger.info(f"\nCity: {city_name}")
        logger.info(f"Found {len(properties)} properties")
        
        # Print a sample of the properties
        sample_size = min(3, len(properties))
        for i in range(sample_size):
            prop = properties[i]
            logger.info(f"  Property {i+1}:")
            logger.info(f"    Type: {prop.get('property_type', 'Unknown')}")
            logger.info(f"    Address: {prop.get('address', 'Unknown')}")
            logger.info(f"    Coordinates: ({prop.get('lat', 'Unknown')}, {prop.get('lng', 'Unknown')})")
    
    # Clean up
    app.root.destroy()
    
    return results

if __name__ == "__main__":
    test_city_search()
