"""
Window management for the Czech Property Registry application.
"""

import tkinter as tk

class WindowManager:
    """
    Manages window-related functionality such as fullscreen mode and resizing.
    """
    
    def __init__(self, app):
        """
        Initialize the window manager.
        
        Args:
            app: The main application instance
        """
        self.app = app
        self.root = app.root
        
        # Store original window state for fullscreen toggle
        self._fullscreen = False
        self._processing_fullscreen = False
        self._normal_state = {
            'geometry': self.root.geometry()
        }
        
        # Store the last window size for comparison
        self._last_window_size = (self.root.winfo_width(), self.root.winfo_height())
        self._resize_after_id = None
        
        # Bind F11 and Escape keys for fullscreen toggle
        self.root.bind("<F11>", lambda event: self.toggle_fullscreen())
        self.root.bind("<Escape>", lambda event: self.exit_fullscreen())
        
        # Bind to window resize event
        self.root.bind("<Configure>", lambda event: self.on_window_resize(event))
    
    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        # Set a flag to prevent conflicts with the window resize handler
        self._processing_fullscreen = True
        
        try:
            if self._fullscreen:
                self.exit_fullscreen()
            else:
                # Save current geometry before going fullscreen
                self._normal_state['geometry'] = self.root.geometry()
                
                # Enter fullscreen mode
                self.root.attributes('-fullscreen', True)
                self._fullscreen = True
                if hasattr(self.app, 'fullscreen_var'):
                    self.app.fullscreen_var.set(True)
                
                # Call the resize handler after a short delay to ensure window has resized
                self.root.after(100, self._handle_resize)
                
                # Show a brief status message
                self.app.show_status("Fullscreen mode enabled (Press ESC to exit)")
        finally:
            # Clear the processing flag after a short delay
            self.root.after(200, self._clear_fullscreen_flag)
    
    def exit_fullscreen(self):
        """Exit fullscreen mode"""
        # Set a flag to prevent conflicts with the window resize handler
        self._processing_fullscreen = True
        
        try:
            if self._fullscreen:
                # Exit fullscreen mode
                self.root.attributes('-fullscreen', False)
                self._fullscreen = False
                if hasattr(self.app, 'fullscreen_var'):
                    self.app.fullscreen_var.set(False)
                
                # Restore previous geometry
                if 'geometry' in self._normal_state:
                    self.root.geometry(self._normal_state['geometry'])
                
                # Call the resize handler after a short delay to ensure window has resized
                self.root.after(100, self._handle_resize)
                
                # Show a brief status message
                self.app.show_status("Fullscreen mode disabled")
        finally:
            # Clear the processing flag after a short delay
            self.root.after(200, self._clear_fullscreen_flag)
    
    def _clear_fullscreen_flag(self):
        """Clear the fullscreen processing flag"""
        self._processing_fullscreen = False
    
    def on_window_resize(self, event):
        """
        Handle window resize events
        
        Args:
            event: The Configure event
        """
        # Ignore events from widgets other than the main window
        if event.widget != self.root:
            return
        
        # Ignore events during fullscreen toggle to prevent conflicts
        if self._processing_fullscreen:
            return
        
        # Check if the size has actually changed significantly (avoid processing tiny changes)
        current_size = (self.root.winfo_width(), self.root.winfo_height())
        if (abs(current_size[0] - self._last_window_size[0]) < 5 and
            abs(current_size[1] - self._last_window_size[1]) < 5):
            return
        
        # Update the last window size
        self._last_window_size = current_size
        
        # Throttle resize events to prevent performance issues
        if self._resize_after_id:
            self.root.after_cancel(self._resize_after_id)
        
        # Schedule the resize handler after a short delay - longer delay for better performance
        self._resize_after_id = self.root.after(150, self._handle_resize)
    
    def _handle_resize(self):
        """Handle window resize events"""
        # Only resize if we have a valid canvas manager
        if hasattr(self.app, 'canvas_manager'):
            # Force all canvas managers to resize
            self.app.canvas_manager.force_resize()
            
            # Update main PanedWindow sash positions if they exist
            if hasattr(self.app, 'main_paned'):
                # Reset the sash position to maintain the proper proportions
                main_width = self.app.main_paned.winfo_width()
                if main_width > 100:  # Only adjust if we have a reasonable width
                    left_width = int(main_width * 0.3)  # 30% for left pane
                    try:
                        self.app.main_paned.sashpos(0, left_width)
                    except:
                        pass  # Ignore errors if sash doesn't exist yet
                self.app.main_paned.update()
            
            if hasattr(self.app, 'right_paned'):
                # Reset the sash position to maintain the proper proportions
                right_height = self.app.right_paned.winfo_height()
                if right_height > 100:  # Only adjust if we have a reasonable height
                    top_height = int(right_height * 0.8)  # 80% for top pane
                    try:
                        self.app.right_paned.sashpos(0, top_height)
                    except:
                        pass  # Ignore errors if sash doesn't exist yet
                self.app.right_paned.update()
    
    def set_initial_sash_positions(self):
        """Set the initial PanedWindow sash positions"""
        # Set main PanedWindow sash position
        if hasattr(self.app, 'main_paned'):
            main_width = self.app.main_paned.winfo_width()
            if main_width > 100:  # Only adjust if we have a reasonable width
                left_width = int(main_width * 0.3)  # 30% for left pane
                try:
                    self.app.main_paned.sashpos(0, left_width)
                except:
                    pass  # Ignore errors if sash doesn't exist yet
        
        # Set right PanedWindow sash position
        if hasattr(self.app, 'right_paned'):
            right_height = self.app.right_paned.winfo_height()
            if right_height > 100:  # Only adjust if we have a reasonable height
                top_height = int(right_height * 0.8)  # 80% for top pane
                try:
                    self.app.right_paned.sashpos(0, top_height)
                except:
                    pass  # Ignore errors if sash doesn't exist yet
        
        # Force a resize to update the layout
        self._handle_resize()
