"""
Test coordinate conversion between WGS84 and S-JTSK.

This script tests the coordinate conversion between WGS84 and S-JTSK
and compares the results with the expected values.
"""

import os
import sys
import logging
from typing import Dict, Any, <PERSON><PERSON>

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CoordinateTest")

# Import coordinate helpers
from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk, convert_sjtsk_to_wgs84


def test_known_locations():
    """Test coordinate conversion for known locations."""
    # Define known locations with their WGS84 and S-JTSK coordinates
    known_locations = [
        {
            'name': 'Prague (Václavské náměstí)',
            'wgs84': (50.0811, 14.4280),
            'sjtsk': (-743000, -1043000)  # Approximate values
        },
        {
            'name': '<PERSON><PERSON>',
            'wgs84': (49.1951, 16.6068),
            'sjtsk': (-598000, -1160000)  # Approximate values
        },
        {
            'name': 'Bušovice',
            'wgs84': (49.7955, 13.5347),
            'sjtsk': (-743030, -1032898)  # From our test
        },
        {
            'name': 'Klecany',
            'wgs84': (50.1754, 14.4141),
            'sjtsk': (-807231, -1075185)  # From our test
        }
    ]
    
    print("\n=== Testing Known Locations ===")
    for location in known_locations:
        print(f"\nLocation: {location['name']}")
        print(f"WGS84: {location['wgs84'][0]}, {location['wgs84'][1]}")
        print(f"Expected S-JTSK: {location['sjtsk'][0]}, {location['sjtsk'][1]}")
        
        # Convert WGS84 to S-JTSK
        sjtsk_x, sjtsk_y = convert_wgs84_to_sjtsk(location['wgs84'][0], location['wgs84'][1])
        print(f"Converted S-JTSK: {sjtsk_x}, {sjtsk_y}")
        
        # Calculate difference
        x_diff = abs(sjtsk_x - location['sjtsk'][0])
        y_diff = abs(sjtsk_y - location['sjtsk'][1])
        print(f"Difference: x_diff={x_diff:.2f}, y_diff={y_diff:.2f}")
        
        # Convert back to WGS84
        wgs84_lat, wgs84_lng = convert_sjtsk_to_wgs84(sjtsk_x, sjtsk_y)
        print(f"Reconverted WGS84: {wgs84_lat}, {wgs84_lng}")
        
        # Calculate difference
        lat_diff = abs(wgs84_lat - location['wgs84'][0])
        lng_diff = abs(wgs84_lng - location['wgs84'][1])
        print(f"Difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")
        
        # Generate URLs
        google_maps_url = f"https://www.google.com/maps/search/?api=1&query={location['wgs84'][0]},{location['wgs84'][1]}"
        cuzk_url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={int(sjtsk_x)}&y={int(sjtsk_y)}"
        
        print(f"Google Maps URL: {google_maps_url}")
        print(f"CUZK URL: {cuzk_url}")


def test_coordinate_grid():
    """Test coordinate conversion for a grid of coordinates across the Czech Republic."""
    # Define a grid of WGS84 coordinates covering the Czech Republic
    lat_min, lat_max = 48.5, 51.0  # Latitude range for Czech Republic
    lng_min, lng_max = 12.0, 18.8  # Longitude range for Czech Republic
    step = 0.5  # Grid step in degrees
    
    print("\n=== Testing Coordinate Grid ===")
    for lat in [lat_min + i * step for i in range(int((lat_max - lat_min) / step) + 1)]:
        for lng in [lng_min + i * step for i in range(int((lng_max - lng_min) / step) + 1)]:
            print(f"\nWGS84: {lat}, {lng}")
            
            # Convert WGS84 to S-JTSK
            sjtsk_x, sjtsk_y = convert_wgs84_to_sjtsk(lat, lng)
            print(f"S-JTSK: {sjtsk_x}, {sjtsk_y}")
            
            # Convert back to WGS84
            wgs84_lat, wgs84_lng = convert_sjtsk_to_wgs84(sjtsk_x, sjtsk_y)
            print(f"Reconverted WGS84: {wgs84_lat}, {wgs84_lng}")
            
            # Calculate difference
            lat_diff = abs(wgs84_lat - lat)
            lng_diff = abs(wgs84_lng - lng)
            print(f"Difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")
            
            # Check if the difference is significant
            if lat_diff > 0.001 or lng_diff > 0.001:
                print(f"WARNING: Significant difference detected!")


def test_specific_coordinates():
    """Test specific coordinates that have been problematic."""
    # Define specific coordinates to test
    coordinates = [
        {
            'name': 'Václavské náměstí (Google Maps)',
            'wgs84': (50.0811, 14.4280)
        },
        {
            'name': 'Bušovice (Google Maps)',
            'wgs84': (49.7955, 13.5347)
        },
        {
            'name': 'Klecany (Google Maps)',
            'wgs84': (50.1754, 14.4141)
        }
    ]
    
    print("\n=== Testing Specific Coordinates ===")
    for coord in coordinates:
        print(f"\nCoordinate: {coord['name']}")
        print(f"WGS84: {coord['wgs84'][0]}, {coord['wgs84'][1]}")
        
        # Convert WGS84 to S-JTSK
        sjtsk_x, sjtsk_y = convert_wgs84_to_sjtsk(coord['wgs84'][0], coord['wgs84'][1])
        print(f"S-JTSK: {sjtsk_x}, {sjtsk_y}")
        
        # Convert back to WGS84
        wgs84_lat, wgs84_lng = convert_sjtsk_to_wgs84(sjtsk_x, sjtsk_y)
        print(f"Reconverted WGS84: {wgs84_lat}, {wgs84_lng}")
        
        # Calculate difference
        lat_diff = abs(wgs84_lat - coord['wgs84'][0])
        lng_diff = abs(wgs84_lng - coord['wgs84'][1])
        print(f"Difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")
        
        # Generate URLs
        google_maps_url = f"https://www.google.com/maps/search/?api=1&query={coord['wgs84'][0]},{coord['wgs84'][1]}"
        cuzk_url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={int(sjtsk_x)}&y={int(sjtsk_y)}"
        
        print(f"Google Maps URL: {google_maps_url}")
        print(f"CUZK URL: {cuzk_url}")


def main():
    """Main function to run the coordinate conversion tests."""
    print("=== Coordinate Conversion Test ===")
    
    # Test known locations
    test_known_locations()
    
    # Test specific coordinates
    test_specific_coordinates()
    
    # Uncomment to test a grid of coordinates (this will generate a lot of output)
    # test_coordinate_grid()


if __name__ == "__main__":
    main()
