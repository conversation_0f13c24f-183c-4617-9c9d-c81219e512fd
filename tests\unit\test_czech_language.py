#!/usr/bin/env python3
"""
Test script for Czech language handling in the Google Maps integration.
This script tests the geocoding and place predictions functionality with Czech language input.
"""

from api.google_maps.google_maps_integration import GoogleMapsIntegration

def test_czech_region_mapping():
    """Test the Czech region name mapping"""
    print("\n=== Testing Czech Region Name Mapping ===")
    
    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()
    
    # Get the region name mapping
    region_mapping = google_maps.get_region_name_mapping()
    
    # Test some Czech region names
    test_names = [
        "praha",
        "brno",
        "ostrava",
        "plzeň",
        "liberec",
        "olomouc",
        "ústí nad labem",
        "hradec králové",
        "české budějovice",
        "pardubice",
        "zlín",
        "jihlava",
        "karlovy vary"
    ]
    
    for name in test_names:
        english_name = region_mapping.get(name, "Not found")
        print(f"{name} -> {english_name}")

def test_geocoding_czech_names():
    """Test geocoding with Czech language input"""
    print("\n=== Testing Geocoding with Czech Names ===")
    
    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()
    
    # Test addresses
    test_addresses = [
        "Praha",
        "Brno",
        "Ostrava",
        "Plzeň",
        "Liberec",
        "Olomouc",
        "Ústí nad Labem",
        "Hradec Králové",
        "České Budějovice",
        "Pardubice",
        "Zlín",
        "Jihlava",
        "Karlovy Vary"
    ]
    
    results = {}
    for address in test_addresses:
        print(f"\nGeocoding '{address}'...")
        result = google_maps.geocode_address(address)
        results[address] = result
        
        if result:
            print(f"Success: {result['formatted_address']}")
            print(f"Coordinates: {result['lat']}, {result['lng']}")
        else:
            print(f"Failed to geocode '{address}'")
    
    # Summary
    print("\n=== Geocoding Summary ===")
    for address, result in results.items():
        status = "OK" if result else "FAILED"
        print(f"{address}: {status}")

def test_place_predictions_czech_names():
    """Test place predictions with Czech language input"""
    print("\n=== Testing Place Predictions with Czech Names ===")
    
    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()
    
    # Test queries
    test_queries = [
        "Praha",
        "Brno",
        "Ostrava",
        "Plzeň",
        "Liberec",
        "Olomouc",
        "Ústí nad Labem",
        "Hradec Králové",
        "České Budějovice",
        "Pardubice",
        "Zlín",
        "Jihlava",
        "Karlovy Vary"
    ]
    
    results = {}
    for query in test_queries:
        print(f"\nGetting place predictions for '{query}'...")
        predictions = google_maps.get_place_predictions(query)
        results[query] = predictions
        
        if predictions:
            print(f"Found {len(predictions)} predictions:")
            for i, prediction in enumerate(predictions[:3]):  # Show only first 3
                print(f"  {i+1}. {prediction.get('description')}")
        else:
            print(f"No predictions found for '{query}'")
    
    # Summary
    print("\n=== Place Predictions Summary ===")
    for query, predictions in results.items():
        status = f"OK ({len(predictions)} results)" if predictions else "FAILED"
        print(f"{query}: {status}")

def test_get_cities_in_region_czech_names():
    """Test getting cities in a region with Czech language input"""
    print("\n=== Testing Get Cities in Region with Czech Names ===")
    
    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()
    
    # Test regions
    test_regions = [
        "Praha",
        "Brno",
        "Ostrava",
        "Plzeň",
        "Liberec",
        "Olomouc",
        "Ústí nad Labem",
        "Hradec Králové",
        "České Budějovice",
        "Pardubice",
        "Zlín",
        "Jihlava",
        "Karlovy Vary"
    ]
    
    results = {}
    for region in test_regions:
        print(f"\nGetting cities in region '{region}'...")
        cities = google_maps.get_cities_in_region(region)
        results[region] = cities
        
        if cities:
            print(f"Found {len(cities)} cities:")
            for i, city in enumerate(cities[:5]):  # Show only first 5
                print(f"  {i+1}. {city}")
        else:
            print(f"No cities found for region '{region}'")
    
    # Summary
    print("\n=== Get Cities in Region Summary ===")
    for region, cities in results.items():
        status = f"OK ({len(cities)} cities)" if cities else "FAILED"
        print(f"{region}: {status}")

def main():
    """Main function"""
    print("Czech Language Handling Test")
    print("===========================")
    
    # Test Czech region name mapping
    test_czech_region_mapping()
    
    # Test geocoding with Czech language input
    test_geocoding_czech_names()
    
    # Test place predictions with Czech language input
    test_place_predictions_czech_names()
    
    # Test getting cities in a region with Czech language input
    test_get_cities_in_region_czech_names()

if __name__ == "__main__":
    main()
