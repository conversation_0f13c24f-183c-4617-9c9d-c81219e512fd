"""
Service manager for the Czech Property Scraper application.
Handles initialization and management of services.
"""

import logging

from services.letter_generator import LetterGenerator
from services.property_service import PropertyService
from api.property.property_search import PropertySearch
from api.cuzk.cuzk_scraper_methods import CUZKScraperMethods
from api.osm.osm_methods import OSMMethods
from api.property.batch_search_methods import BatchSearchMethods
from examples.property_examples import PropertyExamples
from core.property_search_manager import PropertySearchManager
from core.osm_manager import OSMManager
from core.batch_search_manager import BatchSearchManager
from core.batch_processing_manager import BatchProcessingManager
from core.letter_manager import LetterManager

# Set up logging
logger = logging.getLogger(__name__)


class ServiceManager:
    """Manages the services of the application."""

    def __init__(self, app):
        """
        Initialize the service manager.

        Args:
            app: The main application instance
        """
        self.app = app

        # Initialize services
        self.initialize_services()

    def initialize_services(self):
        """Initialize all services."""
        # Initialize services with special parameters
        self.app.letter_generator = LetterGenerator(root=self.app.root, gpt_integration=self.app.gpt)

        # Initialize services that take only the app parameter
        services = {
            'property_service': PropertyService,
            'property_search': PropertySearch,
            'cuzk_scraper_methods': CUZKScraperMethods,
            'osm_methods': OSMMethods,
            'batch_search_methods': BatchSearchMethods,
            'examples': PropertyExamples,
            'property_search_manager': PropertySearchManager,
            'osm_manager': OSMManager,
            'batch_search_manager': BatchSearchManager,
            'batch_processing_manager': BatchProcessingManager,
            'letter_manager': LetterManager
        }

        logger.info("Initializing services")

        # Initialize all services
        for attr_name, service_class in services.items():
            setattr(self.app, attr_name, service_class(self.app))
