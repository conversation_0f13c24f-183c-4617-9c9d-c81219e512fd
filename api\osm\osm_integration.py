"""
OpenStreetMap integration for the Czech Property Registry application.

This module provides functionality for interacting with OpenStreetMap,
including fetching buildings with RUIAN references.
"""

import time
import threading
import inspect
import logging
import tkinter as tk
from tkinter import ttk
from ui.message_boxes import MessageBoxes

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OSMIntegration")


class OSMIntegration:
    """
    Class for handling OpenStreetMap integration.

    This class provides methods for fetching buildings and other
    property data from OpenStreetMap.
    """

    def __init__(self, app):
        """
        Initialize the OSM integration.

        Args:
            app: The main application instance for callbacks and UI access
        """
        self.app = app
        # Use the optimized session from the app
        self.session = app.session
        logger.info("OSMIntegration initialized")

    def fetch_buildings_by_coordinates(self, lat, lng, radius=500):
        """
        Fetch buildings with RUIAN references from OpenStreetMap.

        Args:
            lat (float): Latitude
            lng (float): Longitude
            radius (int): Search radius in meters

        Returns:
            list: List of buildings with RUIAN references
        """
        try:
            # Show status
            self.app.show_status(f"Fetching buildings from OpenStreetMap at coordinates {lat}, {lng}...")
            logger.info(f"Fetching OSM buildings at coordinates {lat}, {lng} with radius {radius}m")

            # Check if we have cached results
            cache_key = f"{round(float(lat), 5)}_{round(float(lng), 5)}_{radius}"
            cached_buildings = self.app.cache_manager.get('osm_buildings', cache_key)
            if cached_buildings:
                logger.info(f"Using cached OSM buildings for {cache_key}")
                self.app.show_status(f"Using cached data: Found {len(cached_buildings)} buildings with RUIAN references.")
                return cached_buildings

            # Construct the Overpass API query
            # This query searches for buildings with RUIAN references within the specified radius
            overpass_url = "https://overpass-api.de/api/interpreter"
            overpass_query = f"""
            [out:json];
            (
              node["ref:ruian"](around:{radius},{lat},{lng});
              way["ref:ruian"](around:{radius},{lat},{lng});
              relation["ref:ruian"](around:{radius},{lat},{lng});
            );
            out body;
            >;
            out skel qt;
            """

            # Make the request using the optimized session
            logger.info(f"Sending request to Overpass API")
            response = self.session.post(overpass_url, data={"data": overpass_query})

            # Check if the request was successful
            if response.status_code != 200:
                logger.error(f"Error fetching buildings from OpenStreetMap: {response.status_code}")
                return []

            # Parse the response
            data = response.json()
            logger.info(f"Received response from Overpass API with {len(data.get('elements', []))} elements")

            # Extract buildings with RUIAN references
            buildings = []

            for element in data.get('elements', []):
                # Check if the element has a RUIAN reference
                if 'tags' in element and 'ref:ruian' in element['tags']:
                    # Extract the RUIAN reference
                    ruian_ref = element['tags']['ref:ruian']

                    # Extract other useful information
                    building_info = {
                        'ruian_ref': ruian_ref,
                        'osm_id': element['id'],
                        'osm_type': element['type'],
                        'lat': element.get('lat', lat),  # Use the search lat if not available
                        'lng': element.get('lon', lng),  # Use the search lng if not available
                        'tags': element.get('tags', {}),
                        'building_type': element.get('tags', {}).get('building', 'unknown'),
                        'name': element.get('tags', {}).get('name', ''),
                        'address': self._extract_address_from_tags(element.get('tags', {}))
                    }

                    # Add to the list
                    buildings.append(building_info)

            # Cache the results
            if buildings:
                logger.info(f"Caching {len(buildings)} buildings for {cache_key}")
                self.app.cache_manager.set('osm_buildings', cache_key, buildings)

            # Show status
            self.app.show_status(f"Found {len(buildings)} buildings with RUIAN references.")
            logger.info(f"Found {len(buildings)} buildings with RUIAN references")

            return buildings

        except Exception as e:
            logger.error(f"Error fetching buildings from OpenStreetMap: {e}", exc_info=True)
            return []

    def search_buildings_by_coordinates(self, lat, lng, radius=500):
        """
        Search for buildings at specific coordinates.

        Args:
            lat (float): Latitude
            lng (float): Longitude
            radius (int): Search radius in meters

        Returns:
            list: List of buildings with RUIAN references (when called from OSMMethods)
        """
        try:
            # Show status
            self.app.show_status(f"Searching for buildings at coordinates {lat}, {lng}...")
            logger.info(f"Searching for buildings at coordinates {lat}, {lng} with radius {radius}m")

            # Check if this is being called from OSMMethods (which expects a return value)
            caller_frame = inspect.currentframe().f_back
            caller_function = caller_frame.f_code.co_name if caller_frame else None

            # If called from fetch_properties_from_osm, return the buildings directly
            if caller_function == 'fetch_properties_from_osm':
                return self.fetch_buildings_by_coordinates(lat, lng, radius)

            # Otherwise, use the thread manager to avoid freezing the UI
            task_id = f"osm_search_{lat}_{lng}_{radius}"

            def search_task():
                try:
                    # Search for buildings with RUIAN references in OpenStreetMap
                    buildings = self.fetch_buildings_by_coordinates(lat, lng, radius)

                    # Update the UI in the main thread
                    location_name = f"coordinates {lat}, {lng}"
                    self.app.root.after(0, lambda: self.process_buildings(buildings, lat, lng, location_name))
                    return buildings
                except Exception as e:
                    logger.error(f"Error searching for buildings in OpenStreetMap: {e}", exc_info=True)
                    self.app.root.after(0, lambda: MessageBoxes.show_error("Search Error", f"Error searching for buildings: {str(e)}"))
                    self.app.show_status("Ready")
                    return []

            # Submit the task to the thread manager with high priority
            self.app.thread_manager.submit(task_id, search_task, priority=1)

        except Exception as e:
            logger.error(f"Error searching for buildings: {e}", exc_info=True)
            MessageBoxes.show_error("Search Error", f"Error searching for buildings: {str(e)}")
            self.app.show_status("Ready")

    def search_buildings_by_address(self, address):
        """
        Search for buildings at a specific address.

        Args:
            address (str): Address to search for
        """
        try:
            # Show status
            self.app.show_status(f"Searching for buildings at {address}...")
            logger.info(f"Searching for buildings at address: {address}")

            # Geocode the address to get coordinates
            if hasattr(self.app, 'google_maps'):
                # Check cache first
                cache_key = f"geocode_{address}"
                geocode_result = self.app.cache_manager.get('address_suggestions', cache_key)

                if not geocode_result:
                    logger.info(f"Geocoding address: {address}")
                    geocode_result = self.app.google_maps.geocode_address(address)

                    # Cache the result if successful
                    if geocode_result:
                        logger.info(f"Caching geocode result for {address}")
                        self.app.cache_manager.set('address_suggestions', cache_key, geocode_result)
                else:
                    logger.info(f"Using cached geocode result for {address}")

                if geocode_result:
                    lat = geocode_result['lat']
                    lng = geocode_result['lng']
                    logger.info(f"Geocoded {address} to coordinates {lat}, {lng}")

                    # Show the coordinates on the map
                    self.app.show_embedded_map(lat, lng)

                    # Use the thread manager to avoid freezing the UI
                    task_id = f"osm_address_search_{address}"

                    def search_task():
                        try:
                            # Search for buildings with RUIAN references in OpenStreetMap
                            buildings = self.fetch_buildings_by_coordinates(lat, lng, 500)  # 500m radius

                            # Update the UI in the main thread
                            self.app.root.after(0, lambda: self.process_buildings(buildings, lat, lng, address))
                            return buildings
                        except Exception as e:
                            logger.error(f"Error searching for buildings in OpenStreetMap: {e}", exc_info=True)
                            self.app.root.after(0, lambda: MessageBoxes.show_error("Search Error", f"Error searching for buildings: {str(e)}"))
                            self.app.show_status("Ready")
                            return []

                    # Submit the task to the thread manager with high priority
                    self.app.thread_manager.submit(task_id, search_task, priority=1)
                else:
                    logger.warning(f"Could not geocode address: {address}")
                    MessageBoxes.show_error("Geocoding Error", f"Could not geocode address: {address}")
                    self.app.show_status("Ready")
            else:
                logger.error("Google Maps integration not available")
                MessageBoxes.show_error("Error", "Google Maps integration not available.")
                self.app.show_status("Ready")

        except Exception as e:
            logger.error(f"Error searching for buildings by address: {e}", exc_info=True)
            MessageBoxes.show_error("Search Error", f"Error searching for buildings: {str(e)}")
            self.app.show_status("Ready")

    def process_buildings(self, buildings, lat, lng, location_name):
        """
        Process the buildings found in OpenStreetMap.

        Args:
            buildings (list): List of buildings with RUIAN references
            lat (float): Latitude
            lng (float): Longitude
            location_name (str): Name of the location (address or coordinates)
        """
        # Update the map with the location if available
        if hasattr(self.app, 'show_embedded_map'):
            self.app.show_embedded_map(lat, lng)
        try:
            # Show status
            self.app.show_status(f"Processing {len(buildings)} buildings...")

            # Clear the results frame
            for widget in self.app.results_frame.winfo_children():
                widget.destroy()

            # If no buildings were found, show a message
            if not buildings or len(buildings) == 0:
                # Create a label with tk styling
                label_frame = tk.Frame(self.app.results_frame)
                label_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

                ttk.Label(
                    label_frame,
                    text=f"No buildings with RUIAN references found at {location_name}.",
                    font=("Arial", 12, "bold"),
                    foreground="red"
                ).pack(pady=20)

                # Show a message box
                MessageBoxes.show_info(
                    "No Buildings Found",
                    f"No buildings with RUIAN references were found at {location_name}.\n\n"
                    "Try searching in a different location or increasing the search radius."
                )

                self.app.show_status("Ready")
                return

            # Create a frame for the building list
            building_frame = ttk.Frame(self.app.results_frame)
            building_frame.pack(fill="both", expand=True, padx=5, pady=5)

            # Create a treeview for the buildings
            self.app.building_tree = ttk.Treeview(
                building_frame,
                columns=("ruian_ref", "type", "name", "actions"),
                show="headings",
                height=10
            )

            # Define column headings
            self.app.building_tree.heading("ruian_ref", text="RUIAN Reference")
            self.app.building_tree.heading("type", text="Type")
            self.app.building_tree.heading("name", text="Name")
            self.app.building_tree.heading("actions", text="Actions")

            # Define column widths
            self.app.building_tree.column("ruian_ref", width=150)
            self.app.building_tree.column("type", width=100)
            self.app.building_tree.column("name", width=200)
            self.app.building_tree.column("actions", width=100)

            # Add a scrollbar
            scrollbar = ttk.Scrollbar(building_frame, orient="vertical", command=self.app.building_tree.yview)
            self.app.building_tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side="right", fill="y")
            self.app.building_tree.pack(side="left", fill="both", expand=True)

            # Add touchpad scrolling support for the building tree
            if hasattr(self.app, 'scroll_manager'):
                self.app.building_tree.bind("<MouseWheel>",
                    lambda event: self.app.scroll_manager._on_treeview_mousewheel(event))  # Windows
                self.app.building_tree.bind("<Button-4>",
                    lambda event: self.app.scroll_manager._on_treeview_mousewheel(event))  # Linux scroll up
                self.app.building_tree.bind("<Button-5>",
                    lambda event: self.app.scroll_manager._on_treeview_mousewheel(event))  # Linux scroll down

            # Store the buildings for later use
            self.app.osm_buildings = buildings

            # Add buildings to the treeview
            for building in buildings:
                ruian_ref = building.get('ruian_ref', 'Unknown')
                building_type = building.get('tags', {}).get('building', 'Unknown')
                building_name = building.get('tags', {}).get('name', '')

                self.app.building_tree.insert("", "end", values=(
                    ruian_ref,
                    building_type,
                    building_name,
                    "Get Owner Info"
                ))

            # Add a button to get owner info for all buildings
            button_frame = ttk.Frame(self.app.results_frame)
            button_frame.pack(fill="x", padx=5, pady=5)

            ttk.Button(
                button_frame,
                text="Get Owner Info for All",
                command=self.get_owner_info_for_all
            ).pack(side="left", padx=5)

            ttk.Button(
                button_frame,
                text="Get Owner Info for Selected",
                command=self.get_owner_info_for_selected
            ).pack(side="right", padx=5)

            # Add a click handler for the treeview
            self.app.building_tree.bind("<ButtonRelease-1>", self.on_building_tree_click)

            # Show status
            self.app.show_status(f"Found {len(buildings)} buildings with RUIAN references.")

        except Exception as e:
            print(f"Error processing buildings: {e}")
            MessageBoxes.show_error("Error", f"An error occurred while processing buildings: {str(e)}")
            self.app.show_status("Ready")

    def on_building_tree_click(self, event):
        """
        Handle clicks on the building treeview.

        Args:
            event: The click event
        """
        try:
            # Get the clicked item
            item_id = self.app.building_tree.identify_row(event.y)
            if not item_id:
                return

            # Get the clicked column
            column_id = self.app.building_tree.identify_column(event.x)
            column_index = int(column_id.replace('#', '')) - 1

            # Get the building data
            values = self.app.building_tree.item(item_id, 'values')

            # Check if the Actions column was clicked
            if column_index == 3:  # Actions column
                # Get the RUIAN reference
                ruian_ref = values[0]

                # Get owner info for this building
                self.get_owner_info_for_ruian(ruian_ref)

        except Exception as e:
            print(f"Error handling building tree click: {e}")

    def get_owner_info_for_ruian(self, ruian_ref):
        """
        Get owner information for a building with a specific RUIAN reference.

        Args:
            ruian_ref (str): RUIAN reference
        """
        try:
            # Show status
            self.app.show_status(f"Getting owner info for RUIAN reference {ruian_ref}...")

            # Use the CUZK scraper to get owner info
            if hasattr(self.app, 'cuzk_scraper'):
                # Use threading to avoid freezing the UI
                def scrape_thread():
                    try:
                        # Get owner info from CUZK
                        property_data = self.app.cuzk_scraper.search_by_ruian(ruian_ref)

                        # Update the UI in the main thread
                        self.app.root.after(0, lambda: self.display_property_data(property_data))
                    except Exception as e:
                        print(f"Error getting owner info from CUZK: {e}")
                        self.app.root.after(0, lambda: MessageBoxes.show_error("Scraping Error", f"Error getting owner info: {str(e)}"))
                        self.app.show_status("Ready")

                # Start the thread
                threading.Thread(target=scrape_thread, daemon=True).start()
            else:
                MessageBoxes.show_error("Error", "CUZK scraper not available.")
                self.app.show_status("Ready")

        except Exception as e:
            print(f"Error getting owner info: {e}")
            MessageBoxes.show_error("Error", f"An error occurred while getting owner info: {str(e)}")
            self.app.show_status("Ready")

    def get_owner_info_for_selected(self):
        """Get owner information for the selected building"""
        try:
            # Get the selected building
            if not hasattr(self.app, 'building_tree'):
                MessageBoxes.show_info("No Selection", "Please select a building first.")
                return

            selected_items = self.app.building_tree.selection()
            if not selected_items:
                MessageBoxes.show_info("No Selection", "Please select a building first.")
                return

            # Get the RUIAN reference
            values = self.app.building_tree.item(selected_items[0], 'values')
            ruian_ref = values[0]

            # Get owner info for this building
            self.get_owner_info_for_ruian(ruian_ref)

        except Exception as e:
            print(f"Error getting owner info for selected building: {e}")
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")

    def get_owner_info_for_all(self):
        """Get owner information for all buildings"""
        try:
            # Check if we have buildings
            if not hasattr(self.app, 'osm_buildings') or not self.app.osm_buildings:
                MessageBoxes.show_info("No Buildings", "No buildings found.")
                return

            # Ask for confirmation
            if not MessageBoxes.ask_yes_no(
                "Confirm",
                f"This will fetch owner information for {len(self.app.osm_buildings)} buildings.\n\n"
                "This may take some time and could trigger rate limits.\n\n"
                "Do you want to continue?"
            ):
                return

            # Show status
            self.app.show_status(f"Getting owner info for {len(self.app.osm_buildings)} buildings...")

            # Use the CUZK scraper to get owner info
            if hasattr(self.app, 'cuzk_scraper'):
                # Use threading to avoid freezing the UI
                def scrape_thread():
                    try:
                        # Get owner info for each building
                        property_data_list = []

                        for i, building in enumerate(self.app.osm_buildings):
                            # Update status
                            self.app.root.after(0, lambda: self.app.show_status(
                                f"Getting owner info for building {i+1} of {len(self.app.osm_buildings)}..."
                            ))

                            # Get the RUIAN reference
                            ruian_ref = building.get('ruian_ref')

                            # Get owner info from CUZK
                            property_data = self.app.cuzk_scraper.search_by_ruian(ruian_ref)

                            if property_data:
                                property_data_list.append(property_data)

                            # Sleep to avoid rate limiting
                            time.sleep(1)

                        # Update the UI in the main thread
                        self.app.root.after(0, lambda: self.display_property_data_list(property_data_list))
                    except Exception as e:
                        print(f"Error getting owner info from CUZK: {e}")
                        self.app.root.after(0, lambda: MessageBoxes.show_error("Scraping Error", f"Error getting owner info: {str(e)}"))
                        self.app.show_status("Ready")

                # Start the thread
                threading.Thread(target=scrape_thread, daemon=True).start()
            else:
                MessageBoxes.show_error("Error", "CUZK scraper not available.")
                self.app.show_status("Ready")

        except Exception as e:
            print(f"Error getting owner info for all buildings: {e}")
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.app.show_status("Ready")

    def display_property_data(self, property_data):
        """
        Display property data.

        Args:
            property_data (dict): Property data dictionary
        """
        try:
            # Show status
            self.app.show_status("Displaying property data...")

            # If no property data was found, show a message
            if not property_data:
                MessageBoxes.show_info("No Data", "No property data found.")
                self.app.show_status("Ready")
                return

            # Use the property service to display the property
            if hasattr(self.app, 'property_service'):
                self.app.property_service.display_properties([property_data])
            else:
                # Fallback to the app's display method
                if hasattr(self.app, 'display_properties'):
                    self.app.display_properties([property_data])
                else:
                    MessageBoxes.show_info("Not Implemented", "Property display is not implemented.")

            # Show status
            self.app.show_status("Ready")

        except Exception as e:
            print(f"Error displaying property data: {e}")
            MessageBoxes.show_error("Error", f"An error occurred while displaying property data: {str(e)}")
            self.app.show_status("Ready")

    def _extract_address_from_tags(self, tags):
        """
        Extract address from OSM tags.

        Args:
            tags (dict): OSM tags

        Returns:
            str: Formatted address
        """
        address_parts = []

        # Extract street
        if 'addr:street' in tags:
            address_parts.append(tags['addr:street'])

        # Extract house number
        if 'addr:housenumber' in tags:
            address_parts.append(tags['addr:housenumber'])

        # Extract city
        if 'addr:city' in tags:
            address_parts.append(tags['addr:city'])

        # Extract postal code
        if 'addr:postcode' in tags:
            address_parts.append(tags['addr:postcode'])

        # If we have an address, join the parts
        if address_parts:
            return ' '.join(address_parts)

        # If we don't have an address but have a name, use that
        if 'name' in tags:
            return tags['name']

        # If we don't have an address or name, return empty string
        return ''

    def display_property_data_list(self, property_data_list):
        """
        Display a list of property data.

        Args:
            property_data_list (list): List of property data dictionaries
        """
        try:
            # Show status
            self.app.show_status(f"Displaying {len(property_data_list)} properties...")

            # If no property data was found, show a message
            if not property_data_list or len(property_data_list) == 0:
                MessageBoxes.show_info("No Data", "No property data found.")
                self.app.show_status("Ready")
                return

            # Use the property service to display the properties
            if hasattr(self.app, 'property_service'):
                self.app.property_service.display_properties(property_data_list)
            else:
                # Fallback to the app's display method
                if hasattr(self.app, 'display_properties'):
                    self.app.display_properties(property_data_list)
                else:
                    MessageBoxes.show_info("Not Implemented", "Property display is not implemented.")

            # Show status
            self.app.show_status("Ready")

        except Exception as e:
            print(f"Error displaying property data list: {e}")
            MessageBoxes.show_error("Error", f"An error occurred while displaying property data: {str(e)}")
            self.app.show_status("Ready")
