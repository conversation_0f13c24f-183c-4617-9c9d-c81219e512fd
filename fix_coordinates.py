"""
Fix coordinate conversion issues in the Czech Property Registry application.

This script clears all caches and ensures the coordinate conversion is consistent.
Run this script directly to fix coordinate conversion issues.
"""

import os
import shutil
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_cache_directories():
    """Clear all cache directories."""
    cache_dirs = [
        'cache',
        '.cache',
        'czech_data/cache',
        'data/cache',
    ]
    
    cleared_count = 0
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir) and os.path.isdir(cache_dir):
            try:
                # Remove the directory and all its contents
                shutil.rmtree(cache_dir)
                logger.info(f"Cleared cache directory: {cache_dir}")
                cleared_count += 1
                
                # Recreate the empty directory
                os.makedirs(cache_dir, exist_ok=True)
            except Exception as e:
                logger.error(f"Error clearing cache directory {cache_dir}: {e}")
    
    return cleared_count

def clear_cache_files():
    """Clear all cache files."""
    cache_files = [
        'czech_data/cache.json',
        'czech_data/city_cache.json',
        'czech_data/address_cache.json',
        'czech_data/property_cache.json',
        'czech_data/suggestions_cache.json',
        'czech_data/encrypted_urls.json',
    ]
    
    cleared_count = 0
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            try:
                # Remove the file
                os.remove(cache_file)
                logger.info(f"Cleared cache file: {cache_file}")
                cleared_count += 1
            except Exception as e:
                logger.error(f"Error clearing cache file {cache_file}: {e}")
    
    return cleared_count

def restart_application():
    """Restart the main application."""
    try:
        # Get the path to the main.py file
        main_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
        
        # Check if the file exists
        if not os.path.exists(main_path):
            logger.error(f"Main file not found at: {main_path}")
            return False
        
        # Launch the application with the current Python interpreter
        cmd = [sys.executable, main_path]
        
        # Use Popen to launch the process without waiting for it to complete
        subprocess.Popen(cmd)
        
        return True
    except Exception as e:
        logger.error(f"Error restarting application: {e}")
        return False

def main():
    """Main function to fix coordinate conversion issues."""
    logger.info("Starting coordinate conversion fix...")
    
    # Clear cache directories
    dir_count = clear_cache_directories()
    logger.info(f"Cleared {dir_count} cache directories")
    
    # Clear cache files
    file_count = clear_cache_files()
    logger.info(f"Cleared {file_count} cache files")
    
    # Restart the application
    if restart_application():
        logger.info("Application restarted successfully")
    else:
        logger.error("Failed to restart application")
        logger.info("Please restart the application manually by running 'python main.py'")
    
    logger.info("Coordinate conversion fix completed")

if __name__ == "__main__":
    main()
