"""
Cache management utilities for the Czech Property Registry application.
"""

import threading
import time
from typing import Dict, Any, Tuple, List, Optional, Callable


class CacheManager:
    """
    Manages caching operations for the application.
    Provides a unified interface for caching and retrieving data with expiration.
    """

    def __init__(self, default_expiry: int = 300):
        """
        Initialize the cache manager.

        Args:
            default_expiry (int): Default cache expiry time in seconds (default: 300 seconds/5 minutes)
        """
        self._caches = {}
        self._locks = {}
        self._cache_expiry = {}  # Store custom expiry times for each cache
        self._default_expiry = default_expiry
        self._cleanup_scheduled = False

    def create_cache(self, cache_name: str, expiry: int = None) -> None:
        """
        Create a new cache with the given name and optional custom expiry time.

        Args:
            cache_name (str): Name of the cache to create
            expiry (int, optional): Custom expiry time in seconds for this cache
        """
        if cache_name not in self._caches:
            self._caches[cache_name] = {}
            self._locks[cache_name] = threading.Lock()
            # Set custom expiry time if provided, otherwise use default
            self._cache_expiry[cache_name] = expiry if expiry is not None else self._default_expiry

    def get(self, cache_name: str, key: str) -> Optional[Any]:
        """
        Get a value from the cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key

        Returns:
            Any: The cached value or None if not found or expired
        """
        if cache_name not in self._caches:
            return None

        with self._locks[cache_name]:
            if key in self._caches[cache_name]:
                value, expiry = self._caches[cache_name][key]
                if time.time() < expiry:
                    return value
                else:
                    # Remove expired entry
                    del self._caches[cache_name][key]
        return None

    def set(self, cache_name: str, key: str, value: Any, expiry: Optional[int] = None) -> None:
        """
        Set a value in the cache.

        Args:
            cache_name (str): Name of the cache
            key (str): Cache key
            value (Any): Value to cache
            expiry (int, optional): Expiry time in seconds. If None, uses cache-specific or default expiry.
        """
        if cache_name not in self._caches:
            self.create_cache(cache_name)

        # Use provided expiry time, or cache-specific expiry, or default expiry
        if expiry is None:
            expiry = self._cache_expiry.get(cache_name, self._default_expiry)

        with self._locks[cache_name]:
            self._caches[cache_name][key] = (value, time.time() + expiry)

        # Schedule cleanup if not already scheduled
        if not self._cleanup_scheduled:
            self.schedule_cleanup()

    def get_prefix_matches(self, cache_name: str, prefix: str, min_length: int = 3) -> List[Any]:
        """
        Get all values from the cache where the key starts with the given prefix.

        Args:
            cache_name (str): Name of the cache
            prefix (str): Prefix to match
            min_length (int): Minimum length of prefix to consider

        Returns:
            List[Any]: List of matching values
        """
        if cache_name not in self._caches or len(prefix) < min_length:
            return []

        matches = []
        current_time = time.time()

        with self._locks[cache_name]:
            for key, (value, expiry) in self._caches[cache_name].items():
                if key.startswith(prefix) and current_time < expiry:
                    matches.append(value)

        return matches

    def clear(self, cache_name: str) -> int:
        """
        Clear a specific cache.

        Args:
            cache_name (str): Name of the cache to clear

        Returns:
            int: Number of items cleared
        """
        if cache_name not in self._caches:
            return 0

        with self._locks[cache_name]:
            count = len(self._caches[cache_name])
            self._caches[cache_name].clear()
            return count

    def clear_all(self) -> Dict[str, int]:
        """
        Clear all caches.

        Returns:
            Dict[str, int]: Dictionary mapping cache names to number of items cleared
        """
        result = {}
        for cache_name in self._caches:
            result[cache_name] = self.clear(cache_name)
        return result

    def cleanup(self) -> Dict[str, int]:
        """
        Remove expired entries from all caches.

        Returns:
            Dict[str, int]: Dictionary mapping cache names to number of items removed
        """
        result = {}
        current_time = time.time()

        for cache_name in self._caches:
            with self._locks[cache_name]:
                count = 0
                keys_to_remove = []

                for key, (_, expiry) in self._caches[cache_name].items():
                    if current_time >= expiry:
                        keys_to_remove.append(key)
                        count += 1

                for key in keys_to_remove:
                    del self._caches[cache_name][key]

                result[cache_name] = count

        return result

    def schedule_cleanup(self, interval: int = 300) -> None:
        """
        Schedule periodic cache cleanup.

        Args:
            interval (int): Cleanup interval in seconds (default: 300 seconds/5 minutes)
        """
        self._cleanup_scheduled = True

        def _cleanup():
            self.cleanup()
            # Schedule next cleanup
            threading.Timer(interval, _cleanup).start()

        # Start the cleanup timer
        threading.Timer(interval, _cleanup).start()

    def get_stats(self) -> Dict[str, Dict[str, int]]:
        """
        Get statistics about the caches.

        Returns:
            Dict[str, Dict[str, int]]: Dictionary with cache statistics
        """
        stats = {}
        current_time = time.time()

        for cache_name in self._caches:
            with self._locks[cache_name]:
                total = len(self._caches[cache_name])
                expired = sum(1 for _, expiry in self._caches[cache_name].values() if current_time >= expiry)
                valid = total - expired

                stats[cache_name] = {
                    'total': total,
                    'valid': valid,
                    'expired': expired
                }

        return stats
