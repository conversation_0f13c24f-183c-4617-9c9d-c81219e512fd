"""
Centralized error handling for the Czech Property Registry application.

This module provides standardized error handling patterns and utilities
for consistent error handling throughout the application.
"""

import logging
import traceback
import sys
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

from ui.message_boxes import MessageBoxes

# Configure logging
logger = logging.getLogger(__name__)


class ErrorHandler:
    """
    Centralized error handler for the application.
    
    Provides standardized error handling patterns and utilities
    for consistent error handling throughout the application.
    """
    
    # Default error messages
    DEFAULT_ERROR_TITLE = "Error"
    DEFAULT_ERROR_MESSAGE = "An unexpected error occurred."
    DEFAULT_NETWORK_ERROR_MESSAGE = "A network error occurred. Please check your internet connection."
    DEFAULT_API_ERROR_MESSAGE = "An API error occurred. Please try again later."
    DEFAULT_DATA_ERROR_MESSAGE = "A data error occurred. Please check your input."
    DEFAULT_UI_ERROR_MESSAGE = "A UI error occurred. Please try again."
    
    @staticmethod
    def handle_exception(exception: Exception, title: str = None, message: str = None,
                        show_message_box: bool = True, log_exception: bool = True,
                        app=None) -> None:
        """
        Handle an exception with standardized logging and user notification.
        
        Args:
            exception (Exception): The exception to handle
            title (str, optional): The title for the error message box
            message (str, optional): The message for the error message box
            show_message_box (bool): Whether to show a message box to the user
            log_exception (bool): Whether to log the exception
            app (object, optional): The application instance for updating status
        """
        # Use default title if not provided
        if title is None:
            title = ErrorHandler.DEFAULT_ERROR_TITLE
            
        # Use default message if not provided
        if message is None:
            message = f"{ErrorHandler.DEFAULT_ERROR_MESSAGE}\n\nError: {str(exception)}"
        elif not message.endswith(str(exception)):
            # Add the exception message if it's not already included
            message = f"{message}\n\nError: {str(exception)}"
            
        # Log the exception
        if log_exception:
            logger.error(f"{title}: {message}", exc_info=True)
            
        # Show a message box to the user
        if show_message_box:
            MessageBoxes.show_exception(title, exception, message)
            
        # Update the application status if provided
        if app is not None and hasattr(app, 'show_status'):
            app.show_status(f"Error: {str(exception)}")
    
    @staticmethod
    def handle_network_exception(exception: Exception, title: str = None, message: str = None,
                               show_message_box: bool = True, log_exception: bool = True,
                               app=None) -> None:
        """
        Handle a network exception with standardized logging and user notification.
        
        Args:
            exception (Exception): The exception to handle
            title (str, optional): The title for the error message box
            message (str, optional): The message for the error message box
            show_message_box (bool): Whether to show a message box to the user
            log_exception (bool): Whether to log the exception
            app (object, optional): The application instance for updating status
        """
        # Use default title if not provided
        if title is None:
            title = "Network Error"
            
        # Use default message if not provided
        if message is None:
            message = f"{ErrorHandler.DEFAULT_NETWORK_ERROR_MESSAGE}\n\nError: {str(exception)}"
            
        # Handle the exception
        ErrorHandler.handle_exception(exception, title, message, show_message_box, log_exception, app)
    
    @staticmethod
    def handle_api_exception(exception: Exception, title: str = None, message: str = None,
                           show_message_box: bool = True, log_exception: bool = True,
                           app=None) -> None:
        """
        Handle an API exception with standardized logging and user notification.
        
        Args:
            exception (Exception): The exception to handle
            title (str, optional): The title for the error message box
            message (str, optional): The message for the error message box
            show_message_box (bool): Whether to show a message box to the user
            log_exception (bool): Whether to log the exception
            app (object, optional): The application instance for updating status
        """
        # Use default title if not provided
        if title is None:
            title = "API Error"
            
        # Use default message if not provided
        if message is None:
            message = f"{ErrorHandler.DEFAULT_API_ERROR_MESSAGE}\n\nError: {str(exception)}"
            
        # Handle the exception
        ErrorHandler.handle_exception(exception, title, message, show_message_box, log_exception, app)
    
    @staticmethod
    def handle_data_exception(exception: Exception, title: str = None, message: str = None,
                            show_message_box: bool = True, log_exception: bool = True,
                            app=None) -> None:
        """
        Handle a data exception with standardized logging and user notification.
        
        Args:
            exception (Exception): The exception to handle
            title (str, optional): The title for the error message box
            message (str, optional): The message for the error message box
            show_message_box (bool): Whether to show a message box to the user
            log_exception (bool): Whether to log the exception
            app (object, optional): The application instance for updating status
        """
        # Use default title if not provided
        if title is None:
            title = "Data Error"
            
        # Use default message if not provided
        if message is None:
            message = f"{ErrorHandler.DEFAULT_DATA_ERROR_MESSAGE}\n\nError: {str(exception)}"
            
        # Handle the exception
        ErrorHandler.handle_exception(exception, title, message, show_message_box, log_exception, app)
    
    @staticmethod
    def handle_ui_exception(exception: Exception, title: str = None, message: str = None,
                          show_message_box: bool = True, log_exception: bool = True,
                          app=None) -> None:
        """
        Handle a UI exception with standardized logging and user notification.
        
        Args:
            exception (Exception): The exception to handle
            title (str, optional): The title for the error message box
            message (str, optional): The message for the error message box
            show_message_box (bool): Whether to show a message box to the user
            log_exception (bool): Whether to log the exception
            app (object, optional): The application instance for updating status
        """
        # Use default title if not provided
        if title is None:
            title = "UI Error"
            
        # Use default message if not provided
        if message is None:
            message = f"{ErrorHandler.DEFAULT_UI_ERROR_MESSAGE}\n\nError: {str(exception)}"
            
        # Handle the exception
        ErrorHandler.handle_exception(exception, title, message, show_message_box, log_exception, app)
    
    @staticmethod
    def try_except_decorator(func: Callable) -> Callable:
        """
        Decorator for wrapping a function in a try-except block.
        
        Args:
            func (callable): The function to wrap
            
        Returns:
            callable: The wrapped function
        """
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                ErrorHandler.handle_exception(e)
                return None
        return wrapper
    
    @staticmethod
    def try_except_with_app_decorator(app) -> Callable:
        """
        Decorator factory for wrapping a function in a try-except block with app reference.
        
        Args:
            app: The application instance
            
        Returns:
            callable: A decorator function
        """
        def decorator(func: Callable) -> Callable:
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    ErrorHandler.handle_exception(e, app=app)
                    return None
            return wrapper
        return decorator
