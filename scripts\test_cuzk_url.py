"""
Test CUZK URL generation to verify if coordinates are correctly converted.

This script tests if the CUZK URL generated for an address actually points to the correct location.
It does this by:
1. Geocoding the address to get WGS84 coordinates
2. Converting to S-JTSK coordinates
3. Generating a CUZK URL
4. Optionally opening the URL in a browser for visual verification
"""

import os
import sys
import logging
import argparse
import webbrowser
from typing import Dict, Any, Optional

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CUZKURLTester")

# Import necessary modules
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from api.cuzk.cuzk_integration import CUZKIntegration
from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk, convert_sjtsk_to_wgs84


def load_api_key() -> Optional[str]:
    """Load Google Maps API key from config file."""
    import configparser
    config = configparser.ConfigParser()
    if os.path.exists('config.ini'):
        config.read('config.ini')
        if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
            return config['GOOGLE_MAPS']['api_key']
    return None


def test_cuzk_url(address: str, country: str = "Czech Republic", open_browser: bool = False) -> Dict[str, Any]:
    """
    Test CUZK URL generation for an address.
    
    Args:
        address (str): Address to test
        country (str): Country to use for geocoding
        open_browser (bool): Whether to open the URLs in a browser
        
    Returns:
        dict: Test results
    """
    # Initialize Google Maps and CUZK integration
    api_key = load_api_key()
    if not api_key:
        logger.error("Google Maps API key not found in config.ini")
        return {'error': 'Google Maps API key not found'}
    
    google_maps = GoogleMapsIntegration()
    cuzk_integration = CUZKIntegration()
    
    # Step 1: Geocode the address
    logger.info(f"Geocoding address: {address}, {country}")
    try:
        geocode_result = google_maps.geocode_address(address, country)
        if not geocode_result or 'lat' not in geocode_result or 'lng' not in geocode_result:
            logger.error(f"Failed to geocode address: {address}")
            return {'error': 'Geocoding failed'}
        
        lat = geocode_result['lat']
        lng = geocode_result['lng']
        formatted_address = geocode_result.get('formatted_address', address)
        logger.info(f"Geocoded to: {lat}, {lng}")
        logger.info(f"Formatted address: {formatted_address}")
        
        # Step 2: Convert to S-JTSK using our helper function
        helper_sjtsk_x, helper_sjtsk_y = convert_wgs84_to_sjtsk(lat, lng)
        logger.info(f"Helper converted to S-JTSK: {helper_sjtsk_x}, {helper_sjtsk_y}")
        
        # Step 3: Convert to S-JTSK using CUZK integration
        cuzk_sjtsk_x, cuzk_sjtsk_y = cuzk_integration.convert_wgs84_to_sjtsk(lat, lng)
        logger.info(f"CUZK converted to S-JTSK: {cuzk_sjtsk_x}, {cuzk_sjtsk_y}")
        
        # Compare the two conversions
        x_diff = abs(helper_sjtsk_x - cuzk_sjtsk_x)
        y_diff = abs(helper_sjtsk_y - cuzk_sjtsk_y)
        logger.info(f"S-JTSK difference: x_diff={x_diff:.2f}, y_diff={y_diff:.2f}")
        
        # Step 4: Generate CUZK URL using our helper function
        helper_cuzk_url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={int(helper_sjtsk_x)}&y={int(helper_sjtsk_y)}"
        logger.info(f"Helper CUZK URL: {helper_cuzk_url}")
        
        # Step 5: Generate CUZK URL using CUZK integration
        cuzk_url = cuzk_integration.generate_cuzk_url(lat, lng, use_mapa_identifikace=True)
        logger.info(f"CUZK integration URL: {cuzk_url}")
        
        # Step 6: Generate Google Maps URL
        google_maps_url = f"https://www.google.com/maps/search/?api=1&query={lat},{lng}"
        logger.info(f"Google Maps URL: {google_maps_url}")
        
        # Step 7: Open URLs in browser if requested
        if open_browser:
            logger.info("Opening URLs in browser")
            webbrowser.open(google_maps_url)
            webbrowser.open(helper_cuzk_url)
            webbrowser.open(cuzk_url)
        
        # Return results
        return {
            'address': address,
            'formatted_address': formatted_address,
            'wgs84': {'lat': lat, 'lng': lng},
            'helper_sjtsk': {'x': helper_sjtsk_x, 'y': helper_sjtsk_y},
            'cuzk_sjtsk': {'x': cuzk_sjtsk_x, 'y': cuzk_sjtsk_y},
            'sjtsk_difference': {'x_diff': x_diff, 'y_diff': y_diff},
            'helper_cuzk_url': helper_cuzk_url,
            'cuzk_url': cuzk_url,
            'google_maps_url': google_maps_url
        }
        
    except Exception as e:
        logger.error(f"Error testing CUZK URL: {e}")
        return {'error': str(e)}


def main():
    """Main function to run the CUZK URL test."""
    parser = argparse.ArgumentParser(description='Test CUZK URL generation for an address.')
    parser.add_argument('address', type=str, help='Address to test')
    parser.add_argument('--country', type=str, default="Czech Republic", help='Country (default: Czech Republic)')
    parser.add_argument('--open-browser', action='store_true', help='Open URLs in browser')
    
    args = parser.parse_args()
    
    results = test_cuzk_url(args.address, args.country, args.open_browser)
    
    if 'error' in results:
        print(f"Error: {results['error']}")
        return
    
    # Print a summary
    print("\n=== CUZK URL Test Results ===")
    print(f"Address: {results['address']}")
    print(f"Formatted address: {results['formatted_address']}")
    print(f"WGS84: {results['wgs84']['lat']}, {results['wgs84']['lng']}")
    print(f"Helper S-JTSK: {results['helper_sjtsk']['x']}, {results['helper_sjtsk']['y']}")
    print(f"CUZK S-JTSK: {results['cuzk_sjtsk']['x']}, {results['cuzk_sjtsk']['y']}")
    print(f"S-JTSK difference: x_diff={results['sjtsk_difference']['x_diff']:.2f}, y_diff={results['sjtsk_difference']['y_diff']:.2f}")
    print(f"Helper CUZK URL: {results['helper_cuzk_url']}")
    print(f"CUZK integration URL: {results['cuzk_url']}")
    print(f"Google Maps URL: {results['google_maps_url']}")


if __name__ == "__main__":
    main()
