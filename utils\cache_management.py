"""
Cache management utilities for the Czech Property Registry application.
"""

import threading
from ui.message_boxes import MessageBoxes

class CacheManagement:
    """
    Provides methods for managing application caches.
    This class is responsible for clearing caches and providing cache statistics.
    """
    
    def __init__(self, app):
        """
        Initialize the cache management.
        
        Args:
            app: The main application instance
        """
        self.app = app
    
    def clear_all_caches(self):
        """Clear all application caches"""
        try:
            cache_stats = {}
            
            # Clear the cache manager caches
            if hasattr(self.app, 'cache_manager'):
                cache_stats['cache_manager'] = self.app.cache_manager.clear_all()
            
            # Clear the address suggestions cache
            if hasattr(self.app, '_address_suggestions_cache'):
                cache_size = len(self.app._address_suggestions_cache)
                self.app._address_suggestions_cache.clear()
                cache_stats['address_suggestions'] = cache_size
            
            # Clear the gender cache
            if hasattr(self.app, '_gender_cache'):
                cache_size = len(self.app._gender_cache)
                self.app._gender_cache.clear()
                cache_stats['gender'] = cache_size
            
            # Clear the encrypted URLs cache
            if hasattr(self.app, 'encrypted_urls_cache'):
                cache_size = len(self.app.encrypted_urls_cache)
                self.app.encrypted_urls_cache.clear()
                cache_stats['encrypted_urls'] = cache_size
            
            # Show a message with the number of items cleared
            total_cleared = sum(cache_stats.values())
            MessageBoxes.show_info(
                "Cache Cleared",
                f"Cleared {total_cleared} items from cache.\n\n"
                f"Details:\n" + "\n".join([f"- {k}: {v} items" for k, v in cache_stats.items()])
            )
            
            # Show status
            self.app.show_status(f"Cleared {total_cleared} items from cache")
            
        except Exception as e:
            print(f"Error clearing caches: {e}")
            MessageBoxes.show_error("Error", f"An error occurred while clearing caches: {str(e)}")
    
    def initialize_caches(self):
        """Initialize the application caches"""
        # Initialize cache manager with optimized expiry times
        self.app.cache_manager.create_cache('address_suggestions', 600)  # 10 minutes for address suggestions
        self.app.cache_manager.create_cache('city_suggestions', 86400)   # 24 hours for city data (changes rarely)
        self.app.cache_manager.create_cache('street_suggestions', 3600)  # 1 hour for street data
        self.app.cache_manager.create_cache('postal_suggestions', 86400) # 24 hours for postal codes (change rarely)
        self.app.cache_manager.create_cache('gender', 604800)            # 1 week for gender data (doesn't change)
        self.app.cache_manager.create_cache('encrypted_urls', 300)       # 5 minutes for encrypted URLs
        self.app.cache_manager.create_cache('osm_buildings', 3600)       # 1 hour for OSM building data
        self.app.cache_manager.create_cache('property_data', 1800)       # 30 minutes for property data
        
        # For backward compatibility - these will be phased out
        # and replaced with cache_manager usage throughout the code
        self.app._address_suggestions_cache = {}
        self.app._address_cache_lock = threading.Lock()
        self.app._address_cache_expiry = 300  # 5 minutes in seconds
        self.app._gender_cache = {}
