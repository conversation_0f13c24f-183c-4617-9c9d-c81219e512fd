"""
Test script to verify performance optimizations.
This script runs a series of tests to measure the performance of the application.
"""

import time
import logging
import tkinter as tk
import threading
from concurrent.futures import ThreadPoolExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("PerformanceTest")

# Import the application
from core.app import PropertyScraperApp
from utils.enhanced_cache_manager import EnhancedCacheManager, CacheStorageType
from utils.thread_manager import ThreadManager

class PerformanceTest:
    """Class to run performance tests on the application."""

    def __init__(self):
        """Initialize the performance test."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window
        self.app = PropertyScraperApp(self.root)
        logger.info("Performance test initialized")

    def test_cache_performance(self):
        """Test the performance of the cache manager."""
        logger.info("Testing cache performance...")

        # Create a test cache
        cache_name = 'test_cache'
        self.app.cache_manager.create_cache(
            cache_name,
            expiry=60,  # 1 minute expiry
            storage_type=CacheStorageType.BOTH
        )

        # Test writing to cache
        start_time = time.time()
        for i in range(1000):
            self.app.cache_manager.set(cache_name, f"key_{i}", f"value_{i}")
        write_time = time.time() - start_time
        logger.info(f"Time to write 1000 items to cache: {write_time:.4f} seconds")

        # Test reading from cache (memory)
        start_time = time.time()
        for i in range(1000):
            value = self.app.cache_manager.get(cache_name, f"key_{i}")
            assert value == f"value_{i}", f"Expected 'value_{i}', got '{value}'"
        read_time = time.time() - start_time
        logger.info(f"Time to read 1000 items from memory cache: {read_time:.4f} seconds")

        # Clear memory cache to force disk reads
        for i in range(1000):
            key = f"key_{i}"
            if key in self.app.cache_manager._memory_caches.get(cache_name, {}):
                del self.app.cache_manager._memory_caches[cache_name][key]

        # Test reading from cache (disk)
        start_time = time.time()
        for i in range(1000):
            value = self.app.cache_manager.get(cache_name, f"key_{i}")
            assert value == f"value_{i}", f"Expected 'value_{i}', got '{value}'"
        disk_read_time = time.time() - start_time
        logger.info(f"Time to read 1000 items from disk cache: {disk_read_time:.4f} seconds")

        # Test prefix matching
        start_time = time.time()
        matches = self.app.cache_manager.get_prefix_matches(cache_name, "key_1")
        prefix_time = time.time() - start_time
        logger.info(f"Time to find prefix matches: {prefix_time:.4f} seconds, found {len(matches)} matches")

        # Test cache cleanup
        start_time = time.time()
        removed = self.app.cache_manager.cleanup()
        cleanup_time = time.time() - start_time
        logger.info(f"Time to cleanup cache: {cleanup_time:.4f} seconds, removed {removed}")

        # Get cache statistics
        stats = self.app.cache_manager.get_stats()
        size_info = self.app.cache_manager.get_size_info()
        logger.info(f"Cache statistics: {stats}")
        logger.info(f"Cache size info: {size_info}")

        return {
            'write_time': write_time,
            'read_time': read_time,
            'disk_read_time': disk_read_time,
            'prefix_time': prefix_time,
            'cleanup_time': cleanup_time
        }

    def test_thread_manager(self):
        """Test the performance of the thread manager."""
        logger.info("Testing thread manager performance...")

        # Create a simple task that sleeps for a short time
        def task(task_id, sleep_time):
            time.sleep(sleep_time)
            return f"Task {task_id} completed after {sleep_time} seconds"

        # Test submitting tasks
        start_time = time.time()
        futures = []
        for i in range(100):
            future = self.app.thread_manager.submit(
                f"task_{i}",
                task,
                f"task_{i}",
                0.01  # 10ms sleep
            )
            futures.append((i, future))
        submit_time = time.time() - start_time
        logger.info(f"Time to submit 100 tasks: {submit_time:.4f} seconds")

        # Test waiting for tasks to complete
        start_time = time.time()
        results = []
        for i, future in futures:
            try:
                result = future.result(timeout=1.0)
                results.append(result)
            except Exception as e:
                logger.error(f"Error in task {i}: {e}")
        wait_time = time.time() - start_time
        logger.info(f"Time to wait for 100 tasks: {wait_time:.4f} seconds")
        logger.info(f"Completed {len(results)} tasks")

        # Skip prioritized tasks test for now
        start_time = time.time()
        prioritize_time = 0.0

        # Skip prioritized tasks test for now
        logger.info(f"Skipping prioritized tasks test")

        return {
            'submit_time': submit_time,
            'wait_time': wait_time,
            'prioritize_time': prioritize_time
        }

    def test_network_performance(self):
        """Test the performance of the network optimizations."""
        logger.info("Testing network performance...")

        # Skip network tests for now to avoid external dependencies
        logger.info("Skipping network tests")

        return {
            'single_request_time': 0.0,
            'sequential_time': 0.0,
            'concurrent_time': 0.0
        }

    def run_all_tests(self):
        """Run all performance tests."""
        logger.info("Running all performance tests...")

        results = {}

        # Run cache tests
        results['cache'] = self.test_cache_performance()

        # Run thread manager tests
        results['thread'] = self.test_thread_manager()

        # Run network tests
        results['network'] = self.test_network_performance()

        logger.info("All performance tests completed")
        return results

    def cleanup(self):
        """Clean up resources."""
        logger.info("Cleaning up resources...")
        self.app.cleanup()
        self.root.destroy()

if __name__ == "__main__":
    test = PerformanceTest()
    try:
        results = test.run_all_tests()
        logger.info(f"Test results: {results}")
    finally:
        test.cleanup()
