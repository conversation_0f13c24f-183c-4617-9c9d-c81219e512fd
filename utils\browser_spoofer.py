"""
Browser Spoofer for making HTTP requests appear more like they're coming from a real browser.

This module provides utilities for spoofing browser headers, cookies, and other
characteristics to make HTTP requests appear more like they're coming from a real browser,
which can help avoid triggering bot detection mechanisms.
"""

import random
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import logging

# Configure logging
logger = logging.getLogger(__name__)

class BrowserSpoofer:
    """
    Utility class for spoofing browser characteristics in HTTP requests.

    This class provides methods for creating requests sessions with realistic
    browser headers, cookie handling, and other characteristics to make
    requests appear more like they're coming from a real browser.
    """

    # List of common user agents
    USER_AGENTS = [
        # Chrome on Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
        # Firefox on Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0",
        # Edge on Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
        # Chrome on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        # Firefox on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:90.0) Gecko/20100101 Firefox/90.0",
        # Safari on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    ]

    # Common headers that browsers send
    COMMON_HEADERS = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Cache-Control": "max-age=0",
    }

    @classmethod
    def get_random_user_agent(cls):
        """Get a random user agent from the list of common user agents."""
        return random.choice(cls.USER_AGENTS)

    @classmethod
    def get_browser_headers(cls, user_agent=None):
        """
        Get a set of headers that mimic a real browser.

        Args:
            user_agent (str, optional): User agent to use. If None, a random one is selected.

        Returns:
            dict: Dictionary of headers
        """
        headers = cls.COMMON_HEADERS.copy()
        headers["User-Agent"] = user_agent or cls.get_random_user_agent()
        return headers

    @classmethod
    def create_spoofed_session(cls, retry_total=3, backoff_factor=0.5,
                              status_forcelist=None, rotate_user_agent=True):
        """
        Create a requests session with browser spoofing and retry capability.

        Args:
            retry_total (int): Number of retries for failed requests
            backoff_factor (float): Backoff factor for retries
            status_forcelist (list): List of status codes to retry on
            rotate_user_agent (bool): Whether to rotate user agent for each request

        Returns:
            requests.Session: Session with browser spoofing
        """
        if status_forcelist is None:
            status_forcelist = [429, 500, 502, 503, 504]

        session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=retry_total,
            backoff_factor=backoff_factor,
            status_forcelist=status_forcelist,
            allowed_methods=["HEAD", "GET", "POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Set initial headers
        user_agent = cls.get_random_user_agent()
        session.headers.update(cls.get_browser_headers(user_agent))

        # Store the rotate_user_agent setting in the session
        session.rotate_user_agent = rotate_user_agent

        # Monkey patch the session's request method to add delays and rotate user agent
        original_request = session.request

        def spoofed_request(method, url, **kwargs):
            # Add a random delay to mimic human behavior (between 1 and 3 seconds)
            delay = random.uniform(1, 3)
            logger.debug(f"Adding delay of {delay:.2f} seconds before request to {url}")
            time.sleep(delay)

            # Rotate user agent if enabled
            if getattr(session, 'rotate_user_agent', False):
                new_user_agent = cls.get_random_user_agent()
                session.headers.update({"User-Agent": new_user_agent})
                logger.debug(f"Rotated user agent to: {new_user_agent}")

            # Make the request
            response = original_request(method, url, **kwargs)

            # Add a random delay after the request (between 0.5 and 1.5 seconds)
            post_delay = random.uniform(0.5, 1.5)
            logger.debug(f"Adding delay of {post_delay:.2f} seconds after request to {url}")
            time.sleep(post_delay)

            return response

        # Replace the request method
        session.request = spoofed_request

        return session

    @staticmethod
    def add_random_delay(min_seconds=0.5, max_seconds=2.0, verbose=False):
        """
        Add a random delay to mimic human behavior.

        Args:
            min_seconds (float): Minimum delay in seconds
            max_seconds (float): Maximum delay in seconds
            verbose (bool): Whether to print the delay to the console
        """
        delay = random.uniform(min_seconds, max_seconds)
        logger.debug(f"Adding random delay of {delay:.2f} seconds")
        if verbose:
            print(f"Adding random delay of {delay:.2f} seconds")
        time.sleep(delay)

    @classmethod
    def add_human_like_delay(cls, action_type="navigation", verbose=False):
        """
        Add a human-like delay based on the type of action.

        Args:
            action_type (str): Type of action ("navigation", "form_fill", "click", "read")
            verbose (bool): Whether to print the delay to the console
        """
        # Different delay ranges for different types of actions
        delay_ranges = {
            "navigation": (2.0, 5.0),  # Longer delay for page navigation
            "form_fill": (0.5, 2.0),   # Medium delay for filling forms
            "click": (0.2, 1.0),       # Short delay for clicking
            "read": (3.0, 8.0)         # Longer delay for reading content
        }

        # Get the delay range for the action type, or use a default range
        min_seconds, max_seconds = delay_ranges.get(action_type.lower(), (1.0, 3.0))

        # Add the random delay
        cls.add_random_delay(min_seconds, max_seconds, verbose)
