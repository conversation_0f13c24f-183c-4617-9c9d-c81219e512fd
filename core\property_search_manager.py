"""
Property search manager for the Czech Property Scraper application.
Handles all property search-related functionality.
"""

from ui.message_boxes import MessageBoxes


class PropertySearchManager:
    """Manages property search functionality for the application."""

    def __init__(self, app):
        """
        Initialize the property search manager.

        Args:
            app: The main application instance
        """
        self.app = app

    def scrape_property(self):
        """Scrape property data from ČÚZK based on coordinates or parcel number"""
        try:
            x_coord = self.app.coord_x.get().strip()
            y_coord = self.app.coord_y.get().strip()
            parcel_number = self.app.parcel_number.get().strip()

            # Check if we have valid search criteria
            if (not x_coord or not y_coord) and not parcel_number:
                MessageBoxes.show_error("Input Error", "Please enter either coordinates or a parcel number")
                return

            # Ask the user if they want to use direct browser scraping
            use_direct_scrape = MessageBoxes.ask_scraping_method()

            # Show a message that we're searching
            MessageBoxes.show_info("Searching", "Searching for property data.\nThis may take a moment...")

            # Use the CUZK scraper
            if parcel_number:
                # Search by parcel number
                property_data = self.app.cuzk_scraper.search_by_parcel_number(parcel_number)
            else:
                # Search by coordinates
                property_data = self.app.cuzk_scraper.search_by_coordinates(x_coord, y_coord)

            if property_data:
                if isinstance(property_data, list) and len(property_data) > 0:
                    # If we got a list of properties, use the first one
                    property_data = property_data[0]

                # Store the property data
                self.app.owner_name = property_data.get('owner_name', 'Unknown')
                self.app.owner_address = property_data.get('owner_address', 'Unknown')

                # Determine gender using AI
                self.app.owner_gender = self.determine_gender(self.app.owner_name)

                # Display the property details
                self.app.property_display.display_property_details(property_data)

                MessageBoxes.show_property_found(self.app.owner_name)
            else:
                # If the CUZK scraper failed, try the old methods
                if use_direct_scrape:
                    # Use direct browser scraping
                    if parcel_number:
                        # Search by parcel number
                        property_data = self.app.cuzk_scraper_methods.direct_cuzk_scrape_by_parcel(parcel_number)
                    else:
                        # Search by coordinates
                        property_data = self.app.cuzk_scraper_methods.direct_cuzk_scrape_by_coordinates(x_coord, y_coord)
                else:
                    # Use the unified search function
                    if parcel_number:
                        # Search by parcel number
                        property_data = self.app.property_search.unified_search('parcel', parcel_number=parcel_number)
                    else:
                        # Search by coordinates
                        property_data = self.app.property_search.unified_search('coordinates', lat=x_coord, lng=y_coord)

                if property_data:
                    if isinstance(property_data, list) and len(property_data) > 0:
                        # If we got a list of properties, use the first one
                        property_data = property_data[0]

                    self.app.owner_name = property_data.get('owner_name', 'Unknown')
                    self.app.owner_address = property_data.get('owner_address', 'Unknown')

                    # Determine gender using AI
                    self.app.owner_gender = self.determine_gender(self.app.owner_name)

                    # Display the property details
                    self.app.property_display.display_property_details(property_data)

                    MessageBoxes.show_property_found(self.app.owner_name)
                else:
                    MessageBoxes.show_error("Error", "Could not find property data with the provided information")

        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def search_by_coordinates(self):
        """Search for property by coordinates"""
        try:
            # Get coordinates
            x_coord = self.app.coord_x.get().strip()
            y_coord = self.app.coord_y.get().strip()
            parcel_number = self.app.parcel_number.get().strip()

            # Check if we have valid search criteria
            if (not x_coord or not y_coord) and not parcel_number:
                MessageBoxes.show_error("Input Error", "Please enter either coordinates or a parcel number")
                return

            # Show status
            self.app.show_status("Searching for property...")

            # Show a message that we're searching
            MessageBoxes.show_info("Searching", "Searching for property data.\nThis may take a moment...")

            # Use the unified search function
            if parcel_number:
                # Search by parcel number
                property_data = self.app.property_search.unified_search('parcel', parcel_number=parcel_number)
            else:
                # Search by coordinates
                property_data = self.app.property_search.unified_search('coordinates', lat=x_coord, lng=y_coord)

            if property_data:
                # Display the property data
                self.app.property_display.display_property_details(property_data)
            else:
                # No fallback to sample data - show an error message
                MessageBoxes.show_error(
                    "Error: Property Not Found",
                    "Could not find property data with the provided information.\n\n"
                    "This application does not use demo or fallback data."
                )

        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def search_by_address(self):
        """Search for properties at the entered address components"""
        try:
            # Get address components
            city = self.app.address_city.get().strip()
            street = self.app.address_street.get().strip()

            # Check if we have at least a city
            if not city:
                MessageBoxes.show_error("Input Error", "Please enter at least a city name")
                return

            # Use the internal method
            self._search_by_component_address(city, street)

        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def _search_by_component_address(self, city, street=None, number=None):
        """
        Search for properties at the specified address components

        Args:
            city (str): City name
            street (str, optional): Street name
            number (str, optional): House number
        """
        try:
            # Show status
            self.app.show_status(f"Searching for properties in {city}...")

            # Construct the full address
            full_address = city
            if street:
                full_address += f", {street}"
            if number:
                full_address += f" {number}"

            # Use the unified search function to fetch properties
            properties = self.fetch_properties_by_address(full_address)

            if properties:
                # Display the properties
                self.app.property_display.display_properties(properties)
            else:
                # No fallback to sample data - show an error message
                MessageBoxes.show_error(
                    "Error: Properties Not Found",
                    "Could not find properties at the specified address.\n\n"
                    "This application does not use demo or fallback data."
                )

        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def fetch_properties_by_address(self, address):
        """
        Fetch properties at a specific address from the CUZK website

        Args:
            address (str): Address to search for

        Returns:
            list: List of property data dictionaries or None if search failed
        """
        try:
            # Show status
            self.app.show_status(f"Searching for properties at {address}...")

            # Use the property search service
            return self.app.property_search.search_by_address(address)
        except Exception as e:
            print(f"Error fetching properties by address: {e}")
            return None

    def determine_gender(self, name):
        """
        Determine the gender of a person based on their name

        Args:
            name (str): Person's name

        Returns:
            str: Gender ('male', 'female', or 'unknown')
        """
        # Check if we have a cached result
        if hasattr(self.app, 'cache_manager'):
            cached_gender = self.app.cache_manager.get('gender', name)
            if cached_gender:
                return cached_gender

        # Try to determine gender using GPT
        if hasattr(self.app, 'gpt') and self.app.gpt:
            try:
                gender = self.app.gpt.determine_gender(name)

                # Cache the result
                if hasattr(self.app, 'cache_manager'):
                    self.app.cache_manager.set('gender', name, gender)

                return gender
            except Exception as e:
                print(f"Error determining gender with GPT: {e}")

        # Fallback to simple heuristic
        name_parts = name.split()
        if name_parts:
            last_name = name_parts[-1].lower()
            if last_name.endswith('ová') or last_name.endswith('ova'):
                return 'female'
            else:
                return 'male'

        return 'unknown'
