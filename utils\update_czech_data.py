#!/usr/bin/env python3
"""
Script to update Czech administrative division data in JSON files
with correct data for use in the Czech Property Registry application.
"""

import os
import json

# Create directory for data if it doesn't exist
if not os.path.exists('czech_data'):
    os.makedirs('czech_data')

def update_regions_json():
    """Update the regions.json file with correct data"""
    print("Updating regions.json...")

    regions_data = {
        "regions_czech": [
            "Hlavní město Praha",
            "Středočeský kraj",
            "Jihočeský kraj",
            "Plzeňský kraj",
            "Karlovarský kraj",
            "Ústecký kraj",
            "Liberecký kraj",
            "Královéhradecký kraj",
            "Pardubický kraj",
            "Kraj Vysočina",
            "Jihomoravský kraj",
            "Olomoucký kraj",
            "Zlínský kraj",
            "Moravskoslezský kraj"
        ],
        "regions_english": [
            "Prague",
            "Central Bohemian Region",
            "South Bohemian Region",
            "Plzeň Region",
            "Karlovy Vary Region",
            "Ústí nad Labem Region",
            "Liberec Region",
            "Hradec Králové Region",
            "Pardubice Region",
            "Vysočina Region",
            "South Moravian Region",
            "Olomouc Region",
            "Zlín Region",
            "Moravian-Silesian Region"
        ],
        "region_mapping": {
            "Prague": "Hlavní město Praha",
            "Central Bohemian Region": "Středočeský kraj",
            "South Bohemian Region": "Jihočeský kraj",
            "Plzeň Region": "Plzeňský kraj",
            "Karlovy Vary Region": "Karlovarský kraj",
            "Ústí nad Labem Region": "Ústecký kraj",
            "Liberec Region": "Liberecký kraj",
            "Hradec Králové Region": "Královéhradecký kraj",
            "Pardubice Region": "Pardubický kraj",
            "Vysočina Region": "Kraj Vysočina",
            "South Moravian Region": "Jihomoravský kraj",
            "Olomouc Region": "Olomoucký kraj",
            "Zlín Region": "Zlínský kraj",
            "Moravian-Silesian Region": "Moravskoslezský kraj"
        },
        "region_codes": {
            "Hlavní město Praha": "CZ010",
            "Středočeský kraj": "CZ020",
            "Jihočeský kraj": "CZ031",
            "Plzeňský kraj": "CZ032",
            "Karlovarský kraj": "CZ041",
            "Ústecký kraj": "CZ042",
            "Liberecký kraj": "CZ051",
            "Královéhradecký kraj": "CZ052",
            "Pardubický kraj": "CZ053",
            "Kraj Vysočina": "CZ063",
            "Jihomoravský kraj": "CZ064",
            "Olomoucký kraj": "CZ071",
            "Zlínský kraj": "CZ072",
            "Moravskoslezský kraj": "CZ080"
        },
        "region_coordinates": {
            "Hlavní město Praha": {"lat": 50.0755, "lng": 14.4378},
            "Středočeský kraj": {"lat": 50.0755, "lng": 14.4378},
            "Jihočeský kraj": {"lat": 48.9747, "lng": 14.4744},
            "Plzeňský kraj": {"lat": 49.7384, "lng": 13.3736},
            "Karlovarský kraj": {"lat": 50.2309, "lng": 12.8716},
            "Ústecký kraj": {"lat": 50.6607, "lng": 14.0328},
            "Liberecký kraj": {"lat": 50.7663, "lng": 15.0543},
            "Královéhradecký kraj": {"lat": 50.2092, "lng": 15.8323},
            "Pardubický kraj": {"lat": 49.8889, "lng": 16.1116},
            "Kraj Vysočina": {"lat": 49.4088, "lng": 15.5836},
            "Jihomoravský kraj": {"lat": 49.1951, "lng": 16.6068},
            "Olomoucký kraj": {"lat": 49.5937, "lng": 17.2508},
            "Zlínský kraj": {"lat": 49.2248, "lng": 17.6627},
            "Moravskoslezský kraj": {"lat": 49.8209, "lng": 18.2625}
        },
        "region_name_mapping": {
            "Prague": "Prague",
            "Central Bohemian Region": "Central Bohemia",
            "South Bohemian Region": "South Bohemia",
            "Plzeň Region": "Plzeň",
            "Karlovy Vary Region": "Karlovy Vary",
            "Ústí nad Labem Region": "Ústí nad Labem",
            "Liberec Region": "Liberec",
            "Hradec Králové Region": "Hradec Králové",
            "Pardubice Region": "Pardubice",
            "Vysočina Region": "Vysočina",
            "South Moravian Region": "South Moravia",
            "Olomouc Region": "Olomouc",
            "Zlín Region": "Zlín",
            "Moravian-Silesian Region": "Moravian-Silesian"
        },
        "region_districts": {
            "Prague": ["Prague"],
            "Central Bohemian Region": ["Benešov", "Beroun", "Kladno", "Kolín", "Kutná Hora", "Mělník", "Mladá Boleslav", "Nymburk", "Praha-východ", "Praha-západ", "Příbram", "Rakovník"],
            "South Bohemian Region": ["České Budějovice", "Český Krumlov", "Jindřichův Hradec", "Písek", "Prachatice", "Strakonice", "Tábor"],
            "Plzeň Region": ["Domažlice", "Klatovy", "Plzeň-město", "Plzeň-jih", "Plzeň-sever", "Rokycany", "Tachov"],
            "Karlovy Vary Region": ["Cheb", "Karlovy Vary", "Sokolov"],
            "Ústí nad Labem Region": ["Děčín", "Chomutov", "Litoměřice", "Louny", "Most", "Teplice", "Ústí nad Labem"],
            "Liberec Region": ["Česká Lípa", "Jablonec nad Nisou", "Liberec", "Semily"],
            "Hradec Králové Region": ["Hradec Králové", "Jičín", "Náchod", "Rychnov nad Kněžnou", "Trutnov"],
            "Pardubice Region": ["Chrudim", "Pardubice", "Svitavy", "Ústí nad Orlicí"],
            "Vysočina Region": ["Havlíčkův Brod", "Jihlava", "Pelhřimov", "Třebíč", "Žďár nad Sázavou"],
            "South Moravian Region": ["Blansko", "Brno-město", "Brno-venkov", "Břeclav", "Hodonín", "Vyškov", "Znojmo"],
            "Olomouc Region": ["Jeseník", "Olomouc", "Prostějov", "Přerov", "Šumperk"],
            "Zlín Region": ["Kroměříž", "Uherské Hradiště", "Vsetín", "Zlín"],
            "Moravian-Silesian Region": ["Bruntál", "Frýdek-Místek", "Karviná", "Nový Jičín", "Opava", "Ostrava-město"]
        }
    }

    # Save to file
    with open('czech_data/regions.json', 'w', encoding='utf-8') as f:
        json.dump(regions_data, f, ensure_ascii=False, indent=4)

    print("regions.json updated successfully")

def update_cities_json():
    """Update the cities.json file with correct data"""
    print("Updating cities.json...")

    # Load existing cities data if available
    cities_data = {
        "cities_by_region": {},
        "cities_by_region_code": {},
        "city_name_mapping": {}
    }

    # Add Karlovy Vary Region cities
    karlovy_vary_cities = [
        "Karlovy Vary", "Cheb", "Sokolov", "Mariánské Lázně", "Aš", "Ostrov", "Kraslice", "Chodov", "Nejdek",
        "Františkovy Lázně", "Jáchymov", "Loket", "Žlutice", "Toužim", "Bochov", "Horní Slavkov", "Kynšperk nad Ohří",
        "Luby", "Plesná", "Rotava", "Hroznětín", "Lázně Kynžvart", "Nová Role", "Habartov", "Březová", "Krásno",
        "Teplá", "Nové Sedlo", "Abertamy", "Horní Blatná", "Krásné Údolí", "Bečov nad Teplou", "Chyše", "Pernink",
        "Merklín", "Potůčky", "Boží Dar", "Dalovice", "Jenišov", "Kolová", "Sadov", "Otovice", "Hory", "Stružná",
        "Andělská Hora", "Kyselka", "Velichov", "Vojkovice", "Stráž nad Ohří", "Děpoltovice", "Smolné Pece", "Šemnice"
    ]

    # Add to cities_by_region
    cities_data["cities_by_region"]["Karlovy Vary Region"] = karlovy_vary_cities
    cities_data["cities_by_region"]["Karlovy Vary"] = karlovy_vary_cities

    # Add to cities_by_region_code
    cities_data["cities_by_region_code"]["CZ041"] = karlovy_vary_cities

    # Add Czech-English city name mappings
    cities_data["city_name_mapping"].update({
        "Prague": "Praha",
        "Brno": "Brno",
        "Ostrava": "Ostrava",
        "Plzeň": "Plzeň",
        "Liberec": "Liberec",
        "Karlovy Vary": "Karlovy Vary",
        "Cheb": "Cheb",
        "Sokolov": "Sokolov",
        "Mariánské Lázně": "Mariánské Lázně",
        "Marianske Lazne": "Mariánské Lázně",
        "Aš": "Aš",
        "As": "Aš",
        "Ostrov": "Ostrov",
        "Chodov": "Chodov",
        "Nejdek": "Nejdek",
        "Františkovy Lázně": "Františkovy Lázně",
        "Frantiskovy Lazne": "Františkovy Lázně",
        "Jáchymov": "Jáchymov",
        "Jachymov": "Jáchymov",
        "Loket": "Loket",
        "Žlutice": "Žlutice",
        "Zlutice": "Žlutice",
        "Toužim": "Toužim",
        "Touzim": "Toužim",
        "České Budějovice": "České Budějovice",
        "Ceske Budejovice": "České Budějovice",
        "Hradec Králové": "Hradec Králové",
        "Hradec Kralove": "Hradec Králové",
        "Ústí nad Labem": "Ústí nad Labem",
        "Usti nad Labem": "Ústí nad Labem"
    })

    # Save to file
    with open('czech_data/cities.json', 'w', encoding='utf-8') as f:
        json.dump(cities_data, f, ensure_ascii=False, indent=4)

    print("cities.json updated successfully")

def update_cadastral_areas_json():
    """Update the cadastral_areas.json file with correct data"""
    print("Updating cadastral_areas.json...")

    # Create a basic structure for cadastral areas
    cadastral_data = {
        "cadastral_areas_by_city": {},
        "cadastral_areas_by_district": {},
        "cadastral_template": [
            "{name}",
            "{name}-město",
            "{name}-okolí",
            "Katastr {name}"
        ]
    }

    # Add Karlovy Vary Region cadastral areas
    karlovy_vary_cadastral = [
        "Karlovy Vary", "Drahovice", "Rybáře", "Stará Role", "Dvory", "Doubí",
        "Tuhnice", "Bohatice", "Olšová Vrata", "Cihelny", "Počerny", "Rosnice",
        "Sedlec", "Čankov", "Tašovice", "Hůrky"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Karlovy Vary"] = karlovy_vary_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in karlovy_vary_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Cheb cadastral areas
    cheb_cadastral = [
        "Cheb", "Háje", "Podhrad", "Skalka", "Hradiště", "Dolní Dvory",
        "Dřenice", "Jindřichov", "Klest", "Podhoří", "Dolní Pelhřimov",
        "Horní Pelhřimov", "Bříza", "Cetnov", "Chvoječná", "Loužek",
        "Pelhřimov", "Svatý Kříž", "Třebeň"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Cheb"] = cheb_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in cheb_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Sokolov cadastral areas
    sokolov_cadastral = [
        "Sokolov", "Vítkov", "Těšovice", "Hrušková", "Novina", "Dolní Rychnov",
        "Svatava", "Citice", "Bukovany", "Habartov", "Březová", "Dasnice",
        "Chlum Svaté Maří", "Josefov", "Kaceřov", "Krajková", "Královské Poříčí",
        "Krásno", "Kynšperk nad Ohří", "Libavské Údolí", "Loket", "Lomnice",
        "Nové Sedlo", "Rovná", "Staré Sedlo", "Šabina", "Tatrovice", "Vintířov"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Sokolov"] = sokolov_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in sokolov_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Mariánské Lázně cadastral areas
    marianske_lazne_cadastral = [
        "Mariánské Lázně", "Úšovice", "Hamrníky", "Chotěnov-Skláře", "Stanoviště",
        "Kladská", "Zádub", "Závišín", "Mnichov", "Velká Hleďsebe", "Drmoul",
        "Lázně Kynžvart", "Stará Voda", "Trstěnice", "Tři Sekery", "Valy", "Vlkovice"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Mariánské Lázně"] = marianske_lazne_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in marianske_lazne_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Aš cadastral areas
    as_cadastral = [
        "Aš", "Mokřiny", "Kopaniny", "Vernéřov", "Nebesa", "Doubrava",
        "Horní Paseky", "Dolní Paseky", "Nový Žďár", "Krásná", "Hazlov",
        "Hranice", "Podhradí", "Vojtanov"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Aš"] = as_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in as_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Ostrov cadastral areas
    ostrov_cadastral = [
        "Ostrov", "Horní Žďár", "Dolní Žďár", "Kfely", "Mořičov", "Vykmanov",
        "Maroltov", "Květnová", "Hluboký", "Arnoldov", "Hájek", "Hroznětín",
        "Krásný Les", "Merklín", "Pernink", "Potůčky", "Stráž nad Ohří",
        "Velichov", "Vojkovice"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Ostrov"] = ostrov_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in ostrov_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Chodov cadastral areas
    chodov_cadastral = [
        "Chodov", "Stará Chodovská", "Dolní Chodov", "Nové Chalupy", "Vintířov",
        "Vřesová", "Jimlíkov", "Božičany", "Mírová", "Tatrovice", "Černava",
        "Jenišov", "Nová Role"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Chodov"] = chodov_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in chodov_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Nejdek cadastral areas
    nejdek_cadastral = [
        "Nejdek", "Bernov", "Lesík", "Oldřichov", "Pozorka", "Suchá", "Tisová",
        "Vysoká Štola", "Fojtov", "Lužec", "Nové Hamry", "Smolné Pece", "Vysoká Pec"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Nejdek"] = nejdek_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in nejdek_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Františkovy Lázně cadastral areas
    frantiskovy_lazne_cadastral = [
        "Františkovy Lázně", "Horní Lomany", "Dolní Lomany", "Slatina", "Žírovice",
        "Krapice", "Dlouhé Mosty", "Seníky", "Hazlov", "Vojtanov", "Libá", "Lipová",
        "Milhostov", "Milíkov", "Nebanice", "Nový Kostel", "Odrava", "Okrouhlá",
        "Plesná", "Pomezí nad Ohří", "Poustka", "Skalná", "Třebeň", "Tuřany", "Velký Luh"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Františkovy Lázně"] = frantiskovy_lazne_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in frantiskovy_lazne_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Jáchymov cadastral areas
    jachymov_cadastral = [
        "Jáchymov", "Mariánská", "Suchá", "Nové Město", "Horní Žďár", "Dolní Žďár",
        "Popov", "Hřebečná", "Abertamy", "Pernink", "Boží Dar", "Horní Blatná", "Potůčky"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Jáchymov"] = jachymov_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in jachymov_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Loket cadastral areas
    loket_cadastral = [
        "Loket", "Nadlesí", "Údolí", "Dvory", "Horní Slavkov", "Krásno", "Rovná",
        "Nová Ves", "Staré Sedlo", "Hory", "Březová", "Dolní Rychnov", "Chodov",
        "Jenišov", "Královské Poříčí", "Mírová", "Nové Sedlo", "Sokolov", "Svatava", "Vintířov"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Loket"] = loket_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in loket_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Žlutice cadastral areas
    zlutice_cadastral = [
        "Žlutice", "Verušice", "Veselov", "Knínice", "Protivec", "Ratiboř", "Skoky",
        "Vladořice", "Záhořice", "Močidlec", "Bochov", "Čichalov", "Chyše", "Pšov",
        "Štědrá", "Valeč", "Verušičky"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Žlutice"] = zlutice_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in zlutice_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Toužim cadastral areas
    touzim_cadastral = [
        "Toužim", "Dobrá Voda", "Kojšovice", "Komárov", "Kosmová", "Lachovice",
        "Luhov", "Políkno", "Radyně", "Smilov", "Bečov nad Teplou", "Chodov",
        "Krásné Údolí", "Otročín", "Teplá", "Útvina"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Toužim"] = touzim_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in touzim_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Prague cadastral areas
    prague_cadastral = [
        "Praha 1", "Praha 2", "Praha 3", "Praha 4", "Praha 5", "Praha 6", "Praha 7",
        "Praha 8", "Praha 9", "Praha 10", "Praha 11", "Praha 12", "Praha 13", "Praha 14",
        "Praha 15", "Praha 16", "Praha 17", "Praha 18", "Praha 19", "Praha 20", "Praha 21",
        "Praha 22", "Staré Město", "Josefov", "Malá Strana", "Hradčany", "Nové Město",
        "Vyšehrad", "Vinohrady", "Vršovice", "Nusle", "Podolí", "Braník", "Krč", "Michle",
        "Záběhlice", "Strašnice", "Malešice", "Žižkov", "Karlín", "Libeň", "Kobylisy",
        "Bohnice", "Troja", "Střížkov", "Prosek", "Vysočany", "Hloubětín", "Černý Most",
        "Kyje", "Hostavice", "Dolní Počernice", "Horní Počernice", "Klánovice", "Újezd nad Lesy",
        "Běchovice", "Koloděje", "Uhříněves", "Pitkovice", "Hájek u Uhříněvsi", "Královice",
        "Nedvězí u Říčan", "Kolovraty", "Lipany", "Benice", "Čestlice", "Křeslice", "Petrovice",
        "Horní Měcholupy", "Dolní Měcholupy", "Dubeč", "Štěrboholy", "Hostivař", "Háje", "Chodov",
        "Šeberov", "Újezd u Průhonic", "Kunratice", "Libuš", "Písnice", "Modřany", "Komořany",
        "Cholupice", "Točná", "Lochkov", "Slivenec", "Holyně", "Velká Chuchle", "Malá Chuchle",
        "Radotín", "Lahovice", "Zbraslav", "Lipence", "Stodůlky", "Třebonice", "Řeporyje", "Zadní Kopanina",
        "Holyně", "Jinonice", "Radlice", "Košíře", "Motol", "Břevnov", "Střešovice", "Veleslavín",
        "Vokovice", "Liboc", "Ruzyně", "Řepy", "Zličín", "Sobín", "Dejvice", "Bubeneč", "Sedlec",
        "Lysolaje", "Suchdol", "Holešovice", "Bubny", "Letná", "Smíchov"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Praha"] = prague_cadastral
    cadastral_data["cadastral_areas_by_city"]["Prague"] = prague_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in prague_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Add Brno cadastral areas
    brno_cadastral = [
        "Brno-město", "Brno-střed", "Brno-sever", "Brno-Židenice", "Brno-Královo Pole",
        "Brno-Žabovřesky", "Brno-Jundrov", "Brno-Kohoutovice", "Brno-Bohunice", "Brno-Starý Lískovec",
        "Brno-Nový Lískovec", "Brno-jih", "Brno-Bosonohy", "Brno-Černovice", "Brno-Slatina",
        "Brno-Tuřany", "Brno-Chrlice", "Brno-Líšeň", "Brno-Vinohrady", "Brno-Maloměřice a Obřany",
        "Brno-Medlánky", "Brno-Řečkovice a Mokrá Hora", "Brno-Jehnice", "Brno-Ořešín",
        "Brno-Útěchov", "Brno-Ivanovice", "Brno-Kníničky", "Brno-Komín", "Brno-Bystrc", "Staré Brno",
        "Veveří", "Stránice", "Pisárky", "Trnitá", "Zábrdovice", "Komárov", "Štýřice", "Černá Pole",
        "Ponava", "Sadová", "Husovice", "Lesná", "Soběšice", "Židenice", "Juliánov", "Maloměřice",
        "Obřany", "Královo Pole", "Žabovřesky", "Jundrov", "Kohoutovice", "Bohunice", "Starý Lískovec",
        "Nový Lískovec", "Horní Heršpice", "Dolní Heršpice", "Přízřenice", "Bosonohy", "Černovice",
        "Slatina", "Tuřany", "Holásky", "Brněnské Ivanovice", "Dvorska", "Chrlice", "Líšeň", "Vinohrady",
        "Židenice", "Medlánky", "Řečkovice", "Mokrá Hora", "Jehnice", "Ořešín", "Útěchov", "Ivanovice",
        "Kníničky", "Komín", "Bystrc"
    ]

    # Add to cadastral_areas_by_city
    cadastral_data["cadastral_areas_by_city"]["Brno"] = brno_cadastral

    # Add to cadastral_areas_by_district for each district
    for district in brno_cadastral:
        cadastral_data["cadastral_areas_by_district"][district] = [
            district,
            f"{district}-centrum",
            f"{district}-okolí",
            f"Katastr {district}"
        ]

    # Save to file
    with open('czech_data/cadastral_areas.json', 'w', encoding='utf-8') as f:
        json.dump(cadastral_data, f, ensure_ascii=False, indent=4)

    print("cadastral_areas.json updated successfully")

def main():
    """Main function to update all data files"""
    update_regions_json()
    update_cities_json()
    update_cadastral_areas_json()
    print("All data files updated successfully")

if __name__ == "__main__":
    main()
