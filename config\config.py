"""
Configuration module for the Czech Property Registry application.
This module handles loading and accessing configuration settings.
"""

import os
import json
import configparser
from typing import Dict, Any, Optional

class Config:
    """
    Configuration manager for the Czech Property Registry application.
    Handles loading configuration from config.ini and environment variables.
    """
    
    def __init__(self, config_file: str = "config.ini"):
        """
        Initialize the configuration manager.
        
        Args:
            config_file (str): Path to the configuration file
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from the config file"""
        # Default configuration
        self.config["DEFAULT"] = {
            "development_mode": "false",
            "default_language": "cs",
            "log_level": "INFO",
            "cache_enabled": "true",
            "cache_expiry": "86400",  # 24 hours in seconds
            "default_region": "Czech Republic",
            "default_city": "Prague",
            "max_batch_size": "100",
            "font_scale": "1.0",
        }
        
        self.config["OPENAI"] = {
            "api_key": "",
            "model": "gpt-3.5-turbo",
            "temperature": "0.7",
            "max_tokens": "150",
        }
        
        self.config["GOOGLE_MAPS"] = {
            "api_key": "",
            "region": "cz",
            "language": "cs",
        }
        
        self.config["PATHS"] = {
            "data_dir": "data",
            "cache_dir": "data/cache",
            "templates_dir": "data/templates",
            "czech_data_dir": "data/czech_data",
        }
        
        self.config["UI"] = {
            "window_width": "900",
            "window_height": "700",
            "theme": "default",
            "font_family": "Arial",
            "default_font_size": "9",
            "small_font_size": "8",
            "medium_font_size": "10",
            "large_font_size": "11",
            "title_font_size": "12",
        }
        
        # Load from config file if it exists
        if os.path.exists(self.config_file):
            self.config.read(self.config_file)
        
        # Override with environment variables
        self._load_from_env()
    
    def _load_from_env(self) -> None:
        """Load configuration from environment variables"""
        # OPENAI_API_KEY
        if "OPENAI_API_KEY" in os.environ:
            self.config["OPENAI"]["api_key"] = os.environ["OPENAI_API_KEY"]
        
        # GOOGLE_MAPS_API_KEY
        if "GOOGLE_MAPS_API_KEY" in os.environ:
            self.config["GOOGLE_MAPS"]["api_key"] = os.environ["GOOGLE_MAPS_API_KEY"]
        
        # DEVELOPMENT_MODE
        if "DEVELOPMENT_MODE" in os.environ:
            self.config["DEFAULT"]["development_mode"] = os.environ["DEVELOPMENT_MODE"]
    
    def save_config(self) -> None:
        """Save the current configuration to the config file"""
        with open(self.config_file, "w") as f:
            self.config.write(f)
    
    def get(self, section: str, key: str, default: Any = None) -> str:
        """
        Get a configuration value.
        
        Args:
            section (str): Configuration section
            key (str): Configuration key
            default (Any, optional): Default value if not found
            
        Returns:
            str: Configuration value
        """
        try:
            return self.config[section][key]
        except (KeyError, configparser.NoSectionError):
            return default
    
    def get_bool(self, section: str, key: str, default: bool = False) -> bool:
        """
        Get a boolean configuration value.
        
        Args:
            section (str): Configuration section
            key (str): Configuration key
            default (bool, optional): Default value if not found
            
        Returns:
            bool: Configuration value as boolean
        """
        value = self.get(section, key, str(default))
        return value.lower() in ("true", "yes", "1", "t", "y")
    
    def get_int(self, section: str, key: str, default: int = 0) -> int:
        """
        Get an integer configuration value.
        
        Args:
            section (str): Configuration section
            key (str): Configuration key
            default (int, optional): Default value if not found
            
        Returns:
            int: Configuration value as integer
        """
        value = self.get(section, key, str(default))
        try:
            return int(value)
        except ValueError:
            return default
    
    def get_float(self, section: str, key: str, default: float = 0.0) -> float:
        """
        Get a float configuration value.
        
        Args:
            section (str): Configuration section
            key (str): Configuration key
            default (float, optional): Default value if not found
            
        Returns:
            float: Configuration value as float
        """
        value = self.get(section, key, str(default))
        try:
            return float(value)
        except ValueError:
            return default
    
    def set(self, section: str, key: str, value: Any) -> None:
        """
        Set a configuration value.
        
        Args:
            section (str): Configuration section
            key (str): Configuration key
            value (Any): Configuration value
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = str(value)
    
    def is_development_mode(self) -> bool:
        """Check if the application is in development mode"""
        return self.get_bool("DEFAULT", "development_mode", False)
    
    def get_api_key(self, service: str) -> Optional[str]:
        """
        Get an API key for a service.
        
        Args:
            service (str): Service name (e.g., "OPENAI", "GOOGLE_MAPS")
            
        Returns:
            Optional[str]: API key or None if not found
        """
        if service.upper() == "OPENAI":
            return self.get("OPENAI", "api_key")
        elif service.upper() == "GOOGLE_MAPS":
            return self.get("GOOGLE_MAPS", "api_key")
        return None
    
    def get_path(self, path_name: str) -> str:
        """
        Get a path from the configuration.
        
        Args:
            path_name (str): Path name (e.g., "data_dir", "cache_dir")
            
        Returns:
            str: Path
        """
        return self.get("PATHS", path_name, "")
    
    def get_ui_setting(self, setting_name: str, default: Any = None) -> str:
        """
        Get a UI setting from the configuration.
        
        Args:
            setting_name (str): Setting name
            default (Any, optional): Default value if not found
            
        Returns:
            str: UI setting
        """
        return self.get("UI", setting_name, default)
    
    def get_font_sizes(self) -> Dict[str, int]:
        """
        Get font sizes from the configuration.
        
        Returns:
            Dict[str, int]: Font sizes
        """
        return {
            "default": self.get_int("UI", "default_font_size", 9),
            "small": self.get_int("UI", "small_font_size", 8),
            "medium": self.get_int("UI", "medium_font_size", 10),
            "large": self.get_int("UI", "large_font_size", 11),
            "title": self.get_int("UI", "title_font_size", 12),
        }

# Create a singleton instance
config = Config()
