name: Czech Property Registry CI

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:  # Allow manual triggering

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest]
        python-version: ['3.9', '3.10', '3.11']

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        pip install pytest-cov

    - name: Create test directories
      run: |
        mkdir -p czech_data
        mkdir -p data/cache

    - name: Run tests with coverage
      run: |
        python -m pytest tests/unit --verbose --cov=. --cov-report=xml

    - name: Run specific tests
      run: |
        python tests/run_tests.py --type unit --test test_region_manager --verbose
        python tests/run_tests.py --type unit --test test_data_source_manager --verbose

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: false

  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black mypy

    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Check formatting with black
      run: |
        black --check --diff .

    - name: Type check with mypy
      run: |
        mypy --ignore-missing-imports .

  build:
    needs: [test, lint]
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Build executable
      run: |
        pyinstaller --onefile --windowed --name CzechPropertyRegistry main.py

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: CzechPropertyRegistry-${{ runner.os }}
        path: dist/CzechPropertyRegistry*
