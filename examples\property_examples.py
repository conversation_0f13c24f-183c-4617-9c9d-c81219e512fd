"""
Property examples for the Czech Property Registry application.
This module contains the PropertyExamples class for generating sample property data.

NOTE: This is an example file only and is not used in the production application.
The application does not use any fallback, demo, or fake data.
"""

# This is an example file only - not used in production
# pylint: disable=unused-argument, unused-import
from ui.message_boxes import MessageBoxes


class PropertyExamples:
    """
    Class for generating sample property data and examples.
    This class provides methods to generate sample properties for demonstration purposes.
    """

    def __init__(self, app):
        """
        Initialize the PropertyExamples class

        Args:
            app: The main PropertyScraperApp instance
        """
        self.app = app

        # Show error message instead of using demo data
        MessageBoxes.show_error(
            "Error: Demo Data Not Available",
            "This application does not use demo or fallback data.\n\n"
            "Please configure the application with valid API keys and try again."
        )

    def generate_sample_property(self):
        """Generate a single sample property for demonstration purposes"""
        MessageBoxes.show_error(
            "Error: Demo Data Not Available",
            "This application does not use demo or fallback data.\n\n"
            "Please configure the application with valid API keys and try again."
        )
        return None

    def generate_sample_properties(self, *args, **kwargs):
        """Generate sample properties for demonstration purposes"""
        MessageBoxes.show_error(
            "Error: Demo Data Not Available",
            "This application does not use demo or fallback data.\n\n"
            "Please configure the application with valid API keys and try again."
        )
        return []

    def get_owner_from_property_tree(self):
        """Get owner information for the selected property in the property tree"""
        MessageBoxes.show_error(
            "Error: Demo Data Not Available",
            "This application does not use demo or fallback data.\n\n"
            "Please configure the application with valid API keys and try again."
        )
        return None

    def generate_sample_location_properties(self, *args, **kwargs):
        """Generate sample properties for a location search"""
        MessageBoxes.show_error(
            "Error: Demo Data Not Available",
            "This application does not use demo or fallback data.\n\n"
            "Please configure the application with valid API keys and try again."
        )
        return []

    def generate_sample_properties_in_area(self, *args, **kwargs):
        """
        Generate sample property data for testing

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments

        Returns:
            list: List of property data dictionaries
        """
        MessageBoxes.show_error(
            "Error: Demo Data Not Available",
            "This application does not use demo or fallback data.\n\n"
            "Please configure the application with valid API keys and try again."
        )
        return []
