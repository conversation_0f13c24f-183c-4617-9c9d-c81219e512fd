"""
Property Type Filter UI component for the Czech Property Registry application.

This module provides a UI component for filtering properties by type,
allowing users to select which property types to include in search results.
Key features include:
- Checkbox-based selection of multiple property types
- Search functionality to filter the property type list
- Batch selection controls (Select All, Select None, Invert Selection)
- Scrollable interface for handling large numbers of property types
"""

import tkinter as tk
from tkinter import ttk
import json
import os
import logging
# Import necessary modules

# Set up logging
logger = logging.getLogger(__name__)


class PropertyTypeFilter:
    """
    UI component for filtering properties by type.

    This class creates and manages UI elements for filtering properties
    by their type, with support for multiple selection. It provides:

    - A scrollable list of property type checkboxes
    - Search functionality to filter the list
    - Batch selection controls (Select All, None, Invert)
    - Callback mechanism to notify when selection changes

    The component loads property types from JSON files with a minimal
    set used only to prevent UI crashes if no data is available.
    """

    def __init__(self, parent_frame, app, on_filter_changed=None):
        """
        Initialize the property type filter.

        Sets up the UI component and loads property types from data sources.
        By default, all property types are selected initially.

        Args:
            parent_frame: The parent frame to place the UI elements in
            app: The main application instance for data access
            on_filter_changed: Callback function to call when the filter changes,
                               receives a list of selected type keys as argument
        """
        self.parent = parent_frame
        self.app = app
        self.on_filter_changed = on_filter_changed
        self.selected_types = []
        self.property_types = {}
        self.type_vars = {}

        # Load property types
        self.load_property_types()

        # Create the UI
        self.create_ui()

        logger.info("PropertyTypeFilter initialized")

    def load_property_types(self):
        """
        Load property types from JSON file or data manager.

        Tries multiple sources in order:
        1. Data manager if available
        2. property_types_dict.json in czech_data directory
        3. property_types.json in czech_data directory
        4. Raise an error if no data is available

        Populates self.property_types with a dictionary mapping
        internal keys to display names.
        """
        try:
            # Try to use the data manager if available
            if hasattr(self.app, 'data_manager'):
                try:
                    property_data = self.app.data_manager.load_czech_data('property_types')
                    if property_data and "property_types" in property_data:
                        property_types = property_data["property_types"]
                        self.property_types = {prop_type.lower().replace(" ", "_"): prop_type for prop_type in property_types}
                        logger.info(f"Loaded {len(self.property_types)} property types from data manager")
                        return
                except Exception as e:
                    logger.warning(f"Error loading property types from data manager: {e}")
                    # Continue to file-based loading

            # Try to load from the czech_data directory
            file_path = os.path.join("czech_data", "property_types_dict.json")
            if os.path.exists(file_path):
                with open(file_path, "r", encoding="utf-8") as f:
                    self.property_types = json.load(f)
                    logger.info(f"Loaded {len(self.property_types)} property types from {file_path}")
                    return

            # Try the property_types.json file
            file_path = os.path.join("czech_data", "property_types.json")
            if os.path.exists(file_path):
                with open(file_path, "r", encoding="utf-8") as f:
                    property_data = json.load(f)

                    # Check if it's the new format with property_types key
                    if isinstance(property_data, dict) and "property_types" in property_data:
                        property_types = property_data["property_types"]
                        # Convert list to dictionary
                        self.property_types = {prop_type.lower().replace(" ", "_"): prop_type for prop_type in property_types}
                        logger.info(f"Loaded {len(self.property_types)} property types from {file_path}")
                        return
                    # Check if it's the old format (just a list)
                    elif isinstance(property_data, list):
                        self.property_types = {prop_type.lower().replace(" ", "_"): prop_type for prop_type in property_data}
                        logger.info(f"Loaded {len(self.property_types)} property types from {file_path} (old format)")
                        return

            # If we get here, we couldn't load property types
            error_msg = "No property types data available"
            logger.error(error_msg)
            raise ValueError(error_msg)

        except Exception as e:
            logger.error(f"Error loading property types: {e}", exc_info=True)
            # Use a minimal set of property types for UI to function
            # This is not fallback data, but a last resort to prevent UI crashes
            logger.warning("Using minimal property types set to prevent UI crashes")
            self.property_types = {
                "residential": "Residential Building",
                "commercial": "Commercial Building",
                "industrial": "Industrial Building",
                "apartment": "Apartment",
                "house": "House",
                "land": "Land",
                "other": "Other"
            }

    def create_ui(self):
        """
        Create the UI elements for the property type filter.

        Sets up:
        - A labeled frame containing the filter controls
        - Buttons for batch selection operations
        - A search field for filtering the property type list
        - A scrollable canvas containing checkboxes for each property type
        """
        # Create a labelframe for the property type filter
        self.filter_frame = ttk.LabelFrame(self.parent, text="Filter by Property Type")
        self.filter_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Create a frame for the filter controls
        controls_frame = ttk.Frame(self.filter_frame)
        controls_frame.pack(fill="x", padx=2, pady=2)

        # Create buttons for selecting all, none, or inverting selection
        ttk.Button(
            controls_frame,
            text="Select All",
            command=self.select_all_types,
            style="Small.TButton"
        ).pack(side="left", padx=2, pady=2)

        ttk.Button(
            controls_frame,
            text="Select None",
            command=self.select_no_types,
            style="Small.TButton"
        ).pack(side="left", padx=2, pady=2)

        ttk.Button(
            controls_frame,
            text="Invert Selection",
            command=self.invert_selection,
            style="Small.TButton"
        ).pack(side="left", padx=2, pady=2)

        # Create a search entry for filtering property types
        search_frame = ttk.Frame(self.filter_frame)
        search_frame.pack(fill="x", padx=2, pady=2)

        ttk.Label(search_frame, text="Search:").pack(side="left", padx=2, pady=2)

        self.search_var = tk.StringVar()
        self.search_var.trace_add("write", self.on_search_changed)

        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side="left", fill="x", expand=True, padx=2, pady=2)

        # Create a frame for the property type checkboxes
        self.types_frame = ttk.Frame(self.filter_frame)
        self.types_frame.pack(fill="both", expand=True, padx=2, pady=2)

        # Create a canvas and scrollbar for the property types
        self.canvas = tk.Canvas(self.types_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.types_frame, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=scrollbar.set)

        scrollbar.pack(side="right", fill="y")
        self.canvas.pack(side="left", fill="both", expand=True)

        # Create a frame inside the canvas for the checkboxes
        self.checkboxes_frame = ttk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window((0, 0), window=self.checkboxes_frame, anchor="nw")

        # Configure the canvas to resize with the frame
        self.checkboxes_frame.bind("<Configure>", self.on_frame_configure)
        self.canvas.bind("<Configure>", self.on_canvas_configure)

        # Create checkboxes for each property type
        self.create_type_checkboxes()

    def create_type_checkboxes(self):
        """
        Create checkboxes for each property type.

        Clears existing checkboxes and creates new ones based on:
        - The loaded property types dictionary
        - The current search filter text

        Creates checkboxes in batches for better performance with
        large numbers of property types. All checkboxes are initially
        selected by default.
        """
        # Clear existing checkboxes
        for widget in self.checkboxes_frame.winfo_children():
            widget.destroy()

        # Clear selected types
        self.selected_types = []

        # Create a variable for each property type
        self.type_vars = {}

        # Sort property types alphabetically by display name
        sorted_types = sorted(self.property_types.items(), key=lambda x: x[1])

        # Filter by search text if provided
        search_text = self.search_var.get().lower()
        if search_text:
            sorted_types = [(k, v) for k, v in sorted_types if search_text in v.lower()]

        # Create checkboxes in batches for better performance
        batch_size = 20
        for batch_start in range(0, len(sorted_types), batch_size):
            batch_end = min(batch_start + batch_size, len(sorted_types))
            batch = sorted_types[batch_start:batch_end]

            # Create a checkbox for each property type in the batch
            for i, (type_key, type_name) in enumerate(batch):
                row = batch_start + i

                # Create a variable for this type
                var = tk.BooleanVar(value=True)  # Default to selected
                self.type_vars[type_key] = var

                # Create the checkbox
                checkbox = ttk.Checkbutton(
                    self.checkboxes_frame,
                    text=type_name,
                    variable=var,
                    command=self.on_type_selection_changed
                )
                checkbox.grid(row=row, column=0, sticky="w", padx=2, pady=1)

                # Add this type to the selected types
                self.selected_types.append(type_key)

            # Update the UI to prevent freezing
            self.parent.update_idletasks()

    def on_frame_configure(self, _):
        """
        Handle the frame configuration event.

        Updates the canvas scroll region when the inner frame size changes.
        This ensures the scrollbar works correctly with the dynamic content.

        Args:
            _: Event object (unused but required by tkinter)
        """
        # Update the scrollregion to encompass the inner frame
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def on_canvas_configure(self, event):
        """
        Handle the canvas configuration event.

        Adjusts the width of the inner frame when the canvas is resized.
        This ensures the checkboxes use the full available width.

        Args:
            event: Event object containing the new canvas dimensions
        """
        # Update the width of the canvas window
        self.canvas.itemconfig(self.canvas_window, width=event.width)

    def on_search_changed(self, *_):
        """
        Handle changes to the search text.

        Recreates the checkboxes with the current search filter applied.
        Called automatically when the user types in the search field.

        Args:
            *_: Variable arguments (unused but required by tkinter trace)
        """
        # Recreate the checkboxes with the new search filter
        self.create_type_checkboxes()

    def on_type_selection_changed(self):
        """
        Handle changes to the property type selection.

        Updates the selected_types list based on checkbox states
        and calls the on_filter_changed callback if provided.
        Called when any checkbox is toggled.
        """
        # Update the selected types
        self.selected_types = [type_key for type_key, var in self.type_vars.items() if var.get()]

        # Call the callback if provided
        if self.on_filter_changed:
            self.on_filter_changed(self.selected_types)

    def select_all_types(self):
        """
        Select all property types.

        Sets all checkboxes to checked state, updates the selected_types list,
        and calls the on_filter_changed callback. Called by the "Select All" button.
        """
        for var in self.type_vars.values():
            var.set(True)

        # Update the selected types
        self.selected_types = list(self.type_vars.keys())

        # Call the callback if provided
        if self.on_filter_changed:
            self.on_filter_changed(self.selected_types)

    def select_no_types(self):
        """
        Deselect all property types.

        Sets all checkboxes to unchecked state, clears the selected_types list,
        and calls the on_filter_changed callback. Called by the "Select None" button.
        """
        for var in self.type_vars.values():
            var.set(False)

        # Update the selected types
        self.selected_types = []

        # Call the callback if provided
        if self.on_filter_changed:
            self.on_filter_changed(self.selected_types)

    def invert_selection(self):
        """
        Invert the property type selection.

        Toggles the state of all checkboxes, updates the selected_types list,
        and calls the on_filter_changed callback. Called by the "Invert Selection" button.
        """
        for var in self.type_vars.values():
            var.set(not var.get())

        # Update the selected types
        self.selected_types = [type_key for type_key, var in self.type_vars.items() if var.get()]

        # Call the callback if provided
        if self.on_filter_changed:
            self.on_filter_changed(self.selected_types)

    def get_selected_types(self):
        """
        Get the selected property types.

        Returns:
            list: List of selected property type keys
        """
        return self.selected_types

    def get_selected_type_names(self):
        """
        Get the display names of the selected property types.

        Returns:
            list: List of selected property type display names
        """
        return [self.property_types.get(type_key, type_key) for type_key in self.selected_types]
