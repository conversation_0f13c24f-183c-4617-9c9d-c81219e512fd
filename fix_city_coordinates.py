"""
Fix city coordinates issues in the Czech Property Registry application.

This script:
1. Uses the Google Maps API directly to get accurate coordinates for cities
2. Creates a city coordinates database for reliable lookups
3. Modifies the application to use these coordinates consistently
4. Clears all caches to ensure fresh data
"""

import os
import sys
import json
import logging
import shutil
import subprocess
import requests
import time
from typing import Dict, Any, List, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Cities to include in the database
CZECH_CITIES = [
    "Praha", "Brno", "Ostrava", "Plzeň", "Liberec", "Olomouc", "České Budějovice", 
    "Hradec Králové", "Ústí nad Labem", "Pardubice", "Zlín", "Hav<PERSON><PERSON>ov", "Kladno", 
    "Most", "Opava", "Frýdek-Místek", "Karviná", "Jihlava", "<PERSON><PERSON><PERSON>", "D<PERSON>čín",
    "Karlovy Vary", "<PERSON>mu<PERSON>", "Jablonec nad Nisou", "Mladá Boleslav", "Prostějov",
    "Přerov", "Česká Lípa", "Třebíč", "Třinec", "Tábor", "Znojmo", "Příbram",
    "Cheb", "Kolín", "Trutnov", "Orlová", "Písek", "Kroměříž", "Šumperk",
    "Vsetín", "Uherské Hradiště", "Břeclav", "Hodonín", "Český Těšín", "Litoměřice",
    "Litvínov", "Havlíčkův Brod", "Nový Jičín", "Chrudim", "Krnov", "Sokolov",
    "Strakonice", "Valašské Meziříčí", "Klatovy", "Kopřivnice", "Jindřichův Hradec",
    "Vyškov", "Žďár nad Sázavou", "Bohumín", "Blansko", "Mělník", "Náchod",
    "Brandýs nad Labem-Stará Boleslav", "Jirkov", "Žatec", "Kralupy nad Vltavou",
    "Louny", "Kadaň", "Hranice", "Otrokovice", "Bílina", "Benešov", "Svitavy",
    "Jičín", "Ostrov", "Uherský Brod", "Rožnov pod Radhoštěm", "Neratovice",
    "Pelhřimov", "Bruntál", "Dvůr Králové nad Labem", "Varnsdorf", "Nymburk",
    "Klášterec nad Ohří", "Turnov", "Jeseník", "Rokycany", "Poděbrady", "Slaný",
    "Rakovník", "Mariánské Lázně", "Stod", "Choťovice", "Velké Všelisy"
]

def get_google_maps_api_key():
    """Get the Google Maps API key from the config file."""
    try:
        # Try to read from config.ini
        import configparser
        config = configparser.ConfigParser()
        
        # Check if config.ini exists
        if os.path.exists('config.ini'):
            config.read('config.ini')
            if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
                api_key = config['GOOGLE_MAPS']['api_key']
                logger.info("Found Google Maps API key in config.ini")
                return api_key
        
        # If not found, try to read from environment variables
        if 'GOOGLE_MAPS_API_KEY' in os.environ:
            api_key = os.environ['GOOGLE_MAPS_API_KEY']
            logger.info("Found Google Maps API key in environment variables")
            return api_key
        
        logger.error("Google Maps API key not found")
        return None
    except Exception as e:
        logger.error(f"Error getting Google Maps API key: {e}")
        return None

def geocode_city(city_name: str, api_key: str) -> Dict[str, Any]:
    """
    Geocode a city using the Google Maps Geocoding API.
    
    Args:
        city_name (str): Name of the city
        api_key (str): Google Maps API key
        
    Returns:
        dict: City data with coordinates and boundaries
    """
    try:
        # Add country to city name for more accurate results
        query = f"{city_name}, Czech Republic"
        
        # Encode the query
        import urllib.parse
        encoded_query = urllib.parse.quote(query)
        
        # Build the URL
        url = f"https://maps.googleapis.com/maps/api/geocode/json?address={encoded_query}&key={api_key}"
        
        # Make the request
        response = requests.get(url)
        data = response.json()
        
        if data.get('status') == 'OK' and data.get('results'):
            result = data['results'][0]
            
            # Check if this is a city/locality
            is_city = False
            for component in result.get('address_components', []):
                if 'locality' in component.get('types', []) or 'administrative_area_level_2' in component.get('types', []):
                    is_city = True
                    break
            
            if not is_city:
                logger.warning(f"{city_name} does not appear to be a city/locality")
            
            # Get the coordinates
            location = result.get('geometry', {}).get('location', {})
            lat = location.get('lat')
            lng = location.get('lng')
            
            # Get the viewport
            viewport = result.get('geometry', {}).get('viewport', {})
            northeast = viewport.get('northeast', {})
            southwest = viewport.get('southwest', {})
            
            # Get the formatted address
            formatted_address = result.get('formatted_address', '')
            
            # Get the place ID
            place_id = result.get('place_id', '')
            
            logger.info(f"Geocoded {city_name}: {lat}, {lng}")
            
            return {
                'name': city_name,
                'formatted_address': formatted_address,
                'lat': lat,
                'lng': lng,
                'place_id': place_id,
                'viewport': {
                    'northeast': {
                        'lat': northeast.get('lat'),
                        'lng': northeast.get('lng')
                    },
                    'southwest': {
                        'lat': southwest.get('lat'),
                        'lng': southwest.get('lng')
                    }
                }
            }
        else:
            logger.error(f"Failed to geocode {city_name}: {data.get('status')}")
            return {
                'name': city_name,
                'error': data.get('status'),
                'error_message': data.get('error_message', 'Unknown error')
            }
    except Exception as e:
        logger.error(f"Error geocoding {city_name}: {e}")
        return {
            'name': city_name,
            'error': str(e)
        }

def create_city_coordinates_database(api_key: str) -> Dict[str, Dict[str, Any]]:
    """
    Create a database of city coordinates.
    
    Args:
        api_key (str): Google Maps API key
        
    Returns:
        dict: Database of city coordinates
    """
    logger.info("Creating city coordinates database...")
    
    database = {}
    
    for city in CZECH_CITIES:
        try:
            # Add a small delay to avoid hitting API rate limits
            time.sleep(0.2)
            
            # Geocode the city
            city_data = geocode_city(city, api_key)
            
            # Add to database if geocoding was successful
            if 'lat' in city_data and 'lng' in city_data:
                database[city.lower()] = city_data
                logger.info(f"Added {city} to database: {city_data['lat']}, {city_data['lng']}")
            else:
                logger.error(f"Failed to geocode {city}: {city_data.get('error', 'Unknown error')}")
        except Exception as e:
            logger.error(f"Error processing {city}: {e}")
    
    logger.info(f"Created city coordinates database with {len(database)} cities")
    return database

def save_city_coordinates_database(database: Dict[str, Dict[str, Any]]) -> str:
    """
    Save the city coordinates database to a file.
    
    Args:
        database (dict): Database of city coordinates
        
    Returns:
        str: Path to the saved database file
    """
    try:
        # Create the directory if it doesn't exist
        os.makedirs('data', exist_ok=True)
        
        # Save the database to a JSON file
        database_path = 'data/city_coordinates.json'
        with open(database_path, 'w', encoding='utf-8') as f:
            json.dump(database, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved city coordinates database to {database_path}")
        return database_path
    except Exception as e:
        logger.error(f"Error saving city coordinates database: {e}")
        return None

def modify_application_to_use_database():
    """Modify the application to use the city coordinates database."""
    try:
        # Create the city lookup service
        city_lookup_service_path = 'utils/city_lookup_service.py'
        
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(city_lookup_service_path), exist_ok=True)
        
        # Create the city lookup service
        with open(city_lookup_service_path, 'w', encoding='utf-8') as f:
            f.write('''"""
City lookup service for the Czech Property Registry application.

This service provides reliable city coordinates lookup using a pre-built database.
"""

import os
import json
import logging
from typing import Dict, Any, Tuple, Optional

# Configure logging
logger = logging.getLogger(__name__)

class CityLookupService:
    """Service for looking up city coordinates."""
    
    def __init__(self):
        """Initialize the city lookup service."""
        self.database = {}
        self.load_database()
    
    def load_database(self):
        """Load the city coordinates database."""
        try:
            database_path = 'data/city_coordinates.json'
            if os.path.exists(database_path):
                with open(database_path, 'r', encoding='utf-8') as f:
                    self.database = json.load(f)
                logger.info(f"Loaded city coordinates database with {len(self.database)} cities")
            else:
                logger.warning(f"City coordinates database not found at {database_path}")
        except Exception as e:
            logger.error(f"Error loading city coordinates database: {e}")
    
    def get_city_coordinates(self, city_name: str) -> Tuple[Optional[float], Optional[float]]:
        """
        Get the coordinates for a city.
        
        Args:
            city_name (str): Name of the city
            
        Returns:
            tuple: (lat, lng) coordinates of the city or (None, None) if not found
        """
        try:
            # Normalize the city name
            normalized_name = city_name.lower()
            
            # Check if the city is in the database
            if normalized_name in self.database:
                city_data = self.database[normalized_name]
                lat = city_data.get('lat')
                lng = city_data.get('lng')
                logger.info(f"Found coordinates for {city_name}: {lat}, {lng}")
                return (lat, lng)
            
            # If not found, try to find a partial match
            for db_city_name, city_data in self.database.items():
                if normalized_name in db_city_name or db_city_name in normalized_name:
                    lat = city_data.get('lat')
                    lng = city_data.get('lng')
                    logger.info(f"Found partial match for {city_name} ({db_city_name}): {lat}, {lng}")
                    return (lat, lng)
            
            logger.warning(f"City not found in database: {city_name}")
            return (None, None)
        except Exception as e:
            logger.error(f"Error getting coordinates for {city_name}: {e}")
            return (None, None)
    
    def get_city_data(self, city_name: str) -> Dict[str, Any]:
        """
        Get all data for a city.
        
        Args:
            city_name (str): Name of the city
            
        Returns:
            dict: City data or empty dict if not found
        """
        try:
            # Normalize the city name
            normalized_name = city_name.lower()
            
            # Check if the city is in the database
            if normalized_name in self.database:
                return self.database[normalized_name]
            
            # If not found, try to find a partial match
            for db_city_name, city_data in self.database.items():
                if normalized_name in db_city_name or db_city_name in normalized_name:
                    return city_data
            
            logger.warning(f"City not found in database: {city_name}")
            return {}
        except Exception as e:
            logger.error(f"Error getting data for {city_name}: {e}")
            return {}
''')
        
        logger.info(f"Created city lookup service at {city_lookup_service_path}")
        
        # Now modify the batch search manager to use the city lookup service
        batch_search_manager_path = 'core/real_batch_search_manager.py'
        
        if os.path.exists(batch_search_manager_path):
            with open(batch_search_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add import for the city lookup service
            if 'from utils.city_lookup_service import CityLookupService' not in content:
                import_index = content.find('import')
                if import_index >= 0:
                    # Find the end of the import block
                    import_block_end = content.find('\n\n', import_index)
                    if import_block_end >= 0:
                        content = content[:import_block_end] + '\nfrom utils.city_lookup_service import CityLookupService' + content[import_block_end:]
            
            # Add initialization of the city lookup service
            if 'self.city_lookup_service = CityLookupService()' not in content:
                init_method = content.find('def __init__')
                if init_method >= 0:
                    # Find the end of the __init__ method
                    init_block_end = content.find('\n\n', init_method)
                    if init_block_end >= 0:
                        # Find the last line of the __init__ method
                        last_line = content.rfind('\n', init_method, init_block_end)
                        if last_line >= 0:
                            # Add the city lookup service initialization
                            content = content[:last_line] + '\n        # Initialize the city lookup service\n        self.city_lookup_service = CityLookupService()' + content[last_line:]
            
            # Modify the get_coordinates_for_address method to use the city lookup service
            if 'def get_coordinates_for_address' in content:
                # Find the start of the method
                method_start = content.find('def get_coordinates_for_address')
                if method_start >= 0:
                    # Find the end of the method
                    method_end = content.find('\n\n', method_start)
                    if method_end >= 0:
                        # Replace the method with our improved version
                        new_method = '''    def get_coordinates_for_address(self, address: str) -> Tuple[Optional[float], Optional[float]]:
        """
        Get coordinates for an address.
        
        Args:
            address (str): Address to geocode
            
        Returns:
            tuple: (lat, lng) coordinates or (None, None) if geocoding failed
        """
        try:
            # First check if this is a city in our database
            city_coords = self.city_lookup_service.get_city_coordinates(address)
            if city_coords[0] is not None and city_coords[1] is not None:
                logger.info(f"Using coordinates from city database for {address}: {city_coords}")
                return city_coords
            
            # If not found in the database, use Google Maps geocoding
            if hasattr(self.app, 'google_maps_api'):
                # Geocode the address
                geocode_result = self.app.google_maps_api.geocode(address)
                
                if geocode_result and 'lat' in geocode_result and 'lng' in geocode_result:
                    lat = geocode_result['lat']
                    lng = geocode_result['lng']
                    logger.info(f"Geocoded {address}: {lat}, {lng}")
                    return (lat, lng)
            
            logger.error(f"Failed to get coordinates for address: {address}")
            return (None, None)
        except Exception as e:
            logger.error(f"Error getting coordinates for address: {e}")
            return (None, None)'''
                        
                        content = content[:method_start] + new_method + content[method_end:]
            
            # Write the modified content back to the file
            with open(batch_search_manager_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Modified batch search manager to use city lookup service: {batch_search_manager_path}")
        else:
            logger.error(f"Batch search manager not found at {batch_search_manager_path}")
        
        return True
    except Exception as e:
        logger.error(f"Error modifying application: {e}")
        return False

def clear_cache_directories():
    """Clear all cache directories."""
    cache_dirs = [
        'cache',
        '.cache',
        'czech_data/cache',
        'data/cache',
    ]
    
    cleared_count = 0
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir) and os.path.isdir(cache_dir):
            try:
                # Remove the directory and all its contents
                shutil.rmtree(cache_dir)
                logger.info(f"Cleared cache directory: {cache_dir}")
                cleared_count += 1
                
                # Recreate the empty directory
                os.makedirs(cache_dir, exist_ok=True)
            except Exception as e:
                logger.error(f"Error clearing cache directory {cache_dir}: {e}")
    
    return cleared_count

def restart_application():
    """Restart the main application."""
    try:
        # Get the path to the main.py file
        main_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
        
        # Check if the file exists
        if not os.path.exists(main_path):
            logger.error(f"Main file not found at: {main_path}")
            return False
        
        # Launch the application with the current Python interpreter
        cmd = [sys.executable, main_path]
        
        # Use Popen to launch the process without waiting for it to complete
        subprocess.Popen(cmd)
        
        return True
    except Exception as e:
        logger.error(f"Error restarting application: {e}")
        return False

def main():
    """Main function to fix city coordinates issues."""
    logger.info("Starting city coordinates fix...")
    
    # Get the Google Maps API key
    api_key = get_google_maps_api_key()
    if not api_key:
        logger.error("Google Maps API key not found. Exiting.")
        return
    
    # Create the city coordinates database
    database = create_city_coordinates_database(api_key)
    
    # Save the database
    database_path = save_city_coordinates_database(database)
    if not database_path:
        logger.error("Failed to save city coordinates database. Exiting.")
        return
    
    # Modify the application to use the database
    if not modify_application_to_use_database():
        logger.error("Failed to modify application. Exiting.")
        return
    
    # Clear cache directories
    clear_cache_directories()
    
    # Restart the application
    if restart_application():
        logger.info("Application restarted successfully")
    else:
        logger.error("Failed to restart application")
        logger.info("Please restart the application manually by running 'python main.py'")
    
    logger.info("City coordinates fix completed")

if __name__ == "__main__":
    main()
