"""
Cache Status Indicator for the Czech Property Registry application.

This module provides a UI component that displays information about cached data,
including when it was cached and how old it is.
"""

import tkinter as tk
from tkinter import ttk
import time
import datetime
import logging
import threading

# Configure logging
logger = logging.getLogger(__name__)

class CacheStatusIndicator:
    """
    UI component that displays information about cached data.
    
    This component shows when cached data is being used and how old it is,
    providing users with transparency about the data they're viewing.
    """
    
    def __init__(self, parent, position="bottom", font=None):
        """
        Initialize the cache status indicator.
        
        Args:
            parent: Parent widget
            position (str): Where to place the indicator ("bottom", "top", "statusbar")
            font: Font to use for the indicator text
        """
        self.parent = parent
        self.position = position
        self.font = font or ("Arial", 8)
        self.frame = None
        self.label = None
        self.visible = False
        self.auto_hide_timer = None
        self.cache_info = {}
        self.lock = threading.Lock()
        
        # Create the indicator
        self._create_indicator()
    
    def _create_indicator(self):
        """Create the cache status indicator UI elements."""
        # Create a frame for the indicator
        self.frame = ttk.Frame(self.parent)
        
        # Create a style for the indicator
        style = ttk.Style()
        style.configure("CacheIndicator.TLabel", 
                        font=self.font,
                        foreground="#e65100",  # Dark orange
                        background="#fff3e0")  # Light orange
        
        # Create the label
        self.label = ttk.Label(
            self.frame,
            text="",
            style="CacheIndicator.TLabel",
            anchor=tk.W,
            padding=(5, 2)
        )
        self.label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Initially hide the indicator
        self.hide()
    
    def show(self, data_type, timestamp, auto_hide=True, duration=5000):
        """
        Show the cache status indicator with information about cached data.
        
        Args:
            data_type (str): Type of data being displayed from cache
            timestamp (float): Unix timestamp when the data was cached
            auto_hide (bool): Whether to automatically hide the indicator after a duration
            duration (int): Duration in milliseconds before auto-hiding
        """
        with self.lock:
            # Store the cache info
            self.cache_info[data_type] = {
                "timestamp": timestamp,
                "displayed_at": time.time()
            }
            
            # Update the indicator text
            self._update_text()
            
            # Show the indicator if it's not already visible
            if not self.visible:
                self._show_indicator()
            
            # Cancel any existing auto-hide timer
            if self.auto_hide_timer:
                self.parent.after_cancel(self.auto_hide_timer)
                self.auto_hide_timer = None
            
            # Set up auto-hide if requested
            if auto_hide:
                self.auto_hide_timer = self.parent.after(duration, self.hide)
    
    def _show_indicator(self):
        """Show the indicator in the appropriate position."""
        if self.position == "bottom":
            self.frame.pack(side=tk.BOTTOM, fill=tk.X, padx=0, pady=0)
        elif self.position == "top":
            self.frame.pack(side=tk.TOP, fill=tk.X, padx=0, pady=0)
        elif self.position == "statusbar":
            # For statusbar, we don't pack the frame, just update the text
            # This assumes the parent is a status bar or similar widget
            pass
        
        self.visible = True
    
    def hide(self):
        """Hide the cache status indicator."""
        if self.frame and self.visible:
            self.frame.pack_forget()
            self.visible = False
        
        # Cancel any auto-hide timer
        if self.auto_hide_timer:
            self.parent.after_cancel(self.auto_hide_timer)
            self.auto_hide_timer = None
        
        # Clear the cache info
        with self.lock:
            self.cache_info = {}
    
    def _update_text(self):
        """Update the indicator text based on the current cache info."""
        if not self.cache_info:
            self.label.config(text="")
            return
        
        # Get the current time
        current_time = time.time()
        
        # Create a list of cache info strings
        cache_strings = []
        
        for data_type, info in self.cache_info.items():
            # Calculate how old the cache is
            age_seconds = current_time - info["timestamp"]
            age_str = self._format_age(age_seconds)
            
            # Add to the list of cache strings
            cache_strings.append(f"{data_type}: {age_str} old")
        
        # Join the cache strings with commas
        if len(cache_strings) == 1:
            text = f"Using cached data: {cache_strings[0]}"
        else:
            text = f"Using cached data: {', '.join(cache_strings)}"
        
        # Update the label text
        self.label.config(text=text)
    
    def _format_age(self, seconds):
        """
        Format the age of cached data in a human-readable format.
        
        Args:
            seconds (float): Age in seconds
            
        Returns:
            str: Formatted age string
        """
        if seconds < 60:
            return "just now"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            return f"{minutes} minute{'s' if minutes != 1 else ''}"
        elif seconds < 86400:
            hours = int(seconds / 3600)
            return f"{hours} hour{'s' if hours != 1 else ''}"
        elif seconds < 604800:
            days = int(seconds / 86400)
            return f"{days} day{'s' if days != 1 else ''}"
        elif seconds < 2592000:
            weeks = int(seconds / 604800)
            return f"{weeks} week{'s' if weeks != 1 else ''}"
        elif seconds < 31536000:
            months = int(seconds / 2592000)
            return f"{months} month{'s' if months != 1 else ''}"
        else:
            years = int(seconds / 31536000)
            return f"{years} year{'s' if years != 1 else ''}"
    
    def update(self):
        """Update the indicator text (call periodically to update age display)."""
        if self.visible and self.cache_info:
            with self.lock:
                self._update_text()
            
            # Schedule another update in 30 seconds
            self.parent.after(30000, self.update)
