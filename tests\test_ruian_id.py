"""
Tests for RUIAN ID validation and model.

This module contains tests for the RUIAN ID validation module and model.
"""

import os
import sys
import unittest
import logging

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RUIANTest")

# Import the modules to test
from utils.ruian_validator import (
    clean_ruian_id,
    is_valid_ruian_id,
    validate_ruian_id,
    verify_ruian_id_exists,
    RUIAN_ID_MIN_LENGTH,
    RUIAN_ID_MAX_LENGTH,
    ValidationLevel,
    INVALID_RUIAN_VALUES
)
from models.ruian_id import RuianID
import unittest.mock


class TestRuianValidator(unittest.TestCase):
    """Test cases for the RUIAN ID validation module."""

    def test_clean_ruian_id(self):
        """Test the RUIAN ID cleaning functionality."""
        test_cases = [
            ("123456", "123456"),  # Already clean
            ("123-456", "123456"),  # With hyphen
            ("RUIAN:123456", "123456"),  # With prefix
            ("ruian:123456", "123456"),  # With lowercase prefix
            ("ref:ruian=123456", "123456"),  # OSM tag format
            ("ref:ruian:123456", "123456"),  # Alternative OSM tag format
            ("Not a RUIAN ID", ""),  # Invalid
            ("", ""),  # Empty
            (None, ""),  # None
            (123456, "123456"),  # Integer
        ]

        for input_id, expected_output in test_cases:
            clean_id = clean_ruian_id(input_id)
            self.assertEqual(clean_id, expected_output, f"Failed to clean '{input_id}' correctly")

    def test_is_valid_ruian_id(self):
        """Test the RUIAN ID validation functionality."""
        valid_ids = [
            "123456",  # Minimum length
            "1234567890",  # Maximum length
            123456,  # Integer
            "RUIAN:123456",  # With prefix
            "ref:ruian=123456",  # OSM tag format
        ]

        invalid_ids = [
            "12345",  # Too short
            "12345678901",  # Too long
            "Not a RUIAN ID",  # Invalid characters
            "",  # Empty
            None,  # None
            "Unknown",  # Unknown
        ]

        for ruian_id in valid_ids:
            self.assertTrue(is_valid_ruian_id(ruian_id), f"'{ruian_id}' should be valid")

        for ruian_id in invalid_ids:
            self.assertFalse(is_valid_ruian_id(ruian_id), f"'{ruian_id}' should be invalid")

    @unittest.mock.patch('utils.ruian_validator.verify_ruian_id_exists')
    def test_is_valid_ruian_id_with_validation_levels(self, mock_verify):
        """Test the RUIAN ID validation functionality with different validation levels."""
        # Test with basic validation
        self.assertTrue(is_valid_ruian_id("123456", ValidationLevel.BASIC))
        self.assertTrue(is_valid_ruian_id("0123456", ValidationLevel.BASIC))  # Starts with 0, valid in BASIC

        # Test with strict validation
        self.assertTrue(is_valid_ruian_id("123456", ValidationLevel.STRICT))
        self.assertFalse(is_valid_ruian_id("0123456", ValidationLevel.STRICT))  # Starts with 0, invalid in STRICT
        self.assertFalse(is_valid_ruian_id("000000", ValidationLevel.STRICT))  # All zeros, invalid in STRICT

        # Test with online validation
        # Mock a valid RUIAN ID
        mock_verify.return_value = (True, None)
        self.assertTrue(is_valid_ruian_id("123456", ValidationLevel.ONLINE))

        # Mock an invalid RUIAN ID
        mock_verify.return_value = (False, "RUIAN ID not found in CUZK system")
        self.assertFalse(is_valid_ruian_id("123456", ValidationLevel.ONLINE))

    def test_validate_ruian_id(self):
        """Test the RUIAN ID validation with detailed results."""
        test_cases = [
            # (input_id, expected_is_valid, expected_clean_id, expected_error_message)
            ("123456", True, "123456", None),  # Valid
            ("RUIAN:123456", True, "123456", None),  # Valid with prefix
            ("12345", False, "12345", f"RUIAN ID '12345' is too short (minimum {RUIAN_ID_MIN_LENGTH} digits)"),  # Too short
            ("12345678901", False, "12345678901", f"RUIAN ID '12345678901' is too long (maximum {RUIAN_ID_MAX_LENGTH} digits)"),  # Too long
            ("Not a RUIAN ID", False, "", "RUIAN ID 'Not a RUIAN ID' contains no digits"),  # Invalid characters
            ("", False, "", "RUIAN ID '' contains no digits"),  # Empty
            (None, False, "", "RUIAN ID cannot be None"),  # None
            ("Unknown", False, "", "RUIAN ID 'Unknown' is a known invalid value"),  # Unknown
        ]

        for input_id, expected_is_valid, expected_clean_id, expected_error_message in test_cases:
            is_valid, clean_id, error_message = validate_ruian_id(input_id)
            self.assertEqual(is_valid, expected_is_valid, f"Validation result for '{input_id}' is incorrect")
            self.assertEqual(clean_id, expected_clean_id, f"Cleaned ID for '{input_id}' is incorrect")
            self.assertEqual(error_message, expected_error_message, f"Error message for '{input_id}' is incorrect")


class TestRuianIDModel(unittest.TestCase):
    """Test cases for the RUIAN ID model."""

    def test_init(self):
        """Test the initialization of the RUIAN ID model."""
        # Valid RUIAN ID
        ruian_id = RuianID("123456")
        self.assertEqual(ruian_id.value, "123456")
        self.assertEqual(ruian_id.original_value, "123456")
        self.assertTrue(ruian_id.is_valid)
        self.assertIsNone(ruian_id.error_message)
        self.assertEqual(ruian_id.validation_level, ValidationLevel.BASIC)
        self.assertFalse(ruian_id.verified)
        self.assertFalse(ruian_id.exists_in_cuzk)

        # Valid RUIAN ID with prefix
        ruian_id = RuianID("RUIAN:123456")
        self.assertEqual(ruian_id.value, "123456")
        self.assertEqual(ruian_id.original_value, "RUIAN:123456")
        self.assertTrue(ruian_id.is_valid)
        self.assertIsNone(ruian_id.error_message)

        # Invalid RUIAN ID
        with self.assertRaises(ValueError):
            RuianID("12345")  # Too short

        with self.assertRaises(ValueError):
            RuianID("12345678901")  # Too long

        with self.assertRaises(ValueError):
            RuianID("Not a RUIAN ID")  # Invalid characters

        with self.assertRaises(ValueError):
            RuianID(None)  # None

        with self.assertRaises(ValueError):
            RuianID("Unknown")  # Unknown

    @unittest.mock.patch('utils.ruian_validator.verify_ruian_id_exists')
    def test_init_with_validation_levels(self, mock_verify):
        """Test the initialization of the RUIAN ID model with different validation levels."""
        # Test with basic validation
        ruian_id = RuianID("123456", ValidationLevel.BASIC)
        self.assertEqual(ruian_id.value, "123456")
        self.assertTrue(ruian_id.is_valid)
        self.assertEqual(ruian_id.validation_level, ValidationLevel.BASIC)
        self.assertFalse(ruian_id.verified)

        # Test with strict validation
        ruian_id = RuianID("123456", ValidationLevel.STRICT)
        self.assertEqual(ruian_id.value, "123456")
        self.assertTrue(ruian_id.is_valid)
        self.assertEqual(ruian_id.validation_level, ValidationLevel.STRICT)
        self.assertFalse(ruian_id.verified)

        # Invalid in strict validation
        with self.assertRaises(ValueError):
            RuianID("0123456", ValidationLevel.STRICT)  # Starts with 0

        # Test with online validation
        # Mock a valid RUIAN ID
        mock_verify.return_value = (True, None)
        ruian_id = RuianID("123456", ValidationLevel.ONLINE)
        self.assertEqual(ruian_id.value, "123456")
        self.assertTrue(ruian_id.is_valid)
        self.assertEqual(ruian_id.validation_level, ValidationLevel.ONLINE)
        self.assertTrue(ruian_id.verified)
        self.assertTrue(ruian_id.exists_in_cuzk)

        # Reset the mock for the next test
        mock_verify.reset_mock()

        # Mock an invalid RUIAN ID
        mock_verify.return_value = (False, "RUIAN ID not found in CUZK system")
        with self.assertRaises(ValueError):
            RuianID("123456", ValidationLevel.ONLINE)

    def test_from_string(self):
        """Test the creation of a RUIAN ID from a string."""
        # Valid RUIAN ID
        ruian_id = RuianID.from_string("123456")
        self.assertIsNotNone(ruian_id)
        self.assertEqual(ruian_id.value, "123456")

        # Valid RUIAN ID with prefix
        ruian_id = RuianID.from_string("RUIAN:123456")
        self.assertIsNotNone(ruian_id)
        self.assertEqual(ruian_id.value, "123456")

        # Invalid RUIAN ID
        ruian_id = RuianID.from_string("12345")  # Too short
        self.assertIsNone(ruian_id)

        ruian_id = RuianID.from_string("12345678901")  # Too long
        self.assertIsNone(ruian_id)

        ruian_id = RuianID.from_string("Not a RUIAN ID")  # Invalid characters
        self.assertIsNone(ruian_id)

        ruian_id = RuianID.from_string(None)  # None
        self.assertIsNone(ruian_id)

        ruian_id = RuianID.from_string("Unknown")  # Unknown
        self.assertIsNone(ruian_id)

    @unittest.mock.patch('utils.ruian_validator.verify_ruian_id_exists')
    def test_from_string_with_validation_levels(self, mock_verify):
        """Test the creation of a RUIAN ID from a string with different validation levels."""
        # Test with basic validation
        ruian_id = RuianID.from_string("123456", ValidationLevel.BASIC)
        self.assertIsNotNone(ruian_id)
        self.assertEqual(ruian_id.value, "123456")
        self.assertEqual(ruian_id.validation_level, ValidationLevel.BASIC)

        # Test with strict validation
        ruian_id = RuianID.from_string("123456", ValidationLevel.STRICT)
        self.assertIsNotNone(ruian_id)
        self.assertEqual(ruian_id.value, "123456")
        self.assertEqual(ruian_id.validation_level, ValidationLevel.STRICT)

        # Invalid in strict validation
        ruian_id = RuianID.from_string("0123456", ValidationLevel.STRICT)
        self.assertIsNone(ruian_id)

        # Test with online validation
        # Mock a valid RUIAN ID
        mock_verify.return_value = (True, None)
        ruian_id = RuianID.from_string("123456", ValidationLevel.ONLINE)
        self.assertIsNotNone(ruian_id)
        self.assertEqual(ruian_id.value, "123456")
        self.assertEqual(ruian_id.validation_level, ValidationLevel.ONLINE)
        self.assertTrue(ruian_id.verified)
        self.assertTrue(ruian_id.exists_in_cuzk)

        # Reset the mock for the next test
        mock_verify.reset_mock()

        # Mock an invalid RUIAN ID
        mock_verify.return_value = (False, "RUIAN ID not found in CUZK system")
        ruian_id = RuianID.from_string("123456", ValidationLevel.ONLINE)
        self.assertIsNone(ruian_id)

    def test_validate(self):
        """Test the validation of a RUIAN ID."""
        self.assertTrue(RuianID.validate("123456"))
        self.assertTrue(RuianID.validate("RUIAN:123456"))
        self.assertFalse(RuianID.validate("12345"))
        self.assertFalse(RuianID.validate("12345678901"))
        self.assertFalse(RuianID.validate("Not a RUIAN ID"))
        self.assertFalse(RuianID.validate(None))
        self.assertFalse(RuianID.validate("Unknown"))

    @unittest.mock.patch('utils.ruian_validator.verify_ruian_id_exists')
    def test_validate_with_validation_levels(self, mock_verify):
        """Test the validation of a RUIAN ID with different validation levels."""
        # Test with basic validation
        self.assertTrue(RuianID.validate("123456", ValidationLevel.BASIC))
        self.assertTrue(RuianID.validate("0123456", ValidationLevel.BASIC))  # Starts with 0, valid in BASIC

        # Test with strict validation
        self.assertTrue(RuianID.validate("123456", ValidationLevel.STRICT))
        self.assertFalse(RuianID.validate("0123456", ValidationLevel.STRICT))  # Starts with 0, invalid in STRICT
        self.assertFalse(RuianID.validate("000000", ValidationLevel.STRICT))  # All zeros, invalid in STRICT

        # Test with online validation
        # Mock a valid RUIAN ID
        mock_verify.return_value = (True, None)
        self.assertTrue(RuianID.validate("123456", ValidationLevel.ONLINE))

        # Reset the mock for the next test
        mock_verify.reset_mock()

        # Mock an invalid RUIAN ID
        mock_verify.return_value = (False, "RUIAN ID not found in CUZK system")
        self.assertFalse(RuianID.validate("123456", ValidationLevel.ONLINE))

    def test_clean(self):
        """Test the cleaning of a RUIAN ID."""
        self.assertEqual(RuianID.clean("123456"), "123456")
        self.assertEqual(RuianID.clean("RUIAN:123456"), "123456")
        self.assertEqual(RuianID.clean("123-456"), "123456")
        self.assertEqual(RuianID.clean("Not a RUIAN ID"), "")
        self.assertEqual(RuianID.clean(None), "")

    def test_string_representation(self):
        """Test the string representation of a RUIAN ID."""
        ruian_id = RuianID("123456")
        self.assertEqual(str(ruian_id), "123456")
        self.assertEqual(repr(ruian_id), "RuianID('123456')")

    def test_equality(self):
        """Test the equality of RUIAN IDs."""
        ruian_id1 = RuianID("123456")
        ruian_id2 = RuianID("123456")
        ruian_id3 = RuianID("654321")

        self.assertEqual(ruian_id1, ruian_id2)
        self.assertNotEqual(ruian_id1, ruian_id3)
        self.assertEqual(ruian_id1, "123456")
        self.assertEqual(ruian_id1, 123456)
        self.assertNotEqual(ruian_id1, "654321")
        self.assertNotEqual(ruian_id1, 654321)
        self.assertNotEqual(ruian_id1, None)

    def test_bool(self):
        """Test the boolean evaluation of a RUIAN ID."""
        ruian_id = RuianID("123456")
        self.assertTrue(bool(ruian_id))

    def test_verify(self):
        """Test the verification of a RUIAN ID against the CUZK system."""
        # Create a RUIAN ID
        ruian_id = RuianID("123456")
        self.assertFalse(ruian_id.verified)
        self.assertFalse(ruian_id.exists_in_cuzk)

        # Manually set the verification result for testing
        ruian_id._verified = True
        ruian_id._exists_in_cuzk = True

        # Verify the RUIAN ID (should use cached result)
        result = ruian_id.verify()

        # Check the result
        self.assertTrue(result)
        self.assertTrue(ruian_id.verified)
        self.assertTrue(ruian_id.exists_in_cuzk)

        # Create a new RUIAN ID
        ruian_id = RuianID("654321")

        # Manually set the verification result for testing
        ruian_id._verified = True
        ruian_id._exists_in_cuzk = False

        # Verify the RUIAN ID (should use cached result)
        result = ruian_id.verify()

        # Check the result
        self.assertFalse(result)
        self.assertTrue(ruian_id.verified)
        self.assertFalse(ruian_id.exists_in_cuzk)


if __name__ == '__main__':
    unittest.main()
