"""
Test the conversion of building coordinates to CUZK MapaIdentifikace URLs.

This test verifies that the application can convert building coordinates to S-JTSK format
and generate MapaIdentifikace URLs for buildings found in batch search results.
"""

import unittest
import logging
import sys
import os
from unittest.mock import patch, MagicMock

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from api.cuzk.cuzk_integration import CUZKIntegration
from core.batch_search_manager import BatchSearchManager

# Set up logging
logger = logging.getLogger(__name__)


class TestBuildingToCUZKURL(unittest.TestCase):
    """Test the conversion of building coordinates to CUZK MapaIdentifikace URLs."""

    def setUp(self):
        """Set up the test environment."""
        # Create a mock app
        self.app = MagicMock()

        # Create the CUZK integration
        self.cuzk_integration = CUZKIntegration()

        # Create the batch search manager
        self.batch_search_manager = BatchSearchManager(self.app)

        # Set up the app with the integrations
        self.app.cuzk_integration = self.cuzk_integration

        # Test coordinates (Prague)
        self.test_lat = 50.0755
        self.test_lng = 14.4378

        # Create a sample property
        self.sample_property = {
            'lat': self.test_lat,
            'lng': self.test_lng,
            'property_type': 'residential',
            'owner_name': 'Test Owner',
            'owner_address': 'Test Address',
            'source': 'test'
        }

    def test_convert_coordinates_to_sjtsk(self):
        """Test converting WGS84 coordinates to S-JTSK format."""
        logger.info("Testing coordinate conversion to S-JTSK")

        # Convert coordinates
        x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(self.test_lat, self.test_lng)

        # Verify that coordinates were converted
        self.assertIsNotNone(x, "X coordinate is None")
        self.assertIsNotNone(y, "Y coordinate is None")

        logger.info(f"Converted coordinates: x={x}, y={y}")

        return x, y

    def test_generate_mapa_identifikace_url_from_building(self):
        """Test generating a MapaIdentifikace URL from building coordinates."""
        logger.info("Testing MapaIdentifikace URL generation from building")

        # Convert coordinates to S-JTSK
        x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(self.test_lat, self.test_lng)

        # Generate URL
        url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"

        # Verify that a URL was generated
        self.assertIsNotNone(url, "No URL generated")
        self.assertTrue(url.startswith("http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"),
                       "URL does not use MapaIdentifikace format")
        self.assertTrue("l=KN" in url, "URL does not include the layer parameter")
        self.assertTrue(f"x={x}" in url, "URL does not include the x coordinate")
        self.assertTrue(f"y={y}" in url, "URL does not include the y coordinate")

        logger.info(f"Generated MapaIdentifikace URL: {url}")

        return url

    @patch('webbrowser.open')
    def test_batch_search_with_mapa_identifikace_urls(self, mock_open):
        """Test batch search with MapaIdentifikace URLs."""
        logger.info("Testing batch search with MapaIdentifikace URLs")

        # Create a list of sample properties
        properties = [self.sample_property.copy() for _ in range(3)]

        # Mock the fetch_properties_in_area method to return our sample properties
        self.batch_search_manager.fetch_properties_in_area = MagicMock(return_value=properties)

        # Mock the Google Maps geocode method to return coordinates
        self.app.google_maps.geocode = MagicMock(return_value={
            'lat': self.test_lat,
            'lng': self.test_lng,
            'formatted_address': 'Test Address'
        })

        # Create a callback to capture the results
        callback_results = []
        def callback(results):
            nonlocal callback_results
            callback_results = results

        # Directly call the code that generates MapaIdentifikace URLs
        for prop in properties:
            # Check if the property has coordinates
            if 'lat' in prop and 'lng' in prop:
                # Convert WGS84 coordinates to S-JTSK
                x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(prop['lat'], prop['lng'])
                # Generate the MapaIdentifikace URL
                prop['url'] = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x}&y={y}"
                # Add S-JTSK coordinates to the property data
                prop['x_sjtsk'] = x
                prop['y_sjtsk'] = y
                # Update the source to indicate it's a MapaIdentifikace URL
                prop['source'] = f"{prop.get('source', 'unknown')}_mapaidentifikace"
                # Update the owner_address to include the URL
                if 'owner_address' in prop:
                    prop['original_address'] = prop['owner_address']
                prop['owner_address'] = prop['url']
                # Update the owner_name to indicate it's a clickable URL
                if 'owner_name' in prop and prop['owner_name'] != 'Unknown':
                    prop['original_owner'] = prop['owner_name']
                prop['owner_name'] = 'Click URL to view property details'

        # Call the callback with the processed properties
        callback(properties)

        # Verify that the properties were processed
        self.assertEqual(len(callback_results), 3, "Wrong number of properties returned")

        # Verify that each property has a MapaIdentifikace URL
        for prop in callback_results:
            self.assertIn('url', prop, "Property missing URL")
            self.assertTrue(prop['url'].startswith("http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"),
                           "URL does not use MapaIdentifikace format")
            self.assertIn('x_sjtsk', prop, "Property missing x_sjtsk coordinate")
            self.assertIn('y_sjtsk', prop, "Property missing y_sjtsk coordinate")
            self.assertIn('mapaidentifikace', prop['source'].lower(),
                         "Property source does not indicate MapaIdentifikace")

        logger.info(f"Generated {len(callback_results)} properties with MapaIdentifikace URLs")

        return callback_results


if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run the tests
    unittest.main()
