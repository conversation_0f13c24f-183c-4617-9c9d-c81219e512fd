"""
Smart Batch Search UI for Czech Property Registry

This module provides a UI for the smart batch search functionality that finds all buildings
within a modifiable radius around an address, retrieves RUIAN data, and generates
URLs for owner information.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import logging
import webbrowser
import json
import tempfile
import os
import time
from typing import List, Dict, Any, Optional
from ui.message_boxes import MessageBoxes
from utils.template_loader import TemplateLoader
from ui.cache_status_indicator import CacheStatusIndicator

# Configure logging
logger = logging.getLogger(__name__)

class SmartBatchSearchUI:
    """
    UI component for smart batch search functionality.

    Provides a user interface for searching properties by address with a customizable radius,
    displaying results, and generating MapaIdentifikace URLs for owner information.
    """

    def __init__(self, parent_frame, app):
        """
        Initialize the smart batch search UI.

        Args:
            parent_frame: The parent frame to add this UI component to
            app: The main application instance
        """
        self.app = app
        self.parent_frame = parent_frame

        # Create the cache status indicator (will be initialized later)
        self.cache_indicator = None

        # Create custom styles for better UI
        self._create_custom_styles()

        # Create the main frame
        self.frame = ttk.Frame(parent_frame)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create the search frame
        self.create_search_frame()

        # Create a paned window for results and map with vertical orientation
        self.results_paned = ttk.PanedWindow(self.frame, orient=tk.VERTICAL)
        self.results_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create the results frame
        self.create_results_frame()

        # Create the map frame
        self.create_map_frame()

        # Set initial sash position - give more space to the map
        self.root = self.app.root
        self.root.update_idletasks()  # Ensure geometry is calculated
        self.root.after(100, self._set_initial_sash_positions)

        # Initialize variables
        self.search_results = []
        self.location_name = ""
        self.current_lat = None
        self.current_lng = None
        self.current_radius = None

        # Initialize template loader
        self.template_loader = TemplateLoader()

    def _create_custom_styles(self):
        """Create custom styles for the UI components."""
        style = ttk.Style()

        # Create a style for large buttons
        style.configure("Large.TButton",
                        font=("Arial", 11, "bold"),
                        padding=(10, 5))

        # Create a style for section headers
        style.configure("Header.TLabel",
                        font=("Arial", 11, "bold"),
                        foreground="#4a6984")

    def _set_initial_sash_positions(self):
        """Set the initial sash positions for the paned windows."""
        try:
            # Get the height of the paned window
            paned_height = self.results_paned.winfo_height()
            if paned_height > 100:  # Only adjust if we have a reasonable height
                # Set the sash position to give more space to the map (60% of height)
                results_height = int(paned_height * 0.4)  # 40% for results
                self.results_paned.sashpos(0, results_height)
        except Exception as e:
            # Ignore errors if sash doesn't exist yet
            print(f"Error setting sash position: {e}")

    def create_search_frame(self):
        """Create the search input frame with address field, radius slider, and property type selection."""
        search_frame = ttk.LabelFrame(self.frame, text="Smart Batch Search")
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        # Address input with Google Maps autocomplete - using grid for better layout
        address_frame = ttk.Frame(search_frame)
        address_frame.pack(fill=tk.X, padx=5, pady=5)

        # Configure the grid to expand properly
        address_frame.columnconfigure(1, weight=1)

        # Add label and entry in a grid layout
        ttk.Label(address_frame, text="Address:").grid(row=0, column=0, padx=5, pady=5, sticky="w")

        # Import the SimpleAutocompleteEntry
        from utils.simple_autocomplete import SimpleAutocompleteEntry

        # Create the autocomplete entry
        self.address_entry = SimpleAutocompleteEntry(
            address_frame,
            autocomplete_function=self.get_autocomplete_suggestions,
            width=40
        )
        self.address_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        # Store a reference to the entry's variable
        self.address_var = self.address_entry.var

        # Bind the Return key to trigger search
        self.address_entry.entry.bind("<Return>", self.on_search_button_click)

        # Radius selection - using grid layout
        radius_frame = ttk.Frame(search_frame)
        radius_frame.pack(fill=tk.X, padx=5, pady=5)

        # Configure the grid to expand properly
        radius_frame.columnconfigure(1, weight=1)

        ttk.Label(radius_frame, text="Search Radius (km):").grid(row=0, column=0, padx=5, pady=5, sticky="w")

        self.radius_var = tk.DoubleVar(value=2.0)
        self.radius_scale = ttk.Scale(
            radius_frame,
            from_=0.0,
            to=5.0,
            variable=self.radius_var,
            orient=tk.HORIZONTAL,
            length=300
        )
        self.radius_scale.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        self.radius_label = ttk.Label(radius_frame, text="2.0 km", width=6)
        self.radius_label.grid(row=0, column=2, padx=5, pady=5, sticky="e")

        # Update the radius label when the slider is moved
        self.radius_scale.bind("<Motion>", self.update_radius_label)
        self.radius_scale.bind("<ButtonRelease-1>", self.update_radius_label)

        # Property type selection - using grid layout
        property_frame = ttk.LabelFrame(search_frame, text="Property Types")
        property_frame.pack(fill=tk.X, padx=5, pady=5)

        # Property type checkboxes
        self.property_types = {
            "All": tk.BooleanVar(value=True),
            "Residential": tk.BooleanVar(value=False),
            "Commercial": tk.BooleanVar(value=False),
            "Industrial": tk.BooleanVar(value=False),
            "Land": tk.BooleanVar(value=False)
        }

        # Create a frame for the checkboxes with better layout
        checkbox_frame = ttk.Frame(property_frame)
        checkbox_frame.pack(fill=tk.X, padx=5, pady=5)

        # Add the checkboxes in a grid with 3 columns for better space usage
        row, col = 0, 0
        for prop_type, var in self.property_types.items():
            cb = ttk.Checkbutton(
                checkbox_frame,
                text=prop_type,
                variable=var,
                command=self.on_property_type_change
            )
            cb.grid(row=row, column=col, padx=10, pady=5, sticky="w")

            # Move to next column or row
            col += 1
            if col > 2:  # 3 columns per row
                col = 0
                row += 1

            # If this is the "All" checkbox, store a reference to it
            if prop_type == "All":
                self.all_checkbox = cb

        # Max results selection - using grid layout
        max_results_frame = ttk.Frame(search_frame)
        max_results_frame.pack(fill=tk.X, padx=5, pady=5)

        # Configure the grid to expand properly
        max_results_frame.columnconfigure(1, weight=1)

        ttk.Label(max_results_frame, text="Max Results:").grid(row=0, column=0, padx=5, pady=5, sticky="w")

        self.max_results_var = tk.StringVar(value="50")
        max_results_values = ["10", "25", "50", "100", "200"]
        self.max_results_combo = ttk.Combobox(
            max_results_frame,
            textvariable=self.max_results_var,
            values=max_results_values,
            width=5,
            font=("Arial", 10)
        )
        self.max_results_combo.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        # Search button frame with better layout
        button_frame = ttk.Frame(search_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)

        # Center the buttons
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(3, weight=1)

        # Create a larger, more visible search button
        self.search_button = ttk.Button(
            button_frame,
            text="Search",
            command=self.on_search_button_click,
            style="Large.TButton"
        )
        self.search_button.grid(row=0, column=1, padx=10, pady=5)

        self.cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self.on_cancel_button_click,
            state=tk.DISABLED
        )
        self.cancel_button.grid(row=0, column=2, padx=10, pady=5)

    def create_results_frame(self):
        """Create the results display frame with a table and action buttons."""
        # Create a frame for the results
        results_frame = ttk.LabelFrame(self.results_paned, text="Search Results")
        self.results_paned.add(results_frame, weight=1)

        # Create a notebook for radius tabs with a larger font
        style = ttk.Style()
        style.configure("TNotebook.Tab", font=("Arial", 10))

        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Dictionary to store tables for each radius
        self.radius_tables = {}

        # We'll create tabs dynamically when results are received
        # For now, create a placeholder frame
        placeholder_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(placeholder_frame, text="No Results")

        # Create a label in the placeholder frame
        ttk.Label(
            placeholder_frame,
            text="Enter an address and click Search to find properties",
            font=("Arial", 12),
            style="Header.TLabel"
        ).pack(expand=True, padx=20, pady=20)

        # Create the cache status indicator at the bottom of the results frame
        self.cache_indicator = CacheStatusIndicator(results_frame, position="bottom", font=("Arial", 8))

        # We'll create tables with scrollbars dynamically when results are received

        # Create a frame for action buttons with grid layout
        button_frame = ttk.Frame(results_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        # Configure columns for better spacing
        button_frame.columnconfigure(0, weight=0)
        button_frame.columnconfigure(1, weight=0)
        button_frame.columnconfigure(2, weight=0)
        button_frame.columnconfigure(3, weight=1)  # Spacer
        button_frame.columnconfigure(4, weight=0)

        # Add action buttons with consistent size and spacing
        self.open_url_button = ttk.Button(
            button_frame,
            text="Open URL",
            command=self.on_open_url_button_click,
            width=12
        )
        self.open_url_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

        self.export_button = ttk.Button(
            button_frame,
            text="Export Results",
            command=self.on_export_button_click,
            width=12
        )
        self.export_button.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        # Add a button to show the map
        self.show_map_button = ttk.Button(
            button_frame,
            text="Show on Map",
            command=self.show_selected_on_map,
            width=12
        )
        self.show_map_button.grid(row=0, column=2, padx=5, pady=5, sticky="w")

        # Status label
        self.status_label = ttk.Label(button_frame, text="Ready")
        self.status_label.grid(row=0, column=4, padx=5, pady=5, sticky="e")

    def create_map_frame(self):
        """Create the map display frame."""
        # Create a frame for the map
        self.map_frame = ttk.LabelFrame(self.results_paned, text="Map View")
        self.results_paned.add(self.map_frame, weight=2)  # Give map more weight

        # Create a placeholder for the map with better styling
        self.map_placeholder = ttk.Label(
            self.map_frame,
            text="Map will be displayed here after search\n\nEnter an address and click Search to see properties on the map",
            font=("Arial", 12),
            style="Header.TLabel",
            anchor=tk.CENTER,
            justify=tk.CENTER
        )
        self.map_placeholder.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)

        # Create a frame for map controls
        map_controls_frame = ttk.Frame(self.map_frame)
        map_controls_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)

        # Check if tkinterweb is available for embedded maps
        try:
            from tkinterweb import HtmlFrame
            self.tkinterweb_available = True
            # Create an HTML frame for the map
            self.html_frame = HtmlFrame(self.map_frame)
            # Don't pack it yet - we'll pack it when we have a map to show

            # Add a refresh button for the map
            self.refresh_map_button = ttk.Button(
                map_controls_frame,
                text="Refresh Map",
                command=self.refresh_map,
                width=12
            )
            self.refresh_map_button.pack(side=tk.LEFT, padx=5, pady=5)

        except ImportError:
            self.tkinterweb_available = False
            # Create a button to open the map in a browser
            self.open_browser_button = ttk.Button(
                map_controls_frame,
                text="Open Map in Browser",
                command=self.open_map_in_browser,
                width=15
            )
            self.open_browser_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Initialize map-related variables
        self.map_html_path = None

    def get_autocomplete_suggestions(self, text):
        """
        Get autocomplete suggestions for the given text.

        This method is used by the SimpleAutocompleteEntry widget.

        Args:
            text: Text to get suggestions for

        Returns:
            list: List of suggestion strings
        """
        print(f"DEBUG: get_autocomplete_suggestions called with text='{text}'")

        # If the text is too short, return an empty list
        if len(text) < 3:
            print(f"DEBUG: Text is too short for autocomplete")
            return []

        # Check if we have the Google Maps API available
        if hasattr(self.app, 'google_maps'):
            print(f"DEBUG: Google Maps API is available")

            # Try to use get_autocomplete_suggestions if it exists
            if hasattr(self.app.google_maps, 'get_autocomplete_suggestions'):
                print(f"DEBUG: Using get_autocomplete_suggestions method")
                # Get autocomplete suggestions
                suggestions = self.app.google_maps.get_autocomplete_suggestions(text)
            # Fall back to get_place_predictions if get_autocomplete_suggestions doesn't exist
            else:
                print(f"DEBUG: get_autocomplete_suggestions not found, using get_place_predictions instead")
                # Get place predictions and convert them to suggestions
                raw_suggestions = self.app.google_maps.get_place_predictions(text)
                suggestions = []
                if raw_suggestions:
                    for suggestion in raw_suggestions:
                        if isinstance(suggestion, dict) and 'description' in suggestion:
                            suggestions.append(suggestion['description'])
                        else:
                            suggestions.append(str(suggestion))

            print(f"DEBUG: Got {len(suggestions) if suggestions else 0} suggestions")
            return suggestions
        else:
            print(f"DEBUG: Google Maps API is not available")
            return []

    def on_address_focus_out(self, event):
        """Handle focus out events in the address entry field."""
        # Hide the autocomplete dropdown if it's visible
        self.hide_autocomplete_dropdown(event)

    def hide_autocomplete_dropdown(self, event=None):
        """Hide the autocomplete dropdown."""
        if hasattr(self, 'autocomplete_dropdown') and self.autocomplete_dropdown.winfo_exists():
            print(f"DEBUG: Hiding autocomplete dropdown")
            self.autocomplete_dropdown.destroy()

    def check_hide_dropdown(self, event):
        """Check if we should hide the dropdown based on click location."""
        if hasattr(self, 'autocomplete_dropdown') and self.autocomplete_dropdown.winfo_exists():
            # Don't hide if clicking on the dropdown itself
            if event.widget == self.autocomplete_dropdown:
                return

            # Don't hide if clicking on the address entry
            if event.widget == self.address_entry:
                return

            # Hide in all other cases
            print(f"DEBUG: Hiding dropdown because clicked on {event.widget}")
            self.hide_autocomplete_dropdown()

    def show_autocomplete_dropdown(self, suggestions):
        """Show a dropdown with autocomplete suggestions."""
        print(f"DEBUG: show_autocomplete_dropdown called with {len(suggestions)} suggestions")

        # If we already have a dropdown, destroy it
        if hasattr(self, 'autocomplete_dropdown') and self.autocomplete_dropdown.winfo_exists():
            print(f"DEBUG: Destroying existing dropdown")
            self.autocomplete_dropdown.destroy()

        # Create a toplevel window for the dropdown
        self.autocomplete_dropdown = tk.Toplevel(self.root)
        self.autocomplete_dropdown.overrideredirect(True)  # Remove window decorations
        self.autocomplete_dropdown.transient(self.root)    # Make it float on top
        self.autocomplete_dropdown.attributes('-topmost', True)  # Keep it on top

        # Create a frame with a border
        dropdown_frame = ttk.Frame(self.autocomplete_dropdown, style="Dropdown.TFrame")
        dropdown_frame.pack(fill=tk.BOTH, expand=True)

        # Create a listbox inside the frame
        self.suggestion_listbox = tk.Listbox(
            dropdown_frame,
            font=("Arial", 10),
            height=min(5, len(suggestions)),
            borderwidth=0,
            highlightthickness=0,
            selectbackground="#4a6984",
            selectforeground="white"
        )
        self.suggestion_listbox.pack(fill=tk.BOTH, expand=True)

        print(f"DEBUG: Created new dropdown listbox")

        # Add the suggestions to the dropdown
        for i, suggestion in enumerate(suggestions):
            print(f"DEBUG: Adding suggestion {i+1}: '{suggestion}'")
            self.suggestion_listbox.insert(tk.END, suggestion)

            # Add alternating background colors for better readability
            if i % 2 == 0:
                self.suggestion_listbox.itemconfig(i, {'bg': '#f0f0f0'})

        # Position the dropdown directly below the address entry
        self.address_entry.update_idletasks()  # Ensure geometry is up to date

        # Get the address entry's position on screen
        entry_x = self.address_entry.winfo_rootx()
        entry_y = self.address_entry.winfo_rooty()
        entry_height = self.address_entry.winfo_height()
        entry_width = self.address_entry.winfo_width()

        # Position the dropdown below the address entry
        dropdown_x = entry_x
        dropdown_y = entry_y + entry_height

        # Set the size and position of the dropdown
        self.autocomplete_dropdown.geometry(f"{entry_width}x{min(5, len(suggestions))*24}+{dropdown_x}+{dropdown_y}")

        print(f"DEBUG: Positioned dropdown at x={dropdown_x}, y={dropdown_y}, width={entry_width}")
        print(f"DEBUG: Dropdown should now be visible")

        # Ensure the dropdown is visible by bringing it to the front
        self.autocomplete_dropdown.lift()

        # Bind events to hide the dropdown when clicking elsewhere or resizing
        self.root.bind("<Configure>", self.hide_autocomplete_dropdown)
        self.root.bind("<Button-1>", self.check_hide_dropdown, add="+")

        # Make sure the dropdown is visible by updating the UI
        self.root.update_idletasks()

        # Bind events to the dropdown
        self.suggestion_listbox.bind("<ButtonRelease-1>", self.on_autocomplete_select)
        self.suggestion_listbox.bind("<Return>", self.on_autocomplete_select)

        # Create a custom style for the dropdown frame
        style = ttk.Style()
        style.configure("Dropdown.TFrame", background="white", borderwidth=1, relief="solid")

    def on_autocomplete_select(self, event):
        """Handle selection of an autocomplete suggestion."""
        # Get the selected suggestion
        if self.suggestion_listbox.curselection():
            index = self.suggestion_listbox.curselection()[0]
            suggestion = self.suggestion_listbox.get(index)

            print(f"DEBUG: Selected suggestion: '{suggestion}'")

            # Set the address entry to the selected suggestion
            self.address_var.set(suggestion)

            # Destroy the dropdown
            self.autocomplete_dropdown.destroy()

            # Set focus back to the address entry
            self.address_entry.focus()

    def update_radius_label(self, event):
        """Update the radius label when the slider is moved."""
        self.radius_label.config(text=f"{self.radius_var.get():.1f} km")

    def on_property_type_change(self):
        """Handle changes to the property type checkboxes."""
        # If "All" is checked, uncheck the others
        if self.property_types["All"].get():
            for prop_type, var in self.property_types.items():
                if prop_type != "All":
                    var.set(False)
        # If any other is checked, uncheck "All"
        else:
            any_checked = False
            for prop_type, var in self.property_types.items():
                if prop_type != "All" and var.get():
                    any_checked = True
                    break

            if not any_checked:
                # If none are checked, check "All"
                self.property_types["All"].set(True)

    def on_search_button_click(self, event=None):
        """Handle the search button click event."""
        # Get the address
        address = self.address_var.get().strip()

        # Validate the address
        if not address:
            MessageBoxes.show_warning("Missing Information", "Please enter an address to search.")
            return

        # Get the radius
        radius = self.radius_var.get()

        # Get the selected property types
        selected_types = []
        if self.property_types["All"].get():
            selected_types = ["all"]
        else:
            for prop_type, var in self.property_types.items():
                if prop_type != "All" and var.get():
                    selected_types.append(prop_type)

        # Get the maximum number of results
        try:
            max_results = int(self.max_results_var.get())
        except ValueError:
            max_results = 50

        # Update the UI state
        self.search_button.config(state=tk.DISABLED)
        self.cancel_button.config(state=tk.NORMAL)
        self.status_label.config(text="Searching...")

        # Clear existing tabs in the results notebook
        for tab in self.results_notebook.tabs():
            self.results_notebook.forget(tab)

        # Reset the radius tables dictionary
        self.radius_tables = {}

        # Start the search
        if hasattr(self.app, 'smart_batch_search_manager'):
            self.app.smart_batch_search_manager.batch_search_by_address(
                address, selected_types, radius, max_results, self.display_search_results
            )
        else:
            logger.error("Smart batch search manager not initialized")
            MessageBoxes.show_error("Error", "Smart batch search manager not initialized.")
            self.search_button.config(state=tk.NORMAL)
            self.cancel_button.config(state=tk.DISABLED)
            self.status_label.config(text="Ready")

    def on_cancel_button_click(self):
        """Handle the cancel button click event."""
        # Cancel the current search
        if hasattr(self.app, 'smart_batch_search_manager'):
            self.app.smart_batch_search_manager.cancel_current_search()

        # Update the UI state
        self.search_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.status_label.config(text="Cancelled")

    def display_search_results(self, properties, location_name, radius=None, from_cache=False):
        """
        Display the search results in tabs organized by radius.

        Args:
            properties (list): List of property data dictionaries
            location_name (str): Name of the location searched
            radius (float, optional): Radius used for this search
            from_cache (bool, optional): Whether the results are from cache
        """
        # Add debug logging
        logger.info(f"Displaying search results: {len(properties) if properties else 0} properties found near {location_name}")
        logger.info(f"First property: {properties[0] if properties else 'No properties'}")
        logger.info(f"Radius parameter: {radius}")
        logger.info(f"From cache: {from_cache}")

        # Show cache indicator if results are from cache
        if from_cache and self.cache_indicator and properties:
            # Get the timestamp from the first property if available
            cache_timestamp = properties[0].get('cache_timestamp', time.time() - 3600)  # Default to 1 hour ago
            logger.info(f"Showing cache indicator with timestamp: {cache_timestamp}")
            self.cache_indicator.show(
                data_type="search results",
                timestamp=cache_timestamp,
                auto_hide=False  # Keep visible while viewing results
            )
        elif self.cache_indicator:
            # Hide the cache indicator if results are not from cache
            logger.info("Hiding cache indicator")
            self.cache_indicator.hide()

        # Check if properties is None or empty
        if not properties:
            logger.warning("No properties to display")
            # Create a "No Results" tab
            placeholder_frame = ttk.Frame(self.results_notebook)
            self.results_notebook.add(placeholder_frame, text="No Results")

            ttk.Label(
                placeholder_frame,
                text="No properties found. Try a different address or increase the radius.",
                font=("Arial", 12)
            ).pack(expand=True, padx=20, pady=20)

            # Update the UI state
            self.search_button.config(state=tk.NORMAL)
            self.cancel_button.config(state=tk.DISABLED)
            self.status_label.config(text="No properties found")
            return

        # Store the results for later use
        self.search_results = properties
        self.location_name = location_name

        # Clear existing tabs
        for tab in self.results_notebook.tabs():
            self.results_notebook.forget(tab)

        # Reset the radius tables dictionary
        self.radius_tables = {}

        # Group properties by radius if not provided
        if radius is None:
            # Try to extract radius from properties
            radius_groups = {}
            for prop in properties:
                # Default to 2.0 km if radius is not in the property
                prop_radius = prop.get('radius', 2.0)
                if prop_radius not in radius_groups:
                    radius_groups[prop_radius] = []
                radius_groups[prop_radius].append(prop)

            # If no radius groups were created (no properties had a radius), use a default radius
            if not radius_groups and properties:
                radius_groups = {2.0: properties}
                logger.info(f"No radius information found in properties, using default radius of 2.0 km")
        else:
            # Use the provided radius
            radius_groups = {radius: properties}

        # Log the radius groups
        logger.info(f"Created {len(radius_groups)} radius groups: {list(radius_groups.keys())}")

        # Create a tab for each radius
        for radius, radius_properties in radius_groups.items():
            # Create a frame for this radius
            radius_frame = ttk.Frame(self.results_notebook)

            # Special handling for radius 0 (city only)
            if radius == 0 or radius == 0.0:
                self.results_notebook.add(radius_frame, text="City Only")
                status_text = f"Found {len(radius_properties)} properties in {location_name}"
            else:
                self.results_notebook.add(radius_frame, text=f"Radius: {radius} km")
                status_text = f"Found {len(radius_properties)} properties within {radius} km radius of {location_name}"

            # Create a table for this radius
            self._create_results_table(radius_frame, radius_properties, radius)

            # Add a status label
            status_label = ttk.Label(
                radius_frame,
                text=status_text,
                anchor=tk.W
            )
            status_label.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)

        # If no results, add a "No Results" tab
        if not radius_groups:
            placeholder_frame = ttk.Frame(self.results_notebook)
            self.results_notebook.add(placeholder_frame, text="No Results")

            ttk.Label(
                placeholder_frame,
                text="No properties found. Try a different address or increase the radius.",
                font=("Arial", 12)
            ).pack(expand=True, padx=20, pady=20)

        # Update the UI state
        self.search_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.status_label.config(text=f"Found {len(properties)} properties near {location_name}")

        # Get the first property to extract coordinates
        if properties and len(properties) > 0:
            first_property = properties[0]
            lat = first_property.get('lat')
            lng = first_property.get('lng')

            # If we have coordinates, show the map
            if lat and lng:
                # Get the radius in meters
                if radius == 0 or radius == 0.0:
                    # For city-only searches, use a small radius just for display purposes
                    radius_meters = 500  # 500m for city display
                    map_title = f"Buildings in {location_name}"
                else:
                    radius_meters = int(float(radius) * 1000) if radius else 2000
                    map_title = f"Buildings within {radius}km of {location_name}"

                # Store the current coordinates and radius
                self.current_lat = float(lat)
                self.current_lng = float(lng)
                self.current_radius = radius_meters

                # Show the map with radius and properties
                self.show_map_with_radius_and_properties(
                    float(lat), float(lng), radius_meters, properties, map_title
                )

    def _create_results_table(self, parent_frame, properties, radius):
        """
        Create a results table for a specific radius.

        Args:
            parent_frame: Parent frame to add the table to
            properties (list): List of property data dictionaries
            radius (float): Radius used for this search
        """
        # Create a frame for the table
        table_frame = ttk.Frame(parent_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create improved styles for the table
        style = ttk.Style()
        style.configure("Treeview",
                        font=("Arial", 10),
                        rowheight=25)  # Increase row height for better readability

        style.configure("Treeview.Heading",
                        font=("Arial", 10, "bold"),
                        background="#e0e0e0")

        # Create a Treeview widget for the results table
        columns = ("property_type", "ruian_id", "address", "lat", "lng", "x_sjtsk", "y_sjtsk", "url")
        results_table = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            selectmode="browse",
            style="Treeview"
        )

        # Define column headings with centered text
        results_table.heading("property_type", text="Property Type", anchor=tk.CENTER)
        results_table.heading("ruian_id", text="RUIAN ID", anchor=tk.CENTER)
        results_table.heading("address", text="Address", anchor=tk.CENTER)
        results_table.heading("lat", text="Latitude", anchor=tk.CENTER)
        results_table.heading("lng", text="Longitude", anchor=tk.CENTER)
        results_table.heading("x_sjtsk", text="X (S-JTSK)", anchor=tk.CENTER)
        results_table.heading("y_sjtsk", text="Y (S-JTSK)", anchor=tk.CENTER)
        results_table.heading("url", text="CUZK URL", anchor=tk.CENTER)

        # Define column widths with better proportions
        results_table.column("property_type", width=120, anchor=tk.W, stretch=False)
        results_table.column("ruian_id", width=100, anchor=tk.W, stretch=False)
        results_table.column("address", width=250, anchor=tk.W, stretch=True)  # Allow address to stretch
        results_table.column("lat", width=80, anchor=tk.W, stretch=False)
        results_table.column("lng", width=80, anchor=tk.W, stretch=False)
        results_table.column("x_sjtsk", width=80, anchor=tk.W, stretch=False)
        results_table.column("y_sjtsk", width=80, anchor=tk.W, stretch=False)
        results_table.column("url", width=250, anchor=tk.W, stretch=True)  # Allow URL to stretch

        # Add horizontal scrollbar
        x_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=results_table.xview)
        results_table.configure(xscrollcommand=x_scrollbar.set)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Add vertical scrollbar
        y_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=results_table.yview)
        results_table.configure(yscrollcommand=y_scrollbar.set)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Pack the table
        results_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Configure row tags for alternating colors with better contrast
        results_table.tag_configure("0", background="#f5f5f5")
        results_table.tag_configure("1", background="#ffffff")

        # Bind double-click event to open URL
        results_table.bind("<Double-1>", lambda event, table=results_table: self._on_table_double_click(event, table))

        # Store the table in the radius_tables dictionary
        self.radius_tables[radius] = results_table

        # Add the results to the table
        for i, prop in enumerate(properties):
            # Get the property data
            property_type = prop.get('property_type', 'Building')
            ruian_id = prop.get('ruian_id', 'Unknown')
            address = prop.get('owner_address', 'Unknown')
            lat = prop.get('lat', '')
            lng = prop.get('lng', '')
            x_sjtsk = prop.get('x_sjtsk', '')
            y_sjtsk = prop.get('y_sjtsk', '')
            url = prop.get('url', '')

            # Log the URL for debugging
            logger.info(f"Adding property to table with URL: {url}")

            # Add the row to the table
            results_table.insert(
                "",
                tk.END,
                values=(property_type, ruian_id, address, lat, lng, x_sjtsk, y_sjtsk, url),
                tags=(str(i % 2),)  # Alternate row colors
            )

    def _on_table_double_click(self, event, table):
        """
        Handle double-click events on a results table.

        Args:
            event: The event object
            table: The table that was double-clicked
        """
        logger.info(f"Double-click event on table: {table}")

        # Get the selected item
        selection = table.selection()
        if not selection:
            logger.warning("Double-click event but no item selected")
            return

        logger.info(f"Selected items: {selection}")

        try:
            # Import the BrowserSpoofer for adding human-like delays
            try:
                from utils.browser_spoofer import BrowserSpoofer
                use_browser_spoofer = True
            except ImportError:
                use_browser_spoofer = False

            item = selection[0]
            logger.info(f"Processing item: {item}")

            values = table.item(item, "values")
            logger.info(f"Item values: {values}")

            # Get the URL from the values (last column)
            url = values[-1]
            logger.info(f"URL from values: {url}")

            # Open the URL in the default browser
            if url:
                logger.info(f"Opening URL: {url}")

                # Add a human-like delay before opening the URL
                if use_browser_spoofer:
                    BrowserSpoofer.add_human_like_delay("click", verbose=True)

                webbrowser.open(url)
            else:
                logger.warning("URL is empty or None")
                MessageBoxes.show_warning("Missing URL", "No URL available for this property.")
        except Exception as e:
            logger.error(f"Error in _on_table_double_click: {str(e)}", exc_info=True)
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")

    def on_open_url_button_click(self):
        """Handle the open URL button click event."""
        logger.info("Open URL button clicked")

        # Get the current tab
        current_tab = self.results_notebook.select()
        if not current_tab:
            logger.warning("No tab selected in results notebook")
            MessageBoxes.show_info("No Results", "There are no results to display.")
            return

        # Get the tab index
        tab_index = self.results_notebook.index(current_tab)
        logger.info(f"Current tab index: {tab_index}")

        # Get the radius for this tab
        try:
            tab_text = self.results_notebook.tab(tab_index, "text")
            logger.info(f"Tab text: {tab_text}")

            if tab_text == "City Only":
                radius = 0.0
            else:
                radius = float(tab_text.split(":")[1].strip().split(" ")[0])

            logger.info(f"Radius from tab: {radius}")
        except (ValueError, IndexError) as e:
            logger.error(f"Error parsing tab text: {str(e)}")
            MessageBoxes.show_info("No Results", "There are no results in this tab.")
            return

        # Get the table for this radius
        if radius not in self.radius_tables:
            logger.warning(f"No table found for radius {radius}")
            MessageBoxes.show_info("No Results", "There are no results in this tab.")
            return

        table = self.radius_tables[radius]
        logger.info(f"Found table for radius {radius}: {table}")

        # Get the selected item
        selection = table.selection()
        if not selection:
            logger.warning("No item selected in table")
            MessageBoxes.show_info("No Selection", "Please select a property from the table first.")
            return

        logger.info(f"Selected items: {selection}")

        try:
            # Import the BrowserSpoofer for adding human-like delays
            try:
                from utils.browser_spoofer import BrowserSpoofer
                use_browser_spoofer = True
            except ImportError:
                use_browser_spoofer = False

            # Process each selected item
            for i, item in enumerate(selection):
                # Add a delay between opening multiple tabs to avoid triggering bot detection
                if i > 0 and use_browser_spoofer:
                    BrowserSpoofer.add_human_like_delay("navigation", verbose=True)

                logger.info(f"Processing item: {item}")

                values = table.item(item, "values")
                logger.info(f"Item values: {values}")

                # Get the URL from the values (last column)
                url = values[-1]
                logger.info(f"URL from values: {url}")

                # Open the URL in the default browser
                if url:
                    logger.info(f"Opening URL: {url}")

                    # Add a human-like delay before opening the URL
                    if i == 0 and use_browser_spoofer:
                        BrowserSpoofer.add_human_like_delay("click", verbose=True)

                    webbrowser.open(url)
                else:
                    logger.warning("URL is empty or None")
                    MessageBoxes.show_warning("Missing URL", "No URL available for this property.")
        except Exception as e:
            logger.error(f"Error in on_open_url_button_click: {str(e)}", exc_info=True)
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")

    def update_buildings_treeview(self, buildings):
        """
        Update the buildings treeview in the BatchSearchUI.

        This method is called by the batch search manager to update the buildings treeview
        in the BatchSearchUI with the search results.

        Args:
            buildings (list): List of building dictionaries with metadata
        """
        logger.info(f"SmartBatchSearchUI.update_buildings_treeview called with {len(buildings)} buildings")

        # Check if the batch_search_ui exists
        if hasattr(self.app, 'batch_search_ui') and hasattr(self.app.batch_search_ui, 'update_buildings_treeview'):
            # Forward the buildings to the batch_search_ui
            self.app.batch_search_ui.update_buildings_treeview(buildings)
        else:
            logger.warning("BatchSearchUI not found or doesn't have update_buildings_treeview method")

    def show_map_with_radius_and_properties(self, lat, lng, radius, properties, location_name):
        """
        Show a map with a radius circle and property markers.

        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius (int): Radius in meters
            properties (list): List of property data dictionaries
            location_name (str): Name of the location
        """
        logger.info(f"Showing map with radius {radius}m at {lat}, {lng} ({location_name})")

        # Get the Google Maps API key
        api_key = ''
        if hasattr(self.app, 'google_maps'):
            api_key = getattr(self.app.google_maps, 'api_key', '')

        # Prepare properties for JSON serialization
        properties_json = []
        for prop in properties:
            # Only include properties with coordinates
            if prop.get('lat') and prop.get('lng'):
                prop_json = {
                    'lat': float(prop.get('lat')),
                    'lng': float(prop.get('lng')),
                    'property_type': prop.get('property_type', 'Building'),
                    'ruian_id': prop.get('ruian_id', 'Unknown'),
                    'address': prop.get('owner_address', 'Unknown'),
                    'url': prop.get('url', '')
                }
                properties_json.append(prop_json)

        # Create the HTML for the map using the template
        try:
            # Use our own template loader
            html = self.template_loader.format_template(
                'map_with_radius_and_properties',
                center_lat=lat,
                center_lng=lng,
                radius=radius,
                properties_json=json.dumps(properties_json),
                location_name=location_name,
                api_key=api_key
            )

            # If tkinterweb is available, use it
            if self.tkinterweb_available:
                # Hide the placeholder
                self.map_placeholder.pack_forget()

                # Pack the HTML frame
                self.html_frame.pack(fill=tk.BOTH, expand=True)

                # Load the HTML
                self.html_frame.load_html(html)
            else:
                # Create a temporary HTML file
                fd, path = tempfile.mkstemp(suffix=".html")
                with os.fdopen(fd, 'w') as f:
                    f.write(html)

                # Store the path for later use
                self.map_html_path = path

                # Update the placeholder text
                self.map_placeholder.config(
                    text=f"Map created for {location_name}\nClick 'Open Map in Browser' to view"
                )
        except Exception as e:
            logger.error(f"Error creating map: {e}", exc_info=True)
            MessageBoxes.show_error("Map Error", f"Error creating map: {str(e)}")

    def _create_simple_map_html(self, lat, lng, radius, properties, location_name, api_key):
        """
        Create a simple HTML for the map when template loader is not available.

        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius (int): Radius in meters
            properties (list): List of property data dictionaries
            location_name (str): Name of the location
            api_key (str): Google Maps API key

        Returns:
            str: HTML for the map
        """
        # Create a simple HTML with the map
        html = f"""<!DOCTYPE html>
<html>
<head>
    <style>
        body {{ margin: 0; padding: 0; height: 100%; }}
        #map {{ width: 100%; height: 100%; }}
        .info-window {{ max-width: 300px; }}
    </style>
</head>
<body>
    <div id="map"></div>
    <script>
        function initMap() {{
            var center = {{lat: {lat}, lng: {lng}}};
            var map = new google.maps.Map(document.getElementById('map'), {{
                zoom: 14,
                center: center
            }});

            // Add a marker at the center
            var centerMarker = new google.maps.Marker({{
                position: center,
                map: map,
                title: 'Search Location: {location_name}',
                icon: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png'
            }});

            // Add a circle with the specified radius
            var circle = new google.maps.Circle({{
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.1,
                map: map,
                center: center,
                radius: {radius}
            }});

            // Create bounds to fit all markers
            var bounds = new google.maps.LatLngBounds();
            bounds.extend(center);

            // Add markers for each property
            var properties = {json.dumps(properties)};
            var infoWindow = new google.maps.InfoWindow();

            properties.forEach(function(property) {{
                if (property.lat && property.lng) {{
                    var position = {{lat: parseFloat(property.lat), lng: parseFloat(property.lng)}};
                    var marker = new google.maps.Marker({{
                        position: position,
                        map: map,
                        title: property.property_type || 'Property'
                    }});

                    bounds.extend(position);

                    // Create info window content
                    var content = '<div class="info-window">';
                    content += '<h3>' + (property.property_type || 'Property') + '</h3>';
                    if (property.ruian_id) {{
                        content += '<p><strong>RUIAN ID:</strong> ' + property.ruian_id + '</p>';
                    }}
                    if (property.address) {{
                        content += '<p><strong>Address:</strong> ' + property.address + '</p>';
                    }}
                    if (property.url) {{
                        content += '<p><a href="' + property.url + '" target="_blank">View on CUZK</a></p>';
                    }}
                    content += '</div>';

                    marker.addListener('click', function() {{
                        infoWindow.setContent(content);
                        infoWindow.open(map, marker);
                    }});
                }}
            }});

            // Adjust bounds to fit all markers
            map.fitBounds(bounds);
        }}
    </script>
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={api_key}&callback=initMap">
    </script>
</body>
</html>"""
        return html

    def open_map_in_browser(self):
        """Open the map in the default browser."""
        if self.map_html_path:
            webbrowser.open('file://' + self.map_html_path)
        elif self.current_lat and self.current_lng:
            # If we have coordinates but no HTML file, create one
            self.show_map_with_radius_and_properties(
                self.current_lat, self.current_lng, self.current_radius or 2000,
                self.search_results, self.location_name
            )
            # Then open it
            if self.map_html_path:
                webbrowser.open('file://' + self.map_html_path)
        else:
            MessageBoxes.show_info("No Map", "No map is available. Please perform a search first.")

    def refresh_map(self):
        """Refresh the map display with current data."""
        if self.current_lat and self.current_lng and self.search_results:
            # Regenerate the map with current data
            self.show_map_with_radius_and_properties(
                self.current_lat, self.current_lng, self.current_radius or 2000,
                self.search_results, self.location_name
            )
        else:
            MessageBoxes.show_info("No Map Data", "No map data is available. Please perform a search first.")

    def show_selected_on_map(self):
        """Show the selected property on the map."""
        # Get the current tab
        current_tab = self.results_notebook.select()
        if not current_tab:
            MessageBoxes.show_info("No Results", "There are no results to display.")
            return

        # Get the tab index
        tab_index = self.results_notebook.index(current_tab)

        # Get the radius for this tab
        try:
            tab_text = self.results_notebook.tab(tab_index, "text")
            if tab_text == "City Only":
                radius = 0.0
            else:
                radius = float(tab_text.split(":")[1].strip().split(" ")[0])
        except (ValueError, IndexError):
            MessageBoxes.show_info("No Results", "There are no results in this tab.")
            return

        # Get the table for this radius
        if radius not in self.radius_tables:
            MessageBoxes.show_info("No Results", "There are no results in this tab.")
            return

        table = self.radius_tables[radius]

        # Get the selected item
        selection = table.selection()
        if selection:
            item = selection[0]
            values = table.item(item, "values")

            # Get the coordinates from the values
            try:
                lat = float(values[3])
                lng = float(values[4])

                # Show the map with the selected property highlighted
                self.show_map_with_radius_and_properties(
                    lat, lng, int(radius * 1000),
                    self.search_results, self.location_name
                )
            except (ValueError, IndexError):
                MessageBoxes.show_info("Invalid Coordinates", "The selected property has invalid coordinates.")
        else:
            MessageBoxes.show_info("No Selection", "Please select a property from the table first.")

    def on_export_button_click(self):
        """Handle the export button click event."""
        # Check if we have results to export
        if not self.search_results:
            MessageBoxes.show_info("No Results", "There are no results to export.")
            return

        # Ask the user for a file name
        from tkinter import filedialog
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV Files", "*.csv"), ("All Files", "*.*")],
            title="Export Results"
        )

        # If the user cancelled, return
        if not file_path:
            return

        # Export the results to CSV
        try:
            import csv
            from datetime import datetime

            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                # Create a CSV writer
                writer = csv.writer(csvfile)

                # Write the header row
                writer.writerow([
                    "Radius (km)", "Property Type", "RUIAN ID", "Address",
                    "Latitude", "Longitude", "X (S-JTSK)", "Y (S-JTSK)", "CUZK URL"
                ])

                # Get all properties from all radius tabs
                all_properties = []

                for radius, table in self.radius_tables.items():
                    # Get all items from the table
                    for item_id in table.get_children():
                        values = table.item(item_id, "values")

                        # Create a property dictionary
                        prop = {
                            'radius': radius,
                            'property_type': values[0],
                            'ruian_id': values[1],
                            'owner_address': values[2],
                            'lat': values[3],
                            'lng': values[4],
                            'x_sjtsk': values[5],
                            'y_sjtsk': values[6],
                            'url': values[7]
                        }

                        all_properties.append(prop)

                # Write the data rows
                for prop in all_properties:
                    writer.writerow([
                        prop.get('radius', ''),
                        prop.get('property_type', 'Building'),
                        prop.get('ruian_id', 'Unknown'),
                        prop.get('owner_address', 'Unknown'),
                        prop.get('lat', ''),
                        prop.get('lng', ''),
                        prop.get('x_sjtsk', ''),
                        prop.get('y_sjtsk', ''),
                        prop.get('url', '')
                    ])

            MessageBoxes.show_info("Export Complete", f"Results exported to {file_path}")

        except Exception as e:
            logger.error(f"Error exporting results: {e}", exc_info=True)
            MessageBoxes.show_error("Export Error", f"Error exporting results: {str(e)}")
