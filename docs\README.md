# Czech Property Registry Documentation

This directory contains documentation for the Czech Property Registry application.

## Table of Contents

1. [Installation](installation.md)
2. [Configuration](configuration.md)
3. [Usage](usage.md)
4. [API Reference](api_reference.md)
5. [Development](development.md)
6. [Troubleshooting](troubleshooting.md)

## Overview

The Czech Property Registry application is a tool for retrieving property owner information from the Czech property registry system. It integrates with various data sources, including OpenStreetMap, Google Maps, and the CUZK (Czech Office for Surveying, Mapping and Cadastre) website.

## Features

- Search for property information by address, coordinates, or location
- Batch selection of buildings to retrieve property owner information
- Integration with CUZK website (nahlizenidokn.cuzk.gov.cz)
- Support for multiple data sources (OpenStreetMap, Google Maps, Wikipedia)
- Gender determination for letter personalization
- Letter generation for property owners

## Architecture

The application is organized into several modules:

- **API**: Integration with external services (CUZK, Google Maps, OSM)
- **Core**: Core application logic
- **Data**: Data files and caching
- **Models**: Data models
- **Services**: Service classes
- **UI**: User interface components
- **Utils**: Utility functions and classes

## Getting Started

See the [Installation](installation.md) and [Usage](usage.md) guides to get started with the application.
