# API Reference

This document provides a reference for the main APIs and classes in the Czech Property Registry application.

## API Package

The `api` package contains modules for interacting with external services.

### CUZK API

The CUZK API provides integration with the Czech Property Registry (CUZK) website.

#### CUZKIntegration

```python
from api.cuzk import CUZKIntegration

cuzk = CUZKIntegration()
property_data = cuzk.search_property_by_coordinates(x, y)
```

Main methods:

- `search_property_by_coordinates(x, y)`: Search for a property by coordinates
- `search_property_by_parcel(parcel_number, cadastral_territory)`: Search for a property by parcel number
- `generate_cuzk_url(lat, lng, parcel_id, building_id, cadastral_territory)`: Generate a URL for the CUZK website
- `open_cuzk_in_browser(lat, lng, parcel_id, building_id, cadastral_territory)`: Open the CUZK website in a browser

#### CUZKScraper

```python
from api.cuzk import CU<PERSON><PERSON>craper

scraper = C<PERSON><PERSON>KScraper()
owner_info = scraper.get_owner_info(parcel_id, cadastral_territory)
```

Main methods:

- `get_owner_info(parcel_id, cadastral_territory)`: Get owner information for a parcel
- `get_property_details(parcel_id, cadastral_territory)`: Get detailed property information
- `search_by_address(city, street, number)`: Search for properties by address

### Google Maps API

The Google Maps API provides integration with Google Maps services.

#### GoogleMapsIntegration

```python
from api.google_maps import GoogleMapsIntegration

gmaps = GoogleMapsIntegration(api_key)
location = gmaps.geocode_address("Prague, Czech Republic")
```

Main methods:

- `geocode_address(address)`: Convert an address to coordinates
- `reverse_geocode(lat, lng)`: Convert coordinates to an address
- `get_place_details(place_id)`: Get details for a place
- `search_places(query, location)`: Search for places near a location

### OpenStreetMap API

The OpenStreetMap API provides integration with OpenStreetMap services.

#### OSMIntegration

```python
from api.osm import OSMIntegration

osm = OSMIntegration()
buildings = osm.get_buildings_in_area(lat, lng, radius)
```

Main methods:

- `get_buildings_in_area(lat, lng, radius)`: Get buildings in a circular area
- `get_buildings_in_bbox(min_lat, min_lng, max_lat, max_lng)`: Get buildings in a bounding box
- `get_building_details(building_id)`: Get details for a building
- `search_by_name(name)`: Search for features by name

## Core Package

The `core` package contains the core application logic.

### PropertyScraperApp

```python
from core import PropertyScraperApp

app = PropertyScraperApp()
app.run()
```

Main methods:

- `run()`: Run the application
- `search_by_address(city, street, number)`: Search for properties by address
- `search_by_location(location_name)`: Search for properties by location name
- `search_by_coordinates(lat, lng)`: Search for properties by coordinates
- `get_owner_info(property_id)`: Get owner information for a property

## Models Package

The `models` package contains data models used in the application.

### Property

```python
from models import Property

property = Property(
    id="123",
    parcel_number="123/45",
    cadastral_territory="Vinohrady",
    property_type="Residential Building",
    owner_name="Jan Novák",
    owner_address="Hlavní 123, 110 00 Praha"
)
```

Properties:

- `id`: Property ID
- `parcel_number`: Parcel number
- `cadastral_territory`: Cadastral territory
- `property_type`: Property type
- `owner_name`: Owner name
- `owner_address`: Owner address

## Services Package

The `services` package contains service classes used in the application.

### GPTIntegration

```python
from services import GPTIntegration

gpt = GPTIntegration(api_key)
gender = gpt.determine_gender("Jan Novák")
```

Main methods:

- `determine_gender(name)`: Determine the gender of a person based on their name
- `generate_letter(template, data)`: Generate a letter using a template and data
- `solve_captcha(image)`: Solve a CAPTCHA image

## Utils Package

The `utils` package contains utility functions and classes.

### RateLimiter

```python
from utils import RateLimiter

limiter = RateLimiter(requests_per_minute=60)
with limiter:
    # Make API request
```

Main methods:

- `__enter__()`: Enter the rate limiter context
- `__exit__()`: Exit the rate limiter context
- `wait()`: Wait for the next available request slot

### CacheManager

```python
from utils import CacheManager

cache = CacheManager(cache_dir="data/cache", expiry_hours=24)
data = cache.get("key")
if data is None:
    data = fetch_data()
    cache.set("key", data)
```

Main methods:

- `get(key)`: Get a value from the cache
- `set(key, value)`: Set a value in the cache
- `delete(key)`: Delete a value from the cache
- `clear()`: Clear the entire cache
