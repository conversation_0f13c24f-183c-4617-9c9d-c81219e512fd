"""
Menu management for the Czech Property Registry application.
"""

import tkinter as tk
import webbrowser
from ui.message_boxes import MessageBoxes
from ui.settings_dialog import SettingsDialog

class MenuManager:
    """
    Manages the application's menu system.
    This class is responsible for creating and managing the main menu.
    """

    def __init__(self, app):
        """
        Initialize the menu manager.

        Args:
            app: The main application instance
        """
        self.app = app
        self.root = app.root

        # Create the menu
        self.create_menu()

    def create_menu(self):
        """Create a simplified main menu for the application"""
        # Create a menu bar
        self.menu_bar = tk.Menu(self.root)
        self.root.config(menu=self.menu_bar)

        # Create File menu
        file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Create Display menu
        display_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="Display", menu=display_menu)

        # Add fullscreen toggle option
        self.app.fullscreen_var = tk.BooleanVar(value=False)
        display_menu.add_checkbutton(label="Fullscreen Mode (F11)", variable=self.app.fullscreen_var,
                                    command=self.app.toggle_fullscreen)

        # Create CUZK menu
        cuzk_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="ČÚZK", menu=cuzk_menu)
        cuzk_menu.add_command(label="Open ČÚZK Website", command=lambda: webbrowser.open("https://nahlizenidokn.cuzk.gov.cz/"))
        cuzk_menu.add_command(label="Open iKatastr", command=lambda: webbrowser.open("https://www.ikatastr.cz/"))

        # Create Help menu
        help_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)

    def show_settings(self):
        """Show the settings dialog"""
        # Create and show the settings dialog
        settings_dialog = SettingsDialog(self.root)
        settings_changed = settings_dialog.show()

        # If settings were changed, update the application
        if settings_changed:
            # Show a message about the changes
            MessageBoxes.show_info(
                "Settings Changed",
                "Some settings have been changed. Some changes may require a restart to take effect."
            )

            # Reload any settings that can be applied immediately
            if hasattr(self.app, 'reload_settings'):
                self.app.reload_settings()

    def show_about(self):
        """Show information about the application"""
        MessageBoxes.show_info(
            "About Czech Property Registry",
            "Czech Property Registry - Batch Search\n"
            "Version 2.0\n\n"
            "This application allows you to search for property owners in the Czech Republic "
            "using the ČÚZK (Czech Office for Surveying, Mapping and Cadastre) database.\n\n"
            "It specializes in batch property owner search, allowing you to search for "
            "specific building types within a modifiable radius around a chosen address.\n\n"
            "© 2024 All rights reserved."
        )
