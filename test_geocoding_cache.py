"""
Test Geocoding Cache for Czech Property Registry

This script tests the geocoding cache in the Czech Property Registry application
to ensure it correctly handles geocoding requests and avoids using incorrect cached data.
"""

import os
import sys
import logging
import time
import configparser
from api.google_maps.google_maps_integration import GoogleMapsIntegration

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_geocoding_cache():
    """Test the geocoding cache"""
    # Create a new instance of the Google Maps integration
    google_maps = GoogleMapsIntegration()
    
    # Test cities
    test_cities = [
        "Česká Lípa",
        "Praha",
        "Brno",
        "Ostrava",
        "Plzeň",
        "Liberec",
        "Olomouc",
        "Ústí nad Labem",
        "Hradec Králové",
        "České Budějovice",
        "Pardubice",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "Most",
        "Opava",
        "Frýdek-Místek",
        "<PERSON><PERSON>á",
        "Jihlava",
        "Teplice"
    ]
    
    # Clear the geocoding cache first
    logger.info("Clearing geocoding cache before tests")
    google_maps.clear_geocoding_cache()
    
    # Test geocoding each city
    for city in test_cities:
        logger.info(f"Testing geocoding for city: {city}")
        
        # First geocoding request (should use API)
        start_time = time.time()
        result1 = google_maps.geocode_address(city)
        api_time = time.time() - start_time
        
        if result1:
            logger.info(f"First geocoding result for {city}: {result1.get('lat')}, {result1.get('lng')}")
            logger.info(f"API geocoding time: {api_time:.4f} seconds")
            
            # Second geocoding request (should use cache)
            start_time = time.time()
            result2 = google_maps.geocode_address(city)
            cache_time = time.time() - start_time
            
            if result2:
                logger.info(f"Second geocoding result for {city}: {result2.get('lat')}, {result2.get('lng')}")
                logger.info(f"Cache geocoding time: {cache_time:.4f} seconds")
                
                # Verify results match
                if (result1.get('lat') == result2.get('lat') and 
                    result1.get('lng') == result2.get('lng')):
                    logger.info(f"✅ Results match for {city}")
                else:
                    logger.error(f"❌ Results do not match for {city}")
                
                # Verify cache is faster
                if cache_time < api_time:
                    logger.info(f"✅ Cache is faster ({cache_time:.4f}s vs {api_time:.4f}s)")
                else:
                    logger.warning(f"⚠️ Cache is not faster ({cache_time:.4f}s vs {api_time:.4f}s)")
            else:
                logger.error(f"❌ Second geocoding failed for {city}")
        else:
            logger.error(f"❌ First geocoding failed for {city}")
    
    # Test with variations of the same city
    variations = [
        "Česká Lípa",
        "Ceska Lipa",
        "Česká Lípa, Czech Republic",
        "Ceska Lipa, Czech Republic",
        "Česká Lípa, Czechia"
    ]
    
    # Clear the geocoding cache first
    logger.info("\nClearing geocoding cache before variation tests")
    google_maps.clear_geocoding_cache()
    
    # Get the reference result
    reference = google_maps.geocode_address(variations[0])
    if reference:
        reference_lat = reference.get('lat')
        reference_lng = reference.get('lng')
        logger.info(f"Reference coordinates for {variations[0]}: {reference_lat}, {reference_lng}")
        
        # Test each variation
        for variation in variations[1:]:
            logger.info(f"Testing variation: {variation}")
            result = google_maps.geocode_address(variation)
            
            if result:
                logger.info(f"Geocoding result for {variation}: {result.get('lat')}, {result.get('lng')}")
                
                # Verify results are close to reference
                lat_diff = abs(result.get('lat') - reference_lat)
                lng_diff = abs(result.get('lng') - reference_lng)
                
                if lat_diff < 0.01 and lng_diff < 0.01:
                    logger.info(f"✅ Results are close to reference for {variation}")
                else:
                    logger.error(f"❌ Results are not close to reference for {variation}")
            else:
                logger.error(f"❌ Geocoding failed for {variation}")
    else:
        logger.error(f"❌ Reference geocoding failed for {variations[0]}")

def main():
    """Main function"""
    logger.info("Starting geocoding cache tests")
    
    # Run the tests
    test_geocoding_cache()
    
    logger.info("Geocoding cache tests completed")

if __name__ == "__main__":
    main()
