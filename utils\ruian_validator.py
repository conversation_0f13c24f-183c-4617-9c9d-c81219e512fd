"""
RUIAN ID Validation Module

This module provides functions for validating and cleaning RUIAN IDs.
RUIAN (Registry of Territorial Identification, Addresses and Real Estates) is the Czech Republic's
official system for recording data about territorial identification, addresses, and real estate.

RUIAN IDs are numeric identifiers used to uniquely identify buildings and properties in the Czech Republic.
"""

import re
import logging
from typing import Optional, Tuple, Union

# Configure logging
logger = logging.getLogger(__name__)

# Constants for RUIAN ID validation
RUIAN_ID_MIN_LENGTH = 6  # Minimum length of a valid RUIAN ID
RUIAN_ID_MAX_LENGTH = 10  # Maximum length of a valid RUIAN ID
RUIAN_ID_PATTERN = r'^\d+$'  # Pattern for a valid RUIAN ID (only digits)

# Common prefixes that might appear in RUIAN IDs from various sources
COMMON_PREFIXES = [
    'RUIAN:',
    'ruian:',
    'ref:ruian=',
    'ref:ruian:',
    'RUIAN_ID:',
    'ruian_id:',
    'ruianid:',
    'RUIANID:',
]

# Known invalid RUIAN ID values
INVALID_RUIAN_VALUES = [
    'Unknown',
    'None',
    'null',
    'undefined',
    'N/A',
    '0',
    '00000',
    '000000',
    '0000000',
    '00000000',
    '000000000',
    '0000000000',
]

# Validation levels
class ValidationLevel:
    """Validation levels for RUIAN IDs"""
    BASIC = 0  # Basic format validation
    STRICT = 1  # Strict format validation
    ONLINE = 2  # Online verification against CUZK


def clean_ruian_id(ruian_id: Optional[Union[str, int]]) -> str:
    """
    Clean a RUIAN ID by removing non-numeric characters and common prefixes.

    Args:
        ruian_id (str or int, optional): The RUIAN ID to clean

    Returns:
        str: The cleaned RUIAN ID (only digits)
    """
    if ruian_id is None:
        return ""

    # Convert to string if it's an integer
    ruian_id_str = str(ruian_id)

    # Remove common prefixes
    for prefix in COMMON_PREFIXES:
        if ruian_id_str.startswith(prefix):
            ruian_id_str = ruian_id_str[len(prefix):]
            break

    # Remove any non-digit characters
    clean_id = ''.join(filter(str.isdigit, ruian_id_str))

    return clean_id


def is_valid_ruian_id(ruian_id: Optional[Union[str, int]], validation_level: int = ValidationLevel.BASIC) -> bool:
    """
    Check if a RUIAN ID is valid.

    Args:
        ruian_id (str or int, optional): The RUIAN ID to validate
        validation_level (int, optional): The validation level to use
            - ValidationLevel.BASIC: Basic format validation
            - ValidationLevel.STRICT: Strict format validation
            - ValidationLevel.ONLINE: Online verification against CUZK

    Returns:
        bool: True if the RUIAN ID is valid, False otherwise
    """
    if ruian_id is None:
        return False

    # Convert to string if it's an integer
    ruian_id_str = str(ruian_id)

    # Check if it's a known invalid value
    if ruian_id_str in INVALID_RUIAN_VALUES:
        return False

    # Clean the RUIAN ID
    clean_id = clean_ruian_id(ruian_id)

    # Check if the cleaned ID is empty
    if not clean_id:
        return False

    # Check if the cleaned ID matches the pattern (only digits)
    if not re.match(RUIAN_ID_PATTERN, clean_id):
        return False

    # Check if the cleaned ID has a valid length
    if not (RUIAN_ID_MIN_LENGTH <= len(clean_id) <= RUIAN_ID_MAX_LENGTH):
        return False

    # Additional checks for strict validation
    if validation_level >= ValidationLevel.STRICT:
        # Check if the ID is all zeros
        if clean_id == '0' * len(clean_id):
            return False

        # Check if the ID starts with a valid prefix
        # (In a real implementation, this would check against known valid prefixes)
        # For now, we'll just check that it doesn't start with 0
        if clean_id.startswith('0'):
            return False

    # Online verification against CUZK
    if validation_level >= ValidationLevel.ONLINE:
        exists, _ = verify_ruian_id_exists(clean_id)
        return exists

    return True


def validate_ruian_id(ruian_id: Optional[Union[str, int]], validation_level: int = ValidationLevel.BASIC) -> Tuple[bool, str, Optional[str]]:
    """
    Validate a RUIAN ID and return validation results.

    Args:
        ruian_id (str or int, optional): The RUIAN ID to validate
        validation_level (int, optional): The validation level to use
            - ValidationLevel.BASIC: Basic format validation
            - ValidationLevel.STRICT: Strict format validation
            - ValidationLevel.ONLINE: Online verification against CUZK

    Returns:
        tuple: (is_valid, clean_id, error_message)
            - is_valid (bool): True if the RUIAN ID is valid, False otherwise
            - clean_id (str): The cleaned RUIAN ID (only digits)
            - error_message (str, optional): Error message if the RUIAN ID is invalid, None otherwise
    """
    if ruian_id is None:
        return False, "", "RUIAN ID cannot be None"

    # Convert to string if it's an integer
    ruian_id_str = str(ruian_id)

    # Check if it's a known invalid value
    if ruian_id_str in INVALID_RUIAN_VALUES:
        return False, "", f"RUIAN ID '{ruian_id_str}' is a known invalid value"

    # Clean the RUIAN ID
    clean_id = clean_ruian_id(ruian_id)

    # Check if the cleaned ID is empty
    if not clean_id:
        return False, "", f"RUIAN ID '{ruian_id}' contains no digits"

    # Check if the cleaned ID matches the pattern (only digits)
    if not re.match(RUIAN_ID_PATTERN, clean_id):
        return False, clean_id, f"RUIAN ID '{clean_id}' contains non-digit characters"

    # Check if the cleaned ID has a valid length
    if len(clean_id) < RUIAN_ID_MIN_LENGTH:
        return False, clean_id, f"RUIAN ID '{clean_id}' is too short (minimum {RUIAN_ID_MIN_LENGTH} digits)"

    if len(clean_id) > RUIAN_ID_MAX_LENGTH:
        return False, clean_id, f"RUIAN ID '{clean_id}' is too long (maximum {RUIAN_ID_MAX_LENGTH} digits)"

    # Additional checks for strict validation
    if validation_level >= ValidationLevel.STRICT:
        # Check if the ID is all zeros
        if clean_id == '0' * len(clean_id):
            return False, clean_id, f"RUIAN ID '{clean_id}' cannot be all zeros"

        # Check if the ID starts with a valid prefix
        # (In a real implementation, this would check against known valid prefixes)
        # For now, we'll just check that it doesn't start with 0
        if clean_id.startswith('0'):
            return False, clean_id, f"RUIAN ID '{clean_id}' cannot start with 0"

    # Online verification against CUZK
    if validation_level >= ValidationLevel.ONLINE:
        exists, error = verify_ruian_id_exists(clean_id)
        if not exists:
            return False, clean_id, error or f"RUIAN ID '{clean_id}' does not exist in CUZK system"

    return True, clean_id, None


def verify_ruian_id_exists(ruian_id: str, use_cache: bool = True) -> tuple[bool, Optional[str]]:
    """
    Verify if a RUIAN ID exists in the CUZK system by making a request to the CUZK website.

    Args:
        ruian_id (str): The RUIAN ID to verify
        use_cache (bool, optional): Whether to use the cache. Defaults to True.

    Returns:
        tuple: (exists, error_message)
            - exists (bool): True if the RUIAN ID exists, False otherwise
            - error_message (str, optional): Error message if verification failed, None otherwise
    """
    import requests
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    from utils.ruian_cache import get_cache

    logger.info(f"Verifying RUIAN ID: {ruian_id}")

    # Check if the RUIAN ID is in the cache
    if use_cache:
        cache = get_cache()
        cached_result = cache.get(ruian_id)
        if cached_result is not None:
            exists, error = cached_result
            logger.info(f"Using cached result for RUIAN ID {ruian_id}: exists={exists}")
            return exists, error

    try:
        # Set up a session with retry capability
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Try different URL formats
        urls_to_try = [
            # Standard building URL
            f"https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id={ruian_id}",
            # Alternative format
            f"https://nahlizenidokn.cuzk.cz/VyberBudovu.aspx?typ=Stavba&id={ruian_id}"
        ]

        for url in urls_to_try:
            logger.info(f"Trying URL: {url}")

            # Make a HEAD request first to check if the page exists
            head_response = session.head(url, timeout=10)

            if head_response.status_code != 200:
                logger.info(f"HEAD request failed with status code: {head_response.status_code}")
                continue

            # If HEAD request is successful, make a GET request to check the content
            response = session.get(url, timeout=10)

            if response.status_code != 200:
                logger.info(f"GET request failed with status code: {response.status_code}")
                continue

            # Check if the response contains an error message
            if "Špatná identifikace objektu" in response.text:
                logger.info("Error in response: Bad object identification")
                continue

            # If we get here, the RUIAN ID exists
            logger.info(f"RUIAN ID {ruian_id} exists")

            # Add the result to the cache
            if use_cache:
                cache = get_cache()
                cache.add(ruian_id, True, None)

            return True, None

        # If we've tried all URLs and none worked, the RUIAN ID doesn't exist
        logger.info(f"RUIAN ID {ruian_id} does not exist")

        # Add the result to the cache
        if use_cache:
            cache = get_cache()
            cache.add(ruian_id, False, "RUIAN ID not found in CUZK system")

        return False, "RUIAN ID not found in CUZK system"

    except requests.exceptions.RequestException as e:
        error_message = f"Error verifying RUIAN ID: {str(e)}"
        logger.error(error_message)

        # Don't cache network errors as they might be temporary

        return False, error_message
    except Exception as e:
        error_message = f"Unexpected error verifying RUIAN ID: {str(e)}"
        logger.error(error_message)

        # Don't cache unexpected errors as they might be temporary

        return False, error_message
