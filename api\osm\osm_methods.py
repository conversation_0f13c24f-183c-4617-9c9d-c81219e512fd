"""
OpenStreetMap integration methods for the Czech Property Registry application.

This module provides methods for interacting with OpenStreetMap to find buildings
with RUIAN references.
"""

from tkinter import ttk
import threading
import webbrowser
import tkinter as tk
import time
from typing import Union, Optional

from ui.message_boxes import MessageBoxes
from ui.progress_dialog import ProgressDialog
from models.ruian_id import RuianID


class OSMMethods:
    """
    Methods for interacting with OpenStreetMap.
    """

    def __init__(self, app):
        """
        Initialize the OSM methods.

        Args:
            app: The main application instance
        """
        self.app = app
        self.session = app.session

    def search_buildings_in_osm_by_address(self):
        """
        Search for buildings in OpenStreetMap by address
        """
        try:
            # Get the address from the UI
            full_address = self.app.osm_full_address.get().strip()

            # If the full address is empty, try to construct it from components
            if not full_address:
                components = []

                city = self.app.osm_city_entry.get().strip()
                street = self.app.osm_street_entry.get().strip()
                number = self.app.osm_number_entry.get().strip()
                postal = self.app.osm_postal_entry.get().strip()

                if street:
                    components.append(street)
                if number:
                    components.append(number)
                if city:
                    components.append(city)
                if postal:
                    components.append(postal)

                full_address = ", ".join(components)

            # Check if we have an address
            if not full_address:
                MessageBoxes.show_error("Input Error", "Please enter an address to search for")
                return

            # Get the search radius
            try:
                radius = int(self.app.osm_address_radius_var.get())
            except ValueError:
                radius = 2000  # Default radius

            # Show status
            self.app.show_status(f"Searching for buildings at address: {full_address}...")

            # Use the Google Maps API to get coordinates for the address
            coordinates = self.app.google_maps.geocode_address(full_address)

            if not coordinates:
                MessageBoxes.show_error("Geocoding Error", f"Could not find coordinates for address: {full_address}")
                self.app.show_status("Ready")
                return

            lat, lng = coordinates

            # Show the coordinates on the map
            self.app.show_embedded_map(lat, lng)

            # Use the thread pool for better resource management
            def search_thread():
                try:
                    # Search for buildings with RUIAN references in OpenStreetMap
                    buildings = self.fetch_properties_from_osm(lat, lng, radius)

                    # Update the UI in the main thread
                    self.app.root.after(0, lambda: self.process_osm_buildings(buildings, lat, lng, full_address))
                except Exception as e:
                    print(f"Error searching for buildings in OpenStreetMap: {e}")
                    self.app.root.after(0, lambda: MessageBoxes.show_error("Search Error", f"Error searching for buildings: {str(e)}"))
                    self.app.show_status("Ready")

            # Submit the task to the thread pool
            if hasattr(self.app, 'thread_pool'):
                self.app.thread_pool.submit(search_thread)
            else:
                # Fallback to direct threading if thread pool is not available
                threading.Thread(target=search_thread, daemon=True).start()

        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")
            self.app.show_status("Ready")

    def search_buildings_in_osm_by_coordinates_from_osm_tab(self):
        """
        Search for buildings in OpenStreetMap by coordinates from the OSM tab
        """
        try:
            # Get the coordinates from the UI
            lat = self.app.osm_lat_entry.get().strip()
            lng = self.app.osm_lng_entry.get().strip()

            # Check if we have coordinates
            if not lat or not lng:
                MessageBoxes.show_error("Input Error", "Please enter coordinates to search for")
                return

            # Get the search radius
            try:
                radius = int(self.app.osm_coords_radius_var.get())
            except ValueError:
                radius = 2000  # Default radius

            # Show status
            self.app.show_status(f"Searching for buildings at coordinates {lat}, {lng}...")

            # Show the coordinates on the map
            self.app.show_embedded_map(lat, lng)

            # Use the thread pool for better resource management
            def search_thread():
                try:
                    # Search for buildings with RUIAN references in OpenStreetMap
                    buildings = self.fetch_properties_from_osm(lat, lng, radius)

                    # Update the UI in the main thread
                    location_name = f"coordinates {lat}, {lng}"
                    self.app.root.after(0, lambda: self.process_osm_buildings(buildings, lat, lng, location_name))
                except Exception as e:
                    print(f"Error searching for buildings in OpenStreetMap: {e}")
                    self.app.root.after(0, lambda: MessageBoxes.show_error("Search Error", f"Error searching for buildings: {str(e)}"))
                    self.app.show_status("Ready")

            # Submit the task to the thread pool
            if hasattr(self.app, 'thread_pool'):
                self.app.thread_pool.submit(search_thread)
            else:
                # Fallback to direct threading if thread pool is not available
                threading.Thread(target=search_thread, daemon=True).start()

        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")
            self.app.show_status("Ready")

    def search_buildings_in_osm_by_location(self, city, region=None, radius=2000):
        """
        Search for buildings in OpenStreetMap by location

        Args:
            city (str): City name
            region (str, optional): Region name
            radius (int, optional): Search radius in meters
        """
        try:
            # Check if we have a city
            if not city:
                MessageBoxes.show_error("Input Error", "Please enter a city to search for")
                return

            # Construct the search query
            query = city
            if region:
                query = f"{city}, {region}"

            # Show status
            self.app.show_status(f"Searching for buildings in {query}...")

            # Use the Google Maps API to get coordinates for the location
            coordinates = self.app.google_maps.geocode_address(query)

            if not coordinates:
                MessageBoxes.show_error("Geocoding Error", f"Could not find coordinates for location: {query}")
                self.app.show_status("Ready")
                return

            lat, lng = coordinates

            # Show the coordinates on the map
            self.app.show_embedded_map(lat, lng)

            # Use the thread pool for better resource management
            def search_thread():
                try:
                    # Search for buildings with RUIAN references in OpenStreetMap
                    buildings = self.fetch_properties_from_osm(lat, lng, radius)

                    # Update the UI in the main thread
                    self.app.root.after(0, lambda: self.process_osm_buildings(buildings, lat, lng, city))
                except Exception as e:
                    print(f"Error searching for buildings in OpenStreetMap: {e}")
                    self.app.root.after(0, lambda: MessageBoxes.show_error("Search Error", f"Error searching for buildings: {str(e)}"))
                    self.app.show_status("Ready")

            # Submit the task to the thread pool
            if hasattr(self.app, 'thread_pool'):
                self.app.thread_pool.submit(search_thread)
            else:
                # Fallback to direct threading if thread pool is not available
                threading.Thread(target=search_thread, daemon=True).start()

        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")
            self.app.show_status("Ready")

    def fetch_properties_from_osm(self, lat, lng, radius=2000):
        """
        Fetch properties from OpenStreetMap by coordinates with caching

        Args:
            lat (str): Latitude
            lng (str): Longitude
            radius (int, optional): Search radius in meters

        Returns:
            list: List of building dictionaries
        """
        try:
            # Create a cache key based on coordinates and radius
            # Round coordinates to 5 decimal places for better cache hits
            lat_rounded = round(float(lat), 5)
            lng_rounded = round(float(lng), 5)
            cache_key = f"{lat_rounded}_{lng_rounded}_{radius}"

            # Check if we have cached results
            if hasattr(self.app, 'cache_manager'):
                cached_buildings = self.app.cache_manager.get('osm_buildings', cache_key)
                if cached_buildings:
                    print(f"Using cached OSM buildings for {cache_key}")
                    return cached_buildings

            # If not cached, fetch from OSM
            print(f"Fetching OSM buildings for {cache_key}")
            buildings = self.app.osm_integration.search_buildings_by_coordinates(lat, lng, radius)

            # Cache the results if we have a cache manager
            if hasattr(self.app, 'cache_manager') and buildings:
                self.app.cache_manager.set('osm_buildings', cache_key, buildings)

            return buildings
        except Exception as e:
            print(f"Error fetching properties from OSM: {e}")
            return []

    def process_osm_buildings(self, buildings, lat, lng, location_name):
        """
        Process the buildings returned from OpenStreetMap

        Args:
            buildings (list): List of building dictionaries
            lat (str): Latitude
            lng (str): Longitude
            location_name (str): Name of the location
        """
        try:
            # Update the status
            if buildings:
                self.app.show_status(f"Found {len(buildings)} buildings with RUIAN references in {location_name}")

                # Display the buildings in the treeview
                self.display_osm_buildings(buildings, lat, lng)

                # Show a success message
                MessageBoxes.show_info("Success", f"Found {len(buildings)} buildings with RUIAN references in {location_name}")
            else:
                self.app.show_status(f"No buildings with RUIAN references found in {location_name}")

                # Show an error message
                MessageBoxes.show_info("No Buildings Found", f"No buildings with RUIAN references found in {location_name}")
        except Exception as e:
            print(f"Error processing OSM buildings: {e}")
            self.app.show_status("Error processing buildings")

    def display_osm_buildings(self, buildings, lat, lng):
        """
        Display the buildings in the treeview with optimized batch updates

        Args:
            buildings (list): List of building dictionaries
            lat (str): Latitude
            lng (str): Longitude
        """
        try:
            # Create a treeview if it doesn't exist
            if not hasattr(self.app, 'osm_treeview'):
                self.create_osm_treeview()

            # Disable redrawing during updates for better performance
            self.app.osm_treeview.config(state="disabled")

            # Clear the existing treeview
            for item in self.app.osm_treeview.get_children():
                self.app.osm_treeview.delete(item)

            # Prepare all items in a batch for better performance
            items_to_add = []
            for i, building in enumerate(buildings):
                # Extract building details
                name = building.get('name', 'Unknown')
                address = building.get('address', 'Unknown')
                ruian_id = building.get('ruian_ref', 'Unknown')  # Changed from 'ruian_id' to 'ruian_ref'
                building_type = building.get('building_type', 'Unknown')

                # Store the item data
                items_to_add.append((
                    str(i+1),
                    (name, address, ruian_id, building_type, "View/Fetch")
                ))

            # Add all items at once in a batch
            for item_id, values in items_to_add:
                self.app.osm_treeview.insert(
                    '',
                    'end',
                    text=item_id,
                    values=values
                )

            # Store the buildings and coordinates for later use
            self.app.osm_buildings = buildings
            self.app.osm_lat = lat
            self.app.osm_lng = lng

            # Re-enable the treeview
            self.app.osm_treeview.config(state="normal")
        except Exception as e:
            print(f"Error displaying OSM buildings: {e}")
            # Make sure to re-enable the treeview even if there's an error
            if hasattr(self.app, 'osm_treeview'):
                self.app.osm_treeview.config(state="normal")

    def create_osm_treeview(self):
        """
        Create a treeview for displaying OSM buildings
        """
        try:
            # Create a frame for the treeview
            treeview_frame = ttk.Frame(self.app.results_frame)
            treeview_frame.pack(fill="both", expand=True, padx=5, pady=5)

            # Create the treeview
            columns = (
                "name",
                "address",
                "ruian_id",
                "type",
                "actions"
            )

            self.app.osm_treeview = ttk.Treeview(
                treeview_frame,
                columns=columns,
                show="headings",
                selectmode="extended"  # Changed from "browse" to "extended" to allow multiple selection
            )

            # Add the columns
            self.app.osm_treeview.heading("name", text="Building Name")
            self.app.osm_treeview.heading("address", text="Address")
            self.app.osm_treeview.heading("ruian_id", text="RUIAN ID")
            self.app.osm_treeview.heading("type", text="Building Type")
            self.app.osm_treeview.heading("actions", text="Actions")

            # Set column widths
            self.app.osm_treeview.column("name", width=150)
            self.app.osm_treeview.column("address", width=200)
            self.app.osm_treeview.column("ruian_id", width=100)
            self.app.osm_treeview.column("type", width=100)
            self.app.osm_treeview.column("actions", width=100)

            # Add a scrollbar
            scrollbar = ttk.Scrollbar(treeview_frame, orient="vertical", command=self.app.osm_treeview.yview)
            self.app.osm_treeview.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side="right", fill="y")

            # Pack the treeview
            self.app.osm_treeview.pack(side="left", fill="both", expand=True)

            # Add a click handler for the actions column
            self.app.osm_treeview.bind("<ButtonRelease-1>", self.on_osm_treeview_click)

            # Create a context menu for the treeview
            self.context_menu = tk.Menu(self.app.root, tearoff=0)
            self.context_menu.add_command(label="Open Selected in CUZK", command=self.open_selected_in_cuzk)
            self.context_menu.add_command(label="Fetch Owner Info for Selected", command=self.fetch_owner_info_for_selected)

            # Bind right-click to show the context menu
            self.app.osm_treeview.bind("<Button-3>", self.show_context_menu)

            # Add a button frame below the treeview
            button_frame = ttk.Frame(treeview_frame)
            button_frame.pack(fill="x", padx=5, pady=5)

            # Add a button to open selected buildings in CUZK
            ttk.Button(
                button_frame,
                text="Open Selected in CUZK",
                command=self.open_selected_in_cuzk
            ).pack(side="left", padx=5)
        except Exception as e:
            print(f"Error creating OSM treeview: {e}")

    def on_osm_treeview_click(self, event):
        """
        Handle click on the OSM treeview

        Args:
            event: The event that triggered this handler
        """
        try:
            # Get the item that was clicked
            item = self.app.osm_treeview.identify_row(event.y)
            if not item:
                return

            # Get the column that was clicked
            column = self.app.osm_treeview.identify_column(event.x)
            column_index = int(column[1:]) - 1  # Convert #1, #2, etc. to 0, 1, etc.

            # Get the values for this item
            values = self.app.osm_treeview.item(item, "values")

            # Check if the Actions column was clicked
            if column_index == 3:  # Actions column
                # Get the RUIAN ID
                ruian_id = values[2]

                # Check if it's a valid RUIAN ID using the RuianID model
                ruian_id_obj = RuianID.from_string(ruian_id)
                if ruian_id_obj:
                    # Ask the user what they want to do
                    action = MessageBoxes.ask_yes_no(
                        "Building Actions",
                        f"What would you like to do with building {values[1]}?\n\n"
                        f"RUIAN ID: {ruian_id_obj.value}\n\n"
                        "Yes = Open RUIAN detail page in browser\n"
                        "No = Fetch owner information automatically"
                    )

                    if action == 'yes':
                        # Open the RUIAN detail page in browser
                        self.open_cuzk_with_ruian_id(ruian_id_obj)
                    else:
                        # Fetch owner information
                        self.fetch_and_display_owner_info(ruian_id_obj)
                else:
                    MessageBoxes.show_info("No Valid RUIAN ID", f"The RUIAN ID '{ruian_id}' is not valid.")
        except Exception as e:
            print(f"Error handling OSM treeview click: {e}")

    def open_cuzk_with_ruian_id(self, ruian_id: Union[str, int, RuianID]):
        """
        Open the CUZK website with the given RUIAN ID

        Args:
            ruian_id (str, int, or RuianID): RUIAN ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # If ruian_id is already a RuianID object, use it directly
            if isinstance(ruian_id, RuianID):
                if not ruian_id.is_valid:
                    print(f"Invalid RUIAN ID: {ruian_id.original_value}")
                    MessageBoxes.show_error("Invalid RUIAN ID", f"The RUIAN ID '{ruian_id.original_value}' is not valid.")
                    return False
                clean_ruian_id = ruian_id.value
            else:
                # Try to create a RuianID object
                try:
                    ruian_id_obj = RuianID(ruian_id)
                    clean_ruian_id = ruian_id_obj.value
                except ValueError as e:
                    print(f"Invalid RUIAN ID: {ruian_id} - {str(e)}")
                    MessageBoxes.show_error("Invalid RUIAN ID", f"The RUIAN ID '{ruian_id}' is not valid: {str(e)}")
                    return False

            # Try different URL formats for CUZK
            # First try the direct object URL
            url = f"https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx?typ=Stavba&id={clean_ruian_id}"

            # Log the URL being opened
            print(f"Opening CUZK URL: {url}")

            # Open the URL in the default browser
            webbrowser.open(url)

            # Show a message to the user about potential errors
            MessageBoxes.show_info(
                "CUZK Website",
                "The CUZK website has been opened in your browser.\n\n"
                "If you see an error message like 'Špatná identifikace objektu',\n"
                "it means the RUIAN ID is not valid or the building doesn't exist in the CUZK database.\n\n"
                "You can try searching for the building manually on the CUZK website."
            )

            return True

        except Exception as e:
            print(f"Error opening CUZK with RUIAN ID: {e}")
            MessageBoxes.show_error("Error", f"Error opening CUZK with RUIAN ID: {str(e)}")
            return False

    def fetch_and_display_owner_info(self, ruian_id: Union[str, int, RuianID]):
        """
        Fetch and display owner information for the given RUIAN ID

        Args:
            ruian_id (str, int, or RuianID): RUIAN ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # If ruian_id is already a RuianID object, use it directly
            if isinstance(ruian_id, RuianID):
                if not ruian_id.is_valid:
                    print(f"Invalid RUIAN ID: {ruian_id.original_value}")
                    MessageBoxes.show_error("Invalid RUIAN ID", f"The RUIAN ID '{ruian_id.original_value}' is not valid.")
                    return False
                clean_ruian_id = ruian_id.value
            else:
                # Try to create a RuianID object
                try:
                    ruian_id_obj = RuianID(ruian_id)
                    clean_ruian_id = ruian_id_obj.value
                except ValueError as e:
                    print(f"Invalid RUIAN ID: {ruian_id} - {str(e)}")
                    MessageBoxes.show_error("Invalid RUIAN ID", f"The RUIAN ID '{ruian_id}' is not valid: {str(e)}")
                    return False

            # Show status
            self.app.show_status(f"Fetching owner information for RUIAN ID: {clean_ruian_id}...")

            # Use the CUZK integration to get property information
            property_data = self.app.cuzk_integration.get_property_by_ruian_id(clean_ruian_id)

            if property_data:
                # Display the property details
                self.app.property_display.display_property_details(property_data)
                return True
            else:
                # If the CUZK integration failed, try direct browser scraping
                MessageBoxes.show_info(
                    "Direct Browser Scraping",
                    "Automatic fetching failed. The CUZK website will be opened in your browser.\n\n"
                    "Please complete any CAPTCHA that appears and navigate to the property details page.\n\n"
                    "Once you are on the property details page, click OK to continue."
                )

                # Open the CUZK website with the RUIAN ID
                success = self.open_cuzk_with_ruian_id(clean_ruian_id)

                if not success:
                    return False

                # Ask the user to copy the property details
                property_data = self.app.property_ui_helpers.create_property_input_dialog(
                    self.app.root,
                    "Enter Property Details",
                    {
                        'owner_name': '',
                        'owner_address': '',
                        'property_type': '',
                        'parcel_number': '',
                        'cadastral_territory': '',
                        'ownership_share': '',
                        'lv_number': '',
                        'additional_notes': '',
                        'ruian_id': clean_ruian_id,  # Add the RUIAN ID
                    }
                )

                if property_data:
                    # Add source information
                    property_data['source'] = 'direct_browser_input'

                    # Display the property details
                    self.app.property_display.display_property_details(property_data)
                    return True

                return False
        except Exception as e:
            print(f"Error fetching and displaying owner info: {e}")
            MessageBoxes.show_error("Error", f"An error occurred while fetching owner information: {str(e)}")
            self.app.show_status("Ready")
            return False

    def open_selected_in_cuzk(self):
        """
        Open the CUZK website for all selected buildings in the treeview
        """
        try:
            # Get the selected items
            selected_items = self.app.osm_treeview.selection()

            if not selected_items:
                MessageBoxes.show_info("No Selection", "Please select one or more buildings first.")
                return

            # Ask if the user wants to filter by property type
            filter_by_type = MessageBoxes.ask_yes_no(
                "Filter by Property Type",
                "Do you want to filter the selected buildings by property type?\n\n"
                "This can help prevent errors with invalid RUIAN IDs."
            )

            # If the user wants to filter, show the property type filter dialog
            allowed_types = None
            if filter_by_type == 'yes':
                # Get all unique property types from the selected buildings
                available_types = set()
                for item_id in selected_items:
                    values = self.app.osm_treeview.item(item_id, "values")
                    building_type = values[3]  # Building type is in the fourth column
                    if building_type and building_type != 'Unknown':
                        available_types.add(building_type)

                # If we have available types, show the filter dialog
                if available_types:
                    from ui.property_type_filter_dialog import PropertyTypeFilterDialog
                    filter_dialog = PropertyTypeFilterDialog(self.app.root, list(available_types))
                    allowed_types = filter_dialog.show()

                    # If the user didn't select any types, ask if they want to continue without filtering
                    if not allowed_types:
                        continue_without_filter = MessageBoxes.ask_yes_no(
                            "No Types Selected",
                            "You didn't select any property types. Continue without filtering?"
                        )
                        if continue_without_filter != 'yes':
                            return
                        allowed_types = None

            # Count valid RUIAN IDs
            valid_ruian_ids = []
            building_names = {}
            filtered_out = 0

            # Collect all valid RUIAN IDs and building names
            for item_id in selected_items:
                values = self.app.osm_treeview.item(item_id, "values")
                ruian_id = values[2]  # RUIAN ID is in the third column
                name = values[0] or "Building"  # Building name is in the first column
                address = values[1] or "Unknown address"  # Address is in the second column
                building_type = values[3]  # Building type is in the fourth column

                # Check if the building type is allowed
                if allowed_types is not None and building_type not in allowed_types:
                    filtered_out += 1
                    continue

                # Validate the RUIAN ID using the RuianID model
                ruian_id_obj = RuianID.from_string(ruian_id)
                if ruian_id_obj:
                    valid_ruian_ids.append(ruian_id_obj)
                    building_names[ruian_id_obj.value] = f"{name} ({address})"

            # Check if we have any valid RUIAN IDs
            if not valid_ruian_ids:
                if filtered_out > 0:
                    MessageBoxes.show_info(
                        "No Valid Buildings",
                        f"All selected buildings were filtered out by property type filter."
                    )
                else:
                    MessageBoxes.show_info(
                        "No Valid RUIAN IDs",
                        "None of the selected buildings have valid RUIAN IDs."
                    )
                return

            # Get the maximum batch size from config
            try:
                from config.config import config
                max_batch_size = config.get_int("DEFAULT", "max_batch_size", 5)
            except Exception:
                max_batch_size = 5  # Default if config not available

            # Ask for confirmation if there are many buildings
            if len(valid_ruian_ids) > max_batch_size:
                confirm = MessageBoxes.ask_yes_no(
                    "Confirm Multiple Tabs",
                    f"This will open {len(valid_ruian_ids)} browser tabs. Continue?"
                )
                if confirm != 'yes':
                    return

            # Show status
            if filtered_out > 0:
                self.app.show_status(f"Opening {len(valid_ruian_ids)} buildings in CUZK ({filtered_out} filtered out)...")
            else:
                self.app.show_status(f"Opening {len(valid_ruian_ids)} buildings in CUZK...")

            # Create a progress dialog
            progress = ProgressDialog(
                self.app.root,
                title="Opening Buildings in CUZK",
                message=f"Opening {len(valid_ruian_ids)} buildings in CUZK...",
                max_value=len(valid_ruian_ids),
                determinate=True,
                cancelable=True
            )

            # Open each building in CUZK with progress updates
            try:
                for i, ruian_id in enumerate(valid_ruian_ids):
                    # Check if the operation was canceled
                    if progress.is_canceled():
                        break

                    # Update the progress dialog
                    building_name = building_names.get(ruian_id, f"Building {i+1}")
                    progress.update(i, item=building_name)

                    # Open the CUZK website
                    success = self.open_cuzk_with_ruian_id(ruian_id)

                    # Add a small delay to avoid overwhelming the browser
                    time.sleep(0.5)

                    # Update the progress
                    progress.update(i+1)

                    # If opening failed, we might want to stop to avoid overwhelming the user with error messages
                    if not success and i > 0:  # Allow at least one attempt
                        if MessageBoxes.ask_yes_no(
                            "Continue?",
                            f"Failed to open CUZK for building {i+1}. Continue with remaining buildings?"
                        ) != 'yes':
                            break
            finally:
                # Close the progress dialog
                progress.close()

            # Show success message
            if progress.is_canceled():
                # Get the current index (i is defined in the loop above)
                current_index = min(i+1, len(valid_ruian_ids)) if 'i' in locals() else 0
                self.app.show_status(f"Canceled after opening {current_index} of {len(valid_ruian_ids)} buildings in CUZK")
            else:
                self.app.show_status(f"Opened {len(valid_ruian_ids)} buildings in CUZK")

        except Exception as e:
            print(f"Error opening selected buildings in CUZK: {e}")
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.app.show_status("Ready")

    def show_context_menu(self, event):
        """
        Show the context menu on right-click

        Args:
            event: The event that triggered this handler
        """
        try:
            # Show the context menu at the current mouse position
            self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"Error showing context menu: {e}")

    def fetch_owner_info_for_selected(self):
        """
        Fetch owner information for all selected buildings in the treeview
        """
        try:
            # Get the selected items
            selected_items = self.app.osm_treeview.selection()

            if not selected_items:
                MessageBoxes.show_info("No Selection", "Please select one or more buildings first.")
                return

            # Ask if the user wants to filter by property type
            filter_by_type = MessageBoxes.ask_yes_no(
                "Filter by Property Type",
                "Do you want to filter the selected buildings by property type?\n\n"
                "This can help prevent errors with invalid RUIAN IDs."
            )

            # If the user wants to filter, show the property type filter dialog
            allowed_types = None
            if filter_by_type == 'yes':
                # Get all unique property types from the selected buildings
                available_types = set()
                for item_id in selected_items:
                    values = self.app.osm_treeview.item(item_id, "values")
                    building_type = values[3]  # Building type is in the fourth column
                    if building_type and building_type != 'Unknown':
                        available_types.add(building_type)

                # If we have available types, show the filter dialog
                if available_types:
                    from ui.property_type_filter_dialog import PropertyTypeFilterDialog
                    filter_dialog = PropertyTypeFilterDialog(self.app.root, list(available_types))
                    allowed_types = filter_dialog.show()

                    # If the user didn't select any types, ask if they want to continue without filtering
                    if not allowed_types:
                        continue_without_filter = MessageBoxes.ask_yes_no(
                            "No Types Selected",
                            "You didn't select any property types. Continue without filtering?"
                        )
                        if continue_without_filter != 'yes':
                            return
                        allowed_types = None

            # Count valid RUIAN IDs
            valid_ruian_ids = []
            building_names = {}
            filtered_out = 0

            # Collect all valid RUIAN IDs and building names
            for item_id in selected_items:
                values = self.app.osm_treeview.item(item_id, "values")
                ruian_id = values[2]  # RUIAN ID is in the third column
                name = values[0] or "Building"  # Building name is in the first column
                address = values[1] or "Unknown address"  # Address is in the second column
                building_type = values[3]  # Building type is in the fourth column

                # Check if the building type is allowed
                if allowed_types is not None and building_type not in allowed_types:
                    filtered_out += 1
                    continue

                # Validate the RUIAN ID using the RuianID model
                ruian_id_obj = RuianID.from_string(ruian_id)
                if ruian_id_obj:
                    valid_ruian_ids.append(ruian_id_obj)
                    building_names[ruian_id_obj.value] = f"{name} ({address})"

            # Check if we have any valid RUIAN IDs
            if not valid_ruian_ids:
                if filtered_out > 0:
                    MessageBoxes.show_info(
                        "No Valid Buildings",
                        f"All selected buildings were filtered out by property type filter."
                    )
                else:
                    MessageBoxes.show_info(
                        "No Valid RUIAN IDs",
                        "None of the selected buildings have valid RUIAN IDs."
                    )
                return

            # Get the maximum batch size from config
            try:
                from config.config import config
                max_batch_size = config.get_int("DEFAULT", "max_batch_size", 5)
            except Exception:
                max_batch_size = 5  # Default if config not available

            # Ask for confirmation if there are many buildings
            if len(valid_ruian_ids) > max_batch_size:
                confirm = MessageBoxes.ask_yes_no(
                    "Confirm Multiple Fetches",
                    f"This will fetch owner information for {len(valid_ruian_ids)} buildings. This may take some time. Continue?"
                )
                if confirm != 'yes':
                    return

            # Show status
            if filtered_out > 0:
                self.app.show_status(f"Fetching owner information for {len(valid_ruian_ids)} buildings ({filtered_out} filtered out)...")
            else:
                self.app.show_status(f"Fetching owner information for {len(valid_ruian_ids)} buildings...")

            # Create a progress dialog
            progress = ProgressDialog(
                self.app.root,
                title="Fetching Owner Information",
                message=f"Fetching owner information for {len(valid_ruian_ids)} buildings...",
                max_value=len(valid_ruian_ids),
                determinate=True,
                cancelable=True
            )

            # Fetch owner information for each building with progress updates
            results = []
            try:
                for i, ruian_id in enumerate(valid_ruian_ids):
                    # Check if the operation was canceled
                    if progress.is_canceled():
                        break

                    # Update the progress dialog
                    building_name = building_names.get(ruian_id, f"Building {i+1}")
                    progress.update(i, item=building_name)

                    # Fetch owner information
                    property_data = self.app.cuzk_integration.get_property_by_ruian_id(ruian_id)

                    if property_data:
                        results.append(property_data)
                        # Update the progress
                        progress.update(i+1)
                    else:
                        # If automatic fetching failed, ask if the user wants to try manual fetching
                        if i > 0 and MessageBoxes.ask_yes_no(
                            "Continue?",
                            f"Failed to automatically fetch owner information for building {i+1}.\n"
                            f"Would you like to try manual fetching for this building?"
                        ) == 'yes':
                            # Try manual fetching
                            success = self.fetch_and_display_owner_info(ruian_id)
                            if success and hasattr(self.app, 'property_display') and hasattr(self.app.property_display, 'current_property'):
                                results.append(self.app.property_display.current_property)

                        # Update the progress
                        progress.update(i+1)

                        # Ask if the user wants to continue with remaining buildings
                        if i > 0 and MessageBoxes.ask_yes_no(
                            "Continue?",
                            f"Continue with remaining buildings?"
                        ) != 'yes':
                            break
            finally:
                # Close the progress dialog
                progress.close()

            # Show success message
            if progress.is_canceled():
                # Get the current index (i is defined in the loop above)
                current_index = min(i+1, len(valid_ruian_ids)) if 'i' in locals() else 0
                self.app.show_status(f"Canceled after fetching {current_index} of {len(valid_ruian_ids)} buildings")
            else:
                self.app.show_status(f"Fetched owner information for {len(valid_ruian_ids)} buildings")

            # Display the results
            if results:
                # If we have a property display, show the first result
                if hasattr(self.app, 'property_display'):
                    self.app.property_display.display_property_details(results[0])

                # Store all results for later use
                self.app.property_list = results

                # If we have more than one result, ask if the user wants to export them
                if len(results) > 1:
                    export = MessageBoxes.ask_yes_no(
                        "Export Results",
                        f"Successfully fetched information for {len(results)} buildings. Would you like to export the results?"
                    )
                    if export == 'yes':
                        self.export_property_results(results)

        except Exception as e:
            print(f"Error fetching owner information for selected buildings: {e}")
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.app.show_status("Ready")

    def export_property_results(self, properties):
        """
        Export property results to a CSV file

        Args:
            properties (list): List of property data dictionaries
        """
        try:
            # Import here to avoid circular imports
            from tkinter import filedialog
            import csv
            import os

            # Ask for a file to save to
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Export Property Results"
            )

            if not file_path:
                return

            # Show status
            self.app.show_status(f"Exporting {len(properties)} properties to {file_path}...")

            # Create a progress dialog
            progress = ProgressDialog(
                self.app.root,
                title="Exporting Properties",
                message=f"Exporting {len(properties)} properties to CSV...",
                max_value=len(properties),
                determinate=True,
                cancelable=True
            )

            try:
                # Open the file for writing
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    # Create a CSV writer
                    fieldnames = [
                        'ruian_id', 'owner_name', 'owner_address', 'property_type',
                        'parcel_number', 'cadastral_territory', 'ownership_share',
                        'lv_number', 'lat', 'lng'
                    ]
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    # Write the header
                    writer.writeheader()

                    # Write each property
                    for i, property_data in enumerate(properties):
                        # Check if the operation was canceled
                        if progress.is_canceled():
                            break

                        # Update the progress dialog
                        progress.update(i, item=f"Property {i+1}")

                        # Create a row with only the fields we want
                        row = {field: property_data.get(field, '') for field in fieldnames}

                        # Write the row
                        writer.writerow(row)

                        # Update the progress
                        progress.update(i+1)
            finally:
                # Close the progress dialog
                progress.close()

            # Show success message
            if progress.is_canceled():
                # Get the current index (i is defined in the loop above)
                current_index = min(i+1, len(properties)) if 'i' in locals() else 0
                self.app.show_status(f"Export canceled after {current_index} of {len(properties)} properties")
            else:
                self.app.show_status(f"Successfully exported {len(properties)} properties to {file_path}")
                MessageBoxes.show_info("Export Complete", f"Successfully exported {len(properties)} properties to {os.path.basename(file_path)}")

        except Exception as e:
            print(f"Error exporting property results: {e}")
            MessageBoxes.show_error("Export Error", f"Error exporting properties: {str(e)}")
            self.app.show_status("Ready")