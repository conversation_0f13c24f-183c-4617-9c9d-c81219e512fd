"""
Property type filter dialog for the Czech Property Registry application.

This module provides a dialog for filtering property types in batch operations.
"""

import tkinter as tk
from tkinter import ttk


class PropertyTypeFilterDialog:
    """
    A dialog for filtering property types in batch operations.
    
    This dialog allows users to select which property types to include
    in batch operations.
    """
    
    def __init__(self, parent, available_types=None):
        """
        Initialize the property type filter dialog.
        
        Args:
            parent: Parent window
            available_types (list, optional): List of available property types
        """
        self.parent = parent
        self.dialog = None
        self.selected_types = []
        
        # If no available types are provided, use default common types
        self.available_types = available_types or [
            "apartments", "house", "residential", "yes", "detached", 
            "commercial", "industrial", "retail", "office", "school",
            "church", "hospital", "hotel", "public", "garage", "warehouse",
            "farm", "barn", "shed", "roof", "service", "cabin", "hut",
            "dormitory", "terrace", "kindergarten", "university", "college",
            "civic", "government", "train_station", "transportation", "religious",
            "cathedral", "chapel", "mosque", "synagogue", "temple", "shrine",
            "bakehouse", "bridge", "bunker", "carport", "conservatory", "construction",
            "cowshed", "digester", "farm_auxiliary", "grandstand", "greenhouse",
            "hangar", "houseboat", "kiosk", "pavilion", "riding_hall", "sports_hall",
            "stable", "sty", "transformer_tower", "water_tower", "storage_tank"
        ]
        
        # Create variables for checkboxes
        self.type_vars = {}
        for type_name in self.available_types:
            self.type_vars[type_name] = tk.BooleanVar(value=True)  # Default to selected
        
        # Create the dialog
        self._create_dialog()
        
    def _create_dialog(self):
        """Create the property type filter dialog."""
        # Create the dialog window
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Filter Property Types")
        self.dialog.geometry("500x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.focus_set()
        self.dialog.resizable(True, True)
        
        # Make the dialog modal
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Create a frame for the content
        content_frame = ttk.Frame(self.dialog, padding=10)
        content_frame.pack(fill="both", expand=True)
        
        # Add a label
        ttk.Label(
            content_frame,
            text="Select property types to include in batch operations:",
            font=("Arial", 10, "bold")
        ).pack(anchor="w", pady=(0, 10))
        
        # Create a frame for the checkboxes with scrollbar
        checkbox_frame = ttk.Frame(content_frame)
        checkbox_frame.pack(fill="both", expand=True)
        
        # Add a canvas for scrolling
        canvas = tk.Canvas(checkbox_frame)
        scrollbar = ttk.Scrollbar(checkbox_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack the canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Add checkboxes for each property type
        for i, type_name in enumerate(sorted(self.available_types)):
            ttk.Checkbutton(
                scrollable_frame,
                text=type_name,
                variable=self.type_vars[type_name]
            ).grid(row=i//3, column=i%3, sticky="w", padx=5, pady=2)
        
        # Add buttons for select all/none
        button_frame = ttk.Frame(content_frame)
        button_frame.pack(fill="x", pady=10)
        
        ttk.Button(
            button_frame,
            text="Select All",
            command=self._select_all
        ).pack(side="left", padx=5)
        
        ttk.Button(
            button_frame,
            text="Select None",
            command=self._select_none
        ).pack(side="left", padx=5)
        
        ttk.Button(
            button_frame,
            text="Select Common",
            command=self._select_common
        ).pack(side="left", padx=5)
        
        # Add OK/Cancel buttons
        button_frame = ttk.Frame(content_frame)
        button_frame.pack(fill="x", pady=10)
        
        ttk.Button(
            button_frame,
            text="OK",
            command=self._on_ok
        ).pack(side="right", padx=5)
        
        ttk.Button(
            button_frame,
            text="Cancel",
            command=self._on_close
        ).pack(side="right", padx=5)
        
    def _select_all(self):
        """Select all property types."""
        for var in self.type_vars.values():
            var.set(True)
            
    def _select_none(self):
        """Deselect all property types."""
        for var in self.type_vars.values():
            var.set(False)
            
    def _select_common(self):
        """Select common property types."""
        common_types = ["apartments", "house", "residential", "yes", "detached"]
        for type_name, var in self.type_vars.items():
            var.set(type_name in common_types)
            
    def _on_ok(self):
        """Handle the OK button."""
        # Get the selected types
        self.selected_types = [
            type_name for type_name, var in self.type_vars.items() if var.get()
        ]
        
        # Close the dialog
        self._on_close()
        
    def _on_close(self):
        """Handle the close button."""
        # Close the dialog
        if self.dialog is not None:
            self.dialog.destroy()
            self.dialog = None
            
    def show(self):
        """Show the dialog and return the selected property types."""
        # Wait for the dialog to close
        self.parent.wait_window(self.dialog)
        
        # Return the selected types
        return self.selected_types
