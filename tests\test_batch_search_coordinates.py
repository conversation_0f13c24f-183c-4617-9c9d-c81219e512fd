"""
Test for batch search functionality with random location and radius.

This test selects a random location from Google Maps API, chooses a random radius,
and outputs all coordinates found within that radius.
"""

import os
import sys
import unittest
import logging
import random
import time
import math
import json
import requests
from unittest.mock import MagicMock, patch

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BatchSearchCoordinatesTest")

# Import necessary modules
try:
    from api.google_maps.google_maps_integration import GoogleMapsIntegration
    from api.cuzk.cuzk_integration import CUZKIntegration
    from core.batch_search_manager import BatchSearchManager
    from api.osm.osm_integration import OSMIntegration
except ImportError as e:
    logger.error(f"Import error: {e}")
    logger.error("Make sure you're running this test from the project root directory")
    sys.exit(1)


class TestBatchSearchCoordinates(unittest.TestCase):
    """Tests for batch search functionality with random location and radius."""

    def setUp(self):
        """Initialize test data before each test."""
        # Czech locations for geocoding tests
        self.czech_locations = [
            "Prague",
            "Brno",
            "Ostrava",
            "Plzeň",
            "Liberec",
            "Olomouc",
            "České Budějovice",
            "Hradec Králové",
            "Ústí nad Labem",
            "Pardubice"
        ]

        # Create a mock app for the integrations
        self.app = MagicMock()

        # Initialize Google Maps integration
        self.google_maps = GoogleMapsIntegration()
        self.app.google_maps = self.google_maps

        # Initialize CUZK integration
        self.cuzk_integration = CUZKIntegration()
        self.app.cuzk_integration = self.cuzk_integration

        # Initialize OSM integration with the app
        self.osm_integration = None
        try:
            self.osm_integration = OSMIntegration(self.app)
            self.app.osm_integration = self.osm_integration
        except Exception as e:
            logger.warning(f"Could not initialize OSM integration: {e}")
            logger.warning("Will fall back to direct Overpass API queries")

        # Initialize batch search manager
        self.batch_search_manager = BatchSearchManager(self.app)

    def test_batch_search_with_usti_nad_labem(self):
        """Test batch search with Ústí nad Labem and 0.8 km radius."""
        logger.info("Testing batch search with Ústí nad Labem and 0.8 km radius")

        try:
            # Use Ústí nad Labem for consistent testing
            location_name = "Ústí nad Labem"
            logger.info(f"Selected location: {location_name}")

            # Use 0.8 km radius to match our cached data
            radius_km = 0.8
            logger.info(f"Selected radius: {radius_km} km")

            # Convert radius from km to meters
            radius_meters = int(radius_km * 1000)

            # Get coordinates for the location
            logger.info(f"Geocoding location: {location_name}")
            geocode_result = self.google_maps.geocode(location_name)

            if not geocode_result:
                logger.error(f"Could not geocode location: {location_name}")
                self.fail(f"Could not geocode location: {location_name}")
                return

            if 'lat' not in geocode_result or 'lng' not in geocode_result:
                logger.error(f"Invalid geocode result: {geocode_result}")
                self.fail(f"Invalid geocode result for location: {location_name}")
                return

            lat = geocode_result['lat']
            lng = geocode_result['lng']
            logger.info(f"Geocoded coordinates: lat={lat}, lng={lng}")

            # Get real building data from OpenStreetMap
            logger.info(f"Attempting to fetch real building data from OpenStreetMap for {location_name}")
            real_buildings = self._fetch_real_buildings(lat, lng, radius_meters, location_name, radius_km)

            # Process and verify the real buildings
            self._process_and_verify_buildings(real_buildings, location_name, radius_km, 100, 5000)

        except Exception as e:
            logger.error(f"Error in test_batch_search_with_usti_nad_labem: {e}", exc_info=True)
            self.fail(f"Test failed with error: {e}")

    def test_batch_search_with_prague(self):
        """Test batch search with Prague and 0.5 km radius."""
        logger.info("Testing batch search with Prague and 0.5 km radius")

        try:
            # Use Prague for testing a different location
            location_name = "Prague"
            logger.info(f"Selected location: {location_name}")

            # Use 0.5 km radius for a smaller area
            radius_km = 0.5
            logger.info(f"Selected radius: {radius_km} km")

            # Convert radius from km to meters
            radius_meters = int(radius_km * 1000)

            # Get coordinates for the location
            logger.info(f"Geocoding location: {location_name}")
            geocode_result = self.google_maps.geocode(location_name)

            if not geocode_result:
                logger.error(f"Could not geocode location: {location_name}")
                self.fail(f"Could not geocode location: {location_name}")
                return

            if 'lat' not in geocode_result or 'lng' not in geocode_result:
                logger.error(f"Invalid geocode result: {geocode_result}")
                self.fail(f"Invalid geocode result for location: {location_name}")
                return

            lat = geocode_result['lat']
            lng = geocode_result['lng']
            logger.info(f"Geocoded coordinates: lat={lat}, lng={lng}")

            # Get real building data from OpenStreetMap
            logger.info(f"Attempting to fetch real building data from OpenStreetMap for {location_name}")
            real_buildings = self._fetch_real_buildings(lat, lng, radius_meters, location_name, radius_km)

            # Process and verify the real buildings
            self._process_and_verify_buildings(real_buildings, location_name, radius_km, 100, 10000)

        except Exception as e:
            logger.error(f"Error in test_batch_search_with_prague: {e}", exc_info=True)
            self.fail(f"Test failed with error: {e}")

    def test_batch_search_with_large_radius(self):
        """Test batch search with Brno and a large radius (2.0 km)."""
        logger.info("Testing batch search with Brno and 2.0 km radius")

        try:
            # Use Brno for testing with a large radius
            location_name = "Brno"
            logger.info(f"Selected location: {location_name}")

            # Use 2.0 km radius for a larger area
            radius_km = 2.0
            logger.info(f"Selected radius: {radius_km} km")

            # Convert radius from km to meters
            radius_meters = int(radius_km * 1000)

            # Get coordinates for the location
            logger.info(f"Geocoding location: {location_name}")
            geocode_result = self.google_maps.geocode(location_name)

            if not geocode_result:
                logger.error(f"Could not geocode location: {location_name}")
                self.fail(f"Could not geocode location: {location_name}")
                return

            if 'lat' not in geocode_result or 'lng' not in geocode_result:
                logger.error(f"Invalid geocode result: {geocode_result}")
                self.fail(f"Invalid geocode result for location: {location_name}")
                return

            lat = geocode_result['lat']
            lng = geocode_result['lng']
            logger.info(f"Geocoded coordinates: lat={lat}, lng={lng}")

            # Get real building data from OpenStreetMap
            logger.info(f"Attempting to fetch real building data from OpenStreetMap for {location_name}")
            real_buildings = self._fetch_real_buildings(lat, lng, radius_meters, location_name, radius_km)

            # Process and verify the real buildings
            self._process_and_verify_buildings(real_buildings, location_name, radius_km, 500, 20000)

        except Exception as e:
            logger.error(f"Error in test_batch_search_with_large_radius: {e}", exc_info=True)
            self.fail(f"Test failed with error: {e}")

    def test_batch_search_with_small_radius(self):
        """Test batch search with Olomouc and a small radius (0.2 km)."""
        logger.info("Testing batch search with Olomouc and 0.2 km radius")

        try:
            # Use Olomouc for testing with a small radius
            location_name = "Olomouc"
            logger.info(f"Selected location: {location_name}")

            # Use 0.2 km radius for a smaller area
            radius_km = 0.2
            logger.info(f"Selected radius: {radius_km} km")

            # Convert radius from km to meters
            radius_meters = int(radius_km * 1000)

            # Get coordinates for the location
            logger.info(f"Geocoding location: {location_name}")
            geocode_result = self.google_maps.geocode(location_name)

            if not geocode_result:
                logger.error(f"Could not geocode location: {location_name}")
                self.fail(f"Could not geocode location: {location_name}")
                return

            if 'lat' not in geocode_result or 'lng' not in geocode_result:
                logger.error(f"Invalid geocode result: {geocode_result}")
                self.fail(f"Invalid geocode result for location: {location_name}")
                return

            lat = geocode_result['lat']
            lng = geocode_result['lng']
            logger.info(f"Geocoded coordinates: lat={lat}, lng={lng}")

            # Get real building data from OpenStreetMap
            logger.info(f"Attempting to fetch real building data from OpenStreetMap for {location_name}")
            real_buildings = self._fetch_real_buildings(lat, lng, radius_meters, location_name, radius_km)

            # Process and verify the real buildings
            self._process_and_verify_buildings(real_buildings, location_name, radius_km, 1, 1000)

        except Exception as e:
            logger.error(f"Error in test_batch_search_with_small_radius: {e}", exc_info=True)
            self.fail(f"Test failed with error: {e}")

    def test_batch_search_with_invalid_coordinates(self):
        """Test batch search with invalid coordinates (in the ocean)."""
        logger.info("Testing batch search with invalid coordinates")

        try:
            # Use invalid coordinates (in the ocean)
            lat = 0.0
            lng = 0.0
            radius_km = 1.0
            location_name = "Invalid Location"

            logger.info(f"Testing with invalid coordinates: lat={lat}, lng={lng}")
            logger.info(f"Selected radius: {radius_km} km")

            # Convert radius from km to meters
            radius_meters = int(radius_km * 1000)

            # Try to get building data from OpenStreetMap using our _fetch_real_buildings method
            logger.info(f"Attempting to fetch real building data from OpenStreetMap for invalid coordinates")

            try:
                # We expect this to return an empty list or raise an exception
                buildings = self._fetch_real_buildings(lat, lng, radius_meters, location_name, radius_km)

                # If we get here, we should have an empty list
                self.assertEqual(len(buildings), 0, "Should find 0 buildings at invalid coordinates")
                logger.info("Successfully verified that no buildings are found at invalid coordinates")
            except ValueError as e:
                # It's also acceptable if the method raises a ValueError
                logger.info(f"As expected, no buildings found at invalid coordinates: {e}")
                # Test passes in this case too
                pass

        except Exception as e:
            logger.error(f"Error in test_batch_search_with_invalid_coordinates: {e}", exc_info=True)
            self.fail(f"Test failed with error: {e}")

    def _process_and_verify_buildings(self, buildings, location_name, radius_km, min_expected, max_expected):
        """Process and verify the buildings found."""
        if buildings and len(buildings) > 0:
            logger.info(f"Successfully found {len(buildings)} real buildings from OpenStreetMap")

            # Output all coordinates
            logger.info(f"Found {len(buildings)} coordinates within radius {radius_km} km of {location_name}:")

            # Limit the number of points to process to avoid overwhelming the console
            max_points_to_process = min(100, len(buildings))
            if len(buildings) > max_points_to_process:
                logger.info(f"Processing only {max_points_to_process} out of {len(buildings)} coordinates to avoid overwhelming the console")
                # Take a random sample of points
                import random
                points_to_process = random.sample(buildings, max_points_to_process)
            else:
                points_to_process = buildings

            for idx, point in enumerate(points_to_process):
                try:
                    # Convert WGS84 to S-JTSK
                    x, y = self.cuzk_integration.convert_wgs84_to_sjtsk(point['lat'], point['lng'])

                    # Ensure coordinates are integers
                    x_int = int(x)
                    y_int = int(y)

                    # Generate MapaIdentifikace URL
                    url = f"http://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx?l=KN&x={x_int}&y={y_int}"

                    # Output coordinate information
                    logger.info(f"Coordinate {idx+1}:")
                    logger.info(f"  WGS84: lat={point['lat']}, lng={point['lng']}")
                    logger.info(f"  S-JTSK: x={x_int}, y={y_int}")
                    logger.info(f"  URL: {url}")

                    # Add a small delay to avoid overwhelming the console output
                    time.sleep(0.1)

                except Exception as e:
                    logger.error(f"Error processing coordinate {idx+1}: {e}")

            # Verify we have a reasonable number of buildings
            self.assertGreaterEqual(len(buildings), min_expected, f"Should find at least {min_expected} buildings")
            # We don't need an upper limit - we'll accept whatever number of buildings we find
            # Just log the number for informational purposes
            if len(buildings) > max_expected:
                logger.info(f"Found {len(buildings)} buildings, which is more than the expected {max_expected}, but that's okay")

            logger.info(f"Successfully found and output {len(buildings)} coordinates")
            return buildings
        else:
            # Don't fall back to synthetic data - fail the test if no real buildings are found
            error_msg = "No real buildings found from OpenStreetMap API. This test requires real building data."
            logger.error(error_msg)
            raise ValueError(error_msg)

    # Removed synthetic point generation methods since we're only using real data

    def _fetch_real_buildings(self, lat, lng, radius_meters, location_name=None, radius_km=None):
        """
        Fetch real building data from OpenStreetMap using the Overpass API.
        This method will NOT fall back to cached data if the API request fails.

        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius_meters (int): Search radius in meters
            location_name (str, optional): Name of the location (for logging purposes)
            radius_km (float, optional): Radius in kilometers (for logging purposes)

        Returns:
            list: List of building coordinates

        Raises:
            ValueError: If no real buildings are found or API request fails
        """
        # Log location name and radius if provided (for debugging purposes)
        if location_name:
            logger.info(f"Fetching buildings for location: {location_name}")
        if radius_km:
            logger.info(f"Search radius: {radius_km} km ({radius_meters} meters)")
        try:
            # Always fetch fresh data from the Overpass API
            logger.info("Fetching fresh data from Overpass API")

            # Construct the Overpass API query for buildings
            overpass_url = "https://overpass-api.de/api/interpreter"
            overpass_query = f"""
            [out:json][timeout:60];
            (
              way["building"](around:{radius_meters},{lat},{lng});
              relation["building"](around:{radius_meters},{lat},{lng});
              node["building"](around:{radius_meters},{lat},{lng});
            );
            out center;
            """

            logger.info(f"Querying Overpass API for buildings within {radius_meters}m of {lat}, {lng}")

            # Define cache file path for saving results
            cache_file = f"overpass_result_{lat:.5f}_{lng:.5f}_{radius_meters}.json"

            # Make the request
            response = requests.post(overpass_url, data={"data": overpass_query}, timeout=30)

            # Check if the request was successful
            if response.status_code == 200:
                # Parse the response
                data = response.json()

                # Save the results to a cache file for future reference (not for fallback)
                try:
                    with open(cache_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f)
                    logger.info(f"Saved Overpass API results to cache file {cache_file}")
                except Exception as e:
                    logger.error(f"Error saving Overpass API results to cache: {e}")

                # Log the number of elements found
                num_elements = len(data.get('elements', []))
                logger.info(f"Overpass API returned {num_elements} elements")

                # Extract building coordinates
                points = []
                for element in data.get('elements', []):
                    if element.get('type') == 'way' and 'center' in element:
                        points.append({
                            'lat': element['center']['lat'],
                            'lng': element['center']['lon']
                        })
                    elif element.get('type') == 'relation' and 'center' in element:
                        points.append({
                            'lat': element['center']['lat'],
                            'lng': element['center']['lon']
                        })
                    elif element.get('type') == 'node' and 'lat' in element and 'lon' in element:
                        points.append({
                            'lat': element['lat'],
                            'lng': element['lon']
                        })

                # Log the number of points extracted
                logger.info(f"Extracted {len(points)} building coordinates from Overpass API response")

                if len(points) > 0:
                    return points
                else:
                    error_msg = f"No buildings found at coordinates {lat}, {lng} with radius {radius_meters}m"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            else:
                error_msg = f"Error fetching buildings from OpenStreetMap: {response.status_code}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        except requests.exceptions.RequestException as e:
            error_msg = f"Error making Overpass API call: {e}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"Error fetching real buildings: {e}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg)


if __name__ == '__main__':
    unittest.main()
