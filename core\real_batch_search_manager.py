"""
Real batch search manager for the Czech Property Registry application.

This module provides a batch search manager that uses the Overpass API to fetch
real building data within a specified radius of a given address or coordinates.
It does not use any synthetic data or fallback to cached data.
"""

import os
import json
import time
import math
import threading
import logging
import requests
from typing import List, Dict, Any, Optional, Callable, Tuple
from datetime import datetime
from ui.message_boxes import MessageBoxes
from utils.city_lookup_service import CityLookupService

# Import city boundaries utility
from api.google_maps.city_boundaries import get_city_boundaries

# Setup logging
logger = logging.getLogger("RealBatchSearchManager")

class RealBatchSearchManager:
    """
    Real batch search manager that uses the Overpass API to fetch building data.

    This class provides methods for searching buildings within a radius of a given
    address or coordinates. It uses the Overpass API to fetch real building data
    and does not fall back to cached data if the API request fails.
    """

    def __init__(self, app):
        """
        Initialize the real batch search manager.

        Args:
            app: The main application instance with access to Google Maps, OSM, and CUZK APIs
        """
        self.app = app
        self.search_in_progress = False
        self.cancel_search = False

        # Initialize the city lookup service
        self.city_lookup_service = CityLookupService()

    def batch_search_by_address(self, address: str, property_types: List[str] = None,
                               radius: float = 2.0, max_results: int = None,
                               callback: Optional[Callable] = None, progress_callback: Optional[Callable] = None):
        """
        Perform a batch search for buildings by address with a customizable radius.

        If radius is 0, this will search the entire city area if the address is a city.

        Args:
            address (str): Full address to search in (from Google Maps autocomplete)
            property_types (list, optional): List of property types to include in results
            radius (float): Search radius in kilometers (default: 2.0)
                            Use 0 to search the entire city if the address is a city
            max_results (int, optional): Maximum number of results to return (default: None, no limit)
            callback (callable, optional): Function to call with the results
            progress_callback (callable, optional): Function to call with progress updates
                                                   Signature: progress_callback(current, total, status_message)
        """
        try:
            if self.search_in_progress:
                logger.warning("Search already in progress, ignoring new request")
                return

            self.search_in_progress = True
            self.cancel_search = False

            # Check if this is a city-level search (radius 0)
            is_city_search = radius == 0
            if is_city_search:
                logger.info(f"Real batch searching for entire city: {address}")
                self.app.show_status(f"Searching for all buildings in {address}...")
            else:
                logger.info(f"Real batch searching by address: {address} with radius {radius}km")
                self.app.show_status(f"Searching for buildings near {address}...")

            # Convert radius from km to meters
            radius_meters = int(float(radius) * 1000)

            # Start the search in a background thread
            threading.Thread(
                target=self._batch_search_thread,
                args=(address, property_types, radius_meters, max_results, callback, progress_callback),
                daemon=True
            ).start()

        except Exception as e:
            logger.error(f"Error in real batch search: {e}", exc_info=True)
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            self.search_in_progress = False
            self.app.show_status("Ready")

    def _batch_search_thread(self, address: str, property_types: List[str],
                            radius_meters: int, max_results: Optional[int],
                            callback: Optional[Callable], progress_callback: Optional[Callable] = None):
        """
        Background thread for batch searching buildings by address.

        Args:
            address (str): Full address to search in
            property_types (list): List of property types to include
            radius_meters (int): Search radius in meters
            max_results (int, optional): Maximum number of results to return
            callback (callable, optional): Function to call with the results
            progress_callback (callable, optional): Function to call with progress updates
        """
        try:
            # Update progress
            if progress_callback:
                self.app.root.after(0, lambda: progress_callback(0, 100, "Geocoding address..."))

            # Get coordinates for the address using our improved method
            lat, lng = self.get_coordinates_for_address(address)

            if lat is None or lng is None:
                logger.error(f"Could not get coordinates for address: {address}")
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.app.root.after(0, lambda: MessageBoxes.show_error(
                    "Geocoding Error", f"Could not find coordinates for address: {address}"))
                self.search_in_progress = False
                return

            logger.info(f"Using coordinates: lat={lat}, lng={lng}")

            # Check if this is a city-level search (radius 0)
            is_city_search = radius_meters == 0

            # Check if the address is a city
            is_city = False
            city_name = None

            # Try to determine if this is a city from the address itself
            # If the address is short and doesn't contain a street number, it's likely a city
            if len(address.split(',')) <= 2 and not any(c.isdigit() for c in address.split(',')[0]):
                is_city = True
                city_name = address.split(',')[0].strip()
                logger.info(f"Detected city from address: {city_name}")

            # Check if this is one of our special cities
            if "stod" in address.lower():
                is_city = True
                city_name = "Stod"
                logger.info(f"Detected special city: {city_name}")
            elif "mariánské" in address.lower() or "lazne" in address.lower():
                is_city = True
                city_name = "Mariánské Lázně"
                logger.info(f"Detected special city: {city_name}")

            # Try to get city data from our city lookup service
            city_data = self.city_lookup_service.get_city_data(address)
            if city_data:
                is_city = True
                city_name = city_data.get('name', city_name or address)
                logger.info(f"Detected city from database: {city_name}")

            # If this is a city-level search, try to get the city boundaries
            if is_city_search and is_city and city_name:
                self.app.root.after(0, lambda: self.app.show_status(
                    f"Searching for all buildings in {city_name}..."))

                # Update progress
                if progress_callback:
                    self.app.root.after(0, lambda: progress_callback(10, 100, f"Finding boundaries for {city_name}..."))

                # Get city boundaries using our improved city boundaries utility
                city_bounds = get_city_boundaries(city_name, "Czech Republic", self.app.google_maps)

                if city_bounds:
                    logger.info(f"Found boundaries for {city_name}: {city_bounds}")

                    # Update status
                    status_msg = f"Found boundaries for {city_name}, generating coordinates..."
                    self.app.root.after(0, lambda: self.app.show_status(status_msg))

                    # Update progress
                    if progress_callback:
                        self.app.root.after(0, lambda: progress_callback(20, 100, status_msg))

                    # Calculate city area size to determine appropriate density
                    city_width = city_bounds['east'] - city_bounds['west']
                    city_height = city_bounds['north'] - city_bounds['south']
                    city_area = city_width * city_height

                    # Calculate adaptive density based on city size
                    # Larger cities should have more points, not fewer
                    # We'll use a base density and scale it by the area

                    # Base density for a medium-sized city
                    base_density = 100

                    # Scale factor based on area
                    # For reference: Prague is about 0.11 in area units
                    # Mariánské Lázně is about 0.015 in area units
                    if city_area < 0.01:  # Very small city
                        # For small cities, we want points closer together
                        # but not too many total points
                        density = int(base_density * 1.5)
                        logger.info(f"Using density {density} for small city {city_name} (area: {city_area:.6f})")
                    elif city_area < 0.05:  # Small to medium city
                        density = int(base_density * 2)
                        logger.info(f"Using density {density} for medium city {city_name} (area: {city_area:.6f})")
                    else:  # Large city
                        # For large cities, we want more total points
                        # Scale based on area compared to a reference city (Prague)
                        scale = (city_area / 0.11) * 2.5
                        density = int(base_density * scale)
                        # Cap at a reasonable maximum
                        density = min(density, 500)
                        logger.info(f"Using density {density} for large city {city_name} (area: {city_area:.6f})")

                    # Generate coordinates within the city boundaries with adaptive density
                    coordinates = self._generate_coordinates_in_city(city_bounds, density=density)

                    if coordinates and len(coordinates) > 0:
                        logger.info(f"Generated {len(coordinates)} coordinates within {city_name}")

                        # Update status
                        self.app.root.after(0, lambda: self.app.show_status(
                            f"Processing {len(coordinates)} coordinates in {city_name}..."))

                        # Process each coordinate to get building information
                        buildings = []
                        processed_count = 0

                        for coord in coordinates:
                            if self.cancel_search:
                                logger.info("Search cancelled during coordinate processing")
                                break

                            # Update progress every 100 coordinates
                            if processed_count % 100 == 0:
                                progress_pct = (processed_count / len(coordinates)) * 100
                                status_msg = f"Processing coordinates... {progress_pct:.1f}% ({processed_count}/{len(coordinates)})"

                                # Update status in the main UI and call progress callback
                                # Use a single after() call to reduce overhead
                                def update_ui():
                                    # Update status in the main UI
                                    self.app.show_status(status_msg)

                                    # Call progress callback if provided
                                    if progress_callback:
                                        progress_callback(processed_count, len(coordinates), status_msg)

                                # Schedule the UI update on the main thread
                                self.app.root.after(0, update_ui)

                            # Generate CUZK URL with coordinate verification
                            url = self.app.cuzk_integration.generate_cuzk_url(
                                coord['lat'],
                                coord['lng'],
                                use_mapa_identifikace=True,
                                verify_coordinates=True
                            )

                            # Extract x and y from the URL
                            import re
                            match = re.search(r'x=(-?\d+)&y=(-?\d+)', url)
                            if match:
                                x_sjtsk = int(match.group(1))
                                y_sjtsk = int(match.group(2))
                            else:
                                # Fallback if URL parsing fails
                                x, y = self.app.cuzk_integration.convert_wgs84_to_sjtsk(coord['lat'], coord['lng'])
                                x_sjtsk = int(x)
                                y_sjtsk = int(y)

                            # Create a building entry
                            building = {
                                'lat': coord['lat'],
                                'lng': coord['lng'],
                                'x_sjtsk': x_sjtsk,
                                'y_sjtsk': y_sjtsk,
                                'url': url,
                                'tags': {
                                    'source': 'city_boundary_search',
                                    'city': city_name
                                }
                            }

                            buildings.append(building)
                            processed_count += 1

                            # Add a small delay to avoid overwhelming the CPU
                            if processed_count % 500 == 0:
                                time.sleep(0.1)

                        logger.info(f"Processed {processed_count} coordinates in {city_name}")
                    else:
                        # Fall back to bounding box query if no coordinates were generated
                        logger.warning(f"No coordinates generated for {city_name}, falling back to bounding box query")
                        self.app.root.after(0, lambda: self.app.show_status(
                            f"Using bounding box query for {city_name}..."))

                        # Use the boundaries to create a bounding box query
                        buildings = self._fetch_buildings_in_bounding_box(
                            city_bounds['south'],
                            city_bounds['west'],
                            city_bounds['north'],
                            city_bounds['east'],
                            city_name
                        )
                else:
                    # Fail if we can't get city boundaries - no fallback
                    error_msg = f"Could not determine boundaries for {city_name}. City search requires actual city boundaries."
                    logger.error(error_msg)
                    self.app.root.after(0, lambda: self.app.show_status("Ready"))
                    self.app.root.after(0, lambda: MessageBoxes.show_error("City Boundary Error", error_msg))
                    self.search_in_progress = False
                    return
            else:
                # Regular radius-based search
                self.app.root.after(0, lambda: self.app.show_status(
                    f"Fetching buildings within {radius_meters/1000:.1f}km of {address}..."))

                # For radius 0, use a small radius to avoid errors
                search_radius = max(radius_meters, 100)  # Use at least 100 meters

                # Fetch buildings using the Overpass API
                buildings = self._fetch_real_buildings(lat, lng, search_radius, address, radius_meters/1000)

            # Process the buildings and prepare results
            if buildings and len(buildings) > 0:
                logger.info(f"Successfully found {len(buildings)} real buildings from OpenStreetMap")

                # Process buildings and generate URLs
                # Pass the city name if this is a city-level search
                if is_city_search and is_city and city_name:
                    processed_buildings = self._process_buildings(buildings, city_name)
                else:
                    processed_buildings = self._process_buildings(buildings)

                # Call the callback function with the results
                if callback:
                    self.app.root.after(0, lambda: callback(processed_buildings))

                # Update status
                self.app.root.after(0, lambda: self.app.show_status(
                    f"Found {len(buildings)} buildings near {address}"))
            else:
                logger.warning(f"No buildings found near {address}")
                self.app.root.after(0, lambda: self.app.show_status("Ready"))
                self.app.root.after(0, lambda: MessageBoxes.show_warning(
                    "No Buildings Found", f"No buildings found within {radius_meters/1000:.1f}km of {address}"))

        except Exception as e:
            logger.error(f"Error in batch search thread: {e}", exc_info=True)
            self.app.root.after(0, lambda: self.app.show_status("Ready"))
            self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"An error occurred: {str(e)}"))

        finally:
            self.search_in_progress = False

    def _fetch_real_buildings(self, lat: float, lng: float, radius_meters: int,
                             location_name: str = None, radius_km: float = None) -> List[Dict[str, float]]:
        """
        Fetch real building data from OpenStreetMap using the Overpass API.
        This method will NOT fall back to cached data if the API request fails.
        It uses a multi-query approach to overcome API limitations.

        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius_meters (int): Search radius in meters
            location_name (str, optional): Name of the location (for logging purposes)
            radius_km (float, optional): Radius in kilometers (for logging purposes)

        Returns:
            list: List of building coordinates

        Raises:
            ValueError: If no real buildings are found or API request fails
        """
        # Log location name and radius if provided (for debugging purposes)
        if location_name:
            logger.info(f"Fetching buildings for location: {location_name}")
        if radius_km:
            logger.info(f"Search radius: {radius_km} km ({radius_meters} meters)")

        # For large radius searches, we'll use a multi-query approach
        # This helps overcome API limitations and timeouts
        if radius_meters > 500:
            return self._fetch_buildings_with_multi_query(lat, lng, radius_meters, location_name)


        try:
            # Always fetch fresh data from the Overpass API
            logger.info("Fetching fresh data from Overpass API")

            # Construct the Overpass API query for buildings
            overpass_url = "https://overpass-api.de/api/interpreter"

            # Calculate a slightly larger radius to ensure we capture all buildings
            # This helps overcome potential precision issues in the API
            extended_radius = int(radius_meters * 1.1)  # 10% larger radius

            overpass_query = f"""
            [out:json][timeout:180];
            (
              // Standard building tags
              way["building"](around:{extended_radius},{lat},{lng});
              relation["building"](around:{extended_radius},{lat},{lng});
              node["building"](around:{extended_radius},{lat},{lng});

              // Building parts
              way["building:part"](around:{extended_radius},{lat},{lng});
              relation["building:part"](around:{extended_radius},{lat},{lng});
              node["building:part"](around:{extended_radius},{lat},{lng});

              // Address-related tags
              way["addr:housenumber"](around:{extended_radius},{lat},{lng});
              node["addr:housenumber"](around:{extended_radius},{lat},{lng});
              relation["addr:housenumber"](around:{extended_radius},{lat},{lng});

              // Additional building-related tags
              way["amenity"="shelter"](around:{extended_radius},{lat},{lng});
              way["man_made"="building"](around:{extended_radius},{lat},{lng});
              way["historic"="building"](around:{extended_radius},{lat},{lng});
              way["leisure"="sports_centre"](around:{extended_radius},{lat},{lng});
              way["shop"](around:{extended_radius},{lat},{lng});
              way["office"](around:{extended_radius},{lat},{lng});
              way["tourism"="hotel"](around:{extended_radius},{lat},{lng});
              way["tourism"="apartment"](around:{extended_radius},{lat},{lng});
              way["tourism"="guest_house"](around:{extended_radius},{lat},{lng});

              // RUIAN-specific tags
              way["ref:ruian"](around:{extended_radius},{lat},{lng});
              relation["ref:ruian"](around:{extended_radius},{lat},{lng});
              node["ref:ruian"](around:{extended_radius},{lat},{lng});
              way["ref:ruian:building"](around:{extended_radius},{lat},{lng});
              relation["ref:ruian:building"](around:{extended_radius},{lat},{lng});
              node["ref:ruian:building"](around:{extended_radius},{lat},{lng});
            );
            out center;
            """

            logger.info(f"Querying Overpass API for buildings within {radius_meters}m of {lat}, {lng}")

            # Define cache file path for saving results
            cache_file = f"overpass_result_{lat:.5f}_{lng:.5f}_{radius_meters}.json"

            # Update status in the main thread
            self.app.root.after(0, lambda: self.app.show_status(
                f"Querying Overpass API for buildings within {radius_meters/1000:.2f}km of {lat:.6f}, {lng:.6f}..."))

            # Make the request with increased timeout
            response = requests.post(overpass_url, data={"data": overpass_query}, timeout=180)

            # Check if the request was successful
            if response.status_code == 200:
                # Parse the response
                data = response.json()

                # Save the results to a cache file for future reference (not for fallback)
                try:
                    with open(cache_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f)
                    logger.info(f"Saved Overpass API results to cache file {cache_file}")
                except Exception as e:
                    logger.error(f"Error saving Overpass API results to cache: {e}")

                # Log the number of elements found
                num_elements = len(data.get('elements', []))
                logger.info(f"Overpass API returned {num_elements} elements")

                # Check if we have a large number of elements
                if num_elements > 1000:
                    logger.info(f"Large result set detected: {num_elements} elements")
                    # Update status to indicate a large result set
                    self.app.root.after(0, lambda: self.app.show_status(
                        f"Processing large result set: {num_elements} buildings found..."))
                else:
                    # Update status in the main thread
                    self.app.root.after(0, lambda: self.app.show_status(
                        f"Processing {num_elements} buildings..."))

                # Extract building coordinates
                points = []
                processed_ids = set()  # To avoid duplicates

                for element in data.get('elements', []):
                    element_id = f"{element.get('type')}_{element.get('id')}"

                    # Skip if we've already processed this element
                    if element_id in processed_ids:
                        continue

                    # Extract coordinates based on element type
                    if element.get('type') == 'way' and 'center' in element:
                        points.append({
                            'lat': element['center']['lat'],
                            'lng': element['center']['lon'],
                            'tags': element.get('tags', {}),
                            'id': element.get('id'),
                            'type': element.get('type')
                        })
                        processed_ids.add(element_id)
                    elif element.get('type') == 'relation' and 'center' in element:
                        points.append({
                            'lat': element['center']['lat'],
                            'lng': element['center']['lon'],
                            'tags': element.get('tags', {}),
                            'id': element.get('id'),
                            'type': element.get('type')
                        })
                        processed_ids.add(element_id)
                    elif element.get('type') == 'node' and 'lat' in element and 'lon' in element:
                        # Only include nodes that are actually buildings
                        tags = element.get('tags', {})
                        if tags.get('building') or tags.get('building:part') or tags.get('addr:housenumber'):
                            points.append({
                                'lat': element['lat'],
                                'lng': element['lon'],
                                'tags': tags,
                                'id': element.get('id'),
                                'type': element.get('type')
                            })
                            processed_ids.add(element_id)

                # Log the number of points extracted
                logger.info(f"Extracted {len(points)} building coordinates from Overpass API response")

                if len(points) > 0:
                    return points
                else:
                    error_msg = f"No buildings found at coordinates {lat}, {lng} with radius {radius_meters}m"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            else:
                error_msg = f"Error fetching buildings from OpenStreetMap: {response.status_code}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        except requests.exceptions.RequestException as e:
            error_msg = f"Error making Overpass API call: {e}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"Error fetching real buildings: {e}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg)

    def _generate_coordinates_in_city(self, boundaries: Dict[str, float], density: int = 100) -> List[Dict[str, float]]:
        """
        Generate coordinates within the city boundaries.

        This method generates a dense grid of points within the city boundaries.

        Args:
            boundaries (dict): Dictionary with bounds information (north, south, east, west)
            density (int): Density of the grid (higher values mean more points)

        Returns:
            list: List of dictionaries with lat and lng keys
        """
        logger.info(f"Generating coordinates within city boundaries with density {density}")
        logger.info(f"City boundaries: North={boundaries['north']:.6f}, South={boundaries['south']:.6f}, East={boundaries['east']:.6f}, West={boundaries['west']:.6f}")

        # Extract boundaries
        north = boundaries['north']
        south = boundaries['south']
        east = boundaries['east']
        west = boundaries['west']

        # Calculate area dimensions
        width = east - west
        height = north - south
        area = width * height

        # Log area information
        logger.info(f"City area dimensions: Width={width:.6f}, Height={height:.6f}, Area={area:.6f}")

        # Calculate step sizes based on density
        # We want approximately density^2 points in the area
        lat_step = height / density
        lng_step = width / density

        logger.info(f"Step sizes: Latitude={lat_step:.6f}, Longitude={lng_step:.6f}")

        # Generate dense grid of coordinates
        all_coordinates = []
        for i in range(density + 1):
            for j in range(density + 1):
                lat = south + i * lat_step
                lng = west + j * lng_step

                all_coordinates.append({
                    'lat': lat,
                    'lng': lng
                })

        # Log some sample coordinates for debugging
        if all_coordinates:
            logger.info(f"Sample coordinates: First={all_coordinates[0]}, Middle={all_coordinates[len(all_coordinates)//2]}, Last={all_coordinates[-1]}")

        logger.info(f"Generated {len(all_coordinates)} coordinates within city boundaries")

        # Add a warning if the number of coordinates is suspiciously low
        if len(all_coordinates) < 100:
            logger.warning(f"Very few coordinates ({len(all_coordinates)}) generated. This might indicate an issue with city boundaries.")

        return all_coordinates

    def _process_buildings(self, buildings: List[Dict[str, float]], city_name: str = None) -> List[Dict[str, Any]]:
        """
        Process the buildings found and generate URLs for each one.

        Args:
            buildings (list): List of building coordinates
            city_name (str, optional): Name of the city for city-level searches

        Returns:
            list: List of processed building data
        """
        processed_buildings = []
        total_buildings = len(buildings)

        # Update status in the main thread
        self.app.root.after(0, lambda: self.app.show_status(
            f"Processing {total_buildings} buildings..."))

        for idx, point in enumerate(buildings):
            try:
                # Update progress every 10 buildings
                if idx % 10 == 0:
                    progress_pct = (idx / total_buildings) * 100
                    status_msg = f"Processing buildings... {progress_pct:.1f}% ({idx}/{total_buildings})"

                    # Use a function to avoid lambda capture issues
                    def update_status():
                        self.app.show_status(status_msg)

                    self.app.root.after(0, update_status)

                # Verify and convert WGS84 to S-JTSK with verification
                # Use the new method that verifies coordinates
                url = self.app.cuzk_integration.generate_cuzk_url(
                    point['lat'],
                    point['lng'],
                    use_mapa_identifikace=True,
                    verify_coordinates=True
                )

                # Extract x and y from the URL for consistency
                import re
                match = re.search(r'x=(-?\d+)&y=(-?\d+)', url)
                if match:
                    x_int = int(match.group(1))
                    y_int = int(match.group(2))
                else:
                    # Fallback if URL parsing fails
                    x, y = self.app.cuzk_integration.convert_wgs84_to_sjtsk(point['lat'], point['lng'])
                    x_int = int(x)
                    y_int = int(y)

                # Create building data dictionary
                building_data = {
                    'id': idx + 1,
                    'lat': point['lat'],
                    'lng': point['lng'],
                    'x': x_int,
                    'y': y_int,
                    'url': url
                }

                # Add additional data if available
                if 'tags' in point:
                    # Extract useful tags
                    tags = point.get('tags', {})

                    # Extract name
                    if tags.get('name'):
                        building_data['name'] = tags.get('name')

                    # Extract address components
                    if tags.get('addr:street'):
                        building_data['street'] = tags.get('addr:street')
                    if tags.get('addr:housenumber'):
                        building_data['housenumber'] = tags.get('addr:housenumber')
                    if tags.get('addr:city'):
                        building_data['city'] = tags.get('addr:city')
                    if tags.get('addr:postcode'):
                        building_data['postcode'] = tags.get('addr:postcode')

                    # Extract building type
                    if tags.get('building'):
                        building_data['building_type'] = tags.get('building')
                    elif tags.get('building:part'):
                        building_data['building_type'] = tags.get('building:part')
                    elif tags.get('amenity'):
                        building_data['building_type'] = tags.get('amenity')
                    elif tags.get('shop'):
                        building_data['building_type'] = tags.get('shop')
                    elif tags.get('office'):
                        building_data['building_type'] = tags.get('office')

                    # Extract RUIAN ID
                    if tags.get('ref:ruian'):
                        building_data['ruian_id'] = tags.get('ref:ruian')
                    elif tags.get('ref:ruian:building'):
                        building_data['ruian_id'] = tags.get('ref:ruian:building')

                    # Store the full tags for reference
                    building_data['tags'] = tags

                    # Try to extract address from alternative tags if standard ones are missing
                    if 'street' not in building_data:
                        # Try addr:full first
                        if tags.get('addr:full'):
                            building_data['street'] = tags.get('addr:full')
                        # Try addr:place
                        elif tags.get('addr:place'):
                            building_data['street'] = tags.get('addr:place')
                        # Try addr:hamlet
                        elif tags.get('addr:hamlet'):
                            building_data['street'] = tags.get('addr:hamlet')
                        # Try addr:suburb
                        elif tags.get('addr:suburb'):
                            building_data['street'] = tags.get('addr:suburb')
                        # Try addr:neighbourhood
                        elif tags.get('addr:neighbourhood'):
                            building_data['street'] = tags.get('addr:neighbourhood')

                    # If we have a city but no street, use the city as the main address component
                    if 'street' not in building_data and 'city' in building_data:
                        building_data['street'] = building_data['city']

                    # If we still don't have address information but we know the city name, use it
                    if ('street' not in building_data and 'city' not in building_data and
                        city_name and not building_data.get('name')):
                        building_data['city'] = city_name
                        building_data['street'] = city_name

                        # If we have a building type, add it to the name
                        if 'building_type' in building_data and building_data['building_type']:
                            building_type = building_data['building_type']
                            # Capitalize the first letter of the building type
                            if building_type:
                                building_type = building_type[0].upper() + building_type[1:]
                            building_data['name'] = f"{building_type} in {city_name}"

                processed_buildings.append(building_data)

                # Add a small delay to avoid overwhelming the CPU
                if idx % 100 == 0:
                    time.sleep(0.01)

            except Exception as e:
                logger.error(f"Error processing building {idx+1}: {e}")

        # Update status in the main thread
        self.app.root.after(0, lambda: self.app.show_status(
            f"Completed processing {total_buildings} buildings"))

        return processed_buildings

    def _fetch_buildings_in_bounding_box(self, south: float, west: float, north: float, east: float,
                                  location_name: str = None) -> List[Dict[str, float]]:
        """
        Fetch buildings within a bounding box using the Overpass API.

        Args:
            south (float): Southern latitude
            west (float): Western longitude
            north (float): Northern latitude
            east (float): Eastern longitude
            location_name (str, optional): Name of the location (for logging)

        Returns:
            list: List of buildings within the bounding box

        Raises:
            ValueError: If no buildings are found or API request fails
        """
        try:
            if location_name:
                logger.info(f"Fetching buildings in {location_name} using bounding box")
                self.app.root.after(0, lambda: self.app.show_status(
                    f"Fetching buildings in {location_name} using exact boundaries..."))
            else:
                logger.info(f"Fetching buildings in bounding box: {south},{west},{north},{east}")
                self.app.root.after(0, lambda: self.app.show_status(
                    f"Fetching buildings in bounding box..."))

            # Construct the Overpass API query for buildings within the bounding box
            overpass_url = "https://overpass-api.de/api/interpreter"

            overpass_query = f"""
            [out:json][timeout:180];
            (
              // Standard building tags
              way["building"]({south},{west},{north},{east});
              relation["building"]({south},{west},{north},{east});
              node["building"]({south},{west},{north},{east});

              // Building parts
              way["building:part"]({south},{west},{north},{east});
              relation["building:part"]({south},{west},{north},{east});
              node["building:part"]({south},{west},{north},{east});

              // Address-related tags
              way["addr:housenumber"]({south},{west},{north},{east});
              node["addr:housenumber"]({south},{west},{north},{east});
              relation["addr:housenumber"]({south},{west},{north},{east});

              // Additional building-related tags
              way["amenity"="shelter"]({south},{west},{north},{east});
              way["man_made"="building"]({south},{west},{north},{east});
              way["historic"="building"]({south},{west},{north},{east});
              way["leisure"="sports_centre"]({south},{west},{north},{east});
              way["shop"]({south},{west},{north},{east});
              way["office"]({south},{west},{north},{east});
              way["tourism"="hotel"]({south},{west},{north},{east});
              way["tourism"="apartment"]({south},{west},{north},{east});
              way["tourism"="guest_house"]({south},{west},{north},{east});

              // RUIAN-specific tags
              way["ref:ruian"]({south},{west},{north},{east});
              relation["ref:ruian"]({south},{west},{north},{east});
              node["ref:ruian"]({south},{west},{north},{east});
              way["ref:ruian:building"]({south},{west},{north},{east});
              relation["ref:ruian:building"]({south},{west},{north},{east});
              node["ref:ruian:building"]({south},{west},{north},{east});
            );
            out center;
            """

            # Make the request with increased timeout
            response = requests.post(overpass_url, data={"data": overpass_query}, timeout=180)

            # Check if the request was successful
            if response.status_code == 200:
                # Parse the response
                data = response.json()

                # Log the number of elements found
                num_elements = len(data.get('elements', []))
                logger.info(f"Overpass API returned {num_elements} elements for bounding box query")

                # Check if we have a large number of elements
                if num_elements > 1000:
                    logger.info(f"Large result set detected: {num_elements} elements")
                    # Update status to indicate a large result set
                    self.app.root.after(0, lambda: self.app.show_status(
                        f"Processing large result set: {num_elements} buildings found in {location_name or 'bounding box'}..."))
                else:
                    # Update status in the main thread
                    self.app.root.after(0, lambda: self.app.show_status(
                        f"Processing {num_elements} buildings in {location_name or 'bounding box'}..."))

                # Extract building coordinates
                points = []
                processed_ids = set()  # To avoid duplicates

                for element in data.get('elements', []):
                    element_id = f"{element.get('type')}_{element.get('id')}"

                    # Skip if we've already processed this element
                    if element_id in processed_ids:
                        continue

                    # Extract coordinates based on element type
                    if element.get('type') == 'way' and 'center' in element:
                        points.append({
                            'lat': element['center']['lat'],
                            'lng': element['center']['lon'],
                            'tags': element.get('tags', {}),
                            'id': element.get('id'),
                            'type': element.get('type')
                        })
                        processed_ids.add(element_id)
                    elif element.get('type') == 'relation' and 'center' in element:
                        points.append({
                            'lat': element['center']['lat'],
                            'lng': element['center']['lon'],
                            'tags': element.get('tags', {}),
                            'id': element.get('id'),
                            'type': element.get('type')
                        })
                        processed_ids.add(element_id)
                    elif element.get('type') == 'node' and 'lat' in element and 'lon' in element:
                        # Only include nodes that are actually buildings
                        tags = element.get('tags', {})
                        if tags.get('building') or tags.get('building:part') or tags.get('addr:housenumber'):
                            points.append({
                                'lat': element['lat'],
                                'lng': element['lon'],
                                'tags': tags,
                                'id': element.get('id'),
                                'type': element.get('type')
                            })
                            processed_ids.add(element_id)

                # Log the number of points extracted
                logger.info(f"Extracted {len(points)} building coordinates from bounding box query")

                if len(points) > 0:
                    return points
                else:
                    error_msg = f"No buildings found in bounding box {south},{west},{north},{east}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            else:
                error_msg = f"Error fetching buildings from OpenStreetMap: {response.status_code}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        except requests.exceptions.RequestException as e:
            error_msg = f"Error making Overpass API call: {e}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"Error fetching buildings in bounding box: {e}"
            logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg)

    def _fetch_buildings_with_multi_query(self, lat: float, lng: float, radius_meters: int, location_name: str = None) -> List[Dict[str, float]]:
        """
        Fetch buildings using multiple smaller queries to overcome API limitations.
        This method divides the search area into quadrants and makes separate queries for each.

        Args:
            lat (float): Latitude of the center point
            lng (float): Longitude of the center point
            radius_meters (int): Search radius in meters
            location_name (str, optional): Name of the location (for logging purposes)

        Returns:
            list: List of building coordinates
        """
        logger.info(f"Using multi-query approach for radius {radius_meters}m")

        # Update status in the main thread
        self.app.root.after(0, lambda: self.app.show_status(
            f"Using multi-query approach for {radius_meters/1000:.2f}km radius..."))

        # Calculate the smaller radius for each quadrant
        # We use 70% of the original radius to ensure overlap between quadrants
        smaller_radius = int(radius_meters * 0.7)

        # Calculate the offset for each quadrant (about half the smaller radius)
        offset_meters = smaller_radius * 0.5

        # Convert meters to approximate degrees
        # 1 degree of latitude is approximately 111,000 meters
        # 1 degree of longitude varies with latitude, approximately cos(lat) * 111,000 meters
        lat_offset = offset_meters / 111000
        lng_offset = offset_meters / (111000 * abs(math.cos(math.radians(lat))))

        # Define the center points for each quadrant
        quadrants = [
            {"name": "Northeast", "lat": lat + lat_offset, "lng": lng + lng_offset},
            {"name": "Northwest", "lat": lat + lat_offset, "lng": lng - lng_offset},
            {"name": "Southeast", "lat": lat - lat_offset, "lng": lng + lng_offset},
            {"name": "Southwest", "lat": lat - lat_offset, "lng": lng - lng_offset},
            {"name": "Center", "lat": lat, "lng": lng}  # Also include the center
        ]

        # Collect all buildings from all quadrants
        all_buildings = []
        processed_ids = set()

        for i, quadrant in enumerate(quadrants):
            if self.cancel_search:
                logger.info("Search cancelled during multi-query")
                break

            q_lat = quadrant["lat"]
            q_lng = quadrant["lng"]
            q_name = quadrant["name"]

            # Update status in the main thread
            self.app.root.after(0, lambda qn=q_name, i=i, total=len(quadrants): self.app.show_status(
                f"Querying {qn} quadrant ({i+1}/{total})..."))

            logger.info(f"Querying {q_name} quadrant at {q_lat:.6f}, {q_lng:.6f} with radius {smaller_radius}m")

            try:
                # Construct the Overpass API query for this quadrant
                overpass_url = "https://overpass-api.de/api/interpreter"

                overpass_query = f"""
                [out:json][timeout:60];
                (
                  // Standard building tags
                  way["building"](around:{smaller_radius},{q_lat},{q_lng});
                  relation["building"](around:{smaller_radius},{q_lat},{q_lng});
                  node["building"](around:{smaller_radius},{q_lat},{q_lng});

                  // Building parts
                  way["building:part"](around:{smaller_radius},{q_lat},{q_lng});

                  // Address-related tags
                  way["addr:housenumber"](around:{smaller_radius},{q_lat},{q_lng});

                  // RUIAN-specific tags
                  way["ref:ruian"](around:{smaller_radius},{q_lat},{q_lng});
                );
                out center;
                """

                # Make the request
                response = requests.post(overpass_url, data={"data": overpass_query}, timeout=60)

                # Check if the request was successful
                if response.status_code == 200:
                    # Parse the response
                    data = response.json()

                    # Log the number of elements found
                    num_elements = len(data.get('elements', []))
                    logger.info(f"Quadrant {q_name} returned {num_elements} elements")

                    # Extract building coordinates
                    for element in data.get('elements', []):
                        element_id = f"{element.get('type')}_{element.get('id')}"

                        # Skip if we've already processed this element
                        if element_id in processed_ids:
                            continue

                        # Extract coordinates based on element type
                        if element.get('type') == 'way' and 'center' in element:
                            all_buildings.append({
                                'lat': element['center']['lat'],
                                'lng': element['center']['lon'],
                                'tags': element.get('tags', {}),
                                'id': element.get('id'),
                                'type': element.get('type')
                            })
                            processed_ids.add(element_id)
                        elif element.get('type') == 'relation' and 'center' in element:
                            all_buildings.append({
                                'lat': element['center']['lat'],
                                'lng': element['center']['lon'],
                                'tags': element.get('tags', {}),
                                'id': element.get('id'),
                                'type': element.get('type')
                            })
                            processed_ids.add(element_id)
                        elif element.get('type') == 'node' and 'lat' in element and 'lon' in element:
                            # Only include nodes that are actually buildings
                            tags = element.get('tags', {})
                            if tags.get('building') or tags.get('building:part') or tags.get('addr:housenumber'):
                                all_buildings.append({
                                    'lat': element['lat'],
                                    'lng': element['lon'],
                                    'tags': tags,
                                    'id': element.get('id'),
                                    'type': element.get('type')
                                })
                                processed_ids.add(element_id)
                else:
                    logger.error(f"Error fetching buildings from quadrant {q_name}: {response.status_code}")

            except Exception as e:
                logger.error(f"Error in quadrant {q_name}: {e}")

            # Add a small delay between quadrant queries to avoid overwhelming the API
            time.sleep(1)

        # Log the total number of buildings found
        logger.info(f"Multi-query approach found {len(all_buildings)} buildings in total")

        # Filter buildings to ensure they're within the original radius
        filtered_buildings = []
        for building in all_buildings:
            # Calculate distance from the center point
            b_lat = building['lat']
            b_lng = building['lng']

            # Approximate distance calculation (in meters)
            # This is a simplified version of the Haversine formula
            dlat = (b_lat - lat) * 111000
            dlng = (b_lng - lng) * (111000 * abs(math.cos(math.radians(lat))))
            distance = math.sqrt(dlat**2 + dlng**2)

            # Only include buildings within the original radius
            if distance <= radius_meters:
                filtered_buildings.append(building)

        logger.info(f"After filtering, {len(filtered_buildings)} buildings are within the original {radius_meters}m radius")

        return filtered_buildings

    def get_coordinates_for_address(self, address: str) -> Tuple[Optional[float], Optional[float]]:
        """
        Get coordinates for an address.

        Args:
            address (str): Address to geocode

        Returns:
            tuple: (lat, lng) coordinates or (None, None) if geocoding failed
        """
        try:
            # First check if this is a city in our enhanced database
            city_coords = self.city_lookup_service.get_city_coordinates(address)
            if city_coords[0] is not None and city_coords[1] is not None:
                logger.info(f"Using coordinates from city database for {address}: {city_coords}")

                # Get the city data to show how old the data is
                city_data = self.city_lookup_service.get_city_data(address)
                if city_data and 'last_updated' in city_data:
                    try:
                        from datetime import datetime
                        last_updated = datetime.strptime(city_data['last_updated'], "%Y-%m-%dT%H:%M:%SZ")
                        age_days = (datetime.now().replace(tzinfo=None) - last_updated).days
                        logger.info(f"City data for {address} is {age_days} days old")

                        # Show a message if the data is verified
                        if city_data.get('verified', False):
                            logger.info(f"Using verified coordinates for {address}")
                    except Exception as e:
                        logger.error(f"Error calculating data age: {e}")

                # Verify the coordinates by reverse geocoding
                self._verify_coordinates(address, city_coords[0], city_coords[1], "city database")
                return city_coords

            # If not found in the database, use Google Maps geocoding
            if hasattr(self.app, 'google_maps'):
                # Geocode the address
                geocode_result = self.app.google_maps.geocode(address)

                if geocode_result and 'lat' in geocode_result and 'lng' in geocode_result:
                    lat = geocode_result['lat']
                    lng = geocode_result['lng']
                    logger.info(f"Geocoded {address}: {lat}, {lng}")

                    # Verify the coordinates by reverse geocoding
                    self._verify_coordinates(address, lat, lng, "Google Maps geocoding")

                    # Try to determine if this is a city
                    is_city = False
                    city_name = None

                    # Check if the address is short and doesn't contain a street number
                    if len(address.split(',')) <= 2 and not any(c.isdigit() for c in address.split(',')[0]):
                        is_city = True
                        city_name = address.split(',')[0].strip()

                    # If this is a city, add it to our database
                    if is_city and city_name:
                        try:
                            # Get S-JTSK coordinates
                            from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk
                            sjtsk_x, sjtsk_y = convert_wgs84_to_sjtsk(lat, lng)

                            # Add to database
                            self.city_lookup_service.add_or_update_city(
                                city_name=city_name,
                                wgs84_lat=lat,
                                wgs84_lng=lng,
                                sjtsk_x=sjtsk_x,
                                sjtsk_y=sjtsk_y,
                                formatted_address=geocode_result.get('formatted_address', address),
                                verified=False
                            )

                            logger.info(f"Added city {city_name} to database with coordinates: {lat}, {lng}")
                        except Exception as e:
                            logger.error(f"Error adding city to database: {e}")

                    return (lat, lng)

            logger.error(f"Failed to get coordinates for address: {address}")
            return (None, None)
        except Exception as e:
            logger.error(f"Error getting coordinates for address: {e}")
            return (None, None)

    def _verify_coordinates(self, original_address: str, lat: float, lng: float, source: str = "unknown"):
        """
        Verify coordinates by converting to S-JTSK, back to WGS84, and reverse geocoding.
        This helps identify potential issues with coordinate conversion.

        Args:
            original_address (str): The original address that was geocoded
            lat (float): Latitude from geocoding
            lng (float): Longitude from geocoding
            source (str): Source of the coordinates (e.g., "city database", "Google Maps geocoding")
        """
        try:
            logger.info(f"Verifying coordinates for '{original_address}' from {source}")

            # Step 1: Convert WGS84 to S-JTSK
            from utils.helpers.coordinate_helpers import convert_wgs84_to_sjtsk, convert_sjtsk_to_wgs84
            sjtsk_x, sjtsk_y = convert_wgs84_to_sjtsk(lat, lng)
            logger.info(f"Converted WGS84({lat}, {lng}) to S-JTSK({sjtsk_x}, {sjtsk_y})")

            # Step 2: Convert back to WGS84 to check for conversion errors
            reconverted_lat, reconverted_lng = convert_sjtsk_to_wgs84(sjtsk_x, sjtsk_y)

            # Calculate the difference between original and reconverted coordinates
            lat_diff = abs(lat - reconverted_lat)
            lng_diff = abs(lng - reconverted_lng)
            logger.info(f"Reconverted S-JTSK to WGS84({reconverted_lat}, {reconverted_lng})")
            logger.info(f"Coordinate difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")

            # Check if the difference is significant (more than 0.001 degrees, roughly 100 meters)
            if lat_diff > 0.001 or lng_diff > 0.001:
                logger.warning(f"Significant coordinate conversion error detected for '{original_address}'")
                logger.warning(f"Original WGS84: {lat}, {lng}")
                logger.warning(f"Reconverted WGS84: {reconverted_lat}, {reconverted_lng}")
                logger.warning(f"Difference: lat_diff={lat_diff:.6f}, lng_diff={lng_diff:.6f}")

            # Step 3: Reverse geocode the coordinates to get the address
            if hasattr(self.app, 'google_maps'):
                reverse_result = self.app.google_maps.reverse_geocode(lat, lng)

                if reverse_result and 'formatted_address' in reverse_result:
                    reverse_address = reverse_result['formatted_address']
                    logger.info(f"Reverse geocoded address: {reverse_address}")

                    # Compare with original address (simple string comparison)
                    # This is a basic check - in a real implementation, you might want to compare
                    # specific address components (street, city, etc.) instead of the full string
                    original_lower = original_address.lower()
                    reverse_lower = reverse_address.lower()

                    # Check if the original address is contained in the reverse geocoded address or vice versa
                    if original_lower in reverse_lower or reverse_lower in original_lower:
                        logger.info(f"Address verification passed: '{original_address}' matches '{reverse_address}'")
                    else:
                        # Try to extract the city from both addresses for a more lenient comparison
                        original_parts = original_lower.split(',')
                        reverse_parts = reverse_lower.split(',')

                        original_city = original_parts[0].strip() if len(original_parts) > 0 else ""
                        reverse_city = reverse_parts[0].strip() if len(reverse_parts) > 0 else ""

                        if original_city and reverse_city and (original_city in reverse_lower or reverse_city in original_lower):
                            logger.info(f"City-level address verification passed: '{original_city}' related to '{reverse_city}'")
                        else:
                            logger.warning(f"Address verification failed: '{original_address}' does not match '{reverse_address}'")
                            logger.warning(f"This may indicate incorrect coordinates for the requested location")

                            # Also check the S-JTSK coordinates by reverse geocoding the reconverted coordinates
                            reconverted_reverse = self.app.google_maps.reverse_geocode(reconverted_lat, reconverted_lng)
                            if reconverted_reverse and 'formatted_address' in reconverted_reverse:
                                reconverted_address = reconverted_reverse['formatted_address']
                                logger.warning(f"Reconverted coordinates reverse geocoded to: {reconverted_address}")

                                # Check if the reconverted address matches better
                                if original_lower in reconverted_address.lower() or reconverted_address.lower() in original_lower:
                                    logger.warning(f"The reconverted coordinates match the original address better!")
                                    logger.warning(f"Consider using the reconverted coordinates: {reconverted_lat}, {reconverted_lng}")
                else:
                    logger.warning(f"Could not reverse geocode coordinates ({lat}, {lng})")
        except Exception as e:
            logger.error(f"Error verifying coordinates: {e}")

    def cancel_current_search(self):
        """Cancel the current search operation if one is in progress."""
        if self.search_in_progress:
            logger.info("Cancelling current search operation")
            self.cancel_search = True
            self.app.show_status("Cancelling search...")
        else:
            logger.info("No search in progress to cancel")