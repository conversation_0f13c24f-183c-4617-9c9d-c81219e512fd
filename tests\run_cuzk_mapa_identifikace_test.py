"""
Run the CUZK MapaIdentifikace URL integration test.

This script runs the test for the CUZK MapaIdentifikace URL integration,
which verifies that the CUZK integration can generate MapaIdentifikace URLs
similar to how ikatastr works.
"""

import unittest
import logging
from test_cuzk_mapa_identifikace import TestCUZKMapaIdentifikace

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def run_test() -> None:
    """Run the CUZK MapaIdentifikace URL integration test."""
    logger.info("Running CUZK MapaIdentifikace URL integration test")
    
    # Create a test suite with the TestCUZKMapaIdentifikace class
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCUZKMapaIdentifikace)
    
    # Run the test suite
    result = unittest.TextTestRunner(verbosity=2).run(suite)
    
    # Log the result
    if result.wasSuccessful():
        logger.info("All tests passed successfully")
    else:
        logger.error(f"Tests failed: {len(result.failures)} failures, {len(result.errors)} errors")


if __name__ == '__main__':
    run_test()
