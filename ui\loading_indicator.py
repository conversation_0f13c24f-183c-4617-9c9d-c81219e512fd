"""
Loading indicator for the Czech Property Registry application.

This module provides a loading indicator for initial data loading.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import logging

# Configure logging
logger = logging.getLogger(__name__)


class LoadingIndicator:
    """
    A loading indicator for initial data loading.
    
    This indicator shows progress for initial data loading and provides
    visual feedback to the user during startup.
    """
    
    def __init__(self, parent, title="Loading Data", message="Loading essential data..."):
        """
        Initialize the loading indicator.
        
        Args:
            parent: Parent window
            title (str): Dialog title
            message (str): Message to display
        """
        self.parent = parent
        self.title = title
        self.message = message
        
        # Create variables
        self.dialog = None
        self.progress_bar = None
        self.message_label = None
        self.status_label = None
        self.data_status = {}
        
        # Create the dialog
        self._create_dialog()
        
    def _create_dialog(self):
        """Create the loading indicator dialog."""
        # Create the dialog window
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x200")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.focus_set()
        self.dialog.resizable(<PERSON>als<PERSON>, False)
        
        # Make the dialog modal
        self.dialog.protocol("WM_DELETE_WINDOW", lambda: None)  # Disable close button
        
        # Create a frame for the progress bar
        progress_frame = ttk.Frame(self.dialog, padding=10)
        progress_frame.pack(fill="both", expand=True)
        
        # Create a label for the message
        self.message_label = ttk.Label(progress_frame, text=self.message, font=("Helvetica", 12, "bold"))
        self.message_label.pack(pady=10, anchor="center")
        
        # Create a progress bar
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            mode="indeterminate",
            length=380
        )
        self.progress_bar.pack(fill="x", pady=10)
        self.progress_bar.start()
        
        # Create a frame for data status
        status_frame = ttk.LabelFrame(progress_frame, text="Data Loading Status")
        status_frame.pack(fill="both", expand=True, pady=5)
        
        # Create labels for each data type
        self.status_labels = {}
        data_types = ["Regions", "Cities", "Districts", "Property Types", "Cadastral Areas"]
        
        for i, data_type in enumerate(data_types):
            # Create a label for the data type
            label = ttk.Label(status_frame, text=f"{data_type}: Waiting...")
            label.grid(row=i, column=0, sticky="w", padx=5, pady=2)
            
            # Store the label
            self.status_labels[data_type.lower()] = label
    
    def update(self, message=None, data_type=None, status=None):
        """
        Update the loading indicator.
        
        Args:
            message (str, optional): New message
            data_type (str, optional): Data type to update
            status (str, optional): New status for the data type
        """
        if self.dialog is None or not self.dialog.winfo_exists():
            return
            
        # Update the message
        if message is not None:
            self.message = message
            self.message_label.config(text=message)
            
        # Update the data type status
        if data_type is not None and status is not None:
            data_type_key = data_type.lower().replace("_", " ")
            
            # Find the closest matching key
            matching_key = None
            for key in self.status_labels.keys():
                if data_type_key in key or key in data_type_key:
                    matching_key = key
                    break
            
            if matching_key:
                # Update the label
                self.status_labels[matching_key].config(
                    text=f"{matching_key.title()}: {status}",
                    foreground="green" if status == "Loaded" else "black"
                )
            
        # Update the dialog
        self.dialog.update_idletasks()
    
    def switch_to_determinate(self, max_value=100):
        """
        Switch to determinate mode.
        
        Args:
            max_value (int): Maximum value for the progress bar
        """
        if self.dialog is None or not self.dialog.winfo_exists():
            return
            
        # Stop the indeterminate animation
        self.progress_bar.stop()
        
        # Switch to determinate mode
        self.progress_bar.config(mode="determinate", maximum=max_value, value=0)
    
    def set_progress(self, value):
        """
        Set the progress value.
        
        Args:
            value (int): New progress value
        """
        if self.dialog is None or not self.dialog.winfo_exists():
            return
            
        # Update the progress value
        self.progress_bar["value"] = value
        
        # Update the dialog
        self.dialog.update_idletasks()
    
    def close(self):
        """Close the loading indicator."""
        if self.dialog is not None and self.dialog.winfo_exists():
            self.dialog.destroy()
            self.dialog = None
