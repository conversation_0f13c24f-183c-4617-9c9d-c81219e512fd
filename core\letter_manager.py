"""
Letter generation manager for the Czech Property Scraper application.
Handles all letter generation-related functionality.
"""

import tkinter as tk
from tkinter import filedialog
import threading
import os

from ui.message_boxes import MessageBoxes


class LetterManager:
    """Manages letter generation functionality for the application."""

    def __init__(self, app):
        """
        Initialize the letter manager.

        Args:
            app: The main application instance
        """
        self.app = app
        self.batch_letters = []

    def generate_letter(self):
        """Generate a letter for the current property owner"""
        try:
            # Get letter parameters
            buyer_name = self.app.buyer_name.get().strip()
            buyer_address = self.app.buyer_address.get().strip()
            buyer_contact = self.app.buyer_contact.get().strip()
            style = self.app.letter_style.get().strip()
            tone = self.app.letter_tone.get().strip()
            personalization = self.app.letter_personalization.get()
            use_gpt = self.app.use_gpt.get()

            # Validate inputs
            if not buyer_name or not buyer_address:
                MessageBoxes.show_error("Input Error", "Please enter your name and address")
                return

            # Check if we have an owner name
            if not self.app.owner_name:
                MessageBoxes.show_error("No Owner", "Please search for a property first")
                return

            # Show status
            self.app.show_status("Generating letter...")

            # Use threading to avoid freezing the UI
            def generate_thread():
                try:
                    # Generate the letter using the letter generator
                    letter_content = self.app.letter_generator.generate_letter(
                        owner_name=self.app.owner_name,
                        owner_address=self.app.owner_address,
                        owner_gender=self.app.owner_gender,
                        buyer_name=buyer_name,
                        buyer_address=buyer_address,
                        buyer_contact=buyer_contact,
                        style=style,
                        tone=tone,
                        personalization=personalization,
                        use_gpt=use_gpt
                    )

                    # Update the letter text in the main thread
                    self.app.root.after(0, lambda: self.update_letter_text(letter_content))

                    # Show success message
                    self.app.root.after(0, lambda: MessageBoxes.show_info(
                        "Success",
                        "Letter generated successfully.\n\n"
                        "You can now edit the letter and save it to a file."
                    ))

                    # Show status
                    self.app.root.after(0, lambda: self.app.show_status("Letter generated"))
                except Exception as e:
                    print(f"Error generating letter: {e}")
                    self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"Error generating letter: {str(e)}"))
                    self.app.root.after(0, lambda: self.app.show_status("Ready"))

            # Start the thread
            threading.Thread(target=generate_thread, daemon=True).start()
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def update_letter_text(self, letter_content):
        """
        Update the letter text widget with the generated letter

        Args:
            letter_content (str): The generated letter content
        """
        try:
            # Enable the letter text widget
            if hasattr(self.app, 'letter_text'):
                self.app.letter_text.config(state="normal")

                # Clear the letter text
                self.app.letter_text.delete(1.0, tk.END)

                # Insert the letter content
                self.app.letter_text.insert(tk.END, letter_content)

                # Disable the letter text widget
                self.app.letter_text.config(state="normal")  # Keep it editable
        except Exception as e:
            print(f"Error updating letter text: {e}")

    def generate_batch_letters(self):
        """Generate letters for all properties in the batch"""
        try:
            # Get letter parameters
            buyer_name = self.app.buyer_name.get().strip()
            buyer_address = self.app.buyer_address.get().strip()
            buyer_contact = self.app.buyer_contact.get().strip()
            style = self.app.letter_style.get().strip()
            tone = self.app.letter_tone.get().strip()
            personalization = self.app.letter_personalization.get()
            use_gpt = self.app.use_gpt.get()

            # Validate inputs
            if not buyer_name or not buyer_address:
                MessageBoxes.show_error("Input Error", "Please enter your name and address")
                return

            # Check if we have properties
            if not self.app.property_list:
                MessageBoxes.show_error("No Properties", "Please search for properties first")
                return

            # Show status
            self.app.show_status("Generating batch letters...")

            # Create a progress bar
            if hasattr(self.app, 'batch_progress'):
                self.app.batch_progress['value'] = 0
                self.app.batch_progress['maximum'] = len(self.app.property_list)

            # Define progress callback
            def progress_callback(current, _):  # total is not used
                if hasattr(self.app, 'batch_progress'):
                    self.app.root.after(0, lambda: setattr(self.app.batch_progress, 'value', current))

            # Define status callback
            def status_callback(status):
                self.app.root.after(0, lambda: self.app.show_status(status))

            # Use threading to avoid freezing the UI
            def generate_thread():
                try:
                    # Generate batch letters using the letter generator
                    self.batch_letters = self.app.letter_generator.generate_batch_letters(
                        property_list=self.app.property_list,
                        buyer_name=buyer_name,
                        buyer_address=buyer_address,
                        buyer_contact=buyer_contact,
                        style=style,
                        tone=tone,
                        personalization=personalization,
                        use_gpt=use_gpt,
                        progress_callback=progress_callback,
                        status_callback=status_callback
                    )

                    # Enable the export button
                    if hasattr(self.app, 'export_letters_btn'):
                        self.app.root.after(10, lambda: self.app.export_letters_btn.config(state="normal"))

                    # Show success message
                    self.app.root.after(10, lambda: MessageBoxes.show_info(
                        "Success",
                        f"Generated {len(self.batch_letters)} letters successfully.\n\n"
                        f"You can now export all letters to individual files."
                    ))
                except Exception as e:
                    print(f"Error generating batch letters: {e}")
                    self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"Error generating batch letters: {str(e)}"))
                    self.app.root.after(0, lambda: self.app.show_status("Ready"))

            # Start the thread
            threading.Thread(target=generate_thread, daemon=True).start()
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def export_batch_letters(self):
        """Export all generated batch letters to individual files"""
        try:
            # Check if we have letters
            if not self.batch_letters:
                MessageBoxes.show_error("No Letters", "Please generate letters first")
                return

            # Ask for a directory to save the letters
            export_dir = filedialog.askdirectory(
                title="Select Directory to Save Letters",
                mustexist=True
            )

            if not export_dir:
                return

            # Show status
            self.app.show_status("Exporting letters...")

            # Use threading to avoid freezing the UI
            def export_thread():
                try:
                    # Export each letter
                    for i, letter in enumerate(self.batch_letters):
                        # Get the owner name
                        owner_name = self.app.property_list[i].get('owner_name', f"Unknown_{i+1}")

                        # Sanitize the owner name for use in a filename
                        owner_name = ''.join(c if c.isalnum() or c in ' _-' else '_' for c in owner_name)

                        # Create the filename
                        filename = f"Letter_to_{owner_name}.txt"
                        filepath = os.path.join(export_dir, filename)

                        # Save the letter
                        with open(filepath, 'w', encoding='utf-8') as f:
                            f.write(letter)

                    # Show success message
                    self.app.root.after(0, lambda: MessageBoxes.show_info(
                        "Success",
                        f"Exported {len(self.batch_letters)} letters to {export_dir}"
                    ))

                    # Show status
                    self.app.root.after(0, lambda: self.app.show_status("Letters exported"))
                except Exception as e:
                    print(f"Error exporting letters: {e}")
                    self.app.root.after(0, lambda: MessageBoxes.show_error("Error", f"Error exporting letters: {str(e)}"))
                    self.app.root.after(0, lambda: self.app.show_status("Ready"))

            # Start the thread
            threading.Thread(target=export_thread, daemon=True).start()
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")

    def save_letter(self):
        """Save the current letter to a file"""
        try:
            # Check if we have a letter
            if not hasattr(self.app, 'letter_text'):
                MessageBoxes.show_error("No Letter", "No letter to save")
                return

            # Get the letter content
            letter_content = self.app.letter_text.get(1.0, tk.END)

            # Ask for a file to save the letter
            filepath = filedialog.asksaveasfilename(
                title="Save Letter",
                defaultextension=".txt",
                filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")]
            )

            if not filepath:
                return

            # Save the letter
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(letter_content)

            # Show success message
            MessageBoxes.show_info("Success", f"Letter saved to {filepath}")

            # Show status
            self.app.show_status("Letter saved")
        except Exception as e:
            MessageBoxes.show_error("Error", f"An error occurred: {str(e)}")
            print(f"Error details: {e}")
