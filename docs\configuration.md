# Configuration Guide

This guide explains how to configure the Czech Property Registry application.

## Configuration File

The application uses a configuration file named `config.ini` to store settings. A sample configuration file is provided as `config.ini.sample`.

## Configuration Sections

### DEFAULT Section

General application settings:

```ini
[DEFAULT]
development_mode = false
default_language = cs
log_level = INFO
cache_enabled = true
cache_expiry = 86400
default_region = Czech Republic
default_city = Prague
max_batch_size = 100
font_scale = 1.0
```

- `development_mode`: Enable development mode (true/false)
- `default_language`: Default language code (cs for Czech)
- `log_level`: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `cache_enabled`: Enable caching (true/false)
- `cache_expiry`: Cache expiry time in seconds
- `default_region`: Default region for searches
- `default_city`: Default city for searches
- `max_batch_size`: Maximum number of items in a batch search
- `font_scale`: Scale factor for fonts

### OPENAI Section

OpenAI API settings:

```ini
[OPENAI]
api_key = your_openai_api_key_here
model = gpt-3.5-turbo
temperature = 0.7
max_tokens = 150
```

- `api_key`: Your OpenAI API key
- `model`: OpenAI model to use
- `temperature`: Temperature parameter for text generation
- `max_tokens`: Maximum number of tokens to generate

### GOOGLE_MAPS Section

Google Maps API settings:

```ini
[GOOGLE_MAPS]
api_key = your_google_maps_api_key_here
region = cz
language = cs
```

- `api_key`: Your Google Maps API key
- `region`: Region code for Google Maps
- `language`: Language code for Google Maps

### CUZK Section

CUZK (Czech Office for Surveying, Mapping and Cadastre) credentials:

```ini
[CUZK]
username = your_cuzk_username
password = your_cuzk_password
```

- `username`: Your CUZK username
- `password`: Your CUZK password

### PATHS Section

Application file paths:

```ini
[PATHS]
data_dir = data
cache_dir = data/cache
templates_dir = data/templates
czech_data_dir = data/czech_data
```

- `data_dir`: Directory for data files
- `cache_dir`: Directory for cache files
- `templates_dir`: Directory for template files
- `czech_data_dir`: Directory for Czech-specific data files

### UI Section

User interface settings:

```ini
[UI]
window_width = 900
window_height = 700
theme = default
font_family = Arial
default_font_size = 9
small_font_size = 8
medium_font_size = 10
large_font_size = 11
title_font_size = 12
```

- `window_width`: Initial window width
- `window_height`: Initial window height
- `theme`: UI theme
- `font_family`: Font family for UI elements
- `*_font_size`: Font sizes for different UI elements

### CACHE Section

Cache settings:

```ini
[CACHE]
enabled = true
expiry_hours = 24
max_size_mb = 100
cleanup_on_start = true
```

- `enabled`: Enable caching (true/false)
- `expiry_hours`: Cache expiry time in hours
- `max_size_mb`: Maximum cache size in megabytes
- `cleanup_on_start`: Clean up expired cache entries on application start (true/false)

## Environment Variables

The application also supports configuration through environment variables:

- `OPENAI_API_KEY`: OpenAI API key
- `GOOGLE_MAPS_API_KEY`: Google Maps API key
- `DEVELOPMENT_MODE`: Enable development mode (true/false)

Environment variables take precedence over settings in the configuration file.
