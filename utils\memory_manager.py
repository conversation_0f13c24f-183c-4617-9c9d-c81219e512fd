"""
Memory management utilities for the Czech Property Registry application.
Provides tools for optimizing memory usage and preventing memory leaks.
"""

import gc
import weakref
import logging
import threading
import time
import os
import psutil
import sys
from typing import Dict, List, Set, Any, Optional, Callable, Tuple, Union, TypeVar, Generic

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("MemoryManager")

# Type variable for generic types
T = TypeVar('T')


class WeakCache(Generic[T]):
    """
    A cache that uses weak references to prevent memory leaks.
    Objects are automatically removed from the cache when they are no longer referenced elsewhere.
    """
    
    def __init__(self):
        """Initialize the weak cache."""
        self._cache: Dict[str, weakref.ref] = {}
        self._lock = threading.Lock()
    
    def get(self, key: str) -> Optional[T]:
        """
        Get an object from the cache.
        
        Args:
            key (str): Cache key
            
        Returns:
            The cached object or None if not found or garbage collected
        """
        with self._lock:
            ref = self._cache.get(key)
            if ref is None:
                return None
            
            obj = ref()
            if obj is None:
                # Object has been garbage collected, remove the reference
                del self._cache[key]
                return None
            
            return obj
    
    def set(self, key: str, obj: T) -> None:
        """
        Add an object to the cache.
        
        Args:
            key (str): Cache key
            obj: Object to cache
        """
        with self._lock:
            self._cache[key] = weakref.ref(obj, lambda _: self._remove_key(key))
    
    def _remove_key(self, key: str) -> None:
        """
        Remove a key from the cache when its object is garbage collected.
        
        Args:
            key (str): Cache key to remove
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
    
    def remove(self, key: str) -> bool:
        """
        Remove an object from the cache.
        
        Args:
            key (str): Cache key
            
        Returns:
            bool: True if the object was removed, False if it wasn't in the cache
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all objects from the cache."""
        with self._lock:
            self._cache.clear()
    
    def keys(self) -> List[str]:
        """
        Get all keys in the cache.
        
        Returns:
            List of cache keys
        """
        with self._lock:
            return list(self._cache.keys())
    
    def values(self) -> List[T]:
        """
        Get all objects in the cache.
        
        Returns:
            List of cached objects (excluding those that have been garbage collected)
        """
        result = []
        with self._lock:
            for ref in self._cache.values():
                obj = ref()
                if obj is not None:
                    result.append(obj)
        return result
    
    def items(self) -> List[Tuple[str, T]]:
        """
        Get all key-object pairs in the cache.
        
        Returns:
            List of (key, object) tuples (excluding those that have been garbage collected)
        """
        result = []
        with self._lock:
            for key, ref in list(self._cache.items()):
                obj = ref()
                if obj is not None:
                    result.append((key, obj))
        return result
    
    def __len__(self) -> int:
        """
        Get the number of objects in the cache.
        
        Returns:
            int: Number of objects
        """
        return len(self.values())


class ObjectPool(Generic[T]):
    """
    A pool of reusable objects to reduce memory allocations.
    Objects are created on demand and reused when returned to the pool.
    """
    
    def __init__(self, factory: Callable[[], T], max_size: int = 100, cleanup_interval: int = 60):
        """
        Initialize the object pool.
        
        Args:
            factory: Function that creates new objects
            max_size (int): Maximum number of objects to keep in the pool
            cleanup_interval (int): Interval in seconds for automatic cleanup
        """
        self._factory = factory
        self._max_size = max_size
        self._cleanup_interval = cleanup_interval
        self._pool: List[T] = []
        self._lock = threading.Lock()
        self._last_cleanup = time.time()
    
    def get(self) -> T:
        """
        Get an object from the pool or create a new one if the pool is empty.
        
        Returns:
            An object from the pool
        """
        with self._lock:
            if self._pool:
                return self._pool.pop()
            
            # Pool is empty, create a new object
            return self._factory()
    
    def put(self, obj: T) -> None:
        """
        Return an object to the pool.
        
        Args:
            obj: Object to return to the pool
        """
        with self._lock:
            # Only add to the pool if we're under the maximum size
            if len(self._pool) < self._max_size:
                self._pool.append(obj)
            
            # Check if we need to clean up
            current_time = time.time()
            if current_time - self._last_cleanup > self._cleanup_interval:
                self._cleanup()
                self._last_cleanup = current_time
    
    def _cleanup(self) -> None:
        """Clean up the pool by removing excess objects."""
        with self._lock:
            # If the pool is more than 75% full, reduce it to 50%
            if len(self._pool) > self._max_size * 0.75:
                target_size = int(self._max_size * 0.5)
                self._pool = self._pool[:target_size]
                gc.collect()  # Force garbage collection
    
    def clear(self) -> None:
        """Clear all objects from the pool."""
        with self._lock:
            self._pool.clear()
            gc.collect()  # Force garbage collection
    
    def resize(self, max_size: int) -> None:
        """
        Resize the pool.
        
        Args:
            max_size (int): New maximum size
        """
        with self._lock:
            self._max_size = max_size
            if len(self._pool) > max_size:
                self._pool = self._pool[:max_size]
                gc.collect()  # Force garbage collection
    
    def __len__(self) -> int:
        """
        Get the number of objects in the pool.
        
        Returns:
            int: Number of objects
        """
        with self._lock:
            return len(self._pool)


class MemoryMonitor:
    """
    Monitors memory usage and provides alerts when memory usage is high.
    Can also perform automatic garbage collection to free memory.
    """
    
    def __init__(self, 
                 warning_threshold: float = 80.0, 
                 critical_threshold: float = 90.0,
                 check_interval: int = 60,
                 auto_gc: bool = True):
        """
        Initialize the memory monitor.
        
        Args:
            warning_threshold (float): Memory usage percentage for warning alerts
            critical_threshold (float): Memory usage percentage for critical alerts
            check_interval (int): Interval in seconds between memory checks
            auto_gc (bool): Whether to automatically run garbage collection when memory usage is high
        """
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.check_interval = check_interval
        self.auto_gc = auto_gc
        
        self._running = False
        self._thread = None
        self._lock = threading.Lock()
        self._callbacks: Dict[str, Callable[[Dict[str, Any]], None]] = {}
    
    def start(self) -> None:
        """Start monitoring memory usage."""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            self._thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._thread.start()
            
            logger.info("Memory monitoring started")
    
    def stop(self) -> None:
        """Stop monitoring memory usage."""
        with self._lock:
            self._running = False
            if self._thread:
                self._thread.join(timeout=1.0)
                self._thread = None
            
            logger.info("Memory monitoring stopped")
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while self._running:
            try:
                # Get memory usage
                memory_info = self.get_memory_info()
                
                # Check thresholds
                if memory_info['percent'] >= self.critical_threshold:
                    logger.warning(f"Critical memory usage: {memory_info['percent']:.1f}%")
                    if self.auto_gc:
                        self.force_garbage_collection()
                elif memory_info['percent'] >= self.warning_threshold:
                    logger.info(f"High memory usage: {memory_info['percent']:.1f}%")
                
                # Notify callbacks
                self._notify_callbacks(memory_info)
                
                # Wait for next check
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in memory monitor: {e}")
                time.sleep(self.check_interval)
    
    def get_memory_info(self) -> Dict[str, Any]:
        """
        Get information about current memory usage.
        
        Returns:
            Dictionary with memory usage information
        """
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        # Get system memory info
        system_memory = psutil.virtual_memory()
        
        return {
            'rss': memory_info.rss,  # Resident Set Size
            'rss_mb': memory_info.rss / (1024 * 1024),
            'vms': memory_info.vms,  # Virtual Memory Size
            'vms_mb': memory_info.vms / (1024 * 1024),
            'percent': process.memory_percent(),
            'system_total': system_memory.total,
            'system_total_mb': system_memory.total / (1024 * 1024),
            'system_available': system_memory.available,
            'system_available_mb': system_memory.available / (1024 * 1024),
            'system_percent': system_memory.percent,
            'python_objects': len(gc.get_objects()),
            'gc_enabled': gc.isenabled(),
            'gc_counts': gc.get_count(),
            'gc_threshold': gc.get_threshold()
        }
    
    def force_garbage_collection(self) -> Dict[str, Any]:
        """
        Force garbage collection to free memory.
        
        Returns:
            Dictionary with garbage collection statistics
        """
        # Get memory usage before collection
        before = self.get_memory_info()
        
        # Collect garbage
        gc.collect()
        
        # Get memory usage after collection
        after = self.get_memory_info()
        
        # Calculate memory freed
        memory_freed = before['rss'] - after['rss']
        memory_freed_mb = memory_freed / (1024 * 1024)
        
        logger.info(f"Garbage collection freed {memory_freed_mb:.2f} MB of memory")
        
        return {
            'before': before,
            'after': after,
            'freed': memory_freed,
            'freed_mb': memory_freed_mb
        }
    
    def register_callback(self, name: str, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Register a callback to be called when memory usage is checked.
        
        Args:
            name (str): Name for the callback
            callback: Function to call with memory information
        """
        with self._lock:
            self._callbacks[name] = callback
    
    def unregister_callback(self, name: str) -> bool:
        """
        Unregister a callback.
        
        Args:
            name (str): Name of the callback to unregister
            
        Returns:
            bool: True if the callback was unregistered, False if it wasn't registered
        """
        with self._lock:
            if name in self._callbacks:
                del self._callbacks[name]
                return True
            return False
    
    def _notify_callbacks(self, memory_info: Dict[str, Any]) -> None:
        """
        Notify all registered callbacks with memory information.
        
        Args:
            memory_info: Dictionary with memory usage information
        """
        with self._lock:
            for name, callback in list(self._callbacks.items()):
                try:
                    callback(memory_info)
                except Exception as e:
                    logger.error(f"Error in memory callback '{name}': {e}")


# Create global instances for convenience
weak_cache = WeakCache()
memory_monitor = MemoryMonitor()
