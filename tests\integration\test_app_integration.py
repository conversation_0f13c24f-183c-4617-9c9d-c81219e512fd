#!/usr/bin/env python3
"""
Test script for the integration of Google Maps with the main application.
This script tests the geocoding and reverse geocoding functionality in the context of the main application.
"""

import tkinter as tk
from api.google_maps.google_maps_integration import GoogleMapsIntegration
from utils.data_source_manager import DataSourceManager

def test_data_source_manager():
    """Test the DataSourceManager class"""
    print("\n=== Testing DataSourceManager ===")

    # Create a root window for Tkinter
    root = tk.Tk()
    root.withdraw()  # Hide the window

    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()

    # Initialize the DataSourceManager
    data_source_manager = DataSourceManager(google_maps, root)

    # Test getting regions
    print("\nGetting regions...")
    regions = data_source_manager.get_regions()
    print(f"Found {len(regions)} regions")
    if regions:
        print("First 5 regions:")
        for region in list(regions)[:5]:
            print(f"- {region}")

    # Test getting cities for a region
    if regions:
        test_region = list(regions)[0]
        print(f"\nGetting cities for region '{test_region}'...")
        cities, source = data_source_manager.get_cities_in_region(test_region)
        print(f"Found {len(cities)} cities from source: {source}")
        if cities:
            print("First 5 cities:")
            for city in list(cities)[:5]:
                print(f"- {city}")

    # Test geocoding a city directly with Google Maps API
    test_city = "Prague"
    print(f"\nGeocoding city '{test_city}' directly with Google Maps API...")
    result = google_maps.geocode_address(test_city)
    if result:
        print(f"Success: {result['formatted_address']}")
        print(f"Coordinates: {result['lat']}, {result['lng']}")

        # Test reverse geocoding the coordinates
        print(f"\nReverse geocoding coordinates {result['lat']}, {result['lng']}...")
        address = google_maps.reverse_geocode(result['lat'], result['lng'])
        if address:
            print(f"Success: {address['formatted_address']}")
            if 'address_components' in address:
                components = address['address_components']
                if 'locality' in components:
                    print(f"City: {components['locality']}")
                if 'administrative_area_level_1' in components:
                    print(f"Region: {components['administrative_area_level_1']}")
                if 'country' in components:
                    print(f"Country: {components['country']}")
        else:
            print(f"Failed to reverse geocode coordinates {result['lat']}, {result['lng']}")
    else:
        print(f"Failed to geocode '{test_city}'")

    # Clean up
    root.destroy()

def test_google_maps_in_app():
    """Test the GoogleMapsIntegration class in the context of the main application"""
    print("\n=== Testing GoogleMapsIntegration in App Context ===")

    # Create a root window for Tkinter
    root = tk.Tk()
    root.withdraw()  # Hide the window

    # Initialize the Google Maps integration
    google_maps = GoogleMapsIntegration()

    # Initialize the DataSourceManager
    data_source_manager = DataSourceManager(google_maps, root)

    # Test getting regions
    print("\nGetting regions from DataSourceManager...")
    regions, source = data_source_manager.get_regions()
    print(f"Found {len(regions)} regions from source: {source}")
    if regions:
        print("First 5 regions:")
        for region in list(regions)[:5]:
            print(f"- {region}")

    # Test getting cities for a region
    if regions:
        test_region = list(regions)[0]
        print(f"\nGetting cities for region '{test_region}' from DataSourceManager...")
        cities, source = data_source_manager.get_cities_in_region(test_region)
        print(f"Found {len(cities)} cities from source: {source}")
        if cities:
            print("First 5 cities:")
            for city in list(cities)[:5]:
                print(f"- {city}")

    # Test geocoding a city directly with Google Maps API
    test_city = "Prague"
    print(f"\nGeocoding city '{test_city}' directly with Google Maps API...")
    result = google_maps.geocode_address(test_city)
    if result:
        print(f"Success: {result['formatted_address']}")
        print(f"Coordinates: {result['lat']}, {result['lng']}")

        # Test reverse geocoding the coordinates
        print(f"\nReverse geocoding coordinates {result['lat']}, {result['lng']}...")
        address = google_maps.reverse_geocode(result['lat'], result['lng'])
        if address:
            print(f"Success: {address['formatted_address']}")
            if 'address_components' in address:
                components = address['address_components']
                if 'locality' in components:
                    print(f"City: {components['locality']}")
                if 'administrative_area_level_1' in components:
                    print(f"Region: {components['administrative_area_level_1']}")
                if 'country' in components:
                    print(f"Country: {components['country']}")
        else:
            print(f"Failed to reverse geocode coordinates {result['lat']}, {result['lng']}")
    else:
        print(f"Failed to geocode '{test_city}'")

    # Test place predictions
    test_query = "Prague"
    print(f"\nGetting place predictions for '{test_query}'...")
    predictions = google_maps.get_place_predictions(test_query)
    if predictions:
        print(f"Found {len(predictions)} predictions:")
        for i, prediction in enumerate(predictions[:3]):  # Show only first 3
            print(f"  {i+1}. {prediction.get('description')}")
    else:
        print(f"No predictions found for '{test_query}'")

    # Clean up
    root.destroy()

def main():
    """Main function"""
    print("Application Integration Test")
    print("===========================")

    # Test the DataSourceManager
    test_data_source_manager()

    # Test the GoogleMapsIntegration in app context
    test_google_maps_in_app()

if __name__ == "__main__":
    main()
