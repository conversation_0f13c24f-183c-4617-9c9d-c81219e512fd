# Contributing to Czech Property Registry

Thank you for your interest in contributing to the Czech Property Registry application! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

Please be respectful and considerate of others when contributing to this project. We aim to foster an inclusive and welcoming community.

## How to Contribute

### Reporting Bugs

If you find a bug in the application, please create an issue on GitHub with the following information:

1. A clear, descriptive title
2. A detailed description of the bug
3. Steps to reproduce the bug
4. Expected behavior
5. Actual behavior
6. Screenshots (if applicable)
7. Environment information (OS, Python version, etc.)

### Suggesting Features

If you have an idea for a new feature or enhancement, please create an issue on GitHub with the following information:

1. A clear, descriptive title
2. A detailed description of the feature
3. Why the feature would be useful
4. Any implementation ideas you have

### Pull Requests

We welcome pull requests for bug fixes, features, and improvements. To submit a pull request:

1. Fork the repository
2. Create a new branch for your changes
3. Make your changes
4. Write tests for your changes
5. Run the tests to ensure they pass
6. Update documentation as needed
7. Submit a pull request

Please follow these guidelines for pull requests:

- Keep pull requests focused on a single issue or feature
- Follow the coding standards (see below)
- Include tests for new functionality
- Update documentation as needed
- Provide a clear description of the changes in the pull request

## Development Environment

See the [Development Guide](docs/development.md) for information on setting up your development environment.

## Coding Standards

### Python Style Guide

This project follows the [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide for Python code.

Key points:

- Use 4 spaces for indentation
- Maximum line length of 79 characters
- Use docstrings for all public modules, functions, classes, and methods
- Use snake_case for variable and function names
- Use CamelCase for class names
- Use UPPERCASE for constants

### Documentation

- All modules, classes, and functions should have docstrings
- Use [Google-style docstrings](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings)
- Keep docstrings up-to-date with code changes

### Testing

- Write unit tests for all new functionality
- Ensure all tests pass before submitting a pull request
- Aim for high test coverage

## File Organization

The project is organized into several packages:

```
Reality/
├── api/                  # API integrations
│   ├── cuzk/             # CUZK-specific API code
│   ├── google_maps/      # Google Maps API code
│   ├── osm/              # OpenStreetMap API code
│   └── property/         # Property search functionality
├── config/               # Configuration files
├── core/                 # Core application logic
├── data/                 # Data files
│   ├── cache/            # Cache files
│   ├── czech_data/       # Czech-specific data
│   ├── json/             # JSON data files
│   └── templates/        # HTML templates
├── docs/                 # Documentation
├── examples/             # Example data and methods
├── models/               # Data models
├── services/             # Service classes
├── tests/                # Test files
│   ├── integration/      # Integration tests
│   └── unit/             # Unit tests
├── ui/                   # User interface components
│   ├── components/       # Reusable UI components
│   ├── screens/          # Main application screens
│   └── dialogs/          # Dialog windows
└── utils/                # Utility functions and classes
```

When adding new files, please follow this organization:

- Place API integrations in the appropriate subdirectory of `api/`
- Place UI components in the appropriate subdirectory of `ui/`
- Place utility functions in `utils/`
- Place tests in `tests/unit/` or `tests/integration/` as appropriate

## Commit Messages

Please write clear, descriptive commit messages:

- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests after the first line

Example:

```
Add Czech address validation feature

- Add validation for Czech postal codes
- Add validation for Czech street names
- Add tests for address validation

Fixes #123
```

## Versioning

This project follows [Semantic Versioning](https://semver.org/):

- MAJOR version for incompatible API changes
- MINOR version for new functionality in a backward-compatible manner
- PATCH version for backward-compatible bug fixes

## License

By contributing to this project, you agree that your contributions will be licensed under the project's license.

## Questions

If you have any questions about contributing, please create an issue on GitHub or contact the project maintainers.

Thank you for your contributions!
