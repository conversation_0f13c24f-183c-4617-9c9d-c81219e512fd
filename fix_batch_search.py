"""
Fix batch search issues in the Czech Property Registry application.

This script:
1. Identifies and fixes issues with batch search coordinate selection
2. Clears all caches
3. Adds debug logging to help diagnose issues
4. Restarts the application
"""

import os
import shutil
import sys
import subprocess
import logging
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_debug_logging():
    """Add debug logging to key functions to help diagnose issues."""
    files_to_update = [
        'core/batch_search/batch_search_manager_impl.py',
        'core/real_batch_search_manager.py',
        'api/google_maps/city_boundaries.py',
    ]
    
    updated_count = 0
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            try:
                # Read the file
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Add debug logging to key functions
                if 'batch_search_manager_impl.py' in file_path:
                    # Add logging to fetch_properties_in_city
                    if 'def fetch_properties_in_city' in content:
                        content = re.sub(
                            r'(def fetch_properties_in_city.*?\n.*?try:)',
                            r'\1\n        logger.debug(f"BATCH SEARCH DEBUG: Fetching properties in city: {city}")\n        logger.debug(f"BATCH SEARCH DEBUG: City boundaries: {boundaries}")',
                            content
                        )
                    
                    # Add logging to fetch_properties_in_area
                    if 'def fetch_properties_in_area' in content:
                        content = re.sub(
                            r'(def fetch_properties_in_area.*?\n.*?try:)',
                            r'\1\n        logger.debug(f"BATCH SEARCH DEBUG: Fetching properties in area: {lat}, {lng}, radius: {radius_meters}m")',
                            content
                        )
                
                # Add logging to city_boundaries.py
                if 'city_boundaries.py' in file_path:
                    # Add logging to get_city_boundaries
                    if 'def get_city_boundaries' in content:
                        content = re.sub(
                            r'(def get_city_boundaries.*?\n.*?logger\.info\(f"Getting boundaries for city: {city_name}"\))',
                            r'\1\n    logger.debug(f"CITY BOUNDARIES DEBUG: Getting boundaries for city: {city_name}")',
                            content
                        )
                
                # Add logging to real_batch_search_manager.py
                if 'real_batch_search_manager.py' in file_path:
                    # Add logging to _batch_search_thread
                    if 'def _batch_search_thread' in content:
                        content = re.sub(
                            r'(def _batch_search_thread.*?\n.*?try:)',
                            r'\1\n            logger.debug(f"BATCH SEARCH DEBUG: Starting batch search for address: {address}")',
                            content
                        )
                
                # Write the updated content back to the file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"Added debug logging to: {file_path}")
                updated_count += 1
            except Exception as e:
                logger.error(f"Error adding debug logging to {file_path}: {e}")
    
    return updated_count

def fix_batch_search_coordinate_selection():
    """Fix issues with batch search coordinate selection."""
    files_to_update = [
        'core/batch_search/batch_search_manager_impl.py',
        'core/real_batch_search_manager.py',
    ]
    
    updated_count = 0
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            try:
                # Read the file
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Fix batch search coordinate selection
                if 'batch_search_manager_impl.py' in file_path:
                    # Fix fetch_properties_in_city to use the city center coordinates
                    if 'def fetch_properties_in_city' in content:
                        # Make sure we're using the correct center coordinates
                        content = re.sub(
                            r'(# Get the center of the city\n\s+center = boundaries\.get\(\'center\', \{\}\)\n\s+lat = center\.get\(\'lat\'\)\n\s+lng = center\.get\(\'lng\'\))',
                            r'\1\n\n        # If center is not available, calculate it from the viewport\n        if lat is None or lng is None:\n            viewport = boundaries.get("viewport", {})\n            northeast = viewport.get("northeast", {})\n            southwest = viewport.get("southwest", {})\n            \n            # Calculate center from viewport\n            if northeast and southwest:\n                lat = (northeast.get("lat", 0) + southwest.get("lat", 0)) / 2\n                lng = (northeast.get("lng", 0) + southwest.get("lng", 0)) / 2\n                logger.info(f"Calculated center coordinates from viewport: {lat}, {lng}")',
                            content
                        )
                
                # Fix real_batch_search_manager.py
                if 'real_batch_search_manager.py' in file_path:
                    # Fix _batch_search_thread to use the correct coordinates
                    if 'def _batch_search_thread' in content:
                        # Add validation for coordinates
                        content = re.sub(
                            r'(# Get coordinates for the address\n\s+lat, lng = self\.get_coordinates_for_address\(address\))',
                            r'\1\n\n            # Validate coordinates\n            if lat is None or lng is None:\n                logger.error(f"Failed to get coordinates for address: {address}")\n                self.app.root.after(0, lambda: self.app.show_status(f"Failed to get coordinates for {address}"))\n                self.search_in_progress = False\n                return\n            \n            logger.info(f"Got coordinates for {address}: {lat}, {lng}")',
                            content
                        )
                
                # Write the updated content back to the file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"Fixed batch search coordinate selection in: {file_path}")
                updated_count += 1
            except Exception as e:
                logger.error(f"Error fixing batch search coordinate selection in {file_path}: {e}")
    
    return updated_count

def fix_coordinate_generation():
    """Fix issues with coordinate generation for batch search."""
    files_to_update = [
        'utils/helpers/coordinate_helpers.py',
    ]
    
    updated_count = 0
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            try:
                # Read the file
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Add validation to generate_grid_points and generate_spiral_points
                if 'def generate_grid_points' in content:
                    # Add validation for center coordinates
                    content = re.sub(
                        r'(def generate_grid_points.*?\n.*?""".*?""")',
                        r'\1\n    # Validate input coordinates\n    if center_lat is None or center_lng is None or radius_km is None:\n        logger.error(f"Invalid input for generate_grid_points: {center_lat}, {center_lng}, {radius_km}")\n        return []\n    \n    # Ensure coordinates are valid numbers\n    try:\n        center_lat = float(center_lat)\n        center_lng = float(center_lng)\n        radius_km = float(radius_km)\n    except (ValueError, TypeError) as e:\n        logger.error(f"Error converting coordinates to float: {e}")\n        return []',
                        content
                    )
                
                if 'def generate_spiral_points' in content:
                    # Add validation for center coordinates
                    content = re.sub(
                        r'(def generate_spiral_points.*?\n.*?""".*?""")',
                        r'\1\n    # Validate input coordinates\n    if center_lat is None or center_lng is None or radius_km is None:\n        logger.error(f"Invalid input for generate_spiral_points: {center_lat}, {center_lng}, {radius_km}")\n        return []\n    \n    # Ensure coordinates are valid numbers\n    try:\n        center_lat = float(center_lat)\n        center_lng = float(center_lng)\n        radius_km = float(radius_km)\n    except (ValueError, TypeError) as e:\n        logger.error(f"Error converting coordinates to float: {e}")\n        return []',
                        content
                    )
                
                # Write the updated content back to the file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"Fixed coordinate generation in: {file_path}")
                updated_count += 1
            except Exception as e:
                logger.error(f"Error fixing coordinate generation in {file_path}: {e}")
    
    return updated_count

def clear_cache_directories():
    """Clear all cache directories."""
    cache_dirs = [
        'cache',
        '.cache',
        'czech_data/cache',
        'data/cache',
    ]
    
    cleared_count = 0
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir) and os.path.isdir(cache_dir):
            try:
                # Remove the directory and all its contents
                shutil.rmtree(cache_dir)
                logger.info(f"Cleared cache directory: {cache_dir}")
                cleared_count += 1
                
                # Recreate the empty directory
                os.makedirs(cache_dir, exist_ok=True)
            except Exception as e:
                logger.error(f"Error clearing cache directory {cache_dir}: {e}")
    
    return cleared_count

def clear_cache_files():
    """Clear all cache files."""
    cache_files = [
        'czech_data/cache.json',
        'czech_data/city_cache.json',
        'czech_data/address_cache.json',
        'czech_data/property_cache.json',
        'czech_data/suggestions_cache.json',
        'czech_data/encrypted_urls.json',
    ]
    
    cleared_count = 0
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            try:
                # Remove the file
                os.remove(cache_file)
                logger.info(f"Cleared cache file: {cache_file}")
                cleared_count += 1
            except Exception as e:
                logger.error(f"Error clearing cache file {cache_file}: {e}")
    
    return cleared_count

def restart_application():
    """Restart the main application."""
    try:
        # Get the path to the main.py file
        main_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
        
        # Check if the file exists
        if not os.path.exists(main_path):
            logger.error(f"Main file not found at: {main_path}")
            return False
        
        # Launch the application with the current Python interpreter
        cmd = [sys.executable, main_path]
        
        # Use Popen to launch the process without waiting for it to complete
        subprocess.Popen(cmd)
        
        return True
    except Exception as e:
        logger.error(f"Error restarting application: {e}")
        return False

def main():
    """Main function to fix batch search issues."""
    logger.info("Starting batch search fix...")
    
    # Add debug logging
    logging_count = add_debug_logging()
    logger.info(f"Added debug logging to {logging_count} files")
    
    # Fix batch search coordinate selection
    selection_count = fix_batch_search_coordinate_selection()
    logger.info(f"Fixed batch search coordinate selection in {selection_count} files")
    
    # Fix coordinate generation
    generation_count = fix_coordinate_generation()
    logger.info(f"Fixed coordinate generation in {generation_count} files")
    
    # Clear cache directories
    dir_count = clear_cache_directories()
    logger.info(f"Cleared {dir_count} cache directories")
    
    # Clear cache files
    file_count = clear_cache_files()
    logger.info(f"Cleared {file_count} cache files")
    
    # Restart the application
    if restart_application():
        logger.info("Application restarted successfully")
    else:
        logger.error("Failed to restart application")
        logger.info("Please restart the application manually by running 'python main.py'")
    
    logger.info("Batch search fix completed")

if __name__ == "__main__":
    main()
