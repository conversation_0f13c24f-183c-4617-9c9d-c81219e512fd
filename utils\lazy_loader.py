"""
Lazy loading utilities for the Czech Property Registry application.
Provides mechanisms for loading components and data only when needed.
"""

import threading
import time
import logging
import importlib
import inspect
import functools
import weakref
from typing import Dict, Any, Callable, Optional, Type, List, Set, Tuple, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("LazyLoader")


class LazyModule:
    """
    A proxy for lazily loading Python modules.
    The module is only imported when its attributes are accessed.
    """
    
    def __init__(self, module_name: str):
        """
        Initialize the lazy module.
        
        Args:
            module_name (str): Name of the module to load
        """
        self._module_name = module_name
        self._module = None
    
    def __getattr__(self, name: str) -> Any:
        """
        Get an attribute from the module, loading it if necessary.
        
        Args:
            name (str): Name of the attribute
            
        Returns:
            The requested attribute
            
        Raises:
            AttributeError: If the attribute doesn't exist
        """
        if self._module is None:
            logger.info(f"Lazily loading module: {self._module_name}")
            self._module = importlib.import_module(self._module_name)
        
        return getattr(self._module, name)


class LazyObject:
    """
    A proxy for lazily instantiating objects.
    The object is only created when its attributes are accessed.
    """
    
    def __init__(self, factory: Callable[[], Any]):
        """
        Initialize the lazy object.
        
        Args:
            factory: Function that creates the object
        """
        self._factory = factory
        self._object = None
    
    def __getattr__(self, name: str) -> Any:
        """
        Get an attribute from the object, creating it if necessary.
        
        Args:
            name (str): Name of the attribute
            
        Returns:
            The requested attribute
            
        Raises:
            AttributeError: If the attribute doesn't exist
        """
        if self._object is None:
            logger.info(f"Lazily creating object: {self._factory.__name__}")
            self._object = self._factory()
        
        return getattr(self._object, name)


class LazyProperty:
    """
    A descriptor for lazily computed properties.
    The property is only computed once when first accessed.
    """
    
    def __init__(self, func: Callable[[Any], Any]):
        """
        Initialize the lazy property.
        
        Args:
            func: Function that computes the property value
        """
        self._func = func
        self._name = func.__name__
        self.__doc__ = func.__doc__
    
    def __get__(self, instance: Any, owner: Type) -> Any:
        """
        Get the property value, computing it if necessary.
        
        Args:
            instance: Instance that owns the property
            owner: Class that owns the property
            
        Returns:
            The property value
        """
        if instance is None:
            return self
        
        value = self._func(instance)
        setattr(instance, self._name, value)
        return value


class LazyDataLoader:
    """
    A utility for lazily loading data.
    Data is only loaded when requested and can be cached.
    """
    
    def __init__(self):
        """Initialize the lazy data loader."""
        self._loaders: Dict[str, Callable[[], Any]] = {}
        self._data: Dict[str, Any] = {}
        self._loading: Set[str] = set()
        self._loading_lock = threading.Lock()
        self._data_lock = threading.Lock()
        self._callbacks: Dict[str, List[Callable[[Any], None]]] = {}
    
    def register_loader(self, key: str, loader: Callable[[], Any]) -> None:
        """
        Register a data loader function.
        
        Args:
            key (str): Key to identify the data
            loader: Function that loads the data
        """
        with self._data_lock:
            self._loaders[key] = loader
            self._callbacks[key] = []
    
    def get_data(self, key: str, force_reload: bool = False) -> Any:
        """
        Get data, loading it if necessary.
        
        Args:
            key (str): Key identifying the data
            force_reload (bool): Whether to force a reload even if cached
            
        Returns:
            The requested data
            
        Raises:
            KeyError: If no loader is registered for the key
        """
        # Check if we need to load the data
        need_to_load = False
        with self._data_lock:
            if key not in self._loaders:
                raise KeyError(f"No loader registered for key: {key}")
            
            if force_reload or key not in self._data:
                need_to_load = True
        
        # If already loading, wait for it to complete
        if need_to_load:
            with self._loading_lock:
                if key in self._loading:
                    # Wait for loading to complete
                    while key in self._loading:
                        time.sleep(0.01)
                else:
                    # Mark as loading
                    self._loading.add(key)
                    try:
                        # Load the data
                        logger.info(f"Lazily loading data: {key}")
                        data = self._loaders[key]()
                        
                        # Store the data
                        with self._data_lock:
                            self._data[key] = data
                        
                        # Notify callbacks
                        for callback in self._callbacks.get(key, []):
                            try:
                                callback(data)
                            except Exception as e:
                                logger.error(f"Error in callback for {key}: {e}")
                    finally:
                        # Mark as no longer loading
                        self._loading.remove(key)
        
        # Return the data
        with self._data_lock:
            return self._data[key]
    
    def register_callback(self, key: str, callback: Callable[[Any], None]) -> None:
        """
        Register a callback to be called when data is loaded.
        
        Args:
            key (str): Key identifying the data
            callback: Function to call with the loaded data
        """
        with self._data_lock:
            if key not in self._callbacks:
                self._callbacks[key] = []
            self._callbacks[key].append(callback)
            
            # If data is already loaded, call the callback immediately
            if key in self._data:
                try:
                    callback(self._data[key])
                except Exception as e:
                    logger.error(f"Error in callback for {key}: {e}")
    
    def clear_cache(self, key: Optional[str] = None) -> None:
        """
        Clear the cache for a specific key or all keys.
        
        Args:
            key (str, optional): Key to clear, or None to clear all
        """
        with self._data_lock:
            if key is None:
                self._data.clear()
                logger.info("Cleared all cached data")
            elif key in self._data:
                del self._data[key]
                logger.info(f"Cleared cached data: {key}")
    
    def is_loaded(self, key: str) -> bool:
        """
        Check if data is loaded.
        
        Args:
            key (str): Key identifying the data
            
        Returns:
            bool: True if the data is loaded
        """
        with self._data_lock:
            return key in self._data
    
    def get_loaded_keys(self) -> List[str]:
        """
        Get a list of loaded data keys.
        
        Returns:
            List of loaded data keys
        """
        with self._data_lock:
            return list(self._data.keys())


# Create a global instance for convenience
lazy_data_loader = LazyDataLoader()


def lazy_load(func: Callable) -> Callable:
    """
    Decorator for lazily loading data in a function.
    The function is only called once and its result is cached.
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    cache = {}
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        key = (args, frozenset(kwargs.items()))
        if key not in cache:
            logger.info(f"Lazily executing function: {func.__name__}")
            cache[key] = func(*args, **kwargs)
        return cache[key]
    
    return wrapper


def lazy_property(func: Callable) -> LazyProperty:
    """
    Decorator for creating lazy properties.
    
    Args:
        func: Function to decorate
        
    Returns:
        LazyProperty descriptor
    """
    return LazyProperty(func)
