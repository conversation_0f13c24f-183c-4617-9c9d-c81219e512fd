"""
Network utilities for the Czech Property Scraper application.
Handles session creation, connection pooling, and retry mechanisms.
"""

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


def create_session():
    """
    Create a requests session with optimized connection pooling and retry capability.
    
    Returns:
        requests.Session: A configured requests session
    """
    session = requests.Session()
    
    # Create a more robust retry strategy
    retry = Retry(
        total=5,                      # Maximum number of retries
        backoff_factor=0.5,           # Increased backoff factor for exponential delay
        status_forcelist=[429, 500, 502, 503, 504, 507, 524],  # Added more status codes
        allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"],  # Allow retries on all methods
        respect_retry_after_header=True,  # Respect Retry-After header
        raise_on_redirect=False,      # Don't raise on redirects
        raise_on_status=False         # Don't raise on status
    )
    
    # Create connection pool adapter with increased max connections
    adapter = HTTPAdapter(
        max_retries=retry,
        pool_connections=10,    # Number of connection pools (one per host)
        pool_maxsize=20,        # Max connections per pool
        pool_block=False        # Don't block when pool is full
    )
    
    # Mount the adapter for both HTTP and HTTPS
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    
    # Set a reasonable user agent to avoid being blocked
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0'
    })
    
    return session
