"""
UI-related helper functions for the Czech Property Registry application.

This module provides functions for UI operations, such as showing status messages,
managing UI state, and handling UI events.
"""

import tkinter as tk
import logging
import threading
import time
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Configure logging
logger = logging.getLogger(__name__)


def show_status(app, message: str) -> None:
    """
    Show a status message in the status bar.

    Args:
        app: The main application instance
        message (str): The message to display
    """
    if hasattr(app, 'status_bar'):
        app.status_bar.config(text=message)
        app.root.update_idletasks()
        logger.debug(f"Status: {message}")
    elif hasattr(app, 'show_status'):
        app.show_status(message)
        logger.debug(f"Status: {message}")
    else:
        logger.debug(f"Status (no UI): {message}")


def show_temporary_status(app, message: str, duration: int = 3000) -> None:
    """
    Show a temporary status message in the status bar.

    Args:
        app: The main application instance
        message (str): The message to display
        duration (int): Duration in milliseconds
    """
    if hasattr(app, 'status_bar') and hasattr(app, 'root'):
        show_status(app, message)
        app.root.after(duration, lambda: show_status(app, ""))
    elif hasattr(app, 'show_status') and hasattr(app, 'root'):
        app.show_status(message)
        app.root.after(duration, lambda: app.show_status(""))
    else:
        logger.debug(f"Temporary status (no UI): {message}")


def disable_ui_elements(elements: List[tk.Widget]) -> None:
    """
    Disable a list of UI elements.

    Args:
        elements (list): List of UI elements to disable
    """
    for element in elements:
        try:
            element.config(state=tk.DISABLED)
        except (tk.TclError, AttributeError) as e:
            logger.warning(f"Could not disable element {element}: {e}")


def enable_ui_elements(elements: List[tk.Widget]) -> None:
    """
    Enable a list of UI elements.

    Args:
        elements (list): List of UI elements to enable
    """
    for element in elements:
        try:
            element.config(state=tk.NORMAL)
        except (tk.TclError, AttributeError) as e:
            logger.warning(f"Could not enable element {element}: {e}")


def set_ui_busy(app, busy: bool = True) -> None:
    """
    Set the UI to busy or normal state.

    Args:
        app: The main application instance
        busy (bool): Whether to set the UI to busy state
    """
    if hasattr(app, 'root'):
        if busy:
            app.root.config(cursor="wait")
            app.root.update_idletasks()
        else:
            app.root.config(cursor="")
            app.root.update_idletasks()


def run_with_progress(app, task: Callable, title: str = "Processing",
                     message: str = "Please wait...",
                     callback: Optional[Callable] = None) -> None:
    """
    Run a task with a progress dialog.

    Args:
        app: The main application instance
        task (callable): Task to run
        title (str): Dialog title
        message (str): Message to display
        callback (callable, optional): Callback to run when the task is complete
    """
    # Create a progress dialog
    progress_dialog = tk.Toplevel(app.root)
    progress_dialog.title(title)
    progress_dialog.geometry("300x100")
    progress_dialog.transient(app.root)
    progress_dialog.grab_set()
    progress_dialog.focus_set()
    progress_dialog.resizable(False, False)

    # Create a label for the message
    message_label = tk.Label(progress_dialog, text=message, padx=10, pady=10)
    message_label.pack(fill="x")

    # Create a progress bar
    progress_bar = ttk.Progressbar(progress_dialog, mode="indeterminate", length=280)
    progress_bar.pack(padx=10, pady=10)
    progress_bar.start()

    # Create a cancel button
    cancel_button = tk.Button(progress_dialog, text="Cancel", command=progress_dialog.destroy)
    cancel_button.pack(pady=5)

    # Function to run the task in a separate thread
    def run_task():
        try:
            result = task()
            app.root.after(0, lambda: on_task_complete(result))
        except Exception as e:
            logger.error(f"Error in task: {e}")
            app.root.after(0, lambda: on_task_error(e))

    # Function to handle task completion
    def on_task_complete(result):
        progress_dialog.destroy()
        if callback:
            callback(result)

    # Function to handle task error
    def on_task_error(error):
        progress_dialog.destroy()
        from ui.message_boxes import MessageBoxes
        MessageBoxes.show_error("Error", f"An error occurred: {str(error)}")

    # Start the task in a separate thread
    threading.Thread(target=run_task, daemon=True).start()


def center_window(window: tk.Toplevel, width: int = 300, height: int = 200) -> None:
    """
    Center a window on the screen.

    Args:
        window (tk.Toplevel): The window to center
        width (int): Window width
        height (int): Window height
    """
    # Get the screen dimensions
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()

    # Calculate the position
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2

    # Set the window geometry
    window.geometry(f"{width}x{height}+{x}+{y}")


def create_tooltip(widget: tk.Widget, text: str) -> None:
    """
    Create a tooltip for a widget.

    Args:
        widget (tk.Widget): The widget to add a tooltip to
        text (str): The tooltip text
    """
    tooltip = None

    def enter(event):
        nonlocal tooltip
        x, y, _, _ = widget.bbox("insert")
        x += widget.winfo_rootx() + 25
        y += widget.winfo_rooty() + 25

        # Create a toplevel window
        tooltip = tk.Toplevel(widget)
        tooltip.wm_overrideredirect(True)
        tooltip.wm_geometry(f"+{x}+{y}")

        # Create a label
        label = tk.Label(tooltip, text=text, justify="left",
                        background="#ffffe0", relief="solid", borderwidth=1,
                        font=("Arial", "9", "normal"))
        label.pack(ipadx=1)

    def leave(event):
        nonlocal tooltip
        if tooltip:
            tooltip.destroy()
            tooltip = None

    # Bind events
    widget.bind("<Enter>", enter)
    widget.bind("<Leave>", leave)


def create_scrollable_frame(parent: tk.Widget) -> Tuple[tk.Frame, tk.Canvas]:
    """
    Create a scrollable frame.

    Args:
        parent (tk.Widget): The parent widget

    Returns:
        tuple: (frame, canvas) where frame is the scrollable frame and canvas is the canvas
    """
    # Create a canvas
    canvas = tk.Canvas(parent)
    scrollbar = tk.Scrollbar(parent, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas)

    # Configure the canvas
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    # Create a window in the canvas
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    # Pack the canvas and scrollbar
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    return scrollable_frame, canvas


def validate_numeric_input(value: str) -> bool:
    """
    Validate that input is numeric.

    Args:
        value (str): The value to validate

    Returns:
        bool: True if the value is numeric, False otherwise
    """
    if value == "":
        return True
    try:
        float(value)
        return True
    except ValueError:
        return False


def validate_integer_input(value: str) -> bool:
    """
    Validate that input is an integer.

    Args:
        value (str): The value to validate

    Returns:
        bool: True if the value is an integer, False otherwise
    """
    if value == "":
        return True
    try:
        int(value)
        return True
    except ValueError:
        return False


__all__ = [
    'show_status',
    'show_temporary_status',
    'disable_ui_elements',
    'enable_ui_elements',
    'set_ui_busy',
    'run_with_progress',
    'center_window',
    'create_tooltip',
    'create_scrollable_frame',
    'validate_numeric_input',
    'validate_integer_input'
]
