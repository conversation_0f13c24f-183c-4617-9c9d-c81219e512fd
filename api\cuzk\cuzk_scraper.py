"""
CUZK (Czech Cadastral Office) scraping functionality for the Czech Property Registry application.
"""

import requests
from bs4 import BeautifulSoup
import webbrowser
import time
import re
import os
import json
from urllib.parse import urlencode
import xml.etree.ElementTree as ET
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Optional, Union, Dict, Any, List
from models.ruian_id import RuianID

class CUZKScraper:
    """Class for scraping property data from the CUZK website"""

    def __init__(self):
        """Initialize the CUZK scraper"""
        self.session = self.create_session()
        self.is_logged_in = False

    def create_session(self):
        """Create a session with browser spoofing and retry capability"""
        try:
            # Import the BrowserSpoofer
            from utils.browser_spoofer import BrowserSpoofer

            # Create a session with browser spoofing
            session = BrowserSpoofer.create_spoofed_session(
                retry_total=3,
                backoff_factor=0.5,
                status_forcelist=[429, 500, 502, 503, 504],
                rotate_user_agent=True
            )

            print("Created session with browser spoofing")
            return session

        except ImportError:
            # Fall back to the original implementation if BrowserSpoofer is not available
            print("BrowserSpoofer not available, using standard session")
            session = requests.Session()

            # Configure retry strategy
            retry_strategy = Retry(
                total=3,
                backoff_factor=0.5,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "POST"]
            )

            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)

            return session

    def login(self, username=None, password=None):
        """
        Log in to the CUZK website

        Args:
            username (str, optional): Username for CUZK login
            password (str, optional): Password for CUZK login

        Returns:
            bool: True if login was successful, False otherwise
        """
        # Check if we're already logged in
        if self.is_logged_in:
            return True

        # If no credentials provided, try to load from config
        if not username or not password:
            username, password = self.load_credentials()

        # If still no credentials, return False
        if not username or not password:
            print("No credentials available for CUZK login")
            return False

        try:
            # Load the login page
            login_url = "https://nahlizenidokn.cuzk.cz/Prihlaseni.aspx"
            response = self.session.get(login_url)

            if response.status_code != 200:
                print(f"Failed to load login page: {response.status_code}")
                return False

            # Parse the login page to get form fields
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract form fields
            viewstate = soup.find('input', {'name': '__VIEWSTATE'})['value']
            viewstategenerator = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})['value']
            eventvalidation = soup.find('input', {'name': '__EVENTVALIDATION'})['value']

            # Prepare login data
            login_data = {
                '__VIEWSTATE': viewstate,
                '__VIEWSTATEGENERATOR': viewstategenerator,
                '__EVENTVALIDATION': eventvalidation,
                'ctl00$bodyPlaceHolder$txtJmeno': username,
                'ctl00$bodyPlaceHolder$txtHeslo': password,
                'ctl00$bodyPlaceHolder$btnPrihlasit': 'Přihlásit'
            }

            # Submit the login form
            login_response = self.session.post(login_url, data=login_data)

            # Check if login was successful
            if login_response.status_code == 200 and 'Odhlásit' in login_response.text:
                self.is_logged_in = True
                print("Successfully logged in to CUZK")
                return True
            else:
                print("Failed to log in to CUZK")
                return False

        except Exception as e:
            print(f"Error logging in to CUZK: {e}")
            return False

    def load_credentials(self):
        """
        Load CUZK credentials from config file

        Returns:
            tuple: (username, password) or (None, None) if not found
        """
        try:
            if os.path.exists('config.ini'):
                import configparser
                config = configparser.ConfigParser()
                config.read('config.ini')

                if 'CUZK' in config:
                    username = config['CUZK'].get('username')
                    password = config['CUZK'].get('password')
                    return username, password

        except Exception as e:
            print(f"Error loading CUZK credentials: {e}")

        return None, None

    def search_by_coordinates(self, lat, lng):
        """
        Search for property by coordinates

        Args:
            lat (float): Latitude
            lng (float): Longitude

        Returns:
            dict: Property data or None if not found
        """
        try:
            # Convert coordinates to JTSK if needed
            x, y = self.convert_coordinates_to_jtsk(lat, lng)

            # Load the search page
            search_url = "https://nahlizenidokn.cuzk.cz/VyberSouradnice.aspx"
            response = self.session.get(search_url)

            if response.status_code != 200:
                print(f"Failed to load search page: {response.status_code}")
                return None

            # Parse the search page to get form fields
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract form fields
            viewstate_elem = soup.find('input', {'name': '__VIEWSTATE'})
            viewstategenerator_elem = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
            eventvalidation_elem = soup.find('input', {'name': '__EVENTVALIDATION'})

            if not viewstate_elem or not viewstategenerator_elem or not eventvalidation_elem:
                print("Failed to extract form fields from search page")
                return None

            viewstate = viewstate_elem['value']
            viewstategenerator = viewstategenerator_elem['value']
            eventvalidation = eventvalidation_elem['value']

            # Prepare search data for coordinates
            search_data = {
                '__VIEWSTATE': viewstate,
                '__VIEWSTATEGENERATOR': viewstategenerator,
                '__EVENTVALIDATION': eventvalidation,
                'ctl00$bodyPlaceHolder$txtX': x,
                'ctl00$bodyPlaceHolder$txtY': y,
                'ctl00$bodyPlaceHolder$btnVyhledat': 'Vyhledat'
            }

            # Submit the search form
            search_response = self.session.post(search_url, data=search_data)

            # Check if search was successful and extract property details
            if search_response.status_code == 200:
                return self.extract_property_details(search_response.text)
            else:
                print(f"Search request failed: {search_response.status_code}")
                return None

        except Exception as e:
            print(f"Error searching by coordinates: {e}")
            return None

    def search_by_parcel_number(self, parcel_number, cadastral_territory=None):
        """
        Search for property by parcel number

        Args:
            parcel_number (str): Parcel number
            cadastral_territory (str, optional): Cadastral territory

        Returns:
            dict: Property data or None if not found
        """
        try:
            # Load the search page
            search_url = "https://nahlizenidokn.cuzk.cz/VyberParcelu.aspx"
            response = self.session.get(search_url)

            if response.status_code != 200:
                print(f"Failed to load search page: {response.status_code}")
                return None

            # Parse the search page to get form fields
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract form fields
            viewstate_elem = soup.find('input', {'name': '__VIEWSTATE'})
            viewstategenerator_elem = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
            eventvalidation_elem = soup.find('input', {'name': '__EVENTVALIDATION'})

            if not viewstate_elem or not viewstategenerator_elem or not eventvalidation_elem:
                print("Failed to extract form fields from search page")
                return None

            viewstate = viewstate_elem['value']
            viewstategenerator = viewstategenerator_elem['value']
            eventvalidation = eventvalidation_elem['value']

            # Prepare search data for parcel number
            search_data = {
                '__VIEWSTATE': viewstate,
                '__VIEWSTATEGENERATOR': viewstategenerator,
                '__EVENTVALIDATION': eventvalidation,
                'ctl00$bodyPlaceHolder$txtParcela': parcel_number,
                'ctl00$bodyPlaceHolder$btnVyhledat': 'Vyhledat'
            }

            # Add cadastral territory if provided
            if cadastral_territory:
                search_data['ctl00$bodyPlaceHolder$txtKU'] = cadastral_territory

            # Submit the search form
            search_response = self.session.post(search_url, data=search_data)

            # Check if search was successful and extract property details
            if search_response.status_code == 200:
                return self.extract_property_details(search_response.text)
            else:
                print(f"Search request failed: {search_response.status_code}")
                return None

        except Exception as e:
            print(f"Error searching by parcel number: {e}")
            return None

    def extract_property_details(self, html_content):
        """
        Extract property owner details from the HTML response

        Args:
            html_content (str): HTML content from the CUZK website

        Returns:
            dict: Property details including owner name, address, and other information
                  or None if the information couldn't be extracted
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Initialize property data dictionary
            property_data = {
                'source': 'cuzk'
            }

            # Check if we're on a property details page
            if 'Informace o pozemku' in html_content or 'Informace o stavbě' in html_content:
                # Extract property type
                property_type_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblTypParcely'})
                if property_type_elem:
                    property_data['property_type'] = property_type_elem.text.strip()

                # Extract parcel number
                parcel_number_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblParcelniCislo'})
                if parcel_number_elem:
                    property_data['parcel_number'] = parcel_number_elem.text.strip()

                # Extract cadastral territory
                cadastral_territory_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblNazevKU'})
                if cadastral_territory_elem:
                    property_data['cadastral_territory'] = cadastral_territory_elem.text.strip()

                # Extract owner information
                owner_table = soup.find('table', {'id': 'ctl00_bodyPlaceHolder_tblVlastnik'})
                if owner_table:
                    # Extract owner name
                    owner_name_elem = owner_table.find('span', {'id': 'ctl00_bodyPlaceHolder_lblVlastnik'})
                    if owner_name_elem:
                        property_data['owner_name'] = owner_name_elem.text.strip()

                    # Extract owner address
                    owner_address_elem = owner_table.find('span', {'id': 'ctl00_bodyPlaceHolder_lblAdresaVlastnika'})
                    if owner_address_elem:
                        property_data['owner_address'] = owner_address_elem.text.strip()

                # Extract building information if available
                building_table = soup.find('table', {'id': 'ctl00_bodyPlaceHolder_tblStavba'})
                if building_table:
                    # Extract building type
                    building_type_elem = building_table.find('span', {'id': 'ctl00_bodyPlaceHolder_lblTypStavby'})
                    if building_type_elem:
                        property_data['building_type'] = building_type_elem.text.strip()

                    # Extract building number
                    building_number_elem = building_table.find('span', {'id': 'ctl00_bodyPlaceHolder_lblCisloStavby'})
                    if building_number_elem:
                        property_data['building_number'] = building_number_elem.text.strip()

                # Extract land use information
                land_use_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblZpusobVyuziti'})
                if land_use_elem:
                    property_data['land_use'] = land_use_elem.text.strip()

                # Extract area information
                area_elem = soup.find('span', {'id': 'ctl00_bodyPlaceHolder_lblVymera'})
                if area_elem:
                    property_data['area'] = area_elem.text.strip()

                return property_data
            else:
                # Check if we're on a search results page with multiple properties
                property_links = []

                # Look for links to property details
                for a in soup.find_all('a', href=True):
                    href = a['href']
                    if 'VyberParcelu.aspx' in href or 'VyberStavbu.aspx' in href or 'VyberJednotku.aspx' in href:
                        property_links.append(href)

                if property_links:
                    return {'property_links': property_links}

            # If we couldn't extract any useful information
            return None

        except Exception as e:
            print(f"Error extracting property details: {e}")
            return None

    def convert_coordinates_to_jtsk(self, lat, lng):
        """
        Convert WGS84 coordinates to JTSK (S-JTSK) coordinate system

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84

        Returns:
            tuple: (x, y) coordinates in JTSK
        """
        # This is a simplified conversion that may not be accurate
        # For a proper conversion, use a specialized library or service

        # For now, we'll just pass through the coordinates
        # CUZK expects JTSK coordinates, but we'll let the website handle the conversion
        return str(lng), str(lat)

    def reverse_geocode(self, lat, lng):
        """
        Reverse geocode coordinates to get location information

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84

        Returns:
            dict: Location information including city, region, etc.
        """
        try:
            # Try to use Google Maps API if available
            if hasattr(self, 'app') and hasattr(self.app, 'google_maps'):
                return self.app.google_maps.reverse_geocode(lat, lng)

            # If Google Maps API is not available, try to use a simplified approach
            # This is just a placeholder - in a real implementation, we would use
            # a proper geocoding service or database

            # For Prague (approximate coordinates)
            if 49.9 < lat < 50.2 and 14.2 < lng < 14.7:
                return {
                    'city': 'Praha',
                    'region': 'Hlavní město Praha',
                    'country': 'Česká republika'
                }

            # For Brno (approximate coordinates)
            if 49.1 < lat < 49.3 and 16.5 < lng < 16.7:
                return {
                    'city': 'Brno',
                    'region': 'Jihomoravský kraj',
                    'country': 'Česká republika'
                }

            # For Ostrava (approximate coordinates)
            if 49.8 < lat < 49.9 and 18.2 < lng < 18.3:
                return {
                    'city': 'Ostrava',
                    'region': 'Moravskoslezský kraj',
                    'country': 'Česká republika'
                }

            # For Plzeň (approximate coordinates)
            if 49.7 < lat < 49.8 and 13.3 < lng < 13.4:
                return {
                    'city': 'Plzeň',
                    'region': 'Plzeňský kraj',
                    'country': 'Česká republika'
                }

            # Default to unknown
            return None

        except Exception as e:
            print(f"Error reverse geocoding coordinates: {e}")
            return None

    def get_property_info_from_coordinates(self, lat, lng):
        """
        Get property information from coordinates using the CUZK API

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84

        Returns:
            dict: Property information or None if not found
        """
        try:
            # Convert coordinates to JTSK if needed
            x, y = self.convert_coordinates_to_jtsk(lat, lng)

            # Construct the API URL
            api_url = f"https://services.cuzk.cz/wms/wms.asp?REQUEST=GetFeatureInfo&SERVICE=WMS&VERSION=1.3.0&LAYERS=KN&STYLES=&FORMAT=image/png&TRANSPARENT=true&CRS=EPSG:4326&INFO_FORMAT=text/html&I=50&J=50&WIDTH=101&HEIGHT=101&BBOX={float(lng)-0.001},{float(lat)-0.001},{float(lng)+0.001},{float(lat)+0.001}"

            # Make the API request
            response = self.session.get(api_url)

            if response.status_code != 200:
                print(f"API request failed: {response.status_code}")
                return None

            # Parse the response
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract property information
            property_info = {}

            # Extract parcel ID
            parcel_id_elem = soup.find('th', text='Parcelní číslo:')
            if parcel_id_elem and parcel_id_elem.find_next('td'):
                property_info['parcel_id'] = parcel_id_elem.find_next('td').text.strip()

            # Extract cadastral territory
            cadastral_territory_elem = soup.find('th', text='Katastrální území:')
            if cadastral_territory_elem and cadastral_territory_elem.find_next('td'):
                property_info['cadastral_territory'] = cadastral_territory_elem.find_next('td').text.strip()

            # Extract building ID
            building_id_elem = soup.find('th', text='Číslo budovy:')
            if building_id_elem and building_id_elem.find_next('td'):
                property_info['building_id'] = building_id_elem.find_next('td').text.strip()

            return property_info

        except Exception as e:
            print(f"Error getting property info from coordinates: {e}")
            return None

    def generate_cuzk_direct_url(self, parcel_id, cadastral_territory, building_id=None):
        """
        Generate a direct URL to the CUZK property information page

        Args:
            parcel_id (str): Parcel ID (e.g., "1234/5")
            cadastral_territory (str): Cadastral territory name or code
            building_id (str, optional): Building ID

        Returns:
            str: Direct URL or None if it couldn't be generated
        """
        try:
            # Base URL for the CUZK property information page
            base_url = "https://nahlizenidokn.cuzk.cz/ZobrazObjekt.aspx"

            # Prepare query parameters
            params = {}

            if parcel_id:
                params['typ'] = 'Parcela'
                params['id'] = parcel_id

            if cadastral_territory:
                params['ku'] = cadastral_territory

            if building_id:
                params['stavba'] = building_id

            # Generate the URL
            url = f"{base_url}?{urlencode(params)}"

            return url

        except Exception as e:
            print(f"Error generating CUZK direct URL: {e}")
            return None

    def open_cuzk_in_browser(self, lat=None, lng=None, parcel_id=None, cadastral_territory=None, building_id=None):
        """
        Open the CUZK website in the default browser

        Args:
            lat (float, optional): Latitude in WGS84
            lng (float, optional): Longitude in WGS84
            parcel_id (str, optional): Parcel ID
            cadastral_territory (str, optional): Cadastral territory
            building_id (str, optional): Building ID

        Returns:
            bool: True if the website was opened, False otherwise
        """
        try:
            # If we have parcel ID and cadastral territory, try to generate a direct URL
            if parcel_id and cadastral_territory:
                direct_url = self.generate_cuzk_direct_url(parcel_id, cadastral_territory, building_id)
                if direct_url:
                    webbrowser.open(direct_url)
                    return True

            # If we have coordinates, try to open the coordinate search page
            if lat and lng:
                # Convert coordinates to JTSK if needed
                x, y = self.convert_coordinates_to_jtsk(lat, lng)

                # Construct the URL for the coordinate search page
                url = f"https://nahlizenidokn.cuzk.cz/VyberSouradnice.aspx?x={x}&y={y}"

                webbrowser.open(url)
                return True

            # If we don't have enough information, open the main page
            webbrowser.open("https://nahlizenidokn.cuzk.cz/")
            return True

        except Exception as e:
            print(f"Error opening CUZK in browser: {e}")
            return False

    def open_cuzk_with_ruian_id(self, ruian_id: Union[str, int, RuianID]) -> bool:
        """
        Open the CUZK website with a RUIAN ID

        Args:
            ruian_id (str, int, or RuianID): RUIAN ID

        Returns:
            bool: True if the website was opened, False otherwise
        """
        try:
            # If ruian_id is already a RuianID object, use it directly
            if isinstance(ruian_id, RuianID):
                if not ruian_id.is_valid:
                    print(f"Invalid RUIAN ID: {ruian_id.original_value}")
                    return False
                clean_ruian_id = ruian_id.value
            else:
                # Try to create a RuianID object
                try:
                    ruian_id_obj = RuianID(ruian_id)
                    clean_ruian_id = ruian_id_obj.value
                except ValueError as e:
                    print(f"Invalid RUIAN ID: {ruian_id} - {str(e)}")
                    return False

            # Construct the URL for the RUIAN search page
            url = f"https://nahlizenidokn.cuzk.cz/VyberBudovu.aspx?typ=Stavba&id={clean_ruian_id}"

            # Open the URL in the default browser
            webbrowser.open(url)

            return True

        except Exception as e:
            print(f"Error opening CUZK with RUIAN ID: {e}")
            return False

    def direct_cuzk_scrape_by_coordinates(self, x, y):
        """
        Directly scrape property data from the CUZK website using coordinates

        Args:
            x (float): X coordinate (latitude)
            y (float): Y coordinate (longitude)

        Returns:
            dict: Property data or None if not found
        """
        try:
            # Open the CUZK website with the coordinates
            self.open_cuzk_in_browser(lat=x, lng=y)

            # Ask the user to complete the CAPTCHA and extract the property data
            property_data = self.get_property_details_from_user()

            return property_data

        except Exception as e:
            print(f"Error scraping CUZK by coordinates: {e}")
            return None

    def direct_cuzk_scrape_by_parcel(self, parcel_number):
        """
        Directly scrape property data from the CUZK website using parcel number

        Args:
            parcel_number (str): Parcel number

        Returns:
            dict: Property data or None if not found
        """
        try:
            # Construct the URL for the parcel search page
            url = f"https://nahlizenidokn.cuzk.cz/VyberParcelu.aspx?parcelni_cislo={parcel_number}"

            # Open the URL in the default browser
            webbrowser.open(url)

            # Ask the user to complete the CAPTCHA and extract the property data
            property_data = self.get_property_details_from_user()

            return property_data

        except Exception as e:
            print(f"Error scraping CUZK by parcel number: {e}")
            return None

    def get_property_details_from_user(self):
        """
        Get property details from the user after they have completed the CAPTCHA

        Returns:
            dict: Property data or None if the user cancels
        """
        try:
            # Create a dialog to get property details from the user
            import tkinter as tk
            from tkinter import simpledialog, messagebox

            # Create a simple dialog to get property details
            property_data = [None]  # Use a list to store the result

            # Create a dialog
            dialog = tk.Toplevel()
            dialog.title("Enter Property Details")
            dialog.geometry("400x400")
            dialog.transient()
            dialog.grab_set()

            # Create a frame for the form
            form_frame = tk.Frame(dialog)
            form_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Create labels and entry fields
            tk.Label(form_frame, text="Owner Name:").grid(row=0, column=0, sticky="w", pady=5)
            owner_name = tk.Entry(form_frame, width=40)
            owner_name.grid(row=0, column=1, sticky="ew", pady=5)

            tk.Label(form_frame, text="Owner Address:").grid(row=1, column=0, sticky="w", pady=5)
            owner_address = tk.Entry(form_frame, width=40)
            owner_address.grid(row=1, column=1, sticky="ew", pady=5)

            tk.Label(form_frame, text="Property Type:").grid(row=2, column=0, sticky="w", pady=5)
            property_type = tk.Entry(form_frame, width=40)
            property_type.grid(row=2, column=1, sticky="ew", pady=5)

            tk.Label(form_frame, text="Parcel Number:").grid(row=3, column=0, sticky="w", pady=5)
            parcel_number = tk.Entry(form_frame, width=40)
            parcel_number.grid(row=3, column=1, sticky="ew", pady=5)

            tk.Label(form_frame, text="Cadastral Territory:").grid(row=4, column=0, sticky="w", pady=5)
            cadastral_territory = tk.Entry(form_frame, width=40)
            cadastral_territory.grid(row=4, column=1, sticky="ew", pady=5)

            # Create a frame for the buttons
            button_frame = tk.Frame(dialog)
            button_frame.pack(fill="x", padx=10, pady=10)

            # Define button actions
            def on_cancel():
                property_data[0] = None
                dialog.destroy()

            def on_ok():
                # Create a property data dictionary
                property_data[0] = {
                    'owner_name': owner_name.get(),
                    'owner_address': owner_address.get(),
                    'property_type': property_type.get(),
                    'parcel_number': parcel_number.get(),
                    'cadastral_territory': cadastral_territory.get(),
                    'source': 'cuzk_manual'
                }
                dialog.destroy()

            tk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="right", padx=5)
            tk.Button(button_frame, text="OK", command=on_ok).pack(side="right", padx=5)

            # Add mousewheel scrolling support
            def on_mousewheel(event):
                canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

            canvas = tk.Canvas(form_frame)
            canvas.bind_all("<MouseWheel>", on_mousewheel)

            # Wait for the dialog to be closed
            dialog.wait_window()

            return property_data[0]

        except Exception as e:
            print(f"Error getting property details from user: {e}")
            return None

    def fetch_owner_info_from_cuzk(self, ruian_id: Union[str, int, RuianID]) -> Optional[Dict[str, Any]]:
        """
        Fetch owner information from the CUZK website using a RUIAN ID

        Args:
            ruian_id (str, int, or RuianID): RUIAN ID

        Returns:
            dict: Owner information or None if not found
        """
        try:
            # If ruian_id is already a RuianID object, use it directly
            if isinstance(ruian_id, RuianID):
                if not ruian_id.is_valid:
                    print(f"Invalid RUIAN ID: {ruian_id.original_value}")
                    return None
                clean_ruian_id = ruian_id.value
            else:
                # Try to create a RuianID object
                try:
                    ruian_id_obj = RuianID(ruian_id)
                    clean_ruian_id = ruian_id_obj.value
                except ValueError as e:
                    print(f"Invalid RUIAN ID: {ruian_id} - {str(e)}")
                    return None

            # Construct the URL for the RUIAN search page
            url = f"https://nahlizenidokn.cuzk.cz/VyberBudovu.aspx?typ=Stavba&id={clean_ruian_id}"

            # Make the request
            response = self.session.get(url)

            if response.status_code != 200:
                print(f"Failed to load RUIAN search page: {response.status_code}")
                return None

            # Extract owner information from the response
            owner_info = self.extract_property_details(response.text)

            # Add the RUIAN ID to the owner info
            if owner_info:
                owner_info['ruian_id'] = clean_ruian_id

            return owner_info

        except Exception as e:
            print(f"Error fetching owner info from CUZK: {e}")
            return None

    def search_by_ruian(self, ruian_id: Union[str, int, RuianID]) -> Optional[Dict[str, Any]]:
        """
        Search for property by RUIAN ID

        Args:
            ruian_id (str, int, or RuianID): RUIAN ID

        Returns:
            dict: Property data or None if not found
        """
        try:
            # If ruian_id is already a RuianID object, use it directly
            if isinstance(ruian_id, RuianID):
                if not ruian_id.is_valid:
                    print(f"Invalid RUIAN ID: {ruian_id.original_value}")
                    return None
                clean_ruian_id = ruian_id.value
            else:
                # Try to create a RuianID object
                try:
                    ruian_id_obj = RuianID(ruian_id)
                    clean_ruian_id = ruian_id_obj.value
                except ValueError as e:
                    print(f"Invalid RUIAN ID: {ruian_id} - {str(e)}")
                    return None

            # Construct the URL for the RUIAN search page
            url = f"https://nahlizenidokn.cuzk.cz/VyberBudovu.aspx?typ=Stavba&id={clean_ruian_id}"

            # Make the request
            response = self.session.get(url)

            if response.status_code != 200:
                print(f"Failed to load RUIAN search page: {response.status_code}")
                return None

            # Extract property details from the response
            property_data = self.extract_property_details(response.text)

            # Add the RUIAN ID to the property data
            if property_data:
                property_data['ruian_id'] = clean_ruian_id

            return property_data

        except Exception as e:
            print(f"Error searching by RUIAN ID: {e}")
            return None

    def search_by_address(self, address):
        """
        Search for properties at a specific address

        Args:
            address (str): Address to search for

        Returns:
            list: List of property data dictionaries
        """
        try:
            # Construct the URL for the address search page
            url = "https://nahlizenidokn.cuzk.cz/VyberAdresu.aspx"

            # Make the request
            response = self.session.get(url)

            if response.status_code != 200:
                print(f"Failed to load address search page: {response.status_code}")
                return None

            # Parse the search page to get form fields
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract form fields
            viewstate_elem = soup.find('input', {'name': '__VIEWSTATE'})
            viewstategenerator_elem = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
            eventvalidation_elem = soup.find('input', {'name': '__EVENTVALIDATION'})

            if not viewstate_elem or not viewstategenerator_elem or not eventvalidation_elem:
                print("Failed to extract form fields from search page")
                return None

            viewstate = viewstate_elem['value']
            viewstategenerator = viewstategenerator_elem['value']
            eventvalidation = eventvalidation_elem['value']

            # Prepare search data for address
            search_data = {
                '__VIEWSTATE': viewstate,
                '__VIEWSTATEGENERATOR': viewstategenerator,
                '__EVENTVALIDATION': eventvalidation,
                'ctl00$bodyPlaceHolder$txtAdresa': address,
                'ctl00$bodyPlaceHolder$btnVyhledat': 'Vyhledat'
            }

            # Submit the search form
            search_response = self.session.post(url, data=search_data)

            # Check if search was successful
            if search_response.status_code != 200:
                print(f"Search request failed: {search_response.status_code}")
                return None

            # Parse the response to find property links
            soup = BeautifulSoup(search_response.text, 'html.parser')

            # Look for links to property details
            property_links = []
            for a in soup.find_all('a', href=True):
                href = a['href']
                if 'ZobrazObjekt.aspx' in href:
                    property_links.append(href)

            # Process each property link
            properties = []
            for link in property_links:
                # Construct the full URL
                if not link.startswith('http'):
                    link = f"https://nahlizenidokn.cuzk.cz/{link.lstrip('/')}"

                # Fetch the property details
                property_response = self.session.get(link)

                if property_response.status_code == 200:
                    # Extract property details
                    property_data = self.extract_property_details(property_response.text)

                    if property_data and 'owner_name' in property_data:
                        # Add the property to the list
                        properties.append(property_data)

                # Respect rate limiting
                time.sleep(1)

            return properties

        except Exception as e:
            print(f"Error fetching properties by address: {e}")
            return None

    def fetch_properties_in_area(self, lat, lng, radius=2, max_results=10):
        """
        Fetch properties in a circular area around the given coordinates

        Args:
            lat (float): Latitude in WGS84
            lng (float): Longitude in WGS84
            radius (int): Radius in kilometers
            max_results (int): Maximum number of results to return

        Returns:
            list: List of property data dictionaries
        """
        try:
            # First try to use the WMS API to get property information
            # This is less likely to be blocked by CAPTCHA
            property_info = self.get_property_info_from_coordinates(lat, lng)
            properties = []

            if property_info:
                # If we found property information, try to get more details
                # Extract cadastral territory
                cadastral_territory = property_info.get('cadastral_territory')
                parcel_id = property_info.get('parcel_id')
                building_id = property_info.get('building_id')

                # If we have a building ID, try to get its RUIAN ID
                if building_id:
                    # Try to search for the building by its ID
                    building_data = self.search_by_ruian(building_id)
                    if building_data:
                        # Add coordinates to the building data
                        building_data['lat'] = lat
                        building_data['lng'] = lng
                        building_data['source'] = 'cuzk_direct'
                        properties.append(building_data)

                # If we have a parcel ID and cadastral territory, try to get more details
                if parcel_id and cadastral_territory and len(properties) < max_results:
                    # Try to search for the parcel
                    parcel_data = self.search_property_by_parcel(parcel_id, cadastral_territory)
                    if parcel_data:
                        # Add coordinates to the parcel data
                        parcel_data['lat'] = lat
                        parcel_data['lng'] = lng
                        parcel_data['source'] = 'cuzk_direct'
                        properties.append(parcel_data)

            # If we already have enough properties, return them
            if len(properties) >= max_results:
                return properties[:max_results]

            # If we have a cadastral territory, try to search for more properties in it
            if property_info and property_info.get('cadastral_territory'):
                cadastral_territory = property_info.get('cadastral_territory')
            else:
                # Try to get the cadastral territory from reverse geocoding
                # This is a simplified approach - in a real implementation, we would
                # use a more sophisticated method to find the cadastral territory
                # For now, we'll just use the city name as an approximation
                location_info = self.reverse_geocode(lat, lng)
                if location_info:
                    cadastral_territory = location_info.get('city', '')
                else:
                    # If we can't determine the cadastral territory, we can't proceed
                    print("Could not determine cadastral territory")
                    return properties if properties else None

            # Search for properties in the cadastral territory
            # This is a simplified approach - in reality, we would need to use
            # a more sophisticated method to find properties within the radius
            # For now, we'll just search for properties in the cadastral territory
            # and assume they are within the radius

            # Load the search page
            search_url = "https://nahlizenidokn.cuzk.cz/VyberKatastrInfo.aspx"
            response = self.session.get(search_url)

            if response.status_code != 200:
                print(f"Failed to load search page: {response.status_code}")
                return None

            # Parse the search page to get form fields
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract form fields
            viewstate_elem = soup.find('input', {'name': '__VIEWSTATE'})
            viewstategenerator_elem = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
            eventvalidation_elem = soup.find('input', {'name': '__EVENTVALIDATION'})

            if not viewstate_elem or not viewstategenerator_elem or not eventvalidation_elem:
                print("Failed to extract form fields from search page")
                return None

            viewstate = viewstate_elem['value']
            viewstategenerator = viewstategenerator_elem['value']
            eventvalidation = eventvalidation_elem['value']

            # Prepare search data for cadastral territory
            search_data = {
                '__VIEWSTATE': viewstate,
                '__VIEWSTATEGENERATOR': viewstategenerator,
                '__EVENTVALIDATION': eventvalidation,
                'ctl00$bodyPlaceHolder$txtKU': cadastral_territory,
                'ctl00$bodyPlaceHolder$btnVyhledat': 'Vyhledat'
            }

            # Submit the search form
            search_response = self.session.post(search_url, data=search_data)

            # Check if search was successful
            if search_response.status_code != 200:
                print(f"Search request failed: {search_response.status_code}")
                return None

            # Parse the response to find property links
            soup = BeautifulSoup(search_response.text, 'html.parser')

            # Look for links to property details
            property_links = []
            for a in soup.find_all('a', href=True):
                href = a['href']
                if 'ZobrazObjekt.aspx' in href:
                    property_links.append(href)

            # Limit the number of links to process
            property_links = property_links[:int(max_results)]
            print(f"Found {len(property_links)} property links to process")

            # Process each property link
            properties = []
            for link in property_links:
                # Construct the full URL
                if not link.startswith('http'):
                    link = f"https://nahlizenidokn.cuzk.cz/{link.lstrip('/')}"

                # Fetch the property details
                property_response = self.session.get(link)

                if property_response.status_code == 200:
                    # Extract property details
                    property_data = self.extract_property_details(property_response.text)

                    if property_data and 'owner_name' in property_data:
                        # Add the property to the list
                        properties.append(property_data)

                # Respect rate limiting
                time.sleep(1)

                # Check if we have enough properties
                if len(properties) >= int(max_results):
                    break

            return properties

        except Exception as e:
            print(f"Error fetching properties in area: {e}")
            return None