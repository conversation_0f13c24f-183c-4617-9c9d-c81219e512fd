"""
Test script to verify that Mapy.cz city boundary detection works correctly.

This script:
1. Initializes the Mapy.cz integration
2. Tests getting city boundaries for several cities
3. Tests generating coordinates within those boundaries
4. Tests generating CUZK URLs for those coordinates
"""

import logging
import sys
import math
import time
from typing import Dict, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("MapyCZBoundaryTest")

# Import the necessary modules
try:
    from api.mapycz.mapycz_integration import MapyCZIntegration
    from api.cuzk.cuzk_integration import CUZKIntegration
except ImportError as e:
    logger.error(f"Error importing modules: {e}")
    logger.error("Make sure you're running this script from the project root directory")
    sys.exit(1)

def test_city_boundaries():
    """Test Mapy.cz city boundary detection"""
    logger.info("Testing Mapy.cz city boundary detection")
    
    # Initialize the Mapy.cz integration
    mapycz = MapyCZIntegration()
    
    # Initialize the CUZK integration
    cuzk = CUZKIntegration()
    
    # Test cities
    test_cities = [
        "Praha",
        "Brno",
        "Plzeň",
        "Bušovice",
        "Klecany",
        "Jihlava",
        "Zlín"
    ]
    
    results = {}
    
    # Test each city
    for city_name in test_cities:
        logger.info(f"\nTesting city: {city_name}")
        
        # Get city boundaries
        boundaries = mapycz.get_city_boundaries(city_name)
        
        if not boundaries:
            logger.error(f"Could not get boundaries for {city_name}")
            continue
            
        logger.info(f"Boundaries for {city_name}: {boundaries}")
        
        # Generate coordinates within the city
        coordinates = mapycz.generate_coordinates_in_city(boundaries)
        
        if not coordinates:
            logger.error(f"Could not generate coordinates for {city_name}")
            continue
            
        logger.info(f"Generated {len(coordinates)} coordinates for {city_name}")
        
        # Test a sample of the coordinates
        sample_size = min(5, len(coordinates))
        sample_indices = [int(i * (len(coordinates) - 1) / (sample_size - 1)) for i in range(sample_size)]
        
        sample_coordinates = [coordinates[i] for i in sample_indices]
        
        logger.info(f"Testing {sample_size} sample coordinates")
        
        # Generate CUZK URLs for the sample coordinates
        sample_urls = []
        for coord in sample_coordinates:
            lat = coord['lat']
            lng = coord['lng']
            
            # Generate CUZK URL
            url = mapycz.generate_cuzk_url(lat, lng)
            
            sample_urls.append({
                'lat': lat,
                'lng': lng,
                'url': url
            })
            
            # Add a small delay to avoid overwhelming the server
            time.sleep(0.1)
        
        # Store the results
        results[city_name] = {
            'boundaries': boundaries,
            'coordinate_count': len(coordinates),
            'sample_urls': sample_urls
        }
    
    # Print summary
    logger.info("\n\nSummary of results:")
    for city_name, result in results.items():
        logger.info(f"\nCity: {city_name}")
        logger.info(f"Boundaries: {result['boundaries']}")
        logger.info(f"Generated {result['coordinate_count']} coordinates")
        logger.info(f"Sample URLs:")
        for i, url_data in enumerate(result['sample_urls']):
            logger.info(f"  {i+1}. Coordinates: ({url_data['lat']}, {url_data['lng']})")
            logger.info(f"     URL: {url_data['url']}")
    
    return results

if __name__ == "__main__":
    test_city_boundaries()
