#!/usr/bin/env python3
"""
Test runner for the Czech Property Registry application.
This script runs all the tests in the tests directory.
"""

import os
import sys
import unittest
import argparse

# Add the parent directory to the path to ensure imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


def discover_and_run_tests(test_type=None, verbose=False, specific_test=None):
    """
    Discover and run tests.

    Args:
        test_type (str, optional): Type of tests to run ('unit', 'integration', or None for all)
        verbose (bool, optional): Whether to show verbose output
        specific_test (str, optional): Specific test file to run (without .py extension)

    Returns:
        bool: True if all tests passed, False otherwise
    """
    # Create a test loader
    loader = unittest.TestLoader()

    # Determine the test directory
    if test_type == 'unit':
        test_dir = os.path.join(os.path.dirname(__file__), 'unit')
    elif test_type == 'integration':
        test_dir = os.path.join(os.path.dirname(__file__), 'integration')
    else:
        test_dir = os.path.dirname(__file__)

    # Discover tests
    print(f"Discovering tests in {test_dir}...")

    if specific_test:
        # Run a specific test file
        test_file = f"{specific_test}.py"
        test_path = os.path.join(test_dir, test_file)

        if os.path.exists(test_path):
            print(f"Running specific test: {test_file}")
            suite = loader.discover(test_dir, pattern=test_file)
        else:
            print(f"Test file not found: {test_path}")
            return False
    else:
        # Discover all tests
        suite = loader.discover(test_dir, pattern='test_*.py')

    # Create a test runner
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)

    # Run the tests
    print(f"Running {'all' if test_type is None else test_type} tests...")
    result = runner.run(suite)

    # Print a summary
    print(f"\nTest Summary:")
    print(f"  Ran {result.testsRun} tests")
    print(f"  Failures: {len(result.failures)}")
    print(f"  Errors: {len(result.errors)}")
    print(f"  Skipped: {len(result.skipped)}")

    # Return True if all tests passed
    return len(result.failures) == 0 and len(result.errors) == 0


def main():
    """Main entry point for the test runner."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run tests for the Czech Property Registry application.')
    parser.add_argument('--type', choices=['unit', 'integration', 'all'], default='all',
                        help='Type of tests to run (default: all)')
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='Show verbose output')
    parser.add_argument('--test', '-t', type=str,
                        help='Specific test file to run (without .py extension)')
    args = parser.parse_args()

    # Determine the test type
    test_type = None if args.type == 'all' else args.type

    # Run the tests
    success = discover_and_run_tests(test_type, args.verbose, args.test)

    # Exit with an appropriate status code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
