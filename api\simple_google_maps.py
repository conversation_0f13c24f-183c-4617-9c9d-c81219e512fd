"""
Simple Google Maps integration for the Czech Property Registry application.

This module provides a simplified Google Maps integration with minimal dependencies.
"""

import requests
import json
import os
import time
import logging
import urllib.parse

# Configure logging
logger = logging.getLogger(__name__)

class SimpleGoogleMaps:
    """
    A simplified Google Maps integration.

    This class provides basic Google Maps functionality with minimal dependencies.
    It includes geocoding and place autocomplete.
    """

    def __init__(self, api_key=None):
        """
        Initialize the Google Maps integration.

        Args:
            api_key: Google Maps API key
        """
        print(f"DEBUG: Initializing SimpleGoogleMaps with api_key={api_key}")
        self.api_key = api_key

        # Try to load API key from environment variable if not provided
        if not self.api_key:
            print(f"DEBUG: No API key provided, checking environment variable")
            self.api_key = os.environ.get('GOOGLE_MAPS_API_KEY')
            if self.api_key:
                print(f"DEBUG: Loaded API key from environment variable: {self.api_key[:5]}...")
            else:
                print(f"DEBUG: No API key found in environment variable")

        # Try to load API key from config.ini if not found in environment
        if not self.api_key:
            print(f"DEBUG: Checking config.ini for API key")
            try:
                import configparser
                config = configparser.ConfigParser()
                if os.path.exists('config.ini'):
                    config.read('config.ini')
                    if 'GOOGLE_MAPS' in config and 'api_key' in config['GOOGLE_MAPS']:
                        self.api_key = config['GOOGLE_MAPS']['api_key']
                        print(f"DEBUG: Loaded API key from config.ini: {self.api_key[:5]}...")
                    else:
                        print(f"DEBUG: No API key found in config.ini")
                else:
                    print(f"DEBUG: config.ini file not found")
            except Exception as e:
                print(f"DEBUG: Error loading API key from config.ini: {e}")

        # Try to load API key from file if not found in environment or config
        if not self.api_key:
            print(f"DEBUG: Checking google_maps_api_key.txt for API key")
            try:
                with open('google_maps_api_key.txt', 'r') as f:
                    self.api_key = f.read().strip()
                    print(f"DEBUG: Loaded API key from file: {self.api_key[:5]}...")
            except Exception as e:
                print(f"DEBUG: Error loading API key from file: {e}")

        # Initialize cache
        self.cache = {}
        self.cache_expiry = 3600  # 1 hour
        print(f"DEBUG: Initialized cache with expiry of {self.cache_expiry} seconds")

        # Log initialization
        if self.api_key:
            print(f"DEBUG: Google Maps API initialized with API key: {self.api_key[:5]}...")
            logger.info("Google Maps API initialized with API key")
        else:
            print(f"DEBUG: Google Maps API initialized WITHOUT API key")
            logger.warning("Google Maps API initialized without API key")

    def geocode(self, address):
        """
        Geocode an address.

        Args:
            address: Address to geocode

        Returns:
            dict: Geocoding result with lat and lng keys
        """
        # Check cache first
        cache_key = f"geocode:{address}"
        if cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            if time.time() < cache_entry['expiry']:
                logger.info(f"Using cached geocoding for '{address}'")
                return cache_entry['data']

        # If no API key, return None
        if not self.api_key:
            logger.warning("Cannot geocode without API key")
            return None

        try:
            # Encode the address
            encoded_address = urllib.parse.quote(address)

            # Construct the URL
            url = f"https://maps.googleapis.com/maps/api/geocode/json?address={encoded_address}&key={self.api_key}"

            # Make the request
            logger.info(f"Geocoding address: '{address}'")
            response = requests.get(url, timeout=10)

            # Check if the request was successful
            if response.status_code == 200:
                data = response.json()

                if data['status'] == 'OK' and data['results']:
                    # Extract the location
                    location = data['results'][0]['geometry']['location']
                    result = {
                        'lat': location['lat'],
                        'lng': location['lng'],
                        'formatted_address': data['results'][0]['formatted_address'],
                        'address_components': data['results'][0]['address_components']
                    }

                    # Cache the result
                    self.cache[cache_key] = {
                        'data': result,
                        'expiry': time.time() + self.cache_expiry
                    }

                    return result
                else:
                    logger.warning(f"Geocoding error: {data['status']}")
                    return None
            else:
                logger.warning(f"Geocoding HTTP error: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"Geocoding exception: {e}")
            return None

    def get_place_predictions(self, input_text, types=None, components=None):
        """
        Get place predictions from Google Maps Places API.

        Args:
            input_text: Text to get predictions for
            types: Types of predictions to return
            components: Components to restrict predictions to

        Returns:
            list: List of prediction dictionaries
        """
        print(f"DEBUG: get_place_predictions called with input_text='{input_text}', types={types}, components={components}")

        # Check cache first
        cache_key = f"places:{input_text}:{types}:{components}"
        if cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            if time.time() < cache_entry['expiry']:
                print(f"DEBUG: Using cached place predictions for '{input_text}'")
                logger.info(f"Using cached place predictions for '{input_text}'")
                return cache_entry['data']
            else:
                print(f"DEBUG: Cache expired for '{input_text}'")
        else:
            print(f"DEBUG: No cache entry for '{input_text}'")

        # If no API key, return empty list
        if not self.api_key:
            print(f"DEBUG: No API key available")
            logger.warning("Cannot get place predictions without API key")
            return []
        else:
            print(f"DEBUG: Using API key: {self.api_key[:5]}...")

        try:
            # Encode the input text
            encoded_input = urllib.parse.quote(input_text)
            print(f"DEBUG: Encoded input: '{encoded_input}'")

            # Construct the URL
            url = f"https://maps.googleapis.com/maps/api/place/autocomplete/json?input={encoded_input}&key={self.api_key}"

            # Add country restriction for Czech Republic
            url += "&components=country:cz"

            # Add optional parameters
            if types:
                url += f"&types={types}"
                print(f"DEBUG: Added types parameter: {types}")

            if components:
                components_str = "|".join([f"{k}:{v}" for k, v in components.items()])
                url += f"&components={components_str}"
                print(f"DEBUG: Added components parameter: {components_str}")

            # Make the request
            print(f"DEBUG: Making API request to URL: {url.replace(self.api_key, 'API_KEY_HIDDEN')}")
            logger.info(f"Getting place predictions for '{input_text}'")
            response = requests.get(url, timeout=10)

            # Check if the request was successful
            print(f"DEBUG: API response status code: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"DEBUG: API response status: {data.get('status', 'UNKNOWN')}")

                if data['status'] == 'OK':
                    predictions = data.get('predictions', [])
                    print(f"DEBUG: Received {len(predictions)} predictions")

                    # Log the first few predictions
                    if predictions:
                        print(f"DEBUG: First few predictions:")
                        for i, pred in enumerate(predictions[:3]):
                            print(f"DEBUG:   {i+1}. {pred.get('description', 'NO_DESCRIPTION')}")

                    # Cache the result
                    self.cache[cache_key] = {
                        'data': predictions,
                        'expiry': time.time() + self.cache_expiry
                    }
                    print(f"DEBUG: Cached predictions with expiry in {self.cache_expiry} seconds")

                    return predictions
                else:
                    error_message = data.get('error_message', 'No error message')
                    print(f"DEBUG: API error: {data['status']} - {error_message}")
                    logger.warning(f"Place predictions error: {data['status']} - {error_message}")
                    return []
            else:
                print(f"DEBUG: HTTP error: {response.status_code}")
                logger.warning(f"Place predictions HTTP error: {response.status_code}")
                return []
        except Exception as e:
            print(f"DEBUG: Exception: {e}")
            import traceback
            print(f"DEBUG: {traceback.format_exc()}")
            logger.error(f"Place predictions exception: {e}")
            return []

    def get_autocomplete_suggestions(self, text):
        """
        Get autocomplete suggestions for the given text.

        This is an alias for get_place_predictions to maintain compatibility with
        code that expects this method name.

        Args:
            text: Text to get suggestions for

        Returns:
            list: List of suggestion strings
        """
        print(f"DEBUG: get_autocomplete_suggestions called with text='{text}'")

        # Call get_place_predictions to get the raw suggestions
        raw_suggestions = self.get_place_predictions(text)

        # Convert the raw suggestions to a list of strings
        suggestions = []
        if raw_suggestions:
            for suggestion in raw_suggestions:
                if isinstance(suggestion, dict) and 'description' in suggestion:
                    suggestions.append(suggestion['description'])
                else:
                    suggestions.append(str(suggestion))

            print(f"DEBUG: Converted {len(raw_suggestions)} raw suggestions to {len(suggestions)} string suggestions")
            if suggestions:
                print(f"DEBUG: First few suggestions: {suggestions[:3]}")
        else:
            print(f"DEBUG: No suggestions found for '{text}'")

        return suggestions

    def validate_api_key(self):
        """
        Validate the API key.

        Returns:
            bool: True if the API key is valid, False otherwise
        """
        if not self.api_key:
            logger.warning("No API key to validate")
            return False

        try:
            # Try a simple geocoding request
            url = f"https://maps.googleapis.com/maps/api/geocode/json?address=Prague&key={self.api_key}"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data['status'] == 'OK':
                    logger.info("API key is valid")
                    return True
                else:
                    logger.warning(f"API key validation error: {data['status']}")
                    return False
            else:
                logger.warning(f"API key validation HTTP error: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"API key validation exception: {e}")
            return False
