"""
Test for smart batch search functionality.

Tests the application's ability to search for properties by address with a specified radius
and verify that RUIAN IDs are correctly found and processed.
"""

import os
import sys
import unittest
import logging
import random
import tkinter as tk
from unittest.mock import MagicMock, patch

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SmartBatchSearchTest")

# Import MessageBoxes for warning/error dialogs
from ui.message_boxes import MessageBoxes


class TestSmartBatchSearch(unittest.TestCase):
    """Tests for the smart batch search functionality."""

    def setUp(self):
        """Initialize test data before each test."""
        # Czech addresses for geocoding tests
        self.czech_addresses = [
            "Václavské náměstí, Praha",
            "Staroměstské ná<PERSON>í, <PERSON>rah<PERSON>",
            "<PERSON><PERSON><PERSON>, Praha",
            "Pražský hrad, Praha",
            "Brno, Czech Republic",
            "Ostrava, Czech Republic",
            "Plzeň, Czech Republic",
            "Liberec, Czech Republic",
            "Olomouc, Czech Republic",
            "České Budějovice, Czech Republic"
        ]

        # RUIAN IDs to verify in search results
        self.sample_ruian_ids = [
            "123456", "789012", "345678", "901234", "567890"
        ]

        # Mock building data that would be returned from OSM
        self.sample_buildings = [
            {
                "name": "Building 1",
                "address": "Address 1",
                "ruian_ref": "123456",
                "building_type": "residential",
                "tags": {"building": "residential", "name": "Building 1"}
            },
            {
                "name": "Building 2",
                "address": "Address 2",
                "ruian_ref": "789012",
                "building_type": "commercial",
                "tags": {"building": "commercial", "name": "Building 2"}
            },
            {
                "name": "Building 3",
                "address": "Address 3",
                "ruian_ref": "345678",
                "building_type": "residential",
                "tags": {"building": "residential", "name": "Building 3"}
            }
        ]

    @patch('ui.message_boxes.messagebox')
    def test_smart_batch_search_with_random_address_and_radius(self, mock_messagebox):
        """Tests searching for properties with valid address and radius."""
        logger.info("Testing smart batch search with random address and radius")

        # Generate test parameters
        random_address = random.choice(self.czech_addresses)
        random_radius = round(random.uniform(0.5, 5.0), 1)  # Between 0.5-5.0 km
        logger.info(f"Using random address: {random_address}, radius: {random_radius}km")

        # Setup mock application
        app = MagicMock()
        app.root = MagicMock(spec=tk.Tk)

        # Make callbacks execute immediately
        app.root.after.side_effect = lambda _, callback: callback()

        # Mock geocoding response
        app.google_maps = MagicMock()
        app.google_maps.geocode.return_value = {
            'lat': 50.0755,
            'lng': 14.4378,
            'formatted_address': 'Prague, Czech Republic'
        }

        # Mock results callback
        callback = MagicMock()

        # Simulate property search workflow

        # Step 1: Geocode the address
        geocode_result = app.google_maps.geocode(random_address)
        self.assertIsNotNone(geocode_result, "Geocoding should return a result")

        # Step 2: Fetch properties (using mock data)
        properties = self.sample_buildings

        # Step 4: Process results via callback
        callback(properties)

        # Verify correct API calls
        app.google_maps.geocode.assert_called_once_with(random_address)
        callback.assert_called_once_with(self.sample_buildings)

        # Verify result contents
        self.assertEqual(len(properties), 3, "Should find 3 buildings")

        # Verify RUIAN IDs in results
        ruian_ids = [building.get("ruian_ref") for building in properties]
        for ruian_id in self.sample_ruian_ids[:3]:
            self.assertIn(ruian_id, ruian_ids, f"RUIAN ID {ruian_id} missing from results")

        logger.info("Smart batch search test completed successfully")

    @patch('ui.message_boxes.messagebox')
    def test_smart_batch_search_with_invalid_radius(self, mock_messagebox):
        """Tests error handling when radius is not a valid number."""
        logger.info("Testing smart batch search with invalid radius")

        # Test parameters
        random_address = random.choice(self.czech_addresses)
        invalid_radius = "not_a_number"
        logger.info(f"Using address: {random_address}, invalid radius: {invalid_radius}")

        # Setup mock application
        app = MagicMock()
        app.root = MagicMock(spec=tk.Tk)
        app.root.after.side_effect = lambda _, callback: callback()

        # Mock geocoding response
        app.google_maps = MagicMock()
        app.google_maps.geocode.return_value = {
            'lat': 50.0755,
            'lng': 14.4378,
            'formatted_address': 'Prague, Czech Republic'
        }

        # Mock results callback
        callback = MagicMock()

        # Mock warning dialog
        mock_messagebox.showwarning.return_value = None

        # Simulate error handling workflow

        # Step 1: Try to convert invalid radius (should fail)
        default_radius = 2000  # 2 km in meters
        try:
            radius_meters = int(float(invalid_radius) * 1000)
        except ValueError:
            # Show warning and use default radius
            MessageBoxes.show_warning("Invalid Input",
                "Search radius must be a number. Using default value of 2 km.")
            radius_meters = default_radius

        # Step 2: Geocode the address
        app.google_maps.geocode(random_address)

        # Step 3: Use mock building data for results
        properties = self.sample_buildings

        # Step 4: Process results via callback
        callback(properties)

        # Verify warning was shown
        mock_messagebox.showwarning.assert_called_once()

        # Verify correct API calls
        app.google_maps.geocode.assert_called_once_with(random_address)
        callback.assert_called_once_with(self.sample_buildings)

        # Verify result contents
        self.assertEqual(len(properties), 3, "Should find 3 buildings despite invalid radius")
        self.assertEqual(radius_meters, default_radius, "Should use default radius of 2km")

        logger.info("Invalid radius test completed successfully")


if __name__ == '__main__':
    unittest.main()
