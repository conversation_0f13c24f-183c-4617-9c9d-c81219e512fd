"""
Letter generation functionality for the Czech Property Registry application.
"""

import os
import threading
from datetime import datetime
from tkinter import filedialog
import tkinter as tk
from ui.message_boxes import MessageBoxes
from utils.template_loader import TemplateLoader


class LetterGenerator:
    """Class for generating and managing property purchase offer letters"""

    def __init__(self, root=None, gpt_integration=None):
        """
        Initialize the letter generator

        Args:
            root (tk.Tk, optional): Tkinter root window
            gpt_integration: GPT integration instance
        """
        self.root = root
        self.gpt = gpt_integration
        self._gender_cache = {}
        self.batch_letters = {}
        self.template_loader = TemplateLoader()

    def determine_gender(self, name):
        """
        Determine gender from Czech name using GPT, rules, or OpenAI if available

        This method tries multiple approaches in order:
        1. Check cache
        2. Use GPT integration if enabled
        3. Use rule-based approach for common Czech name patterns
        4. Default to male

        Args:
            name (str): Name to determine gender for

        Returns:
            str: 'male' or 'female'
        """
        # Check cache first for better performance
        if name in self._gender_cache:
            return self._gender_cache[name]

        # Handle empty or invalid names
        if not name or not isinstance(name, str):
            return 'male'  # Default for invalid input

        # Check for company names (usually contain legal form indicators)
        company_indicators = ['s.r.o.', 'a.s.', 'k.s.', 'v.o.s.', 'spol.', 'z.ú.', 'o.p.s.']
        if any(indicator in name for indicator in company_indicators):
            self._gender_cache[name] = 'male'  # Companies use male grammatical gender in Czech
            return 'male'

        # Try GPT integration if available
        if self.gpt:
            try:
                gender = self.gpt.determine_gender(name)
                if gender in ['male', 'female']:
                    self._gender_cache[name] = gender
                    return gender
            except Exception as e:
                print(f"Error determining gender with GPT integration: {e}")

        # Rule-based approach for Czech names
        name_parts = name.split()
        last_name = name_parts[-1] if name_parts else ""

        # Most Czech female surnames end with 'ová'
        if last_name.endswith('ová'):
            self._gender_cache[name] = 'female'
            return 'female'

        # Enhanced fallback - check for more Czech naming patterns
        # Most Czech surnames ending with consonants are male
        if last_name and last_name[-1] not in 'aeiouyáéíóúůý':
            self._gender_cache[name] = 'male'
            return 'male'

        # Check first name if available (for cases where last name is ambiguous)
        if len(name_parts) > 1:
            first_name = name_parts[0]
            # Common Czech female first names endings
            if first_name.endswith(('a', 'e', 'ie')):
                self._gender_cache[name] = 'female'
                return 'female'

        # Default to male if we can't determine (more common in property ownership)
        self._gender_cache[name] = 'male'
        return 'male'

    def generate_letter(self, owner_name, owner_address, owner_gender, buyer_name,
                       buyer_address, buyer_contact, style="formal", tone="neutral",
                       personalization=1, use_gpt=True):
        """
        Generate a letter to the property owner using GPT or template

        Args:
            owner_name (str): Property owner's name
            owner_address (str): Property owner's address
            owner_gender (str): Property owner's gender ('male' or 'female')
            buyer_name (str): Buyer's name
            buyer_address (str): Buyer's address
            buyer_contact (str): Buyer's contact information
            style (str, optional): Letter style ('formal', 'semiformal', or 'casual')
            tone (str, optional): Letter tone ('neutral', 'friendly', or 'persuasive')
            personalization (int, optional): Personalization level (1-5)
            use_gpt (bool, optional): Whether to use GPT for letter generation

        Returns:
            str: Generated letter
        """
        # Try to use GPT for letter generation if enabled
        if use_gpt and self.gpt:
            try:
                # Generate letter with GPT
                letter = self.gpt.generate_letter(
                    owner_name=owner_name,
                    owner_address=owner_address,
                    owner_gender=owner_gender,
                    buyer_name=buyer_name,
                    buyer_address=buyer_address,
                    buyer_contact=buyer_contact,
                    style=style,
                    tone=tone,
                    personalization=personalization
                )

                if letter:
                    return letter

            except Exception as e:
                print(f"Error generating letter with GPT: {e}")
                # Fall back to template-based letter

        # Fall back to template-based letter if GPT fails or is disabled
        # Generate appropriate salutation based on gender
        salutation = "Vážený pane" if owner_gender == "male" else "Vážená paní"
        owner_last_name = owner_name.split()[-1] if owner_name else ""

        # Use the template loader to load and format the letter template
        try:
            letter = self.template_loader.format_template(
                'letter_template',
                buyer_name=buyer_name,
                buyer_address=buyer_address,
                owner_name=owner_name,
                owner_address=owner_address,
                current_date=self.get_current_date(),
                salutation=salutation,
                owner_last_name=owner_last_name,
                buyer_contact=buyer_contact
            )
        except Exception as e:
            print(f"Error loading letter template: {e}")
            # Fallback to hardcoded template if template loading fails
            letter = f"""{buyer_name}
{buyer_address}

{owner_name}
{owner_address}

Datum: {self.get_current_date()}

Předmět: Nabídka ke koupi nemovitosti

{salutation} {owner_last_name},

dovoluji si Vás oslovit s nabídkou odkoupení Vaší nemovitosti. Mám vážný zájem o koupi této nemovitosti a rád bych s Vámi dojednal podmínky případného prodeje.

Prosím o kontaktování na níže uvedené údaje pro další jednání.

S pozdravem,

{buyer_name}
Tel: {buyer_contact}
"""

        return letter

    def get_current_date(self):
        """Get the current date in Czech format"""
        return datetime.now().strftime("%d.%m.%Y")

    def save_letter(self, letter_content):
        """
        Save a letter to a file

        Args:
            letter_content (str): Letter content to save

        Returns:
            bool: True if the letter was saved successfully, False otherwise
        """
        if not letter_content.strip():
            if self.root:
                MessageBoxes.show_error("Error", "No letter to save")
            return False

        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(letter_content)
                if self.root:
                    MessageBoxes.show_info("Success", f"Letter saved to {file_path}")
                return True
            except Exception as e:
                if self.root:
                    MessageBoxes.show_error("Error", f"Failed to save file: {str(e)}")
                print(f"Error saving letter: {e}")
                return False

        return False

    def generate_batch_letters(self, property_list, buyer_name, buyer_address, buyer_contact,
                              style="formal", tone="neutral", personalization=1, use_gpt=True,
                              progress_callback=None, status_callback=None):
        """
        Generate letters for all property owners in the property list

        Args:
            property_list (list): List of property data dictionaries
            buyer_name (str): Buyer's name
            buyer_address (str): Buyer's address
            buyer_contact (str): Buyer's contact information
            style (str, optional): Letter style ('formal', 'semiformal', or 'casual')
            tone (str, optional): Letter tone ('neutral', 'friendly', or 'persuasive')
            personalization (int, optional): Personalization level (1-5)
            use_gpt (bool, optional): Whether to use GPT for letter generation
            progress_callback (function, optional): Callback function for progress updates
            status_callback (function, optional): Callback function for status updates

        Returns:
            dict: Dictionary of generated letters (owner_name -> letter)
        """
        if not property_list:
            if self.root:
                MessageBoxes.show_error("Error", "No properties found. Please search for properties first.")
            return {}

        # Create a dictionary to store generated letters
        self.batch_letters = {}

        # Generate letters for each property owner
        for i, property_data in enumerate(property_list):
            # Extract owner information
            owner_name = property_data.get('owner_name', 'Unknown')
            owner_address = property_data.get('owner_address', 'Unknown')

            # Determine gender
            owner_gender = self.determine_gender(owner_name)

            # Update progress if callback provided
            if progress_callback:
                progress_callback(i+1, len(property_list))

            # Update status if callback provided
            if status_callback:
                status_callback(f"Generating letter for {owner_name}...")

            # Generate letter
            try:
                letter = self.generate_letter(
                    owner_name=owner_name,
                    owner_address=owner_address,
                    owner_gender=owner_gender,
                    buyer_name=buyer_name,
                    buyer_address=buyer_address,
                    buyer_contact=buyer_contact,
                    style=style,
                    tone=tone,
                    personalization=personalization,
                    use_gpt=use_gpt
                )

                # Store the letter
                if letter:
                    self.batch_letters[owner_name] = letter

            except Exception as e:
                print(f"Error generating letter for {owner_name}: {e}")
                # Continue with the next property

        # Update status if callback provided
        if status_callback:
            status_callback(f"Generated {len(self.batch_letters)} letters successfully")

        return self.batch_letters

    def generate_letter_from_ui(self, app):
        """
        Generate a letter using data from the UI

        Args:
            app: The main application instance with UI elements

        Returns:
            str: Generated letter
        """
        try:
            # Get owner information
            owner_name = app.owner_name if hasattr(app, 'owner_name') else ""
            owner_address = app.owner_address if hasattr(app, 'owner_address') else ""
            owner_gender = app.owner_gender if hasattr(app, 'owner_gender') else "male"

            # Get buyer information from UI
            buyer_name = app.buyer_name.get().strip() if hasattr(app, 'buyer_name') else ""
            buyer_address = app.buyer_address.get().strip() if hasattr(app, 'buyer_address') else ""
            buyer_contact = app.buyer_contact.get().strip() if hasattr(app, 'buyer_contact') else ""

            # Check if we have the required information
            if not owner_name or not buyer_name:
                if self.root:
                    from ui.message_boxes import MessageBoxes
                    MessageBoxes.show_error("Input Error", "Please enter owner and buyer information")
                return None

            # Get letter style and personalization settings
            style = app.letter_style_var.get() if hasattr(app, 'letter_style_var') else "formal"
            tone = app.letter_tone_var.get() if hasattr(app, 'letter_tone_var') else "neutral"
            personalization = app.letter_personalization_var.get() if hasattr(app, 'letter_personalization_var') else 1
            use_gpt = app.use_gpt_letter.get() if hasattr(app, 'use_gpt_letter') else False

            # Generate the letter
            letter = self.generate_letter(
                owner_name=owner_name,
                owner_address=owner_address,
                owner_gender=owner_gender,
                buyer_name=buyer_name,
                buyer_address=buyer_address,
                buyer_contact=buyer_contact,
                style=style,
                tone=tone,
                personalization=personalization,
                use_gpt=use_gpt
            )

            return letter

        except Exception as e:
            print(f"Error generating letter from UI: {e}")
            if self.root:
                from ui.message_boxes import MessageBoxes
                MessageBoxes.show_error("Error", f"An error occurred while generating the letter: {str(e)}")
            return None

    def export_batch_letters(self, export_dir=None, progress_callback=None, status_callback=None):
        """
        Export all generated letters to individual files

        Args:
            export_dir (str, optional): Directory to save the letters
            progress_callback (function, optional): Callback function for progress updates
            status_callback (function, optional): Callback function for status updates

        Returns:
            int: Number of successfully exported letters
        """
        if not self.batch_letters:
            if self.root:
                MessageBoxes.show_error("Error", "No letters to export. Please generate letters first.")
            return 0

        # Ask for a directory to save the letters if not provided
        if not export_dir:
            export_dir = filedialog.askdirectory(
                title="Select Directory to Save Letters"
            )

        if not export_dir:
            return 0

        # Update status if callback provided
        if status_callback:
            status_callback("Exporting letters...")

        # Export each letter to a file
        success_count = 0
        for i, (owner_name, letter) in enumerate(self.batch_letters.items()):
            # Create a safe filename from the owner name
            safe_name = "".join(c if c.isalnum() or c in " -_" else "_" for c in owner_name)
            file_name = f"Letter_to_{safe_name}.txt"
            file_path = os.path.join(export_dir, file_name)

            # Update progress if callback provided
            if progress_callback:
                progress_callback(i+1, len(self.batch_letters))

            # Update status if callback provided
            if status_callback:
                status_callback(f"Exporting letter for {owner_name}...")

            try:
                # Write the letter to a file
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(letter)
                success_count += 1
            except Exception as e:
                print(f"Error exporting letter for {owner_name}: {e}")
                # Continue with the next letter

        # Update status if callback provided
        if status_callback:
            status_callback(f"Exported {success_count} letters successfully")

        # Show success message
        if self.root and success_count > 0:
            MessageBoxes.show_info(
                "Success",
                f"Exported {success_count} letters to {export_dir}"
            )

        return success_count
